{"name": "armory-x", "version": "1.0.0", "description": "Armory X - Premium Desktop Application", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "npm run build"}, "keywords": ["armory-x", "desktop", "gaming", "utility"], "author": "Armory X", "license": "MIT", "devDependencies": {"@electron/rebuild": "^4.0.1", "electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9"}, "dependencies": {"benchmark": "^2.1.4", "chokidar": "^3.5.3", "firebase": "^11.10.0", "fs-extra": "^11.2.0", "glob": "^10.3.10", "node-hid": "^3.2.0", "node-machine-id": "^1.1.12", "systeminformation": "^5.27.7", "winreg": "^1.2.5"}, "build": {"appId": "com.armoryx.desktop", "productName": "Armory X", "directories": {"output": "dist"}, "files": ["*.js", "*.html", "*.css", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/Armory_X.ico", "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": "dmg", "icon": "assets/Armory_X.ico"}, "linux": {"target": "AppImage", "icon": "assets/Armory_X.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/Armory_X.ico", "uninstallerIcon": "assets/Armory_X.ico"}}}