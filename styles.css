/* ===================================
   ArmoryX Electron - Main Stylesheet
   Modular CSS Architecture
   =================================== */

/* Import modular CSS files in dependency order */

/* 1. Foundation - Variables and Reset */
@import './styles/variables.css';
@import './styles/reset.css';

/* 2. Layout and Structure */
@import './styles/layout.css';

/* 3. Animations and Effects */
@import './styles/animations.css';

/* 4. Reusable Components */
@import './styles/components.css';

/* 5. Dashboard and Widgets (must be first) */
@import './styles/dashboard.css';

/* 6. Modal System */
@import './styles/modals.css';

/* 7. Tools and System Interface */
@import './styles/tools.css';

/* 8. Steam-like Game Cards */
@import './styles/steam-games.css';

/* 9. Mod Manager Interface */
@import './styles/mod-manager.css';

/* 10. HWID Spoofer System */
@import './styles/hwid-spoofer.css';

/* 11. Beta Warning System */
@import './styles/beta-warning.css';

/* 12. Download System */
@import './styles/download-system.css';

/* 13. Confirmation Dialogs */
@import './styles/confirmation-dialogs.css';

/* 14. File Browser System */
@import './styles/file-browser.css';

/* 15. Settings and Configuration */
@import './styles/settings-system.css';

/* 16. Navigation and Sidebar */
@import './styles/navigation-sidebar.css';

/* 17. Widgets and Grid Systems */
@import './styles/widgets-grids.css';

/* 18. Dropdowns and Security */
@import './styles/dropdowns-security.css';

/* 19. Badges and Premium Features */
@import './styles/badges-premium.css';

/* 20. Scrollbars and Overlays */
@import './styles/scrollbars-overlays.css';

/* 21. Responsive Design (must be last) */
@import './styles/responsive.css';

/* 
   All styles have been modularized into separate files.
   This main file now only contains the import statements.
   
   File structure:
   - styles/variables.css: CSS custom properties and theme configuration
   - styles/reset.css: Global reset and base styles
   - styles/layout.css: App layout, header, sidebar, and main content structure
   - styles/animations.css: Keyframe animations and visual effects
   - styles/components.css: Reusable UI components (buttons, cards, forms, etc.)
   - styles/dashboard.css: Dashboard-specific styles and widgets
   - styles/modals.css: Modal system and dialog styles
   - styles/tools.css: Tools interface and system-specific styles
   - styles/steam-games.css: Steam-like game card interface
   - styles/mod-manager.css: Complete mod management interface
   - styles/hwid-spoofer.css: Hardware ID spoofing functionality
   - styles/beta-warning.css: Beta warning banners and modals
   - styles/download-system.css: Download progress and file management
   - styles/confirmation-dialogs.css: Steam/Xbox confirmation dialogs and game integration
   - styles/file-browser.css: File browser system and directory management
   - styles/settings-system.css: Settings and configuration interface
   - styles/navigation-sidebar.css: Navigation menu and sidebar components
   - styles/widgets-grids.css: Widget containers and grid layout systems
   - styles/dropdowns-security.css: Dropdown menus and security components
   - styles/badges-premium.css: Badge systems and premium feature styling
   - styles/scrollbars-overlays.css: Custom scrollbars and overlay systems
   - styles/responsive.css: Media queries and responsive design rules
   
   Benefits of this modular approach:
   - Easier maintenance and debugging
   - Better organization and code reusability
   - Faster development when working on specific features
   - Reduced merge conflicts in team environments
   - Improved performance through selective loading
   - Better caching strategies possible
*/
