# ⚡ INSTANT Deploy with Surge.sh (30 seconds)

## No Signup Required - Deploy Right Now!

### Step 1: Install Surge
```bash
npm install -g surge
```
*Don't have npm? Download from [nodejs.org](https://nodejs.org) first*

### Step 2: Deploy Your Site
1. Open terminal/command prompt
2. Navigate to your `ArmoryX-Website` folder:
   ```bash
   cd path/to/ArmoryX-Website
   ```
3. Run surge:
   ```bash
   surge
   ```
4. When prompted:
   - **Email**: Enter your email
   - **Password**: Create a password  
   - **Domain**: Type `armoryx.software`

### Step 3: Configure Your Domain
1. Buy `armoryx.software` on Namecheap
2. In Namecheap DNS settings, add:
   ```
   Type: CNAME
   Host: @
   Value: na-west1.surge.sh
   TTL: Automatic
   ```

## ✅ Done!
Your site will be live at `https://armoryx.software` in minutes!

## Update Your Site Later
Just run `surge` again in the same folder - it will update instantly!

---

**Surge.sh Benefits:**
- ✅ No signup required initially  
- ✅ Instant deploys
- ✅ Free custom domains
- ✅ HTTPS included
- ✅ Simple updates 