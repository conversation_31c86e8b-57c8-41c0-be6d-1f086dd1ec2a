const robot = require('robotjs');
const { globalShortcut } = require('electron');

class AutomationTools {
    constructor() {
        this.toolStates = {
            autoClicker: {
                active: false,
                interval: null,
                settings: {
                    button: 'left',
                    clickType: 'single',
                    interval: 100,
                    ludicrousMode: false,
                    hotkey: 'F6'
                }
            },
            antiRecoil: {
                active: false,
                interval: null,
                mousePressed: false,
                settings: {
                    strength: 5,
                    hotkey: 'F7'
                }
            },
            keySequence: {
                active: false,
                interval: null,
                currentIndex: 0,
                settings: {
                    keys: 'A,S,D,W',
                    delay: 100,
                    hotkey: 'F8'
                }
            }
        };

        this.hotkeysRegistered = false;
        this.mainWindow = null;

        // Set robot settings for better performance
        robot.setMouseDelay(1);
        robot.setKeyboardDelay(1);
    }

    setMainWindow(window) {
        this.mainWindow = window;
    }

    registerHotkeys() {
        if (this.hotkeysRegistered) return;

        try {
            // Register hotkeys for each tool
            Object.entries(this.toolStates).forEach(([toolName, tool]) => {
                const success = globalShortcut.register(tool.settings.hotkey, () => {
                    this.toggleTool(toolName);
                });

                if (success) {
                    console.log(`✅ Registered hotkey ${tool.settings.hotkey} for ${toolName}`);
                } else {
                    console.warn(`❌ Failed to register hotkey ${tool.settings.hotkey} for ${toolName}`);
                }
            });

            this.hotkeysRegistered = true;
        } catch (error) {
            console.error('❌ Error registering automation tool hotkeys:', error);
        }
    }

    unregisterHotkeys() {
        if (!this.hotkeysRegistered) return;

        try {
            Object.values(this.toolStates).forEach(tool => {
                globalShortcut.unregister(tool.settings.hotkey);
            });
            this.hotkeysRegistered = false;
            console.log('✅ Automation tool hotkeys unregistered');
        } catch (error) {
            console.error('❌ Error unregistering automation tool hotkeys:', error);
        }
    }

    // Auto-Clicker functionality
    startAutoClicker(settings) {
        const tool = this.toolStates.autoClicker;
        
        if (tool.active) {
            this.stopAutoClicker();
        }

        // Update settings
        if (settings) {
            Object.assign(tool.settings, settings);
        }

        tool.active = true;

        const clickFunction = () => {
            if (!tool.active) return;

            try {
                // Get current mouse position
                const mousePos = robot.getMousePos();
                
                // Perform the click
                robot.mouseClick(tool.settings.button, tool.settings.clickType === 'double');
                
                console.log(`🖱️ Auto-click performed at (${mousePos.x}, ${mousePos.y})`);
            } catch (error) {
                console.error('Auto-clicker error:', error);
            }
        };

        // Set interval based on ludicrous mode
        const interval = tool.settings.ludicrousMode ? 1 : tool.settings.interval;
        tool.interval = setInterval(clickFunction, interval);

        console.log(`✅ Auto-clicker started with ${interval}ms interval`);
        this.notifyRenderer('auto-clicker-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Auto-clicker started' };
    }

    stopAutoClicker() {
        const tool = this.toolStates.autoClicker;
        
        if (tool.interval) {
            clearInterval(tool.interval);
            tool.interval = null;
        }
        
        tool.active = false;
        
        console.log('✅ Auto-clicker stopped');
        this.notifyRenderer('auto-clicker-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Auto-clicker stopped' };
    }

    // Anti-Recoil functionality
    startAntiRecoil(settings) {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.active) {
            this.stopAntiRecoil();
        }

        // Update settings
        if (settings) {
            Object.assign(tool.settings, settings);
        }

        tool.active = true;

        // Use a simpler approach - monitor for recoil compensation continuously when active
        // The actual trigger will be when the user physically holds down left mouse button
        tool.compensationInterval = setInterval(() => {
            if (!tool.active) return;

            try {
                // Check if left mouse button is currently pressed
                // Note: robotjs doesn't have a direct way to check mouse button state
                // So we'll rely on system-level detection or use a different approach
                
                // For now, we'll activate compensation and let the user manually toggle
                // This is a simplified version - in a real implementation, you might need
                // additional libraries or Windows API calls to detect mouse button state
                
                // We'll start compensation when tool is active
                // Users can toggle off when not needed
                if (!tool.compensationActive) {
                    this.startRecoilCompensation();
                    tool.compensationActive = true;
                }
            } catch (error) {
                console.error('Anti-recoil monitoring error:', error);
            }
        }, 100); // Check every 100ms

        console.log(`✅ Anti-recoil started with strength ${tool.settings.strength}`);
        this.notifyRenderer('anti-recoil-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Anti-recoil started' };
    }

    stopAntiRecoil() {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.compensationInterval) {
            clearInterval(tool.compensationInterval);
            tool.compensationInterval = null;
        }
        
        if (tool.recoilInterval) {
            clearInterval(tool.recoilInterval);
            tool.recoilInterval = null;
        }
        
        tool.active = false;
        tool.compensationActive = false;
        
        console.log('✅ Anti-recoil stopped');
        this.notifyRenderer('anti-recoil-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Anti-recoil stopped' };
    }

    startRecoilCompensation() {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.recoilInterval) return;

        tool.recoilInterval = setInterval(() => {
            if (!tool.active || !tool.compensationActive) return;

            try {
                const currentPos = robot.getMousePos();
                robot.moveMouse(currentPos.x, currentPos.y - tool.settings.strength);
            } catch (error) {
                console.error('Recoil compensation error:', error);
            }
        }, 10);
    }

    stopRecoilCompensation() {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.recoilInterval) {
            clearInterval(tool.recoilInterval);
            tool.recoilInterval = null;
        }
        
        tool.compensationActive = false;
    }

    // Key Sequence functionality
    startKeySequence(settings) {
        const tool = this.toolStates.keySequence;
        
        if (tool.active) {
            this.stopKeySequence();
        }

        // Update settings
        if (settings) {
            Object.assign(tool.settings, settings);
        }

        const keys = tool.settings.keys.split(',').map(key => key.trim().toLowerCase());
        
        if (keys.length === 0) {
            return { success: false, message: 'No keys specified' };
        }

        tool.active = true;
        tool.currentIndex = 0;

        const sequenceFunction = () => {
            if (!tool.active || keys.length === 0) return;

            try {
                const key = keys[tool.currentIndex];
                robot.keyTap(key);
                
                console.log(`⌨️ Key pressed: ${key}`);
                
                tool.currentIndex = (tool.currentIndex + 1) % keys.length;
            } catch (error) {
                console.error('Key sequence error:', error);
            }
        };

        tool.interval = setInterval(sequenceFunction, tool.settings.delay);

        console.log(`✅ Key sequence started: ${keys.join(', ')} with ${tool.settings.delay}ms delay`);
        this.notifyRenderer('key-sequence-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Key sequence started' };
    }

    stopKeySequence() {
        const tool = this.toolStates.keySequence;
        
        if (tool.interval) {
            clearInterval(tool.interval);
            tool.interval = null;
        }
        
        tool.active = false;
        tool.currentIndex = 0;
        
        console.log('✅ Key sequence stopped');
        this.notifyRenderer('key-sequence-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Key sequence stopped' };
    }

    // Generic tool toggle
    toggleTool(toolName) {
        const tool = this.toolStates[toolName];
        if (!tool) return { success: false, message: 'Unknown tool' };

        switch (toolName) {
            case 'autoClicker':
                return tool.active ? this.stopAutoClicker() : this.startAutoClicker();
            case 'antiRecoil':
                return tool.active ? this.stopAntiRecoil() : this.startAntiRecoil();
            case 'keySequence':
                return tool.active ? this.stopKeySequence() : this.startKeySequence();
            default:
                return { success: false, message: 'Unknown tool' };
        }
    }

    // Update tool settings
    updateToolSettings(toolName, settings) {
        const tool = this.toolStates[toolName];
        if (!tool) return { success: false, message: 'Unknown tool' };

        Object.assign(tool.settings, settings);
        
        // If tool is active, restart with new settings
        if (tool.active) {
            this.toggleTool(toolName);  // Stop
            this.toggleTool(toolName);  // Start with new settings
        }

        console.log(`✅ Updated ${toolName} settings:`, settings);
        return { success: true, message: 'Settings updated' };
    }

    // Get tool states
    getToolStates() {
        return JSON.parse(JSON.stringify(this.toolStates));
    }

    // Notify renderer process of status changes
    notifyRenderer(event, data) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send(event, data);
        }
    }

    // Cleanup function
    cleanup() {
        // Stop all active tools
        Object.keys(this.toolStates).forEach(toolName => {
            const tool = this.toolStates[toolName];
            if (tool.active) {
                this.toggleTool(toolName);
            }
        });

        // Unregister hotkeys
        this.unregisterHotkeys();

        console.log('✅ Automation tools cleaned up');
    }
}

module.exports = { AutomationTools }; 