# Simple Armory X WiX 4.0 Build Script

Write-Host "Building Armory X Installer..." -ForegroundColor Cyan

# Build the MSI
wix build armory-x-simple.wxs -out ArmoryX-Setup.msi

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nSUCCESS: ArmoryX-Setup.msi created!" -ForegroundColor Green
    Write-Host "`nTo install:" -ForegroundColor Yellow
    Write-Host "  msiexec /i ArmoryX-Setup.msi" -ForegroundColor White
    Write-Host "`nTo install silently:" -ForegroundColor Yellow
    Write-Host "  msiexec /i ArmoryX-Setup.msi /quiet" -ForegroundColor White
    Write-Host "`nTo uninstall:" -ForegroundColor Yellow
    Write-Host "  msiexec /x ArmoryX-Setup.msi" -ForegroundColor White
} else {
    Write-Host "ERROR: Build failed" -ForegroundColor Red
} 