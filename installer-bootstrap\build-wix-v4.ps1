# Armory X WiX 4.0 Installer Build Script
# Requires WiX 4.0 dotnet tool

Write-Host "Building Armory X WiX 4.0 Installer..." -ForegroundColor Cyan

# Check if WiX 4.0 is installed
try {
    $wixVersion = & wix --version 2>&1
    Write-Host "Found WiX version: $wixVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: WiX 4.0 not found. Please install it first." -ForegroundColor Red
    Write-Host "Install with: dotnet tool install --global wix" -ForegroundColor Yellow
    exit 1
}

# Create banner images if they don't exist
if (-not (Test-Path "banner.bmp")) {
    Write-Host "Creating placeholder banner.bmp (493x58)..." -ForegroundColor Yellow
    Add-Type -AssemblyName System.Drawing
    $banner = New-Object System.Drawing.Bitmap 493, 58
    $g = [System.Drawing.Graphics]::FromImage($banner)
    $g.Clear([System.Drawing.Color]::FromArgb(26, 26, 46))
    $g.Dispose()
    $banner.Save("banner.bmp", [System.Drawing.Imaging.ImageFormat]::Bmp)
    $banner.Dispose()
}

if (-not (Test-Path "dialog.bmp")) {
    Write-Host "Creating placeholder dialog.bmp (493x312)..." -ForegroundColor Yellow
    $dialog = New-Object System.Drawing.Bitmap 493, 312
    $g = [System.Drawing.Graphics]::FromImage($dialog)
    $g.Clear([System.Drawing.Color]::FromArgb(22, 33, 62))
    $g.Dispose()
    $dialog.Save("dialog.bmp", [System.Drawing.Imaging.ImageFormat]::Bmp)
    $dialog.Dispose()
}

# Check if payload files exist
if (-not (Test-Path "payload\ArmoryX.exe")) {
    Write-Host "WARNING: payload\ArmoryX.exe not found. Using placeholder." -ForegroundColor Yellow
}

# Build MSI using WiX 4.0
Write-Host "Building MSI installer with WiX 4.0..." -ForegroundColor Cyan

# WiX 4.0 uses a single 'wix build' command
$buildCommand = "wix build armory-x-installer-v4.wxs -ext WixToolset.UI.wixext -ext WixToolset.Util.wixext -culture en-US -out ArmoryX-Setup.msi"

Write-Host "Running: $buildCommand" -ForegroundColor Gray
$output = Invoke-Expression $buildCommand 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to build MSI" -ForegroundColor Red
    Write-Host $output -ForegroundColor Red
    
    # Common WiX 4.0 fixes
    Write-Host "`nTroubleshooting tips for WiX 4.0:" -ForegroundColor Yellow
    Write-Host "1. Make sure extensions are installed:" -ForegroundColor Yellow
    Write-Host "   wix extension add WixToolset.UI.wixext" -ForegroundColor White
    Write-Host "   wix extension add WixToolset.Util.wixext" -ForegroundColor White
    Write-Host "2. Check the .wxs file for WiX 4.0 compatibility" -ForegroundColor Yellow
    
    exit 1
}

Write-Host "SUCCESS: ArmoryX-Setup.msi created!" -ForegroundColor Green
Write-Host "Location: installer-bootstrap\ArmoryX-Setup.msi" -ForegroundColor Cyan

# Cleanup
Remove-Item "*.wixpdb" -ErrorAction SilentlyContinue
Remove-Item "*.wixobj" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "To test the installer, run:" -ForegroundColor Yellow
Write-Host "  msiexec /i ArmoryX-Setup.msi" -ForegroundColor White
Write-Host ""
Write-Host "To uninstall:" -ForegroundColor Yellow
Write-Host "  msiexec /x ArmoryX-Setup.msi" -ForegroundColor White 