/* ===================================
   Settings and Configuration System
   =================================== */

/* Settings Container */
.settings-container {
    display: flex;
    gap: 2rem;
    height: calc(100vh - 200px);
    min-height: 600px;
}

.settings-nav {
    flex: 0 0 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem 0;
    overflow-y: auto;
}

.settings-nav-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.settings-nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.settings-nav-btn.active {
    background: var(--primary-color);
    color: #fff;
}

.settings-nav-btn .nav-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.settings-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 2rem;
    overflow-y: auto;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-section h3 {
    color: #fff;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.settings-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Setting Groups */
.setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-group:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
}

.setting-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.setting-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

.setting-control {
    flex-shrink: 0;
    margin-left: 1rem;
}

/* Setting Controls */
.setting-select {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.setting-input {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

/* Ensure settings toggles are consistent */
.settings-section .toggle,
.widget-settings-panel .toggle {
    width: 44px !important;
    height: 24px !important;
    min-width: 44px;
    flex-shrink: 0;
}

.settings-section .slider,
.widget-settings-panel .slider {
    width: 44px !important;
    height: 24px !important;
}

.settings-section .slider:before,
.widget-settings-panel .slider:before {
    height: 18px !important;
    width: 18px !important;
    left: 3px !important;
    bottom: 3px !important;
}

/* Color Settings */
.color-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.color-picker-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.color-picker-label {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.color-picker {
    width: 100%;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-picker:hover {
    border-color: var(--primary-color);
}

/* Settings Actions */
.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-actions .btn {
    min-width: 150px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}

/* Tool Configuration */
.tool-config {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1.5rem 0;
}

.tool-config.compact {
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.config-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.config-row {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.config-row.inline {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.config-row label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.config-row select,
.config-row input[type="text"] {
    padding: 0.5rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.config-row.inline select {
    max-width: 80px;
    flex: 0 0 auto;
}

.config-row select:focus,
.config-row input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.config-row input[type="range"] {
    width: 100%;
    height: 4px;
    background: var(--background-hover);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
}

.config-row input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.config-row input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(0, 180, 255, 0.5);
}

.config-row input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
}

/* Widget Settings Panel */
.widget-settings-panel {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
}

.widget-settings-panel h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Clock Settings Modal Styles */
.settings-section {
    margin-bottom: 2rem;
}

/* Ensure clock settings modal content is visible */
#clock-settings-modal .modal-body {
    display: block !important;
}

#clock-settings-modal .settings-section {
    display: block !important;
}

#clock-settings-modal .setting-group {
    display: flex !important;
}

/* Quick Access Options */
.quick-access-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
    margin: 1.5rem 0;
}

.quick-option {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-option:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.quick-option-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.quick-option-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-option-desc {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        flex-direction: column;
        height: auto;
    }
    
    .settings-nav {
        flex: none;
        display: flex;
        overflow-x: auto;
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .settings-nav-btn {
        flex: 0 0 auto;
        white-space: nowrap;
        margin-bottom: 0;
        padding: 0.5rem 1rem;
    }
    
    .settings-content {
        padding: 1.5rem;
    }
    
    .setting-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .setting-control {
        margin-left: 0;
        width: 100%;
    }
    
    .color-settings {
        grid-template-columns: 1fr;
    }
    
    .settings-actions {
        flex-direction: column;
    }
    
    .settings-actions .btn {
        width: 100%;
    }
    
    .config-group {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .config-row.inline {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }
    
    .config-row.inline select {
        max-width: none;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-access-options {
        grid-template-columns: 1fr;
    }
}
