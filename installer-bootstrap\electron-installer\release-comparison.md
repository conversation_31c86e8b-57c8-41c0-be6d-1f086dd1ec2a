# <PERSON><PERSON> vs Electron: Release & Auto-Update Comparison

## Tauri Release Structure
```
GitHub Release:
├── ArmoryX_1.0.0_x64.msi         # Windows installer
├── ArmoryX_1.0.0_x64.msi.sig     # Signature file
└── latest.json                    # Update manifest
```

**Files delivered to user:**
- Single `.exe` (if using portable)
- `.sig` file for verification

## Electron Release Structure

### Option 1: Portable (Single EXE)
```
GitHub Release:
├── ArmoryX-1.0.0-portable.exe    # Single portable executable
├── ArmoryX-1.0.0-portable.exe.blockmap
├── latest.yml                     # Update manifest
└── latest-mac.yml                 # Mac update manifest
```

### Option 2: Auto-Updating Installer
```
GitHub Release:
├── ArmoryX-Setup-1.0.0.exe       # NSIS installer
├── ArmoryX-Setup-1.0.0.exe.blockmap
├── ArmoryX-1.0.0-full.nupkg      # Update package
├── RELEASES                       # Squirrel manifest
└── latest.yml                     # Update manifest
```

## Auto-Update Feature Comparison

### Tauri Auto-Updater
```javascript
// In Rust backend
use tauri_plugin_updater::UpdaterExt;

// Check for updates
let update = app.updater().check().await?;
if update.is_available() {
    update.download_and_install().await?;
}
```

### Electron Auto-Updater
```javascript
// In main process
const { autoUpdater } = require('electron-updater');

// Configure GitHub releases
autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'your-username',
  repo: 'armory-x'
});

// Check for updates
autoUpdater.checkForUpdatesAndNotify();
```

## Key Differences

| Feature | Tauri | Electron |
|---------|-------|----------|
| **Single EXE Distribution** | ✅ Native | ✅ Portable build |
| **File Count** | 2 files (.exe + .sig) | 1-3 files (exe + blockmap + yml) |
| **Auto-Update Protocol** | Custom or Tauri updater | Squirrel.Windows or electron-updater |
| **GitHub Integration** | ✅ Via plugin | ✅ Native support |
| **Update UI** | Custom implementation | Built-in or custom |
| **Delta Updates** | ❌ Full downloads | ✅ Differential updates |
| **Code Signing** | External .sig file | Embedded in exe |
| **Update Channels** | ✅ Supported | ✅ Supported (alpha/beta/stable) |

## Electron Benefits for Updates

1. **Delta Updates**: Only downloads changed files, not entire app
2. **Background Downloads**: Updates download silently
3. **Staged Rollouts**: Can release to percentage of users
4. **Multiple Channels**: Easy alpha/beta/stable channels
5. **Progress Tracking**: Built-in download progress events
6. **Automatic Rollback**: If update fails, reverts to previous

## electron-builder Configuration

```json
{
  "build": {
    "appId": "com.armory-x.app",
    "productName": "Armory X",
    "directories": {
      "output": "dist"
    },
    "publish": {
      "provider": "github",
      "owner": "your-username",
      "repo": "armory-x",
      "releaseType": "release"
    },
    "win": {
      "target": [
        {
          "target": "portable",
          "arch": ["x64"]
        },
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ],
      "certificateFile": "cert.pfx",
      "certificatePassword": "${CERT_PASSWORD}"
    },
    "portable": {
      "artifactName": "ArmoryX-${version}-portable.exe"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "artifactName": "ArmoryX-Setup-${version}.exe"
    }
  }
}
```

## Summary

**Electron CAN provide:**
- ✅ Single portable .exe distribution
- ✅ Automatic updates from GitHub releases
- ✅ Better update experience (delta updates, progress tracking)
- ✅ Professional signed executables
- ✅ Same GitHub release workflow as Tauri

The main difference is Electron includes 1-2 additional manifest files (.yml, .blockmap) in the GitHub release, but these are only for the auto-updater - users still download a single .exe file. 