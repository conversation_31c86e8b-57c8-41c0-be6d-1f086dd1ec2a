<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installing Armory X</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #edf2f4;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
        }

        .installer {
            background: rgba(22, 33, 62, 0.9);
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            text-align: center;
            min-width: 500px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e94560;
            border-radius: 20px;
            font-size: 40px;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .status {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 40px;
            height: 20px;
        }

        .progress-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
            margin-bottom: 20px;
        }

        .progress-bar {
            background: linear-gradient(90deg, #e94560, #0f3460);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .install-path {
            font-size: 12px;
            opacity: 0.6;
            margin-top: 20px;
            word-break: break-all;
        }

        /* Simple loading animation */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        .loading {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="logo">⚡</div>
        <h1>Installing Armory X</h1>
        <div class="status loading" id="status">Preparing installation...</div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progress"></div>
        </div>
        
        <div class="install-path" id="path"></div>
    </div>

    <script>
        const { invoke } = window.__TAURI__.tauri;
        const { listen } = window.__TAURI__.event;
        const { appLocalDataDir } = window.__TAURI__.path;

        async function startInstallation() {
            try {
                // Get default install path
                const installPath = await appLocalDataDir();
                const fullPath = installPath + 'ArmoryX';
                
                document.getElementById('path').textContent = fullPath;
                
                // Listen for progress updates
                await listen('install-progress', (event) => {
                    const { percent, message } = event.payload;
                    document.getElementById('progress').style.width = percent + '%';
                    document.getElementById('status').textContent = message;
                    
                    if (percent === 100) {
                        document.getElementById('status').classList.remove('loading');
                    }
                });
                
                // Start installation (will auto-close when done)
                await invoke('install_and_exit', { installPath: fullPath });
                
            } catch (error) {
                console.error('Installation error:', error);
                document.getElementById('status').textContent = 'Installation failed: ' + error;
                document.getElementById('status').classList.remove('loading');
            }
        }

        // Start installation automatically
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(startInstallation, 1000);
        });
    </script>
</body>
</html> 