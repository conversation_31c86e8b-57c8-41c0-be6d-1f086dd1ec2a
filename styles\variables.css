/* ===================================
   CSS Variables and Theme Configuration
   =================================== */

/* Import website animations and modern styling */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables (matching website theme) */
:root {
    /* Primary Colors */
    --primary-color: #00b4ff;
    --primary-hover: #0099e6;
    --secondary-color: #1a1a1a;
    
    /* Background Colors */
    --background-dark: #0a0a0a;
    --background-card: #111111;
    --background-hover: #1a1a1a;
    --background-primary: #0a0a0a;
    --bg-primary: #1e293b;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    
    /* Border and UI Colors */
    --border-color: #333333;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --error-color: #ff4444;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #00b4ff, #0099e6);
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #0a0a0a);
    
    /* Shadows and Effects */
    --shadow-glow: 0 0 20px rgba(0, 180, 255, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
    
    /* Layout Properties */
    --border-radius: 12px;
    --transition-fast: 0.2s ease;
    --transition-smooth: 0.3s ease;
    --animation-duration: 0.3s;
}
