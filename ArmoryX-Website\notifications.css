/* ArmoryX Notifications System Styles */

/* Notification icon in navigation */
#notification-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

#notification-icon:hover {
    background: rgba(45, 114, 255, 0.1);
}

#notification-icon .fas {
    font-size: 1.1rem;
}

.notification-badge {
    position: absolute;
    top: -3px;
    right: -3px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    border: 2px solid var(--bg-primary);
    z-index: 10;
    box-shadow: 0 3px 8px rgba(239, 68, 68, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    animation: badgePulse 2s infinite;
    line-height: 1;
    min-width: 22px;
    padding: 0;
    text-align: center;
}

.notification-badge:empty {
    display: none;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Notification panel */
.notification-panel {
    position: fixed;
    top: 70px;
    right: 20px;
    width: 400px;
    max-width: 90vw;
    max-height: 80vh;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 2000;
    display: none;
    overflow: hidden;
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-primary);
}

.notification-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: var(--bg-primary);
}

.notification-item.unread {
    background: rgba(45, 114, 255, 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: white;
    font-size: 0.9rem;
}

.notification-icon.like {
    background: #ef4444;
}

.notification-icon.reply {
    background: #10b981;
}

.notification-icon.dm {
    background: #8b5cf6;
}

.notification-icon.mention {
    background: #f59e0b;
}

.notification-icon.system {
    background: #6b7280;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.7rem;
}

.notification-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.mark-read-btn,
.delete-notification-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.mark-read-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.delete-notification-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
}

.notification-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
    background: var(--bg-primary);
}

.notification-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

.no-notifications {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Toast notifications */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    max-width: 350px;
    z-index: 3000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* DM System Styles */
.dm-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    height: 100vh;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    z-index: 2500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.dm-panel.active {
    transform: translateX(0);
}

.dm-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dm-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.dm-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.dm-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.dm-conversations {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.dm-conversation {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.dm-conversation:hover {
    background: var(--bg-primary);
}

.dm-conversation.active {
    background: var(--primary-color);
    color: white;
}

.dm-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.dm-info {
    flex: 1;
    min-width: 0;
}

.dm-username {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.dm-last-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dm-unread {
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .notification-panel {
        right: 10px;
        left: 10px;
        width: auto;
        max-width: none;
    }
    
    .dm-panel {
        width: 100%;
    }
    
    .notification-toast {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .notification-badge {
        border-color: var(--bg-primary);
    }
    
    .notification-toast {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
} 