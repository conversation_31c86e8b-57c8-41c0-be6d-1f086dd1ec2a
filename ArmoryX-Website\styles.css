/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #1e293b;
    --accent-color: #06b6d4;
    --text-primary: #ffffff;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    --bg-primary: rgba(15, 23, 42, 0.85);
    --bg-secondary: rgba(30, 41, 59, 0.9);
    --bg-tertiary: rgba(51, 65, 85, 0.8);
    --border-color: rgba(71, 85, 105, 0.5);
    --gradient-primary: linear-gradient(135deg, #3b82f6, #06b6d4);
    --gradient-secondary: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.8));
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.6rem;
    font-weight: 800;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    cursor: pointer;
}

.nav-logo span {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: navGradientShift 4s ease infinite;
    font-weight: 800;
    letter-spacing: 0.02em;
    position: relative;
}

.nav-logo:hover span {
    animation-duration: 0.8s;
    transform: scale(1.05);
}

.nav-logo:hover {
    transform: translateY(-1px);
}

.nav-logo::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: width 0.3s ease;
}

.nav-logo:hover::after {
    width: 100%;
}

@keyframes navGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.nav-logo-icon {
    width: 36px;
    height: 36px;
    object-fit: contain;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.nav-logo:hover .nav-logo-icon {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: var(--text-primary);
}

.discord-link {
    background: var(--gradient-primary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    color: white !important;
    font-weight: 600;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 2rem;
}

.hero-title {
    position: relative;
    margin-bottom: 2rem;
    text-align: center;
}

/* Enhanced Title Container */
.title-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* Background Text Effect */
.title-background-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8rem;
    font-weight: 900;
    color: transparent;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
    -webkit-background-clip: text;
    background-clip: text;
    z-index: -1;
    pointer-events: none;
    animation: backgroundPulse 4s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.95); }
    100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.05); }
}

/* Main Title Styling */
.title-main {
    position: relative;
    z-index: 2;
}

/* Individual Letter Animations - simplified */
.title-letter {
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: 
        letterGradient 3s ease infinite,
        letterWave 2s ease-in-out infinite;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center bottom;
}

/* Wave delays for smooth cascading effect */
.title-letter:nth-child(1) { animation-delay: 0s, 0s; }
.title-letter:nth-child(2) { animation-delay: 0s, 0.2s; }
.title-letter:nth-child(3) { animation-delay: 0s, 0.4s; }
.title-letter:nth-child(4) { animation-delay: 0s, 0.6s; }
.title-letter:nth-child(5) { animation-delay: 0s, 0.8s; }
.title-letter:nth-child(6) { animation-delay: 0s, 1.0s; }
.title-letter:nth-child(8) { animation-delay: 0s, 1.4s; } /* X */

.title-space {
    width: 0.5em;
}

/* Title Brand Container - random glitch intervals */
.title-brand {
    display: inline-flex;
    align-items: center;
    font-size: 4.5rem;
    font-weight: 900;
    letter-spacing: 0.05em;
    position: relative;
    justify-content: center;
    animation: 
        vhsGlitch1 5.3s ease-in-out infinite,
        vhsGlitch2 3.7s ease-in-out infinite 1.2s,
        vhsGlitch3 6.1s ease-in-out infinite 2.8s;
}

/* VHS-style glitch pseudo-elements for the entire title */
.title-brand::before,
.title-brand::after {
    content: 'ARMORY X';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0;
    pointer-events: none;
    font-size: inherit;
    font-weight: inherit;
    letter-spacing: inherit;
}

.title-brand::before {
    animation: 
        glitchRed1 5.3s ease-in-out infinite,
        glitchRed2 3.7s ease-in-out infinite 1.2s,
        glitchRed3 6.1s ease-in-out infinite 2.8s;
    background: linear-gradient(135deg, #ff0040, #ff6b6b);
    -webkit-background-clip: text;
    background-clip: text;
    mix-blend-mode: screen;
}

.title-brand::after {
    animation: 
        glitchCyan1 5.3s ease-in-out infinite,
        glitchCyan2 3.7s ease-in-out infinite 1.2s,
        glitchCyan3 6.1s ease-in-out infinite 2.8s;
    background: linear-gradient(135deg, #00ffff, #4ecdc4);
    -webkit-background-clip: text;
    background-clip: text;
    mix-blend-mode: screen;
}

/* Multiple VHS Glitch Variations for Random Timing */
@keyframes vhsGlitch1 {
    0%, 95%, 100% {
        transform: translateX(0);
        filter: none;
    }
    96% {
        transform: translateX(-2px);
        filter: hue-rotate(10deg) saturate(150%);
    }
    97% {
        transform: translateX(3px);
        filter: hue-rotate(-10deg) contrast(120%);
    }
    98% {
        transform: translateX(-1px);
        filter: none;
    }
}

@keyframes vhsGlitch2 {
    0%, 92%, 100% {
        transform: translateX(0);
        filter: none;
    }
    93% {
        transform: translateX(2px);
        filter: hue-rotate(-15deg) saturate(130%);
    }
    94% {
        transform: translateX(-3px);
        filter: hue-rotate(15deg) brightness(110%);
    }
    95% {
        transform: translateX(1px);
        filter: none;
    }
}

@keyframes vhsGlitch3 {
    0%, 96%, 100% {
        transform: translateX(0);
        filter: none;
    }
    97% {
        transform: translateX(-3px);
        filter: hue-rotate(20deg) contrast(130%);
    }
    98% {
        transform: translateX(2px);
        filter: hue-rotate(-20deg) saturate(140%);
    }
    99% {
        transform: translateX(-1px);
        filter: none;
    }
}

/* Red Channel Variations */
@keyframes glitchRed1 {
    0%, 95%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    96% {
        opacity: 0.8;
        transform: translateX(-4px) scaleX(0.98);
        clip-path: inset(20% 0 30% 0);
    }
    97% {
        opacity: 0.6;
        transform: translateX(-2px) scaleX(1.02);
        clip-path: inset(40% 0 10% 0);
    }
    98% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

@keyframes glitchRed2 {
    0%, 92%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    93% {
        opacity: 0.7;
        transform: translateX(-5px) scaleX(0.96);
        clip-path: inset(10% 0 60% 0);
    }
    94% {
        opacity: 0.9;
        transform: translateX(-3px) scaleX(1.01);
        clip-path: inset(70% 0 5% 0);
    }
    95% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

@keyframes glitchRed3 {
    0%, 96%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    97% {
        opacity: 0.5;
        transform: translateX(-6px) scaleX(0.94);
        clip-path: inset(30% 0 20% 0);
    }
    98% {
        opacity: 0.8;
        transform: translateX(-2px) scaleX(1.03);
        clip-path: inset(5% 0 40% 0);
    }
    99% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

/* Cyan Channel Variations */
@keyframes glitchCyan1 {
    0%, 95%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    96% {
        opacity: 0.7;
        transform: translateX(4px) scaleX(1.02);
        clip-path: inset(30% 0 20% 0);
    }
    97% {
        opacity: 0.5;
        transform: translateX(2px) scaleX(0.98);
        clip-path: inset(10% 0 40% 0);
    }
    98% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

@keyframes glitchCyan2 {
    0%, 92%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    93% {
        opacity: 0.8;
        transform: translateX(6px) scaleX(1.04);
        clip-path: inset(60% 0 10% 0);
    }
    94% {
        opacity: 0.3;
        transform: translateX(3px) scaleX(0.97);
        clip-path: inset(5% 0 70% 0);
    }
    95% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

@keyframes glitchCyan3 {
    0%, 96%, 100% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
    97% {
        opacity: 0.6;
        transform: translateX(5px) scaleX(1.01);
        clip-path: inset(40% 0 5% 0);
    }
    98% {
        opacity: 0.4;
        transform: translateX(3px) scaleX(0.99);
        clip-path: inset(15% 0 50% 0);
    }
    99% {
        opacity: 0;
        transform: translateX(0) scaleX(1);
        clip-path: inset(0 0 0 0);
    }
}

/* Smooth Continuous Wave Animation */
@keyframes letterWave {
    0% { 
        transform: translateY(0px); 
    }
    50% { 
        transform: translateY(-10px); 
    }
    100% { 
        transform: translateY(0px); 
    }
}

/* Main VHS Glitch Effect - affects entire title */
@keyframes vhsGlitch {
    0%, 90%, 100% {
        transform: translateX(0);
        filter: none;
    }
    
    91% {
        transform: translateX(-2px);
        filter: hue-rotate(10deg) saturate(150%);
    }
    
    92% {
        transform: translateX(3px);
        filter: hue-rotate(-10deg) contrast(120%);
    }
    
    93% {
        transform: translateX(-1px);
        filter: hue-rotate(5deg) brightness(110%);
    }
    
    94% {
        transform: translateX(2px);
        filter: none;
    }
    
    95% {
        transform: translateX(-3px);
        filter: hue-rotate(-5deg) saturate(130%);
    }
    
    96% {
        transform: translateX(0);
        filter: none;
    }
}

/* Removed individual letter glitch - now handled at title level */

@keyframes letterGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Particle System */
.title-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.title-particles::before,
.title-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #3b82f6, transparent);
    border-radius: 50%;
    animation: particleFloat 3s ease-in-out infinite;
}

.title-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.title-particles::after {
    top: 70%;
    right: 15%;
    animation-delay: 1.5s;
    background: radial-gradient(circle, #8b5cf6, transparent);
}

@keyframes particleFloat {
    0%, 100% { transform: translate(0, 0) scale(0.8); opacity: 0.4; }
    50% { transform: translate(20px, -30px) scale(1.2); opacity: 1; }
}

/* Subtitle Styling */
.title-subtitle {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    position: relative;
    z-index: 2;
    text-align: center;
    width: 100%;
}

.subtitle-line-1,
.subtitle-line-2 {
    position: relative;
    display: inline-block;
    border-right: 2px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    max-width: 0;
    animation: typewriter 2s steps(22) 1s both;
}

.subtitle-line-2 {
    animation: typewriter 2.5s steps(23) 2.5s both;
    color: var(--primary-color);
    font-weight: 700;
}

/* Typewriter Effect */
@keyframes typewriter {
    0% { 
        max-width: 0;
        border-right-color: var(--primary-color);
    }
    99% { 
        border-right-color: var(--primary-color);
    }
    100% { 
        max-width: 100%;
        border-right-color: transparent;
    }
}



/* Title Glow Effect */
.title-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
        ellipse at center,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(139, 92, 246, 0.05) 30%,
        transparent 70%
    );
    border-radius: 50%;
    animation: glowPulse 4s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: -1;
}

@keyframes glowPulse {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
    100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .title-background-text {
        font-size: 6rem;
    }
    
    .title-brand {
        font-size: 3.5rem;
    }
    
    .title-subtitle {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .title-background-text {
        font-size: 4rem;
    }
    
    .title-brand {
        font-size: 2.8rem;
    }
    
    .title-subtitle {
        font-size: 1.1rem;
    }
    
    .title-particles::before,
    .title-particles::after {
        display: none; /* Hide particles on mobile for performance */
    }
}

@media (max-width: 480px) {
    .title-background-text {
        font-size: 3rem;
    }
    
    .title-brand {
        font-size: 2.2rem;
    }
    
    .title-subtitle {
        font-size: 1rem;
    }
}

/* Accessibility and Performance */
@media (prefers-reduced-motion: reduce) {
    .title-letter,
    .title-particles::before,
    .title-particles::after,
    .title-glow,
    .title-background-text {
        animation: letterGradient 3s ease infinite; /* Keep only the gradient, remove wave */
    }
    
    .title-brand {
        animation: none; /* Disable VHS glitch */
    }
    
    .title-brand::before,
    .title-brand::after {
        animation: none; /* Disable glitch pseudo-elements */
        opacity: 0;
    }
    
    .subtitle-line-1,
    .subtitle-line-2 {
        animation: none;
        max-width: none;
        border-right: none;
    }
    
    .nav-logo span {
        animation: none;
    }
    
    .nav-logo:hover span {
        animation: none;
    }
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    justify-content: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.btn-warning {
    background: #d69e2e;
    color: white;
    border: 2px solid #d69e2e;
}

.btn-warning:hover {
    background: #b7791f;
    border-color: #b7791f;
    transform: translateY(-1px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.stat-number:hover {
    color: #2563eb;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.hero-image {
    display: none; /* Hide the right side image */
}

.hero-widget {
    position: relative;
    width: 320px;
    height: 240px;
    background: var(--gradient-secondary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xl);
}

.widget-preview {
    position: relative;
    width: 280px;
    height: 200px;
    background: var(--bg-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.widget-icon {
    font-size: 3rem;
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

.dashboard-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    animation: pulse 2s infinite;
    transform: scale(1.1);
}

.widget-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes pulse-ring {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Download Section */
.download {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.version-info {
    margin: 2rem 0;
}

.version-badge {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: var(--bg-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.version-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.version-number {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
}

.version-details p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.version-details i {
    color: var(--primary-color);
    width: 16px;
}

.download-buttons {
    margin-top: 2rem;
}

.download-note {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-top: 1rem;
}

.download-icon-display {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
}

.download-main-icon {
    width: 200px;
    height: 200px;
    object-fit: contain;
    border-radius: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: iconFloat 3s ease-in-out infinite;
}

.download-main-icon:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.3);
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.preview-window {
    background: var(--bg-secondary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.window-header {
    background: var(--bg-tertiary);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.window-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.close { background: #ef4444; }
.control.minimize { background: #f59e0b; }
.control.maximize { background: #10b981; }

.window-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.window-title-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    border-radius: 3px;
}

.window-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.preview-dashboard {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.dashboard-card {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid var(--border-color);
}

.dashboard-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.dashboard-card span {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Updates Section */
.updates {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.updates-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
}

.updates-sidebar {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 12px;
    height: fit-content;
    border: 1px solid var(--border-color);
}

.updates-sidebar h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.planned-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Account Section */
.account {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.account-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.account-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 24px;
}

.feature-item h4 {
    margin-bottom: 0.25rem;
}

.feature-item p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.form-container {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.form-tabs {
    display: flex;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.forgot-password {
    display: block;
    text-align: center;
    margin-top: 1rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.account-note {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
}

.account-note p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.account-note i {
    color: var(--primary-color);
}

/* Logged-in Account State */
.logged-in-state {
    text-align: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
}

.user-info i {
    font-size: 3rem;
    color: var(--primary-color);
}

.user-details h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.user-email {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.account-actions {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.license-section {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.license-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.license-section h4 i {
    color: var(--primary-color);
}

.license-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.license-input {
    flex: 1;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    font-family: monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.license-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.license-input::placeholder {
    color: var(--text-muted);
    text-transform: none;
    letter-spacing: normal;
    font-family: inherit;
}

.license-status {
    font-size: 0.9rem;
    padding: 0.5rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.license-status.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.license-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.license-status.loading {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.user-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.user-actions .btn {
    flex: 1;
    max-width: 200px;
}

/* Download Modal Styles */
.download-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: modalFadeIn 0.3s ease-out;
}

.download-modal {
    background: var(--bg-secondary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

.download-modal-header {
    background: var(--gradient-primary);
    padding: 2rem;
    text-align: center;
    border-radius: 16px 16px 0 0;
    position: relative;
}

.download-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    animation: iconPulse 2s infinite;
}

.download-icon i {
    font-size: 2.5rem;
    color: white;
}

.download-modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.download-modal-content {
    padding: 2rem;
}

.download-message {
    text-align: center;
    margin-bottom: 2rem;
}

.main-message {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.sub-message {
    color: var(--text-secondary);
    margin: 0;
}

.download-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.feature-highlight:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.feature-highlight i {
    width: 20px;
    color: var(--primary-color);
}

.download-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.modal-btn {
    flex: 1;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
}

.download-footer {
    display: flex;
    justify-content: space-between;
    text-align: center;
    font-size: 0.85rem;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.download-footer p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-footer i {
    color: var(--primary-color);
}

/* Modal Animations */
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from { 
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to { 
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.modal-closing {
    animation: modalFadeOut 0.3s ease-out forwards;
}

.modal-closing .download-modal {
    animation: modalSlideOut 0.3s ease-out forwards;
}

@keyframes modalFadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes modalSlideOut {
    from { 
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    to { 
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
}

/* Mobile responsiveness for modal */
@media (max-width: 768px) {
    .download-modal {
        width: 95%;
        margin: 1rem;
    }
    
    .download-modal-header {
        padding: 1.5rem;
    }
    
    .download-icon {
        width: 60px;
        height: 60px;
    }
    
    .download-icon i {
        font-size: 2rem;
    }
    
    .download-modal-header h2 {
        font-size: 1.4rem;
    }
    
    .download-modal-content {
        padding: 1.5rem;
    }
    
    .download-actions {
        flex-direction: column;
    }
    
    .download-footer {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    padding: 3rem 0 1rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    color: var(--primary-color);
}

.footer-logo-icon {
    width: 28px;
    height: 28px;
    object-fit: contain;
    border-radius: 5px;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Hero Image Responsive */
@media (max-width: 1024px) {
    .hero-widget {
        width: 280px;
        height: 210px;
    }
    
    .widget-preview {
        width: 240px;
        height: 170px;
    }
}

@media (max-width: 768px) {
    .hero-widget {
        width: 240px;
        height: 180px;
    }
    
    .widget-preview {
        width: 200px;
        height: 140px;
    }
}

@media (max-width: 480px) {
    .hero-widget {
        width: 200px;
        height: 150px;
    }
    
    .widget-preview {
        width: 160px;
        height: 110px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-secondary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-logo {
        font-size: 1.4rem;
        gap: 10px;
    }
    
    .nav-logo-icon {
        width: 32px;
        height: 32px;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .download-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .account-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .updates-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .download-icon-display {
        padding: 2rem;
    }
    
    .download-main-icon {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 480px) {
    .download-icon-display {
        padding: 1.5rem;
    }
    
    .download-main-icon {
        width: 120px;
        height: 120px;
    }
}

/* Authentication Message Styles */
.auth-message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.auth-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.auth-message.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.auth-message::before {
    content: "";
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
}

/* Real-time Indicators */
.live-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    margin: 0.5rem auto 0;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Profile Management Styles */
.profile-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.profile-section h4 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.profile-section h4 i {
    color: var(--primary-color);
}

.profile-form .form-group {
    margin-bottom: 1rem;
}

.profile-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.profile-form input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.profile-form input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(45, 114, 255, 0.1);
}

.form-hint {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
}

.profile-status {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.profile-status.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.profile-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.profile-status i {
    font-size: 1rem;
}

/* Account Dropdown Menu */
.nav-account-container {
    position: relative;
    display: inline-block;
}

.account-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.nav-account-container:hover .account-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.account-dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.account-dropdown-user {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.account-dropdown-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.account-dropdown-info h4 {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.account-dropdown-info p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.account-dropdown-menu {
    padding: 0.5rem 0;
}

.account-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 0.9rem;
}

.account-dropdown-item:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

.account-dropdown-item i {
    width: 16px;
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

.account-dropdown-item:hover i {
    color: var(--primary-color);
}

.account-dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

.account-dropdown-logout {
    color: #ef4444 !important;
}

.account-dropdown-logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444 !important;
}

.account-dropdown-logout i {
    color: #ef4444 !important;
}

/* Mobile responsiveness for dropdown */
@media (max-width: 768px) {
    .account-dropdown {
        right: -10px;
        left: -10px;
        min-width: auto;
    }
} 