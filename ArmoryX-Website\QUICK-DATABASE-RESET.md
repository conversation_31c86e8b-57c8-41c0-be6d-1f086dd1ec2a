# 🚨 QUICK DATABASE RESET GUIDE

If the Nuclear Cleanup button doesn't work or you prefer manual control, you can clear the license database directly from Firebase Console.

## 🔥 **Firebase Console Method (Recommended)**

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: TB_AIO_REVAMP_WPF 
3. **Navigate to Firestore Database** (left sidebar)
4. **Delete these collections completely**:

### **Collections to Delete:**
```
📁 license_keys          (Delete entire collection)
📁 user_licenses        (Delete entire collection) 
📁 key_generation_log   (Delete entire collection)
📁 key_validation_log   (Delete entire collection)
```

## 📝 **Step-by-Step Instructions:**

### **Delete license_keys Collection:**
1. Click on `license_keys` collection
2. Click the **3 dots menu** (...) next to collection name
3. Select **"Delete collection"**
4. Type the collection name to confirm
5. Click **"Delete"**

### **Repeat for all collections:**
- `user_licenses` 
- `key_generation_log`
- `key_validation_log`

## ⚡ **Alternative: Firebase CLI Method**

If you have Firebase CLI installed:

```bash
# Delete collections via CLI
firebase firestore:delete license_keys --recursive
firebase firestore:delete user_licenses --recursive  
firebase firestore:delete key_generation_log --recursive
firebase firestore:delete key_validation_log --recursive
```

## 🎯 **After Cleanup:**

1. **Refresh the Key Management page**
2. **All keys should be gone**
3. **All users will be on Free plans**
4. **Generate new keys as needed**

## ⚠️ **WARNING:**
This action is **IRREVERSIBLE**. All license data will be permanently lost.

## 🔄 **Verify Cleanup:**
After deletion, the Key Management panel should show:
```
📭 No license keys found
```

---
*This method bypasses any problematic JavaScript/Firebase permissions and gives you direct database control.* 