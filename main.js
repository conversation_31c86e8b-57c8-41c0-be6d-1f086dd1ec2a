const { app, BrowserWindow, ipc<PERSON>ain, shell, dialog, Menu, screen } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { exec, spawn } = require('child_process');
const si = require('systeminformation');
const { machineIdSync } = require('node-machine-id');
const chokidar = require('chokidar');
const glob = require('glob');

// App configuration (declare before using in modules)
const isDev = process.argv.includes('--dev');
const isStartup = process.argv.includes('--startup');
const APP_NAME = 'Armory X';

// Import utility functions
const { formatBytes } = require('./modules/utils');
const { SystemInfo } = require('./modules/system-info');
const { FileOperations } = require('./modules/file-operations');
const { SettingsManager } = require('./modules/settings-manager');
const { WindowManager } = require('./modules/window-manager');
const { ModManager } = require('./modules/mod-manager');
const { DesktopArsenal } = require('./modules/desktop-arsenal');
const { HWIDSpoofer } = require('./modules/hwid-spoofer');
const { KernelHWIDSpoofer } = require('./modules/kernel-hwid-spoofer');
const { MinecraftProfileManager } = require('./modules/minecraft-profile-manager');
const { ModDownloader } = require('./modules/mod-downloader');
const { BrowserDownloader } = require('./modules/browser-downloader');
const BenchmarkModule = require('./modules/benchmark');
// Try to load RobotJS automation, fall back to PowerShell if it fails
let AutomationTools;
try {
    AutomationTools = require('./modules/automation-tools').AutomationTools;
    console.log('✅ Using RobotJS automation system');
} catch (error) {
    console.warn('⚠️ RobotJS failed to load, using PowerShell fallback:', error.message);
    AutomationTools = require('./modules/automation-tools-fallback').AutomationToolsFallback;
}

// Initialize module instances
const systemInfo = new SystemInfo();
const fileOps = new FileOperations();
const settingsManager = new SettingsManager();
const windowManager = new WindowManager({ app, isDev, APP_NAME });
const modManager = new ModManager({ systemInfo });
const desktopArsenal = new DesktopArsenal();
const automationTools = new AutomationTools();
const hwidSpoofer = new HWIDSpoofer();
const kernelHwidSpoofer = new KernelHWIDSpoofer();
const minecraftProfileManager = new MinecraftProfileManager();
const modDownloader = new ModDownloader(minecraftProfileManager);
const browserDownloader = new BrowserDownloader({ modManager });
const benchmarkModule = new BenchmarkModule();

let mainWindow;
let splashWindow;

function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 435, // Exact width of login panel
    height: 740, // Adjusted height for content
    frame: false,
    show: false,
    transparent: true, // Enable transparency
    backgroundColor: '#00000000', // Fully transparent background
    alwaysOnTop: true,
    resizable: false,
    hasShadow: false, // Disable shadow to remove faded edges
    thickFrame: false, // Disable window frame that might create borders
    roundedCorners: false, // Disable OS-level rounded corners that might cause artifacts
    icon: path.join(__dirname, 'assets', 'Armory_X.ico'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webviewTag: false,
      offscreen: false,
      experimentalFeatures: true,
      disableHtmlFullscreenWindowResize: true
    }
  });

  splashWindow.loadFile('splash.html');
  
  splashWindow.once('ready-to-show', () => {
    splashWindow.show();
    splashWindow.center();
  });

  splashWindow.on('closed', () => {
    splashWindow = null;
  });

  return splashWindow;
}

function createWindow() {
  mainWindow = windowManager.createWindow();
  
  // Update file operations with main window reference
  fileOps.setMainWindow(mainWindow);
  
  // Update desktop arsenal with main window reference
  desktopArsenal.setMainWindow(mainWindow);
  
  // Update automation tools with main window reference
  automationTools.setMainWindow(mainWindow);
  
  // Update HWID spoofer with main window reference
  hwidSpoofer.setMainWindow(mainWindow);
  
  // Update kernel HWID spoofer with main window reference
  kernelHwidSpoofer.setMainWindow(mainWindow);
  
  // Update browser downloader with main window reference
  browserDownloader.setMainWindow(mainWindow);
  
  return mainWindow;
}

// App event handlers
app.whenReady().then(async () => {
  console.log(`🚀 ${APP_NAME} starting...`);
  console.log(`📍 Platform: ${process.platform}`);
  console.log(`📍 Electron: ${process.versions.electron}`);
  console.log(`📍 Node: ${process.versions.node}`);
  console.log(`📍 Development mode: ${isDev}`);
  console.log(`📍 Startup mode: ${isStartup}`);

  if (isStartup) {
    // Startup mode - skip splash and go directly to background mode
    console.log('🚀 Starting in background mode...');
    
    // Initialize Desktop Arsenal only
    desktopArsenal.registerHotkeys();
    
    // Start desktop monitoring if enabled
    const arsenalSettings = await desktopArsenal.getSettings();
    if (arsenalSettings.fileMonitoring.enabled || arsenalSettings.fileMonitoring.autoStart) {
      await desktopArsenal.startDesktopMonitoring();
    }
    
    console.log('✅ Background services initialized');
    
    // Optionally show a system tray icon
    // TODO: Implement system tray functionality
  } else {
    // Normal mode - show splash screen
    createSplashWindow();
    console.log('✅ Splash screen loaded');
  }
});

// Flag to track if we're in the middle of transitioning windows
let isTransitioningWindows = false;

app.on('window-all-closed', () => {
  // Don't quit if we're transitioning between splash and main window
  if (isTransitioningWindows) {
    console.log('⏸️ Window transition in progress, not quitting...');
    return;
  }
  
  // Cleanup Desktop Arsenal
  if (desktopArsenal) {
    desktopArsenal.cleanup();
  }
  
  // Cleanup Automation Tools
  if (automationTools) {
    automationTools.cleanup();
  }
  
  // Cleanup HWID Spoofer
  if (hwidSpoofer) {
    hwidSpoofer.cleanup();
  }
  
  // Cleanup Kernel HWID Spoofer
  if (kernelHwidSpoofer) {
    kernelHwidSpoofer.cleanup();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createSplashWindow();
  }
});

// Window management functions moved to modules/window-manager.js

// Test connection handler for debugging
ipcMain.handle('test-connection', async () => {
  return { success: true, message: 'IPC connection working' };
});

// Splash window control handlers
ipcMain.on('splash-minimize-window', () => {
  if (splashWindow) {
    splashWindow.minimize();
  }
});

ipcMain.on('splash-close-window', () => {
  if (splashWindow) {
    splashWindow.close();
  }
});

// Splash screen login completion handler
ipcMain.on('splash-login-complete', async (event, authData) => {
  console.log('🔑 User authenticated, creating main window...', authData);
  
  // Set transition flag to prevent app from quitting
  isTransitioningWindows = true;
  
  // Close splash window with proper cleanup
  if (splashWindow) {
    // First remove from screen to prevent blocking
    splashWindow.setIgnoreMouseEvents(true);
    splashWindow.setAlwaysOnTop(false);
    splashWindow.hide();
    
    // Then destroy after a brief moment
    setTimeout(() => {
      if (splashWindow) {
        splashWindow.destroy();
        splashWindow = null;
      }
    }, 50);
  }
  
  // Small delay to ensure splash is fully destroyed before creating main window
  setTimeout(() => {
    try {
      // Create main window
      createWindow();
      windowManager.setupMenu();
      windowManager.registerWindowControlHandlers();
      
      // Initialize Desktop Arsenal
      desktopArsenal.registerHotkeys();
      
      // Initialize Automation Tools
      automationTools.registerHotkeys();
      
      // Start desktop monitoring if enabled
      desktopArsenal.getSettings().then(arsenalSettings => {
        if (arsenalSettings.fileMonitoring.enabled) {
          desktopArsenal.startDesktopMonitoring();
        }
      });
      
      console.log('✅ App initialization complete');
      
      // Clear transition flag after main window is created
      isTransitioningWindows = false;
    } catch (error) {
      console.error('❌ Error creating main window:', error);
      isTransitioningWindows = false;
      // Show error dialog
      const { dialog } = require('electron');
      dialog.showErrorBox('Error', 'Failed to create main window: ' + error.message);
      app.quit();
    }
  }, 100);
});

// System information handlers
ipcMain.handle('get-system-info', systemInfo.getSystemInfo.bind(systemInfo));

ipcMain.handle('get-system-stats', systemInfo.getSystemStats.bind(systemInfo));

// Cleanup functionality
ipcMain.handle('clean-junk-files', fileOps.cleanJunkFiles.bind(fileOps));

ipcMain.handle('get-cleanup-progress', fileOps.getCleanupProgress.bind(fileOps));

// All mod management functions moved to modules/mod-manager.js

// File operations
ipcMain.handle('show-in-folder', (event, filePath) => fileOps.showInFolder(filePath));
ipcMain.handle('select-folder', fileOps.selectFolder.bind(fileOps));
ipcMain.handle('select-file', (event, options) => fileOps.selectFile(options));
ipcMain.handle('extract-icon', (event, filePath) => fileOps.extractIcon(filePath));
ipcMain.handle('select-folder-dialog', (event, options) => fileOps.selectFolderDialog(options));

// Settings management
ipcMain.handle('load-settings', settingsManager.loadSettings.bind(settingsManager));
ipcMain.handle('save-settings', (event, settings) => settingsManager.saveSettings(settings));

// Browser navigation handlers (global access for any browser window)
ipcMain.handle('browser-go-back', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    if (browserWindow && browserWindow.webContents.canGoBack()) {
      browserWindow.webContents.goBack();
      return { success: true };
    }
    return { success: false, message: 'Cannot go back' };
  } catch (error) {
    console.error('Browser go back error:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('browser-go-forward', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    if (browserWindow && browserWindow.webContents.canGoForward()) {
      browserWindow.webContents.goForward();
      return { success: true };
    }
    return { success: false, message: 'Cannot go forward' };
  } catch (error) {
    console.error('Browser go forward error:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('browser-refresh', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    if (browserWindow) {
      browserWindow.webContents.reload();
      return { success: true };
    }
    return { success: false, message: 'No browser window' };
  } catch (error) {
    console.error('Browser refresh error:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('browser-navigate', async (event, url) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    if (browserWindow) {
      await browserWindow.webContents.loadURL(url);
      return { success: true };
    }
    return { success: false, message: 'No browser window' };
  } catch (error) {
    console.error('Browser navigate error:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('browser-go-to-site', async (event, site) => {
  try {
    const sites = {
      modrinth: 'https://modrinth.com/mods',
      curseforge: 'https://www.curseforge.com/minecraft/mc-mods',
              planetminecraft: 'https://www.planetminecraft.com/mods/',
      planetmc: 'https://www.planetminecraft.com/mods'
    };
    
    const url = sites[site];
    if (url) {
      const browserWindow = BrowserWindow.getFocusedWindow();
      if (browserWindow) {
        await browserWindow.webContents.loadURL(url);
        return { success: true };
      }
      return { success: false, message: 'No browser window' };
    }
    return { success: false, message: 'Invalid site' };
  } catch (error) {
    console.error('Browser go to site error:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('browser-can-go-back', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    return browserWindow ? browserWindow.webContents.canGoBack() : false;
  } catch (error) {
    return false;
  }
});

ipcMain.handle('browser-can-go-forward', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    return browserWindow ? browserWindow.webContents.canGoForward() : false;
  } catch (error) {
    return false;
  }
});

ipcMain.handle('browser-get-current-url', async (event) => {
  try {
    const browserWindow = BrowserWindow.getFocusedWindow();
    return browserWindow ? browserWindow.webContents.getURL() : '';
  } catch (error) {
    return '';
  }
});

ipcMain.handle('browser-get-settings', async (event) => {
  try {
    const settings = await settingsManager.loadSettings();
    return settings;
  } catch (error) {
    console.error('Error loading browser settings:', error);
    return { browser: { defaultSite: 'modrinth', showQuickNav: true } };
  }
});

// License management
ipcMain.handle('get-machine-id', systemInfo.getMachineId.bind(systemInfo));

// Mod management handlers
ipcMain.handle('get-mods-for-game', modManager.getModsForGame.bind(modManager));
ipcMain.handle('open-mod-folder', modManager.openModFolder.bind(modManager));
ipcMain.handle('get-mod-directories', modManager.getModDirectories.bind(modManager));
ipcMain.handle('create-test-mods', modManager.createTestMods.bind(modManager));
ipcMain.handle('delete-mod', modManager.deleteMod.bind(modManager));
ipcMain.handle('install-mods-with-categories', modManager.installModsWithCategories.bind(modManager));
ipcMain.handle('install-mods', modManager.installMods.bind(modManager));
ipcMain.handle('add-mods', modManager.addMods.bind(modManager));

// Mod Set management handlers (new system for organizing mod collections)
ipcMain.handle('get-mod-sets', modManager.getModSets.bind(modManager));
ipcMain.handle('create-mod-set', modManager.createModSet.bind(modManager));
ipcMain.handle('switch-mod-set', modManager.switchModSet.bind(modManager));
ipcMain.handle('deactivate-mod-set', modManager.deactivateModSet.bind(modManager));
ipcMain.handle('delete-mod-set', modManager.deleteModSet.bind(modManager));
ipcMain.handle('get-mod-set-details', modManager.getModSetDetails.bind(modManager));
ipcMain.handle('rename-mod-set', modManager.renameModSet.bind(modManager));
ipcMain.handle('recover-lost-mod-sets', modManager.recoverLostModSets.bind(modManager));

// All mod installation functions moved to modules/mod-manager.js

// Desktop Arsenal handlers
ipcMain.handle('desktop-arsenal-toggle-overlay', () => desktopArsenal.toggleOverlay());
ipcMain.handle('desktop-arsenal-get-files', (event, filters) => desktopArsenal.getArsenalFiles(filters));
ipcMain.handle('desktop-arsenal-add-file', (event, filePath, customName, customIcon) => 
  desktopArsenal.addFileToArsenal(filePath, customName, customIcon));
ipcMain.handle('desktop-arsenal-restore-file', (event, fileId) => desktopArsenal.restoreFileToDesktop(fileId));
ipcMain.handle('desktop-arsenal-delete-file', (event, fileId) => desktopArsenal.deleteFileFromArsenal(fileId));
ipcMain.handle('desktop-arsenal-open-file', (event, fileId) => desktopArsenal.openFile(fileId));
ipcMain.handle('desktop-arsenal-update-file', (event, fileId, updates) => 
  desktopArsenal.updateFileMetadata(fileId, updates));
ipcMain.handle('desktop-arsenal-get-settings', () => desktopArsenal.getSettings());
ipcMain.handle('desktop-arsenal-update-settings', (event, settings) => desktopArsenal.updateSettings(settings));
ipcMain.handle('desktop-arsenal-get-stats', () => desktopArsenal.getStats());
ipcMain.handle('desktop-arsenal-bulk-delete', (event, fileIds) => desktopArsenal.bulkDeleteFiles(fileIds));
ipcMain.handle('desktop-arsenal-bulk-restore', (event, fileIds) => desktopArsenal.bulkRestoreFiles(fileIds));
ipcMain.handle('desktop-arsenal-bulk-categorize', (event, fileIds, category) => desktopArsenal.bulkCategorizeFiles(fileIds, category));
ipcMain.handle('desktop-arsenal-get-thumbnail', (event, fileId) => desktopArsenal.getFileThumbnail(fileId));
ipcMain.handle('desktop-arsenal-start-monitoring', () => desktopArsenal.startDesktopMonitoring());
ipcMain.handle('desktop-arsenal-stop-monitoring', () => desktopArsenal.stopDesktopMonitoring());
ipcMain.handle('desktop-arsenal-add-category', (event, categoryData) => desktopArsenal.addCustomCategory(categoryData));
ipcMain.handle('desktop-arsenal-remove-category', (event, categoryId) => desktopArsenal.removeCustomCategory(categoryId));
ipcMain.handle('desktop-arsenal-export-settings', () => desktopArsenal.exportSettings());
ipcMain.handle('desktop-arsenal-import-settings', (event, importPath) => desktopArsenal.importSettings(importPath));
ipcMain.handle('desktop-arsenal-save-background', (event, imageData) => desktopArsenal.saveBackgroundImage(imageData));
ipcMain.handle('desktop-arsenal-save-background-from-path', (event, fileData) => desktopArsenal.saveBackgroundFromPath(fileData));
ipcMain.handle('desktop-arsenal-check-autostart', () => desktopArsenal.checkAutoStart());
ipcMain.handle('desktop-arsenal-set-autostart', (event, enabled) => desktopArsenal.setAutoStart(enabled));
ipcMain.handle('desktop-arsenal-cleanup-backgrounds', () => desktopArsenal.cleanupBackgrounds());

// Handle file addition from notification
ipcMain.on('desktop-arsenal-add-file-from-notification', async (event, filePath) => {
  try {
    await desktopArsenal.addFileToArsenal(filePath);
    // Refresh overlay if it's open
    if (desktopArsenal.overlayWindow && !desktopArsenal.overlayWindow.isDestroyed()) {
      desktopArsenal.overlayWindow.webContents.send('refresh-files');
    }
  } catch (error) {
    console.error('Error adding file from notification:', error);
  }
});

// Desktop Arsenal window control handlers
ipcMain.on('desktop-arsenal-minimize-overlay', () => {
  if (desktopArsenal.overlayWindow) {
    desktopArsenal.overlayWindow.minimize();
  }
});

ipcMain.on('desktop-arsenal-maximize-overlay', () => {
  if (desktopArsenal.overlayWindow) {
    if (desktopArsenal.overlayWindow.isMaximized()) {
      desktopArsenal.overlayWindow.unmaximize();
    } else {
      desktopArsenal.overlayWindow.maximize();
    }
  }
});

ipcMain.on('desktop-arsenal-close-overlay', () => {
  if (desktopArsenal.overlayWindow) {
    desktopArsenal.overlayWindow.close();
  }
});

// Automation Tools handlers
ipcMain.handle('automation-toggle-tool', (event, toolName) => automationTools.toggleTool(toolName));
ipcMain.handle('automation-update-settings', (event, toolName, settings) => automationTools.updateToolSettings(toolName, settings));
ipcMain.handle('automation-get-states', () => automationTools.getToolStates());
ipcMain.handle('automation-start-auto-clicker', (event, settings) => automationTools.startAutoClicker(settings));
ipcMain.handle('automation-stop-auto-clicker', () => automationTools.stopAutoClicker());
ipcMain.handle('automation-start-anti-recoil', (event, settings) => automationTools.startAntiRecoil(settings));
ipcMain.handle('automation-stop-anti-recoil', () => automationTools.stopAntiRecoil());
ipcMain.handle('automation-start-key-sequence', (event, settings) => automationTools.startKeySequence(settings));
ipcMain.handle('automation-stop-key-sequence', () => automationTools.stopKeySequence());

// HWID Spoofer handlers
ipcMain.handle('hwid-spoofer-toggle', () => hwidSpoofer.toggleSpoofing());
ipcMain.handle('hwid-spoofer-get-status', () => hwidSpoofer.getStatus());
ipcMain.handle('hwid-spoofer-backup-values', () => hwidSpoofer.backupCurrentValues());
ipcMain.handle('hwid-spoofer-restore-values', () => hwidSpoofer.restoreOriginalValues());
ipcMain.handle('hwid-spoofer-apply-spoofed', () => hwidSpoofer.applySpoofedValues());
ipcMain.handle('hwid-spoofer-spoof-mac-addresses', () => hwidSpoofer.spoofMacAddresses());
ipcMain.handle('hwid-spoofer-get-system-info', () => hwidSpoofer.getSystemHardwareInfo());
ipcMain.handle('hwid-spoofer-get-current-values', () => hwidSpoofer.getCurrentHWIDValues());
ipcMain.handle('hwid-spoofer-get-comparison', () => hwidSpoofer.getHWIDComparison());

// Kernel HWID Spoofer handlers
ipcMain.handle('kernel-hwid-check-availability', () => kernelHwidSpoofer.checkDriverAvailability());
ipcMain.handle('kernel-hwid-get-status', () => kernelHwidSpoofer.getKernelStatus());
ipcMain.handle('kernel-hwid-get-detailed-status', () => kernelHwidSpoofer.getStatus());
ipcMain.handle('kernel-hwid-toggle', () => kernelHwidSpoofer.toggleKernelSpoofing());
ipcMain.handle('kernel-hwid-backup-values', () => kernelHwidSpoofer.backupCurrentValues());
ipcMain.handle('kernel-hwid-restore-values', () => kernelHwidSpoofer.restoreAllValues());
ipcMain.handle('kernel-hwid-evade-anticheat', (event, options) => kernelHwidSpoofer.evadeAntiCheatDetection(options));

// Minecraft Profile Management handlers
ipcMain.handle('create-minecraft-profile', (event, setupData) => minecraftProfileManager.createMinecraftProfile(setupData));
ipcMain.handle('switch-minecraft-profile', (event, fromProfile, toProfile) => minecraftProfileManager.switchProfile(fromProfile, toProfile));
ipcMain.handle('install-minecraft-loader', (event, loader, mcVersion) => minecraftProfileManager.installLoader(loader, mcVersion));
ipcMain.handle('create-launcher-profile', (event, profileData) => minecraftProfileManager.createLauncherProfile(profileData));

// Mod Downloader handlers
ipcMain.handle('search-mods', (event, searchParams) => modDownloader.searchMods(searchParams));
ipcMain.handle('download-mod', (event, downloadData) => modDownloader.downloadMod(downloadData));

// Desktop Arsenal file operations
ipcMain.handle('desktop-arsenal-add-files', async (event, type = 'files') => {
  // Get the overlay window as parent if it exists, otherwise use main window
  const parentWindow = desktopArsenal.overlayWindow || mainWindow;
  
  // If using overlay window, temporarily disable always on top to show dialog properly
  if (desktopArsenal.overlayWindow) {
    desktopArsenal.overlayWindow.setAlwaysOnTop(false);
  }
  
  // Configure dialog based on type
  let dialogOptions = {
    title: type === 'folders' 
      ? 'Select folders to add to Desktop Arsenal' 
      : 'Select files/shortcuts to add to Desktop Arsenal',
    properties: ['multiSelections'],
    filters: type === 'folders' ? [] : [
      { name: 'All Files', extensions: ['*'] },
      { name: 'Shortcuts', extensions: ['lnk'] },
      { name: 'Executables', extensions: ['exe'] },
      { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt'] },
      { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp'] },
      { name: 'Videos', extensions: ['mp4', 'avi', 'mkv', 'mov'] }
    ]
  };
  
  // Add appropriate property based on type
  if (type === 'folders') {
    dialogOptions.properties.push('openDirectory');
  } else {
    dialogOptions.properties.push('openFile');
  }
  
  const result = await dialog.showOpenDialog(parentWindow, dialogOptions);
  
  // Re-enable always on top after dialog closes
  if (desktopArsenal.overlayWindow) {
    desktopArsenal.overlayWindow.setAlwaysOnTop(true);
  }

  if (!result.canceled && result.filePaths.length > 0) {
    const addedFiles = [];
    for (const filePath of result.filePaths) {
      try {
        const fileData = await desktopArsenal.addFileToArsenal(filePath);
        addedFiles.push(fileData);
      } catch (error) {
        console.error('Failed to add file:', filePath, error);
      }
    }
    return addedFiles;
  }
  
  return [];
});

// Utility functions
// formatBytes function moved to modules/utils.js

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('✅ Main process setup complete'); 