/**
 * Window Manager module for Armory X
 * Extracted from main.js for better maintainability
 */

const { BrowserWindow, Menu, screen, ipcMain } = require('electron');
const path = require('path');

class WindowManager {
  constructor(dependencies = {}) {
    this.app = dependencies.app;
    this.isDev = dependencies.isDev || false;
    this.APP_NAME = dependencies.APP_NAME || 'Armory X';
    this.mainWindow = null;
  }

  /**
   * Create the main application window
   * @returns {BrowserWindow} The main window instance
   */
  createWindow() {
    // Get primary display dimensions
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;

    this.mainWindow = new BrowserWindow({
      width: Math.min(1650, width - 50),
      height: Math.min(1000, height - 50),
      minWidth: 1000,
      minHeight: 650,
      frame: false, // Custom title bar
      titleBarStyle: 'hidden',
      backgroundColor: '#0a0a0a',
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: false,
        webSecurity: !this.isDev
      },
      icon: path.join(__dirname, '..', 'assets', 'Armory_X.ico'),
      show: false // Don't show until ready
    });

    // Load the frontend
    this.mainWindow.loadFile(path.join(__dirname, '..', 'index.html'));

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      console.log('✅ Armory X window shown');
    });

    // Open DevTools in development
    if (this.isDev) {
      this.mainWindow.webContents.openDevTools();
    }

    // Prevent new window creation
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      require('electron').shell.openExternal(url);
      return { action: 'deny' };
    });

    // Debug handlers
    this.mainWindow.webContents.on('render-process-gone', (_event, details) => {
      console.error('⚠️  Renderer process gone:', details);
    });
    this.mainWindow.webContents.on('crashed', () => {
      console.error('💥 Renderer process crashed');
    });
    this.mainWindow.webContents.on('console-message', (_e, level, message, line, source) => {
      console.log(`📜 Renderer log [${level}] ${source}:${line} ->`, message);
    });
    this.mainWindow.webContents.on('did-fail-load', (_e, code, desc, url) => {
      console.error('❌ Renderer failed to load:', code, desc, url);
    });
    this.mainWindow.on('closed', () => {
      console.log('🛑 Main window closed');
      this.mainWindow = null;
    });

    return this.mainWindow;
  }

  /**
   * Setup the application menu
   */
  setupMenu() {
    const template = [
      {
        label: this.APP_NAME,
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  /**
   * Register window control IPC handlers
   */
  registerWindowControlHandlers() {
    // Window control handlers
    ipcMain.on('minimize-window', () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });

    ipcMain.on('maximize-window', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow.maximize();
        }
      }
    });

    ipcMain.on('close-window', () => {
      if (this.mainWindow) {
        this.mainWindow.close();
      }
    });
  }

  /**
   * Get the main window instance
   * @returns {BrowserWindow|null} The main window instance
   */
  getMainWindow() {
    return this.mainWindow;
  }

  /**
   * Check if window exists and is not destroyed
   * @returns {boolean} True if window is valid
   */
  isWindowValid() {
    return this.mainWindow && !this.mainWindow.isDestroyed();
  }

  /**
   * Close the main window
   */
  closeWindow() {
    if (this.isWindowValid()) {
      this.mainWindow.close();
    }
  }

  /**
   * Minimize the main window
   */
  minimizeWindow() {
    if (this.isWindowValid()) {
      this.mainWindow.minimize();
    }
  }

  /**
   * Maximize or restore the main window
   */
  toggleMaximizeWindow() {
    if (this.isWindowValid()) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    }
  }
}

module.exports = { WindowManager };