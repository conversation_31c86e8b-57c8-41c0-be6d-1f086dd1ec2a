<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Armory X - Desktop</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="styles/pc-health.css">
    <link rel="icon" href="assets/Armory_X.ico" type="image/x-icon">
</head>
<body>
    <style>
        /* Initial state - hidden */
        body {
            opacity: 0;
            animation: appFadeIn 0.6s ease-out 0.2s forwards;
        }
        
        @keyframes appFadeIn {
            from {
                opacity: 0;
                transform: scale(0.98);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
    
    <!-- Background Effects -->
    <div class="bg-effects">
        <div class="particles"></div>
        <div class="grid-overlay"></div>
    </div>

    <!-- Main App Container -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <button class="menu-toggle" id="menu-toggle" title="Toggle Menu">
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                </button>
                <div class="logo-section">
                    <img src="assets/Armory_X.ico" alt="Armory X" class="app-logo">
                    <h1 class="app-title">Armory X</h1>
                    <span class="version-tag">Desktop v1.0</span>
                </div>
            </div>
            <div class="header-center">
                <div class="current-tab-indicator" id="current-tab-name">Dashboard</div>
            </div>
            <div class="window-controls">
                <button class="control-btn account" onclick="openAccountPage()" title="Account">
                    <span style="font-size: 1.1rem;">👤</span>
                </button>
                <button class="control-btn minimize" onclick="minimizeWindow()" title="Minimize">─</button>
                <button class="control-btn maximize" onclick="maximizeWindow()" title="Maximize">□</button>
                <button class="control-btn close" onclick="closeWindow()" title="Close">✕</button>
            </div>
        </header>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
                        <!-- Sidebar Navigation -->
            <nav class="sidebar collapsed" id="sidebar">
                <div class="nav-items">
                    <button class="nav-item active" onclick="switchTab('dashboard')" data-tab="Dashboard">
                        <i class="icon">🏠</i>
                        <span class="nav-text">Dashboard</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('cleanup')" data-tab="PC Health">
                        <i class="icon">💊</i>
                        <span class="nav-text">PC Health</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('modmanager')" data-tab="Mod Manager">
                        <i class="icon">🎮</i>
                        <span class="nav-text">Mod Manager</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('tools')" data-tab="Tools">
                        <i class="icon">🔧</i>
                        <span class="nav-text">Tools</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('license')" data-tab="License">
                        <i class="icon">🔑</i>
                        <span class="nav-text">License</span>
                    </button>
                    <button class="nav-item" onclick="switchTab('settings')" data-tab="Settings">
                        <i class="icon">⚙️</i>
                        <span class="nav-text">Settings</span>
                    </button>
                </div>
                
                <!-- Sidebar Footer -->
                <div class="sidebar-footer">
                    <div class="nav-item info-item">
                        <i class="icon">📊</i>
                        <span class="nav-text">
                            <small>Memory: <span id="memory-usage">--</span></small>
                            <small>CPU: <span id="cpu-usage">--</span></small>
                        </span>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="content-header">
                    <h2>🏠 Dashboard</h2>
                    <p>Your productivity workspace</p>
                </div>

                <!-- Modern Dashboard Layout -->
                <div class="modern-dashboard">
                    
                    <!-- Compact Welcome & Clock Header -->
                    <div class="dashboard-section welcome-clock-section full-width">
                        <div class="welcome-clock-header">
                            <!-- Welcome Message -->
                            <div class="compact-welcome">
                                <h3 class="welcome-message" id="welcome-message">Welcome back!</h3>
                                <p class="welcome-subtitle" id="welcome-subtitle">Ready to be productive?</p>
                            </div>
                            
                            <!-- Compact Clock & Mini Calendar -->
                            <div class="clock-calendar-compact" id="clock-calendar-display">
                                <div class="clock-display">
                                    <div class="digital-clock-compact" id="digital-clock-display">
                                        <span class="time-compact" id="current-time">12:00:00</span>
                                        <span class="period-compact" id="period">PM</span>
                                    </div>
                                    <div class="date-compact">
                                        <span class="day-name-compact" id="day-name">Monday</span>
                                        <span class="date-detail-compact" id="date-detail">January 1, 2024</span>
                                    </div>
                                </div>
                                <div class="mini-calendar-compact" id="calendar-display">
                                    <div class="calendar-header-compact">
                                        <button class="calendar-nav-compact" id="prev-month" onclick="changeMonth(-1)">‹</button>
                                        <span class="calendar-month-compact" id="calendar-month">Jan 2024</span>
                                        <button class="calendar-nav-compact" id="next-month" onclick="changeMonth(1)">›</button>
                                    </div>
                                    <div class="calendar-grid-compact" id="calendar-grid">
                                        <!-- Calendar days will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Clock Settings Button (Bottom Left Corner) -->
                            <button class="clock-edit-btn" onclick="openClockSettings()" title="Customize Clock & Calendar">
                                <span>✏️</span>
                            </button>
                        </div>
                    </div>

                    <!-- Quick Launch Section -->
                    <div class="dashboard-section launch-section full-width">
                        <div class="section-header">
                            <h3>🚀 Quick Launch</h3>
                            <button class="add-shortcut-btn" onclick="addQuickLaunchApp()">+ Add App</button>
                        </div>
                        <div class="launch-grid" id="launch-grid">
                            <!-- Apps will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Quick Access Section -->
                    <div class="dashboard-section quick-access-section full-width">
                        <div class="section-header">
                            <h3>⚡ Quick Access</h3>
                            <button class="add-shortcut-btn" onclick="addQuickAccessItem()">+ Add Item</button>
                        </div>
                        <div class="quick-access-grid" id="quick-access-grid">
                            <!-- Quick Access items will be populated by JavaScript -->
                        </div>
                    </div>

                </div>
            </div>

            <!-- PC Health Tab -->
            <div id="cleanup-tab" class="tab-content">
                <div class="content-header">
                    <h2>💊 PC Health</h2>
                    <p>Optimize your system, clean junk files, and benchmark your PC performance</p>
                </div>

                <!-- PC Health Main View -->
                <div class="pc-health-main" id="pc-health-main">
                    <!-- PC Health Actions Grid -->
                    <div class="health-actions-grid">
                        <!-- Junk Cleaner Card -->
                        <div class="health-action-card" data-tool="junkCleaner" onclick="openPCHealthTool('junkCleaner')">
                            <div class="card-icon">🧹</div>
                            <div class="card-content">
                                <h3>Junk File Cleaner</h3>
                                <p>Remove temporary files, browser cache, and system junk</p>
                                <div class="card-stats" id="cleanup-stats">
                                    <span class="stat-value">Ready to clean</span>
                                </div>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>

                        <!-- PC Benchmark Card -->
                        <div class="health-action-card" data-tool="benchmark" onclick="openPCHealthTool('benchmark')">
                            <div class="card-icon">📊</div>
                            <div class="card-content">
                                <h3>PC Benchmark</h3>
                                <p>Test your CPU, GPU, RAM and storage performance</p>
                                <div class="card-stats" id="benchmark-stats">
                                    <span class="stat-value">Not tested yet</span>
                                </div>
                            </div>
                            <div class="card-arrow">→</div>
                        </div>

                        <!-- Desktop Arsenal Card -->
                        <div class="health-action-card desktop-arsenal-card" data-tool="desktopArsenal" onclick="openPCHealthTool('desktopArsenal')">
                            <div class="card-icon">🛡️</div>
                            <div class="card-content">
                                <h3>Desktop Arsenal</h3>
                                <p>Organize and hide desktop files instantly</p>
                                <div class="card-stats" id="arsenal-stats">
                                    <span class="stat-value">0 files organized</span>
                                </div>
                            </div>
                            <div class="premium-badge">👑 Premium</div>
                            <div class="card-arrow">→</div>
                        </div>
                    </div>
                </div>

                <!-- PC Health Tool Views -->
                <!-- Junk Cleaner Full View -->
                <div class="pc-health-tool-view" id="junk-cleaner-view" style="display: none;">
                    <div class="tool-header">
                        <button class="btn btn-secondary back-btn" onclick="backToPCHealthMain()">
                            <span class="btn-icon">←</span>
                            Back to PC Health
                        </button>
                        <h3>🧹 Junk File Cleaner</h3>
                    </div>
                    <div class="tool-body">
                        <!-- Cleanup Status -->
                        <div class="status-card">
                            <div class="status-header">
                                <h4>Cleanup Status</h4>
                                <div class="status-indicator" id="cleanup-status">Ready</div>
                            </div>
                            <div class="progress-section">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill"></div>
                                </div>
                                <div class="progress-info">
                                    <span id="current-operation">Ready to clean...</span>
                                    <span id="progress-stats">0 files processed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Cleanup Options -->
                        <div class="cleanup-options-grid">
                            <label class="cleanup-option">
                                <input type="checkbox" checked id="temp-files">
                                <span class="option-content">
                                    <span class="option-icon">🗂️</span>
                                    <span class="option-text">
                                        <strong>Temporary Files</strong>
                                        <small>System and user temporary files</small>
                                    </span>
                                </span>
                            </label>

                            <label class="cleanup-option">
                                <input type="checkbox" checked id="browser-cache">
                                <span class="option-content">
                                    <span class="option-icon">🌐</span>
                                    <span class="option-text">
                                        <strong>Browser Cache</strong>
                                        <small>Chrome, Edge, Firefox cache files</small>
                                    </span>
                                </span>
                            </label>

                            <label class="cleanup-option">
                                <input type="checkbox" checked id="gaming-cache">
                                <span class="option-content">
                                    <span class="option-icon">🎮</span>
                                    <span class="option-text">
                                        <strong>Gaming Cache</strong>
                                        <small>DirectX, GPU shader cache</small>
                                    </span>
                                </span>
                            </label>

                            <label class="cleanup-option">
                                <input type="checkbox" checked id="recycle-bin">
                                <span class="option-content">
                                    <span class="option-icon">🗑️</span>
                                    <span class="option-text">
                                        <strong>Recycle Bin</strong>
                                        <small>Empty recycle bin contents</small>
                                    </span>
                                </span>
                            </label>
                        </div>

                        <div class="tool-actions">
                            <button class="btn btn-primary" onclick="startCleanup()" id="cleanup-btn">
                                <span class="btn-icon">🚀</span>
                                Start Cleanup
                            </button>
                            <button class="btn btn-secondary" onclick="scanSystem()" id="scan-btn">
                                <span class="btn-icon">🔍</span>
                                Scan Only
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Benchmark Full View -->
                <div class="pc-health-tool-view" id="benchmark-view" style="display: none;">
                    <div class="tool-header">
                        <button class="btn btn-secondary back-btn" onclick="backToPCHealthMain()">
                            <span class="btn-icon">←</span>
                            Back to PC Health
                        </button>
                        <h3>📊 PC Benchmark</h3>
                    </div>
                    <div class="tool-body">
                        <div id="benchmark-start" class="benchmark-section">
                            <div class="benchmark-hero">
                                <div class="hero-icon">🚀</div>
                                <h4>Test Your PC Performance</h4>
                                <p>Run a comprehensive benchmark to evaluate your system's CPU, GPU, RAM, and storage performance. Compare your results against baseline scores.</p>
                                <div style="display: flex; justify-content: center; margin-top: 2rem;">
                                    <button class="btn btn-primary btn-large" onclick="startBenchmark()">
                                        <span class="btn-icon">▶️</span>
                                        Start Benchmark
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="benchmark-progress" class="benchmark-section" style="display: none;">
                            <div class="benchmark-progress-card">
                                <h4>Running Benchmark...</h4>
                                <div class="benchmark-progress-visual">
                                    <div class="benchmark-progress-bar">
                                        <div class="benchmark-progress-fill" id="benchmark-progress-fill"></div>
                                    </div>
                                    <div class="progress-animation">
                                        <div class="pulse-circle"></div>
                                        <div class="pulse-circle"></div>
                                        <div class="pulse-circle"></div>
                                    </div>
                                </div>
                                <div class="benchmark-status">
                                    <span id="benchmark-current-test">Initializing...</span>
                                    <span id="benchmark-progress-text">0/4</span>
                                </div>
                                <div class="test-indicators">
                                    <div class="test-indicator" id="cpu-indicator">
                                        <span class="test-icon">💻</span>
                                        <span class="test-name">CPU</span>
                                    </div>
                                    <div class="test-indicator" id="gpu-indicator">
                                        <span class="test-icon">🎮</span>
                                        <span class="test-name">GPU</span>
                                    </div>
                                    <div class="test-indicator" id="ram-indicator">
                                        <span class="test-icon">🧠</span>
                                        <span class="test-name">RAM</span>
                                    </div>
                                    <div class="test-indicator" id="storage-indicator">
                                        <span class="test-icon">💾</span>
                                        <span class="test-name">Storage</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="benchmark-results" class="benchmark-section" style="display: none;">
                            <div class="overall-score-card">
                                <h4>Overall Performance Score</h4>
                                <div class="score-circle">
                                    <div class="score-value" id="overall-score">0</div>
                                    <div class="score-label">Percentile</div>
                                </div>
                                <div class="score-rating" id="overall-rating">Testing...</div>
                                <p class="score-description" id="overall-description"></p>
                            </div>

                            <div class="benchmark-results-grid">
                                <div class="result-card">
                                    <div class="result-icon">💻</div>
                                    <h5>CPU Performance</h5>
                                    <div class="result-details" id="cpu-results">
                                        <div class="result-item">
                                            <span>Single-Core:</span>
                                            <strong>-</strong>
                                        </div>
                                        <div class="result-item">
                                            <span>Multi-Core:</span>
                                            <strong>-</strong>
                                        </div>
                                    </div>
                                </div>

                                <div class="result-card">
                                    <div class="result-icon">🎮</div>
                                    <h5>GPU Performance</h5>
                                    <div class="result-details" id="gpu-results">
                                        <div class="result-item">
                                            <span>Compute:</span>
                                            <strong>-</strong>
                                        </div>
                                        <div class="result-item">
                                            <span>Rendering:</span>
                                            <strong>-</strong>
                                        </div>
                                    </div>
                                </div>

                                <div class="result-card">
                                    <div class="result-icon">🧠</div>
                                    <h5>Memory (RAM)</h5>
                                    <div class="result-details" id="ram-results">
                                        <div class="result-item">
                                            <span>Bandwidth:</span>
                                            <strong>-</strong>
                                        </div>
                                        <div class="result-item">
                                            <span>Capacity:</span>
                                            <strong>-</strong>
                                        </div>
                                    </div>
                                </div>

                                <div class="result-card">
                                    <div class="result-icon">💾</div>
                                    <h5>Storage</h5>
                                    <div class="result-details" id="storage-results">
                                        <div class="result-item">
                                            <span>Read Speed:</span>
                                            <strong>-</strong>
                                        </div>
                                        <div class="result-item">
                                            <span>Write Speed:</span>
                                            <strong>-</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="benchmark-actions">
                                <button class="btn btn-primary" onclick="runBenchmarkAgain()">
                                    <span class="btn-icon">🔄</span>
                                    Run Again
                                </button>
                                <button class="btn btn-secondary" onclick="exportBenchmarkResults()">
                                    <span class="btn-icon">📄</span>
                                    Export Results
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desktop Arsenal Full View -->
                <div class="pc-health-tool-view" id="desktop-arsenal-view" style="display: none;">
                    <div class="tool-header">
                        <button class="btn btn-secondary back-btn" onclick="backToPCHealthMain()">
                            <span class="btn-icon">←</span>
                            Back to PC Health
                        </button>
                        <h3>🛡️ Desktop Arsenal</h3>
                    </div>
                    <div class="tool-body">
                        <!-- Desktop Arsenal content will be loaded here -->
                        <div class="premium-feature-notice">
                            <div class="premium-icon">👑</div>
                            <h4>Premium Feature</h4>
                            <p>Desktop Arsenal is a premium feature. Upgrade your license to access this powerful desktop organization tool.</p>
                            <button class="btn btn-primary" onclick="switchTab('license')">
                                <span class="btn-icon">💳</span>
                                Upgrade to Premium
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mod Manager Tab -->
            <div id="modmanager-tab" class="tab-content">
                <div class="content-header">
                    <h2>🎮 Mod Manager</h2>
                    <p>Manage and organize your game modifications all in one place</p>
                </div>

                <!-- Beta Warning Banner -->
                <div class="beta-warning-banner">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <strong>Beta Feature</strong>
                        <span>The Mod Manager is currently in beta and does not represent the final quality. While it mostly works as intended, there may be occasional issues or bugs. Please do not fully rely on it being perfect and always backup your mods when making changes if they are important. (Not absolutely necessary but to be safe it is recommended)</span>
                    </div>
                </div>

                <!-- New Steam-like Mod Manager Interface -->
                <div class="mod-manager-new-layout" id="mod-manager-main">
                    <!-- Top Controls Bar -->
                    <div class="mod-manager-controls">
                        <!-- Action Buttons (Top Right) -->
                        <div class="mod-manager-actions">
                            <button class="btn-compact btn-primary" onclick="showAddGameDialog()" title="Add Custom Game">
                                <span class="btn-icon">➕</span>
                                <span>Add Game</span>
                            </button>
                            <button class="btn-compact btn-secondary" onclick="showRemoveGameDialog()" title="Remove Custom Game">
                                <span class="btn-icon">🗑️</span>
                                <span>Remove</span>
                            </button>
                            <button class="btn-compact btn-tertiary" onclick="showModManagerHelp()" title="Help & Guide">
                                <span class="btn-icon">❓</span>
                                <span>Help</span>
                            </button>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="game-search-bar">
                        <div class="search-container-professional">
                            <div class="search-input-wrapper">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="game-search" placeholder="Search your game library..." onkeyup="filterGameLibrary()" />
                                <button class="search-clear" id="game-search-clear" onclick="clearGameSearch()" style="display: none;">×</button>
                            </div>
                        </div>
                    </div>

                    <!-- Sort and Filter Controls -->
                    <div class="game-controls-bar">
                        <div class="sort-filter-section">
                            <div class="sort-control">
                                <label for="game-sort">Sort by:</label>
                                <select id="game-sort" onchange="sortGames()">
                                    <option value="name">Game Name</option>
                                    <option value="recent">Recently Played</option>
                                    <option value="dateAdded">Date Added</option>
                                    <option value="type">Type (Preset/Custom)</option>
                                </select>
                            </div>
                            <div class="filter-control">
                                <label for="game-filter">Filter:</label>
                                <select id="game-filter" onchange="filterGames()">
                                    <option value="all">All Games</option>
                                    <option value="preset">Preset Games Only</option>
                                    <option value="custom">Custom Games Only</option>
                                </select>
                            </div>
                            <div class="view-options">
                                <label class="toggle-option">
                                    <input type="checkbox" id="hide-preset-games" onchange="togglePresetGames()" />
                                    <span>Hide Preset Games</span>
                                </label>
                            </div>
                        </div>
                        <div class="game-count" id="game-count">
                            <span>0 games in library</span>
                        </div>
                    </div>

                    <!-- Games Library Grid -->
                    <div class="games-library" id="games-library">
                        <div class="games-grid" id="games-grid">
                            <!-- Games will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Mod Content Area (shown after game selection) -->
                <div class="mod-content" id="mod-content">
                    <!-- Game Title Header -->
                    <div class="mod-header-main">
                        <div class="mod-header-info">
                            <button class="btn btn-secondary back-btn" onclick="backToGameLibrary()">
                                <span class="btn-icon">←</span>
                                Back to Library
                            </button>
                            <h3 id="selected-game-title">🎮 Game Mods</h3>
                            <span class="mod-count" id="mod-count">0 mods loaded</span>
                        </div>
                        <div class="mod-header-actions">
                            <!-- Standard buttons for all games -->
                            <button class="btn-professional btn-primary" onclick="showAddModDialog()" id="add-mod-btn">
                                <span class="btn-icon">➕</span>
                                <span>Add Mods</span>
                            </button>
                            
                            <!-- Minecraft-specific buttons -->
                            <button class="btn-professional btn-secondary minecraft-only" onclick="showDownloadModsDialog()" id="download-mods-btn" style="display: none;">
                                <span class="btn-icon">⬇️</span>
                                <span>Download Mods</span>
                            </button>
                            
                            <!-- Standard buttons continued -->
                            <button class="btn-professional btn-secondary" onclick="refreshMods()" id="refresh-mods-btn">
                                <span class="btn-icon">🔄</span>
                                <span>Refresh</span>
                            </button>
                            <button class="btn-professional btn-tertiary" onclick="openModFolder()" id="open-folder-btn">
                                <span class="btn-icon">📁</span>
                                <span>Open Folder</span>
                            </button>
                        </div>
                    </div>

                    <!-- Minecraft Mod Sets Management Section (only for Minecraft) -->
                    <div class="minecraft-modsets-section" id="minecraft-modsets-section" style="display: none;">
                        <div class="modsets-header">
                            <div class="modsets-info">
                                <h4>📦 Mod Sets</h4>
                                <div class="modsets-controls">
                                    <select class="modsets-dropdown" id="modsets-dropdown" onchange="switchModSet()">
                                        <option value="">No Mod Set Active</option>
                                        <!-- Mod sets will be populated by JavaScript -->
                                    </select>
                                    <button class="btn-compact btn-primary" onclick="showCreateModSetDialog()" title="Create New Mod Set">
                                        <span class="btn-icon">➕</span>
                                        <span>New Set</span>
                                    </button>
                                    <button class="btn-compact btn-secondary" onclick="showManageModSetsDialog()" title="Manage Mod Sets" id="manage-modsets-btn">
                                        <span class="btn-icon">⚙️</span>
                                        <span>Manage</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="modsets-help-text">
                            <small>🔄 Mod Sets let you quickly switch between different collections of mods, shaders, and resource packs for different worlds/servers</small>
                        </div>
                        <div class="modsets-status" id="modsets-status">
                            <span class="status-indicator" id="modsets-status-indicator">⚪</span>
                            <span class="status-text" id="modsets-status-text">No active Mod Set</span>
                        </div>
                    </div>
                    
                    <!-- Mod Categories/Tabs (for games like Minecraft) -->
                    <div class="mod-tabs" id="mod-tabs" style="display: none;">
                        <!-- Tabs will be populated by JavaScript -->
                    </div>

                    <!-- Search and Filter Section (positioned right above mods) -->
                    <div class="mod-filter-section">
                        <div class="search-container-professional">
                            <div class="search-input-wrapper">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="mod-search" placeholder="Search through your mods..." onkeyup="filterMods()" />
                                <button class="search-clear" id="mod-search-clear" onclick="clearSearch()" style="display: none;">×</button>
                            </div>
                        </div>
                        <div class="filter-stats" id="filter-stats">
                            <span class="stats-text">All mods shown</span>
                        </div>
                    </div>

                    <!-- Mod Content Grid -->
                    <div class="mod-grid" id="mod-grid">
                        <!-- Mods will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Tools Tab -->
            <div id="tools-tab" class="tab-content">
                <div class="content-header">
                    <h2>🔧 Tools</h2>
                    <p>Advanced automation and gaming utilities</p>
                </div>

                <div class="tools-container">
                    <!-- Main Tools Grid -->
                    <div class="main-tools-grid">
                        <!-- Auto-Clicker Tool -->
                        <div class="tool-panel compact">
                            <div class="tool-header">
                                <h3>🖱️ Auto-Clicker</h3>
                                <div class="tool-status inactive" id="auto-clicker-status">Inactive</div>
                            </div>
                            
                            <div class="tool-config compact">
                                <div class="config-group">
                                    <div class="config-row inline">
                                        <label>Button:</label>
                                        <select id="mouse-button-select">
                                            <option value="left">Left</option>
                                            <option value="right">Right</option>
                                            <option value="middle">Middle</option>
                                        </select>
                                    </div>
                                    
                                    <div class="config-row inline">
                                        <label>Type:</label>
                                        <select id="click-type-select">
                                            <option value="single">Single</option>
                                            <option value="double">Double</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="config-row">
                                    <label>Interval: <span id="interval-value">100</span>ms <small id="cps-display">≈ 10 CPS</small></label>
                                    <input type="range" id="click-interval" min="1" max="1000" value="100">
                                </div>
                                
                                <div class="config-row inline">
                                    <label class="checkbox-label compact">
                                        <input type="checkbox" id="ludicrous-mode">
                                        <span class="checkmark"></span>
                                        Ludicrous Mode
                                    </label>
                                    <select id="auto-clicker-hotkey">
                                        <option value="F6">F6</option>
                                        <option value="F7">F7</option>
                                        <option value="F8">F8</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="tool-actions">
                                <button class="btn btn-primary" id="auto-clicker-toggle" onclick="toggleAutoClicker()">
                                    Enable Auto-Clicker
                                </button>
                            </div>
                        </div>

                        <!-- Anti-Recoil Tool -->
                        <div class="tool-panel compact">
                            <div class="tool-header">
                                <h3>🎯 Anti-Recoil</h3>
                                <div class="tool-status inactive" id="anti-recoil-status">Inactive</div>
                            </div>
                            
                            <div class="tool-config compact">
                                <div class="config-row">
                                    <label>Strength: <span id="strength-value">5</span> pixels</label>
                                    <input type="range" id="recoil-strength" min="1" max="20" value="5">
                                </div>
                                
                                <div class="config-row inline">
                                    <label>Hotkey:</label>
                                    <select id="anti-recoil-hotkey">
                                        <option value="F6">F6</option>
                                        <option value="F7" selected>F7</option>
                                        <option value="F8">F8</option>
                                    </select>
                                </div>
                                
                                <div class="tool-description compact">
                                    <p>Hold left mouse button while active to apply compensation</p>
                                </div>
                            </div>
                            
                            <div class="tool-actions">
                                <button class="btn btn-primary" id="anti-recoil-toggle" onclick="toggleAntiRecoil()">
                                    Enable Anti-Recoil
                                </button>
                            </div>
                        </div>

                        <!-- Key Sequence Tool -->
                        <div class="tool-panel compact">
                            <div class="tool-header">
                                <h3>⌨️ Key Sequence</h3>
                                <div class="tool-status inactive" id="key-sequence-status">Inactive</div>
                            </div>
                            
                            <div class="tool-config compact">
                                <div class="config-row">
                                    <label>Keys:</label>
                                    <input type="text" id="key-sequence-input" value="A,S,D,W" placeholder="A,S,D,W">
                                </div>
                                
                                <div class="config-row">
                                    <label>Delay: <span id="key-delay-value">100</span>ms</label>
                                    <input type="range" id="key-delay" min="10" max="1000" value="100">
                                </div>
                                
                                <div class="config-row inline">
                                    <label>Hotkey:</label>
                                    <select id="key-sequence-hotkey">
                                        <option value="F6">F6</option>
                                        <option value="F7">F7</option>
                                        <option value="F8" selected>F8</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="tool-actions">
                                <button class="btn btn-primary" id="key-sequence-toggle" onclick="toggleKeySequence()">
                                    Enable Key Sequence
                                </button>
                            </div>
                        </div>

                        <!-- HWID Spoofer Tool -->
                        <div class="tool-panel compact hwid-spoofer-panel">
                            <div class="tool-header">
                                <h3>🛡️ HWID Spoofer</h3>
                                <div class="tool-status inactive" id="hwid-spoofer-status">Inactive</div>
                            </div>
                            
                            <div class="tool-config compact">
                                <div class="config-row">
                                    <label>Privacy Protection:</label>
                                    <p class="tool-description">Protect your hardware fingerprint from malicious actors</p>
                                </div>
                                
                                <div class="hwid-spoofing-options">
                                    <div class="config-row inline">
                                        <label class="checkbox-label compact">
                                            <input type="checkbox" id="hwid-kernel-mode" disabled>
                                            <span class="checkmark"></span>
                                            MAX MODE
                                        </label>
                                        <button class="help-button" onclick="showMaxModeExplanation()" title="What is MAX MODE?">
                                            <span class="help-icon">❓</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="hwid-status-info" id="hwid-status-info">
                                    <div class="status-item">
                                        <span class="status-label">Backup Status:</span>
                                        <span class="status-value" id="hwid-backup-status">No backup</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Spoofed Values:</span>
                                        <span class="status-value" id="hwid-spoofed-count">0</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Protection Level:</span>
                                        <span class="status-value" id="hwid-protection-level">Registry</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tool-actions">
                                <button class="btn btn-primary" id="hwid-spoofer-toggle" onclick="toggleHWIDSpoofer()">
                                    Enable HWID Spoofing
                                </button>
                                <button class="btn btn-tertiary" onclick="showHWIDInfo()">
                                    View System Info
                                </button>
                                <button class="btn btn-secondary" onclick="emergencyRestore()">
                                    Emergency Restore
                                </button>
                            </div>
                            
                            <div class="tool-advanced" id="hwid-advanced-options" style="display: none;">
                                <div class="advanced-header">
                                    <h4>Advanced Options</h4>
                                </div>
                                
                                <div class="advanced-actions">
                                    <button class="btn btn-tertiary" onclick="restoreHWIDValues()">
                                        Restore Original Values
                                    </button>
                                    <button class="btn btn-tertiary" onclick="showHWIDInfo()">
                                        View System Info
                                    </button>
                                    <button class="btn btn-tertiary" onclick="spoofMACAddresses()">
                                        Spoof MAC Addresses
                                    </button>
                                </div>
                            </div>
                            
                            <div class="tool-warning">
                                <div class="warning-icon">⚠️</div>
                                <div class="warning-text">
                                    <strong>Privacy Tool:</strong> This tool modifies system identifiers for privacy protection. 
                                    Always create a backup before use. Some games may require restart to see changes.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Tools -->
                    <div class="additional-tools">
                        <h3>Additional Tools</h3>
                        <div class="tools-grid compact">
                            <div class="tool-card compact" onclick="showComingSoonMessage('Registry Cleaner')">
                                <div class="tool-icon">🗃️</div>
                                <h4>Registry Cleaner</h4>
                                <span class="coming-soon">Coming Soon</span>
                            </div>

                            <div class="tool-card compact" onclick="showComingSoonMessage('Startup Manager')">
                                <div class="tool-icon">🚀</div>
                                <h4>Startup Manager</h4>
                                <span class="coming-soon">Coming Soon</span>
                            </div>

                            <div class="tool-card compact" onclick="showComingSoonMessage('Process Manager')">
                                <div class="tool-icon">⚡</div>
                                <h4>Process Manager</h4>
                                <span class="coming-soon">Coming Soon</span>
                            </div>

                            <div class="tool-card compact" onclick="showComingSoonMessage('Disk Analyzer')">
                                <div class="tool-icon">💾</div>
                                <h4>Disk Analyzer</h4>
                                <span class="coming-soon">Coming Soon</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- License Tab -->
            <div id="license-tab" class="tab-content">
                <div class="content-header">
                    <h2>🔑 License Management</h2>
                    <p>Manage your Armory X license and premium features</p>
                </div>

                <div class="license-container">
                    <!-- License Status Card -->
                    <div class="license-status-card" id="license-status-card">
                        <div class="license-header">
                            <div class="license-icon">🔓</div>
                            <div class="license-info">
                                <h3 id="license-title">No License</h3>
                                <p id="license-subtitle">Free version with limited features</p>
                            </div>
                            <div class="license-badge" id="license-badge">FREE</div>
                        </div>
                        
                        <div class="license-details" id="license-details">
                            <div class="detail-item">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value" id="license-status-text">No License</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Type:</span>
                                <span class="detail-value" id="license-type">Free</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Expires:</span>
                                <span class="detail-value" id="license-expiry">N/A</span>
                            </div>
                        </div>
                    </div>

                    <!-- License Actions -->
                    <div class="license-actions-grid">
                        <div class="license-action-card">
                            <h3>🔑 Activate License</h3>
                            <p>Enter your license key to unlock premium features</p>
                            <button class="btn btn-primary" onclick="activateLicense()">
                                <span class="btn-icon">🔑</span>
                                Activate License
                            </button>
                        </div>

                        <div class="license-action-card">
                            <h3>🆓 Try Premium</h3>
                            <p>Generate a 7-day trial license</p>
                            <button class="btn btn-secondary" onclick="generateTrialKey()">
                                <span class="btn-icon">🎁</span>
                                Generate Trial
                            </button>
                        </div>

                        <div class="license-action-card">
                            <h3>💳 Purchase License</h3>
                            <p>Get lifetime access to all features</p>
                            <button class="btn btn-accent" onclick="purchaseLicense()">
                                <span class="btn-icon">💳</span>
                                Buy Premium
                            </button>
                        </div>
                    </div>

                    <!-- Features Comparison -->
                    <div class="features-comparison">
                        <h3>🎯 Feature Comparison</h3>
                        <div class="features-grid">
                            <div class="feature-item">
                                <span class="feature-icon">🧹</span>
                                <span class="feature-name">System Cleanup</span>
                                <span class="feature-free">✅</span>
                                <span class="feature-premium">✅</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🎮</span>
                                <span class="feature-name">Mod Manager</span>
                                <span class="feature-free">✅</span>
                                <span class="feature-premium">✅</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🔧</span>
                                <span class="feature-name">Gaming Tools</span>
                                <span class="feature-free">❌</span>
                                <span class="feature-premium">✅</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🗂️</span>
                                <span class="feature-name">Desktop Widget</span>
                                <span class="feature-free">❌</span>
                                <span class="feature-premium">✅</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="content-header">
                    <h2>⚙️ Settings</h2>
                    <p>Customize your Armory X experience and preferences</p>
                </div>

                <div class="settings-container">
                    <!-- Settings Navigation -->
                    <div class="settings-nav">
                        <button class="settings-tab active" onclick="showSettingsSection('appearance')" data-section="appearance">
                            <i class="tab-icon">🎨</i>
                            <span>Appearance</span>
                        </button>
                        <button class="settings-tab" onclick="showSettingsSection('behavior')" data-section="behavior">
                            <i class="tab-icon">⚡</i>
                            <span>Behavior</span>
                        </button>
                        <button class="settings-tab" onclick="showSettingsSection('performance')" data-section="performance">
                            <i class="tab-icon">🚀</i>
                            <span>Performance</span>
                        </button>
                        <button class="settings-tab" onclick="showSettingsSection('browser')" data-section="browser">
                            <i class="tab-icon">🌐</i>
                            <span>Browser</span>
                        </button>
                        <button class="settings-tab" onclick="showSettingsSection('advanced')" data-section="advanced">
                            <i class="tab-icon">🔧</i>
                            <span>Advanced</span>
                        </button>
                    </div>

                    <!-- Settings Content -->
                    <div class="settings-content">
                        <!-- Appearance Settings -->
                        <div class="settings-section active" id="appearance-settings">
                            <h3>🎨 Appearance Settings</h3>
                            
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>Theme</h4>
                                    <p>Choose your preferred color scheme</p>
                                    <select class="setting-select" id="theme-select">
                                        <option value="dark">Dark Theme</option>
                                        <option value="light">Light Theme</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>

                                <div class="setting-card">
                                    <h4>Primary Color</h4>
                                    <p>Main accent color for the interface</p>
                                    <div class="color-picker-container">
                                        <input type="color" class="color-picker" id="primary-color" value="#00d4ff">
                                        <span class="color-value">#00d4ff</span>
                                    </div>
                                </div>

                                <div class="setting-card">
                                    <h4>Accent Color</h4>
                                    <p>Secondary accent color</p>
                                    <div class="color-picker-container">
                                        <input type="color" class="color-picker" id="accent-color" value="#00ff88">
                                        <span class="color-value">#00ff88</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Behavior Settings -->
                        <div class="settings-section" id="behavior-settings">
                            <h3>⚡ Behavior Settings</h3>
                            
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Animations</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="animations-enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Enable smooth animations and transitions</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Auto Cleanup</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="auto-cleanup">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Automatically clean system on startup</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Notifications</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="notifications-enabled" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Show desktop notifications for important events</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Minimize to Tray</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="minimize-to-tray">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Minimize to system tray instead of taskbar</p>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Settings -->
                        <div class="settings-section" id="performance-settings">
                            <h3>🚀 Performance Settings</h3>
                            
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>Startup Optimization</h4>
                                    <p>Optimize application startup time</p>
                                    <div class="setting-header">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="startup-optimization" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-card">
                                    <h4>Background Processing</h4>
                                    <p>Allow background system monitoring</p>
                                    <div class="setting-header">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="background-processing" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Browser Settings -->
                        <div class="settings-section" id="browser-settings">
                            <h3>🌐 Browser Settings</h3>
                            
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Default Website</h4>
                                        <select id="default-site-select" class="setting-select">
                                            <option value="modrinth">📦 Modrinth</option>
                                            <option value="curseforge">🔥 CurseForge</option>
                                            <option value="custom">🌐 Custom URL</option>
                                        </select>
                                    </div>
                                    <p>Choose which website opens by default when starting the browser</p>
                                    
                                    <div id="custom-default-url" class="custom-url-input" style="display: none;">
                                        <input type="url" id="custom-default-url-input" placeholder="Enter custom default URL..." class="setting-input">
                                    </div>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Show Quick Navigation</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="show-quick-nav" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Display quick navigation buttons for popular mod sites</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Enable Ad Blocker</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="enable-ad-blocker" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Block ads and trackers for a cleaner browsing experience</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Auto-Open Downloads</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="auto-open-downloads" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Automatically process downloads when they're detected</p>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="settings-section" id="advanced-settings">
                            <h3>🔧 Advanced Settings</h3>
                            
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Start with Windows</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="start-with-windows">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Launch Armory X when Windows starts</p>
                                </div>

                                <div class="setting-card">
                                    <div class="setting-header">
                                        <h4>Developer Mode</h4>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="developer-mode">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p>Enable advanced debugging features</p>
                                </div>

                                <div class="setting-card danger">
                                    <h4>Reset Settings</h4>
                                    <p>Restore all settings to default values</p>
                                    <button class="btn btn-danger" onclick="resetSettings()">
                                        <span class="btn-icon">🔄</span>
                                        Reset to Defaults
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Footer -->
                <div class="settings-footer">
                    <div class="settings-info">
                        <span>Changes are saved automatically</span>
                    </div>
                    <div class="settings-actions">
                        <button class="btn btn-secondary" onclick="exportSettings()">
                            <span class="btn-icon">📄</span>
                            Export
                        </button>
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <span class="btn-icon">💾</span>
                            Save Settings
                        </button>
                    </div>
                                 </div>
             </div>
             
            <!-- Account Tab -->
            <div id="account-tab" class="tab-content">
                <div class="tab-header">
                    <h2>👤 Account Management</h2>
                    <p>Manage your Armory X account and license settings</p>
                </div>

                <div class="account-container">
                    <!-- Account Overview Card -->
                    <div class="account-card overview-card">
                        <div class="account-header">
                            <div class="account-avatar">
                                <span class="avatar-icon">👤</span>
                            </div>
                            <div class="account-info">
                                <h3 class="account-username" id="account-username">Loading...</h3>
                                <p class="account-email" id="account-email">Loading...</p>
                                <span class="account-status" id="account-status">
                                    <span class="status-indicator"></span>
                                    Active
                                </span>
                            </div>
                        </div>
                        <div class="account-stats">
                            <div class="stat-item">
                                <span class="stat-value" id="account-created">--</span>
                                <span class="stat-label">Member Since</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="license-type">--</span>
                                <span class="stat-label">License Type</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="license-expiry">--</span>
                                <span class="stat-label">Valid Until</span>
                            </div>
                        </div>
                    </div>

                    <!-- Account Actions Grid -->
                    <div class="account-actions-grid">
                        <!-- License Key Management -->
                        <div class="account-section">
                            <h3>🔑 License Management</h3>
                            <div class="license-card">
                                <div class="license-info">
                                    <p class="license-label">Current License Key</p>
                                    <div class="license-key-display">
                                        <span id="license-key-masked">****-****-****-****</span>
                                        <button class="btn-icon" onclick="toggleLicenseVisibility()" title="Show/Hide">
                                            <span id="visibility-icon">👁️</span>
                                        </button>
                                        <button class="btn-icon" onclick="copyLicenseKey()" title="Copy">
                                            <span>📋</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="license-actions">
                                    <button class="btn btn-primary" onclick="openRedeemDialog()">
                                        <span class="btn-icon">🎁</span>
                                        Redeem New Key
                                    </button>
                                    <button class="btn btn-secondary" onclick="checkLicenseStatus()">
                                        <span class="btn-icon">🔄</span>
                                        Check Status
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Account Security -->
                        <div class="account-section">
                            <h3>🔐 Account Security</h3>
                            <div class="security-options">
                                <div class="security-item">
                                    <div class="security-info">
                                        <h4>Change Password</h4>
                                        <p>Update your account password</p>
                                    </div>
                                    <button class="btn btn-secondary" onclick="openChangePasswordDialog()">
                                        Change
                                    </button>
                                </div>
                                <div class="security-item">
                                    <div class="security-info">
                                        <h4>Two-Factor Authentication</h4>
                                        <p>Add an extra layer of security</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="2fa-toggle" onchange="toggle2FA()">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="security-item">
                                    <div class="security-info">
                                        <h4>Email Address</h4>
                                        <p id="account-email-display">Loading...</p>
                                    </div>
                                    <button class="btn btn-secondary" onclick="openChangeEmailDialog()">
                                        Change
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Premium Features -->
                        <div class="account-section">
                            <h3>⭐ Premium Features</h3>
                            <div class="premium-features">
                                <div class="feature-item active">
                                    <span class="feature-icon">🚀</span>
                                    <span class="feature-name">Advanced Automation</span>
                                    <span class="feature-status">✅</span>
                                </div>
                                <div class="feature-item active">
                                    <span class="feature-icon">🛡️</span>
                                    <span class="feature-name">Desktop Arsenal</span>
                                    <span class="feature-status">✅</span>
                                </div>
                                <div class="feature-item active">
                                    <span class="feature-icon">🎮</span>
                                    <span class="feature-name">Unlimited Mod Slots</span>
                                    <span class="feature-status">✅</span>
                                </div>
                                <div class="feature-item active">
                                    <span class="feature-icon">🔧</span>
                                    <span class="feature-name">Priority Support</span>
                                    <span class="feature-status">✅</span>
                                </div>
                            </div>
                            <div class="upgrade-cta" id="upgrade-section" style="display: none;">
                                <p>Unlock all premium features with a license key</p>
                                <button class="btn btn-primary" onclick="openUpgradeDialog()">
                                    <span class="btn-icon">⚡</span>
                                    Upgrade Now
                                </button>
                            </div>
                        </div>

                        <!-- Account Data -->
                        <div class="account-section">
                            <h3>📊 Account Data</h3>
                            <div class="data-options">
                                <button class="btn btn-secondary" onclick="exportAccountData()">
                                    <span class="btn-icon">📤</span>
                                    Export Data
                                </button>
                                <button class="btn btn-secondary" onclick="downloadActivityLog()">
                                    <span class="btn-icon">📜</span>
                                    Activity Log
                                </button>
                                <button class="btn btn-danger" onclick="openDeleteAccountDialog()">
                                    <span class="btn-icon">🗑️</span>
                                    Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </main>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Processing...</h3>
            <p id="loading-message">Please wait while we process your request</p>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Quick Access Selection Modal -->
    <div id="quick-access-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>⚡ Add to Quick Access</h3>
                <p>Choose from Armory X pages, system tools, or browse for files</p>
                <button class="modal-close" onclick="closeQuickAccessModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- Armory X Pages Section -->
                <div class="modal-section">
                    <h4>🏠 Armory X Pages</h4>
                    <div class="quick-access-options" id="armory-pages">
                        <div class="option-card" onclick="addQuickAccessPage('dashboard', '🏠', 'Dashboard')">
                            <div class="option-icon">🏠</div>
                            <div class="option-info">
                                <span class="option-name">Dashboard</span>
                                <span class="option-desc">Main productivity workspace</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessPage('cleanup', '🧹', 'System Cleanup')">
                            <div class="option-icon">🧹</div>
                            <div class="option-info">
                                <span class="option-name">System Cleanup</span>
                                <span class="option-desc">Clean temporary files</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessPage('modmanager', '🎮', 'Mod Manager')">
                            <div class="option-icon">🎮</div>
                            <div class="option-info">
                                <span class="option-name">Mod Manager</span>
                                <span class="option-desc">Manage game modifications</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessPage('tools', '🔧', 'System Tools')">
                            <div class="option-icon">🔧</div>
                            <div class="option-info">
                                <span class="option-name">System Tools</span>
                                <span class="option-desc">Gaming utilities & automation</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessPage('license', '🔑', 'License Management')">
                            <div class="option-icon">🔑</div>
                            <div class="option-info">
                                <span class="option-name">License Management</span>
                                <span class="option-desc">Manage premium features</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessPage('settings', '⚙️', 'Settings')">
                            <div class="option-icon">⚙️</div>
                            <div class="option-info">
                                <span class="option-name">Settings</span>
                                <span class="option-desc">Customize your experience</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Tools Section -->
                <div class="modal-section">
                    <h4>🛠️ System Tools</h4>
                    <div class="quick-access-options">
                        <div class="option-card" onclick="addQuickAccessTool('taskmgr.exe', '📊', 'Task Manager')">
                            <div class="option-icon">📊</div>
                            <div class="option-info">
                                <span class="option-name">Task Manager</span>
                                <span class="option-desc">View system processes</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessTool('control', '⚙️', 'Control Panel')">
                            <div class="option-icon">⚙️</div>
                            <div class="option-info">
                                <span class="option-name">Control Panel</span>
                                <span class="option-desc">Windows settings</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessTool('regedit', '🗃️', 'Registry Editor')">
                            <div class="option-icon">🗃️</div>
                            <div class="option-info">
                                <span class="option-name">Registry Editor</span>
                                <span class="option-desc">Advanced system configuration</span>
                            </div>
                        </div>
                        <div class="option-card" onclick="addQuickAccessTool('devmgmt.msc', '🔌', 'Device Manager')">
                            <div class="option-icon">🔌</div>
                            <div class="option-info">
                                <span class="option-name">Device Manager</span>
                                <span class="option-desc">Manage hardware devices</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Browse Files Section -->
                <div class="modal-section">
                    <h4>📁 Browse Files</h4>
                    <div class="browse-section">
                        <button class="btn-browse" onclick="openFileBrowser()">
                            <span class="btn-icon">📂</span>
                            Browse for Application or File
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Clock Settings Modal -->
    <div id="clock-settings-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content modal-medium">
            <div class="modal-header">
                <h3>⚙️ Clock & Calendar Settings</h3>
                <p>Customize your dashboard display preferences</p>
                <button class="modal-close" onclick="closeClockSettings()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4>🕐 Time Display</h4>
                    <div class="setting-group">
                        <div class="setting-item">
                            <label>Time Format:</label>
                            <select id="time-format-select" onchange="changeTimeFormat()">
                                <option value="12">12-Hour</option>
                                <option value="24">24-Hour</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <label class="toggle-setting">
                                <span>Show Seconds</span>
                                <label class="toggle">
                                    <input type="checkbox" id="show-seconds" checked onchange="toggleSeconds()">
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>📅 Calendar Display</h4>
                    <div class="setting-group">
                        <div class="setting-item">
                            <label class="toggle-setting">
                                <span>Show Calendar</span>
                                <label class="toggle">
                                    <input type="checkbox" id="show-calendar" checked onchange="toggleCalendar()">
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="toggle-setting">
                                <span>Show Weekday Headers</span>
                                <label class="toggle">
                                    <input type="checkbox" id="show-weekdays" checked onchange="toggleWeekdays()">
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>🎨 Animation Settings</h4>
                    <div class="setting-group">
                        <div class="setting-item">
                            <label class="toggle-setting">
                                <span>Clock Glow Animation</span>
                                <label class="toggle">
                                    <input type="checkbox" id="clock-animation" checked onchange="toggleClockAnimation()">
                                    <span class="slider"></span>
                                </label>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="resetClockSettings()">Reset to Default</button>
                <button class="btn btn-primary" onclick="closeClockSettings()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Custom File Browser Modal -->
    <div id="file-browser-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content modal-xlarge">
            <div class="modal-header">
                <h3>📂 Select Application or File</h3>
                <div class="breadcrumb" id="file-breadcrumb">
                    <span class="breadcrumb-item">This PC</span>
                </div>
                <button class="modal-close" onclick="closeFileBrowser()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="file-browser-container">
                    <!-- Navigation Bar -->
                    <div class="file-nav">
                        <button class="nav-btn" onclick="fileBrowserBack()" id="back-btn" disabled>
                            <span>←</span> Back
                        </button>
                        <button class="nav-btn" onclick="fileBrowserUp()" id="up-btn">
                            <span>↑</span> Up
                        </button>
                        <div class="path-input">
                            <input type="text" id="current-path" readonly placeholder="Current location...">
                        </div>
                        <button class="nav-btn" onclick="refreshFileBrowser()">
                            <span>🔄</span> Refresh
                        </button>
                    </div>

                    <!-- Quick Access Sidebar -->
                    <div class="file-browser-layout">
                        <div class="file-sidebar">
                            <div class="sidebar-section">
                                <h5>Quick Access</h5>
                                <div class="sidebar-item" onclick="navigateToPath('C:')">
                                    <span class="sidebar-icon">💾</span>
                                    <span>Local Disk (C:)</span>
                                </div>
                                <div class="sidebar-item" onclick="navigateToPath(getUserHome())">
                                    <span class="sidebar-icon">🏠</span>
                                    <span>Home</span>
                                </div>
                                <div class="sidebar-item" onclick="navigateToPath(getUserDesktop())">
                                    <span class="sidebar-icon">🖥️</span>
                                    <span>Desktop</span>
                                </div>
                                <div class="sidebar-item" onclick="navigateToPath(getUserDocuments())">
                                    <span class="sidebar-icon">📄</span>
                                    <span>Documents</span>
                                </div>
                                <div class="sidebar-item" onclick="navigateToPath(getUserDownloads())">
                                    <span class="sidebar-icon">📥</span>
                                    <span>Downloads</span>
                                </div>
                            </div>
                            <div class="sidebar-section">
                                <h5>Common Apps</h5>
                                <div class="sidebar-item" onclick="navigateToPath('C:\\Program Files')">
                                    <span class="sidebar-icon">📁</span>
                                    <span>Program Files</span>
                                </div>
                                <div class="sidebar-item" onclick="navigateToPath('C:\\Program Files (x86)')">
                                    <span class="sidebar-icon">📁</span>
                                    <span>Program Files (x86)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Main File Area -->
                        <div class="file-main">
                            <div class="file-list" id="file-list">
                                <!-- Files will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="file-browser-footer">
                        <div class="selected-file">
                            <span>Selected: </span>
                            <span id="selected-file-name">None</span>
                        </div>
                        <div class="file-actions">
                            <button class="btn-cancel" onclick="closeFileBrowser()">Cancel</button>
                            <button class="btn-confirm" onclick="confirmFileSelection()" id="select-file-btn" disabled>
                                Select File
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
    <script src="dashboard.js"></script>

    <!-- Ensure window control functions are available -->
    <script>
    // Fallback window control functions in case app.js hasn't loaded yet
    if (typeof closeWindow === 'undefined') {
        window.closeWindow = function() {
            console.log('Fallback closeWindow called');
            if (window.ipcRenderer) {
                window.ipcRenderer.send('close-window');
            }
        };
    }
    if (typeof minimizeWindow === 'undefined') {
        window.minimizeWindow = function() {
            console.log('Fallback minimizeWindow called');
            if (window.ipcRenderer) {
                window.ipcRenderer.send('minimize-window');
            }
        };
    }
    if (typeof maximizeWindow === 'undefined') {
        window.maximizeWindow = function() {
            console.log('Fallback maximizeWindow called');
            if (window.ipcRenderer) {
                window.ipcRenderer.send('maximize-window');
            }
        };
    }
    if (typeof openAccountPage === 'undefined') {
        window.openAccountPage = function() {
            console.log('Fallback openAccountPage called');
            // Will be replaced when app.js loads
        };
    }
    </script>

</body>
</html> 