/**
 * Mod Downloader module for Armory X
 * Handles mod searching and downloading from CurseForge and Modrinth APIs
 */

const https = require('https');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');

class ModDownloader {
  constructor(minecraftProfileManager) {
    this.minecraftProfileManager = minecraftProfileManager;
    this.curseForgeApiKey = null; // Would be configured in production
    this.modrinthBaseUrl = 'https://api.modrinth.com/v2';
    this.curseForgeBaseUrl = 'https://api.curseforge.com/v1';
  }

  /**
   * Make an HTTP request
   * @param {string} url - Request URL
   * @param {Object} headers - Request headers
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(url, headers = {}) {
    return new Promise((resolve, reject) => {
      const request = https.get(url, { headers }, (response) => {
        let data = '';
        
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          try {
            const parsedData = JSON.parse(data);
            resolve(parsedData);
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });
      
      request.on('error', reject);
      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Search mods on Modrinth
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Array>} Search results
   */
  async searchModrinth(searchParams) {
    console.log('🔍 Searching Modrinth for:', searchParams.query);
    
    try {
      const { query, mcVersion, category } = searchParams;
      
      // Build search URL with parameters
      const searchUrl = new URL(`${this.modrinthBaseUrl}/search`);
      searchUrl.searchParams.append('query', query);
      searchUrl.searchParams.append('limit', '20');
      searchUrl.searchParams.append('facets', `[["project_type:mod"],["versions:${mcVersion || 'latest'}"]]`);
      
      if (category) {
        searchUrl.searchParams.append('categories', `["${category}"]`);
      }
      
      const response = await this.makeRequest(searchUrl.toString());
      
      return response.hits.map(mod => ({
        id: mod.project_id,
        name: mod.title,
        description: mod.description,
        author: mod.author,
        downloads: this.formatDownloadCount(mod.downloads),
        iconUrl: mod.icon_url,
        source: 'modrinth',
        projectUrl: `https://modrinth.com/mod/${mod.slug}`,
        versions: mod.versions
      }));
      
    } catch (error) {
      console.error('❌ Error searching Modrinth:', error);
      return [];
    }
  }

  /**
   * Search mods on CurseForge
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Array>} Search results
   */
  async searchCurseForge(searchParams) {
    console.log('🔍 Searching CurseForge for:', searchParams.query);
    
    // For demo purposes, return some placeholder results
    // In production, this would use the actual CurseForge API
    return [
      {
        id: 'optifine',
        name: 'OptiFine',
        description: 'Performance and graphics optimization mod',
        author: 'sp614x',
        downloads: '50M+',
        source: 'curseforge',
        projectUrl: 'https://optifine.net'
      },
      {
        id: 'jei',
        name: 'Just Enough Items (JEI)',
        description: 'Recipe viewing mod for Minecraft',
        author: 'mezz',
        downloads: '30M+',
        source: 'curseforge',
        projectUrl: 'https://curseforge.com/minecraft/mc-mods/jei'
      }
    ];
  }

  /**
   * Search for mods across platforms
   * @param {Object} searchParams - Search parameters
   * @returns {Promise<Object>} Search results
   */
  async searchMods(searchParams) {
    console.log('🔍 Searching for mods:', searchParams);
    
    try {
      const { source } = searchParams;
      let results = [];
      
      if (source === 'both' || source === 'modrinth') {
        const modrinthResults = await this.searchModrinth(searchParams);
        results = results.concat(modrinthResults);
      }
      
      if (source === 'both' || source === 'curseforge') {
        const curseForgeResults = await this.searchCurseForge(searchParams);
        results = results.concat(curseForgeResults);
      }
      
      // Sort by downloads (estimated)
      results.sort((a, b) => {
        const aDownloads = this.parseDownloadCount(a.downloads);
        const bDownloads = this.parseDownloadCount(b.downloads);
        return bDownloads - aDownloads;
      });
      
      return {
        success: true,
        mods: results,
        total: results.length
      };
      
    } catch (error) {
      console.error('❌ Error searching mods:', error);
      return {
        success: false,
        mods: [],
        total: 0,
        error: error.message
      };
    }
  }

  /**
   * Download a file from URL
   * @param {string} url - Download URL
   * @param {string} destination - Local file path
   * @returns {Promise<void>}
   */
  async downloadFile(url, destination) {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(destination);
      
      https.get(url, (response) => {
        if (response.statusCode === 302 || response.statusCode === 301) {
          // Handle redirects
          this.downloadFile(response.headers.location, destination).then(resolve).catch(reject);
          return;
        }
        
        if (response.statusCode !== 200) {
          reject(new Error(`Download failed: ${response.statusCode}`));
          return;
        }
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve();
        });
        
        file.on('error', (err) => {
          fs.unlink(destination);
          reject(err);
        });
      }).on('error', reject);
    });
  }

  /**
   * Get download URL for a mod
   * @param {string} modId - Mod identifier
   * @param {string} source - Source platform
   * @param {string} mcVersion - Minecraft version
   * @returns {Promise<Object>} Download information
   */
  async getModDownloadUrl(modId, source, mcVersion) {
    console.log(`📥 Getting download URL for mod: ${modId} from ${source}`);
    
    try {
      if (source === 'modrinth') {
        // Get project versions
        const versionsUrl = `${this.modrinthBaseUrl}/project/${modId}/version`;
        const versions = await this.makeRequest(versionsUrl);
        
        // Find compatible version
        const compatibleVersion = versions.find(version => 
          version.game_versions.includes(mcVersion) && 
          version.files.length > 0
        );
        
        if (!compatibleVersion) {
          throw new Error(`No compatible version found for Minecraft ${mcVersion}`);
        }
        
        const primaryFile = compatibleVersion.files.find(file => file.primary) || compatibleVersion.files[0];
        
        return {
          downloadUrl: primaryFile.url,
          filename: primaryFile.filename,
          size: primaryFile.size,
          version: compatibleVersion.version_number
        };
        
      } else if (source === 'curseforge') {
        // For demo purposes, return a placeholder
        return {
          downloadUrl: 'https://example.com/mod.jar',
          filename: `${modId}.jar`,
          size: 1024000,
          version: '1.0.0'
        };
      }
      
      throw new Error(`Unsupported source: ${source}`);
      
    } catch (error) {
      console.error('❌ Error getting download URL:', error);
      throw error;
    }
  }

  /**
   * Download and install a mod
   * @param {Object} downloadData - Download configuration
   * @returns {Promise<Object>} Download result
   */
  async downloadMod(downloadData) {
    console.log('⬇️ Downloading mod:', downloadData);
    
    try {
      const { modId, profile, source = 'modrinth', mcVersion = 'latest' } = downloadData;
      
      // Get download information
      const downloadInfo = await this.getModDownloadUrl(modId, source, mcVersion);
      
      // Determine installation path based on profile
      let installPath;
      if (profile === 'default') {
        installPath = path.join(this.minecraftProfileManager.getMinecraftDirectory(), 'mods');
      } else {
        installPath = path.join(this.minecraftProfileManager.profilesDir, profile, 'mods');
      }
      
      // Ensure installation directory exists
      await fs.ensureDir(installPath);
      
      // Download the mod
      const modFilePath = path.join(installPath, downloadInfo.filename);
      
      // Check if mod already exists
      if (await fs.pathExists(modFilePath)) {
        throw new Error('Mod already exists in this profile');
      }
      
      // For demo purposes, create a placeholder file instead of actual download
      if (source === 'curseforge' || !downloadInfo.downloadUrl.startsWith('http')) {
        // Create a dummy mod file for demo
        const demoContent = `# Demo mod file for ${modId}\n# This would be the actual mod file in production\n`;
        await fs.writeFile(modFilePath, demoContent);
      } else {
        // Actually download the file
        await this.downloadFile(downloadInfo.downloadUrl, modFilePath);
      }
      
      return {
        success: true,
        message: `Successfully downloaded ${downloadInfo.filename}`,
        filePath: modFilePath,
        size: downloadInfo.size,
        version: downloadInfo.version
      };
      
    } catch (error) {
      console.error('❌ Error downloading mod:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Format download count for display
   * @param {number} count - Download count
   * @returns {string} Formatted count
   */
  formatDownloadCount(count) {
    if (count >= 1000000) {
      return `${Math.floor(count / 1000000)}M+`;
    } else if (count >= 1000) {
      return `${Math.floor(count / 1000)}K+`;
    }
    return count.toString();
  }

  /**
   * Parse download count from string
   * @param {string} countStr - Download count string
   * @returns {number} Numeric count
   */
  parseDownloadCount(countStr) {
    if (typeof countStr === 'number') return countStr;
    
    const str = countStr.toString().toLowerCase();
    const num = parseFloat(str);
    
    if (str.includes('m')) {
      return num * 1000000;
    } else if (str.includes('k')) {
      return num * 1000;
    }
    
    return num || 0;
  }
}

module.exports = { ModDownloader }; 