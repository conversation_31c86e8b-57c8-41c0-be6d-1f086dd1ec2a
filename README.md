# Armory X - Electron Desktop Application

This is the **Electron version** of Armory X, providing a native desktop experience with full system integration capabilities.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm (comes with Node.js)

### Installation & Running

1. **Install Dependencies**
   ```bash
   cd "Armory X - Electron"
   npm install
   ```

2. **Start the Application**
   ```bash
   npm start
   ```
   *or*
   ```bash
   npm run electron
   ```

## 📁 Project Structure

```
Armory X - Electron/
├── main.js           # Main Electron process (backend)
├── app.js            # Renderer process (frontend logic)
├── index.html        # Main application window
├── styles.css        # Main application styles
├── command-center.css # Additional UI styles
├── assets/           # Images, icons, and resources
├── package.json      # Dependencies and scripts
└── README.md         # This file
```

## 🔧 Features

- **System Integration**: Native desktop experience with system tray, notifications, and window controls
- **Mod Manager**: Full mod management for supported games
- **System Monitoring**: Real-time CPU and memory monitoring
- **Cleanup Tools**: System junk file cleanup and optimization
- **License Management**: Professional licensing system
- **Keyboard Shortcuts**: Full keyboard navigation support

## ⚡ Development

### Scripts
- `npm start` - Start the Electron application
- `npm run electron` - Alternative start command
- `npm run build` - Build the application for distribution

### Key Files
- **main.js**: Contains the main Electron process, handles system operations, file management, and IPC communication
- **app.js**: Contains the renderer process logic, UI interactions, and frontend functionality
- **index.html**: The main application interface

## 🛠️ Troubleshooting

### Common Issues

1. **ipcRenderer errors**: Make sure you're running the app through `npm start` and not opening the HTML file directly
2. **Module not found**: Run `npm install` to install all dependencies
3. **Permission errors**: Make sure you have proper permissions to access system resources

### Debug Mode
- Press `F12` to open the debug panel
- Press `F1` for keyboard shortcuts help
- Check the console for detailed error messages

## 🔒 Security

This application implements secure IPC communication between main and renderer processes, follows Electron security best practices, and includes professional-grade license validation.

## 📝 Notes

- This is the **Electron version** - different from the Tauri and WPF versions
- All system operations are handled through secure IPC channels
- Custom modals and dialogs maintain professional appearance
- No default browser popups are used for security reasons

---

**Armory X Electron** - Professional desktop application for gamers and system optimization. 