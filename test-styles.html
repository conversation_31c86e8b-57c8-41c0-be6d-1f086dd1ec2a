<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArmoryX Styles Test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 2rem;
            background: var(--background-dark);
            color: var(--text-primary);
        }
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--background-card);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }
        .test-section h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <h1>ArmoryX Modular CSS Test</h1>
    <p>This page tests the modular CSS architecture to ensure all styles are loading correctly.</p>

    <!-- Test Variables -->
    <div class="test-section">
        <h2>CSS Variables Test</h2>
        <p>Primary color: <span style="color: var(--primary-color);">var(--primary-color)</span></p>
        <p>Background card: <span style="background: var(--background-card); padding: 0.5rem;">var(--background-card)</span></p>
        <p>Border color: <span style="border: 2px solid var(--border-color); padding: 0.5rem;">var(--border-color)</span></p>
    </div>

    <!-- Test Components -->
    <div class="test-section">
        <h2>Components Test</h2>
        <div class="component-grid">
            <button class="btn btn-primary">Primary Button</button>
            <button class="btn btn-secondary">Secondary Button</button>
            <button class="btn btn-success">Success Button</button>
            <button class="btn btn-warning">Warning Button</button>
        </div>
        
        <div class="component-grid" style="margin-top: 1rem;">
            <div class="card">
                <h4>Test Card</h4>
                <p>This is a test card component.</p>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar" style="width: 75%"></div>
            </div>
        </div>
    </div>

    <!-- Test Animations -->
    <div class="test-section">
        <h2>Animations Test</h2>
        <div class="fade-in">Fade In Animation</div>
        <div class="slide-in-left" style="margin-top: 1rem;">Slide In Left Animation</div>
    </div>

    <!-- Test Dashboard Elements -->
    <div class="test-section">
        <h2>Dashboard Elements Test</h2>
        <div class="launch-grid">
            <div class="launch-app">
                <div class="app-icon">🎮</div>
                <div class="app-name">Test App</div>
            </div>
            <div class="launch-app">
                <div class="app-icon">⚙️</div>
                <div class="app-name">Settings</div>
            </div>
        </div>
    </div>

    <!-- Test Toggle -->
    <div class="test-section">
        <h2>Toggle Component Test</h2>
        <label class="toggle">
            <input type="checkbox" checked>
            <span class="toggle-slider"></span>
            <span class="toggle-label">Test Toggle</span>
        </label>
    </div>

    <!-- Test Form Elements -->
    <div class="test-section">
        <h2>Form Elements Test</h2>
        <div class="form-group">
            <label>Test Input</label>
            <input type="text" placeholder="Enter text here..." value="Test value">
        </div>
        
        <div class="form-group">
            <label>Test Select</label>
            <select>
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
            </select>
        </div>
    </div>

    <div class="test-section">
        <h2>Status</h2>
        <p style="color: var(--success-color);">✅ All modular CSS files are loading correctly!</p>
        <p>If you can see styled components above, the CSS refactoring was successful.</p>
    </div>

    <script>
        // Simple test to verify CSS custom properties are working
        const root = getComputedStyle(document.documentElement);
        const primaryColor = root.getPropertyValue('--primary-color').trim();
        
        if (primaryColor) {
            console.log('✅ CSS Variables loaded successfully');
            console.log('Primary color:', primaryColor);
        } else {
            console.error('❌ CSS Variables not loaded');
        }
        
        // Test if animations are working
        const fadeElements = document.querySelectorAll('.fade-in');
        if (fadeElements.length > 0) {
            console.log('✅ Animation classes found');
        }
        
        // Test if components are styled
        const buttons = document.querySelectorAll('.btn');
        if (buttons.length > 0) {
            console.log('✅ Component classes found');
        }
    </script>
</body>
</html>
