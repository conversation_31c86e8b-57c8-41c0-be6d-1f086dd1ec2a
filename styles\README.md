# ArmoryX Electron - Modular CSS Architecture

This directory contains the modularized CSS files for the ArmoryX Electron application. The original monolithic `styles.css` file has been refactored into smaller, focused modules for better maintainability and organization.

## File Structure

### 1. Foundation Layer
- **`variables.css`** - CSS custom properties, theme configuration, and design tokens
- **`reset.css`** - Global reset styles, base HTML/body styles, and scrollbar customization

### 2. Layout Layer
- **`layout.css`** - App container, header, sidebar, navigation, and main content structure

### 3. Effects Layer
- **`animations.css`** - Keyframe animations, transitions, visual effects, and animation utilities

### 4. Components Layer
- **`components.css`** - Reusable UI components (buttons, cards, forms, toggles, progress bars)

### 5. Feature Layers
- **`dashboard.css`** - Dashboard-specific styles, welcome section, clock, calendar, and widgets
- **`modals.css`** - Modal system, dialogs, file browser, and overlay styles
- **`tools.css`** - Tools interface, system panels, mod management, and cleanup utilities

### 6. Responsive Layer
- **`responsive.css`** - Media queries, responsive design rules, and accessibility features

## Import Order

The CSS files are imported in a specific dependency order in the main `styles.css` file:

```css
/* 1. Foundation */
@import './styles/variables.css';
@import './styles/reset.css';

/* 2. Layout */
@import './styles/layout.css';

/* 3. Effects */
@import './styles/animations.css';

/* 4. Components */
@import './styles/components.css';

/* 5. Features */
@import './styles/dashboard.css';
@import './styles/modals.css';
@import './styles/tools.css';

/* 6. Responsive (must be last) */
@import './styles/responsive.css';
```

## Benefits of Modular Architecture

### 🔧 **Maintainability**
- Easier to locate and modify specific styles
- Reduced cognitive load when working on features
- Clear separation of concerns

### 👥 **Team Collaboration**
- Reduced merge conflicts
- Multiple developers can work on different modules simultaneously
- Clearer code ownership and responsibility

### 🚀 **Performance**
- Potential for selective loading in the future
- Better browser caching strategies
- Smaller file sizes for individual modules

### 📱 **Scalability**
- Easy to add new feature-specific stylesheets
- Consistent naming conventions and structure
- Reusable component patterns

## Development Guidelines

### Adding New Styles

1. **Component Styles**: Add to `components.css` if it's a reusable UI element
2. **Feature Styles**: Create a new file or add to existing feature files
3. **Layout Changes**: Modify `layout.css` for structural changes
4. **Responsive Rules**: Add media queries to `responsive.css`

### Naming Conventions

- Use BEM methodology for class names where appropriate
- Prefix utility classes with descriptive names
- Keep CSS custom properties in `variables.css`
- Use semantic class names that describe purpose, not appearance

### File Organization

Each CSS file should:
- Start with a descriptive comment header
- Group related styles together
- Use consistent indentation and formatting
- Include comments for complex or non-obvious styles

## Migration Notes

The original `styles.css` file has been backed up as `styles.css.backup`. The refactoring process:

1. ✅ Extracted CSS variables and global styles
2. ✅ Separated layout and structural styles
3. ✅ Organized animations and effects
4. ✅ Modularized reusable components
5. ✅ Grouped feature-specific styles
6. ✅ Consolidated responsive design rules
7. ✅ Created new import-based main stylesheet

## Troubleshooting

If styles are not loading correctly:

1. Check that all CSS files exist in the `styles/` directory
2. Verify import paths in the main `styles.css` file
3. Ensure the web server can serve files from the `styles/` directory
4. Check browser developer tools for CSS loading errors

## Future Enhancements

Potential improvements for the CSS architecture:

- **CSS Modules**: Consider CSS modules for component isolation
- **PostCSS**: Add PostCSS for advanced CSS processing
- **CSS-in-JS**: Evaluate CSS-in-JS solutions for dynamic styling
- **Design System**: Expand variables.css into a comprehensive design system
- **Performance**: Implement critical CSS extraction for faster loading
