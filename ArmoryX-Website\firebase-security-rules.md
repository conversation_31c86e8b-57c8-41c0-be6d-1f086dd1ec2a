# Firebase Security Rules for Forum Functionality with Role-Based Moderation

The forum is getting permission errors because your Firebase security rules are too restrictive. Here are the proper security rules to fix the likes and replies functionality AND implement role-based moderation:

## Required Security Rules

Add these rules to your Firebase console under "Firestore Database" > "Rules":

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper function to check if user is moderator or admin
    function isModerator() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['moderator', 'admin'];
    }
    
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Users collection - users can read/write their own data, moderators can read all, admins can edit roles
    match /users/{userId} {
      allow read: if true; // Allow reading user profiles (needed for display names)
      allow write: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && 
                    (request.auth.uid == userId || 
                     (isModerator() && !('role' in request.resource.data)) ||
                     (isAdmin() && 'role' in request.resource.data));
    }
    
    // Forum posts - authenticated users can create/read posts, authors + moderators can edit/delete
    match /forum_posts/{postId} {
      allow read: if true; // Allow anyone to read posts
      allow create: if request.auth != null && request.auth.uid == request.resource.data.authorId;
      allow update: if request.auth != null && 
                    (request.auth.uid == resource.data.authorId || isModerator());
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.authorId || isModerator());
    }
    
    // Forum replies - authenticated users can create/read replies, authors + moderators can edit/delete
    match /forum_replies/{replyId} {
      allow read: if true; // Allow anyone to read replies
      allow create: if request.auth != null && request.auth.uid == request.resource.data.authorId;
      allow update: if request.auth != null && 
                    (request.auth.uid == resource.data.authorId || isModerator());
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.authorId || isModerator());
    }
    
    // Notifications - users can read/write their own notifications
    match /notifications/{notificationId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow create: if request.auth != null;
      allow update: if request.auth != null && request.auth.uid == resource.data.userId;
      allow delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Post likes - authenticated users can like/unlike posts
    match /post_likes/{likeId} {
      allow read: if true; // Allow reading like status
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.userId || isModerator());
    }
    
    // Reply likes - authenticated users can like/unlike replies
    match /reply_likes/{likeId} {
      allow read: if true; // Allow reading like status
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.userId || isModerator());
    }
    
    // Post dislikes - authenticated users can dislike/un-dislike posts
    match /post_dislikes/{dislikeId} {
      allow read: if true; // Allow reading dislike status
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.userId || isModerator());
    }
    
    // Reply dislikes - authenticated users can dislike/un-dislike replies
    match /reply_dislikes/{dislikeId} {
      allow read: if true; // Allow reading dislike status
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.userId || isModerator());
    }
    
    // Forum interactions - for tracking user activity
    match /forum_interactions/{interactionId} {
      allow read: if isModerator();
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Moderation logs - only moderators can read/write
    match /moderation_logs/{logId} {
      allow read, write: if isModerator();
    }
    
    // User bans - only moderators can read/write
    match /user_bans/{banId} {
      allow read, write: if isModerator();
    }
    
    // Reported content - authenticated users can report, moderators can manage
    match /reports/{reportId} {
      allow read, write: if isModerator();
      allow create: if request.auth != null && request.auth.uid == request.resource.data.reporterId;
    }
    
    // Email verification tracking - users can read/write their own verification status
    match /email_verification/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // User sessions - for tracking active users
    match /user_sessions/{sessionId} {
      allow read: if true; // Allow reading for user counts
      allow write: if request.auth != null;
    }
    
    // ===== LICENSE SYSTEM SECURITY RULES =====
    
    // License keys - admins can create/manage, users can activate unused keys
    match /license_keys/{keyId} {
      allow read: if request.auth != null && 
                  (isAdmin() || 
                   resource.data.status == 'unused' ||  // Allow reading unused keys for activation
                   (resource.data.userId == request.auth.uid && resource.data.status == 'active'));
      allow create: if isAdmin() && request.auth.uid == request.resource.data.createdBy;
      allow update: if request.auth != null && 
                    (isAdmin() || 
                     // Allow users to activate unused keys - specific activation permission
                     (resource.data.status == 'unused' && 
                      request.resource.data.userId == request.auth.uid &&
                      request.resource.data.status == 'active') ||
                     // Allow users to revalidate their own active keys  
                     (resource.data.userId == request.auth.uid && 
                      resource.data.status == 'active' &&
                      request.resource.data.status == 'active'));
      allow delete: if isAdmin();
    }
    
    // User licenses - users can read/write their own license info
    match /user_licenses/{userId} {
      allow read: if request.auth != null && 
                  (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null && 
                   (request.auth.uid == userId || isAdmin());
    }
    
    // Key generation log - only admins can read/write
    match /key_generation_log/{logId} {
      allow read, write: if isAdmin();
    }
    
    // Key validation log - system generated, admins can read
    match /key_validation_log/{logId} {
      allow read: if isAdmin();
      allow create: if request.auth != null; // Allow system to log validation attempts
    }
    
    // ===== MESSAGING SYSTEM SECURITY RULES =====
    
    // Conversations - users can read/write conversations they participate in
    match /conversations/{conversationId} {
      allow read: if request.auth != null 
        && request.auth.uid in resource.data.participants;
      allow write: if request.auth != null 
        && request.auth.uid in resource.data.participants;
      allow create: if request.auth != null 
        && request.auth.uid in request.resource.data.participants
        && request.resource.data.participants.size() == 2;
      allow update: if request.auth != null 
        && request.auth.uid in resource.data.participants;
    }
    
    // Messages - users can read/send messages in conversations they participate in
    match /messages/{messageId} {
      allow read: if request.auth != null 
        && exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId))
        && request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants;
      
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.senderId
        && exists(/databases/$(database)/documents/conversations/$(request.resource.data.conversationId))
        && request.auth.uid in get(/databases/$(database)/documents/conversations/$(request.resource.data.conversationId)).data.participants;
        
      allow update: if request.auth != null 
        && exists(/databases/$(database)/documents/conversations/$(resource.data.conversationId))
        && request.auth.uid in get(/databases/$(database)/documents/conversations/$(resource.data.conversationId)).data.participants
        && (request.resource.data.keys().hasOnly(['read']) || request.auth.uid == resource.data.senderId);
        
      allow delete: if request.auth != null && 
                    (request.auth.uid == resource.data.senderId || isModerator());
    }
    
    // ===== FRIENDS SYSTEM SECURITY RULES =====
    
    // Friend relationships - users can manage their own friendships
    match /friendships/{friendshipId} {
      allow read: if request.auth != null &&
                  (request.auth.uid in resource.data.users);
      allow create: if request.auth != null &&
                    request.auth.uid in request.resource.data.users &&
                    request.resource.data.users.size() == 2;
      allow update: if request.auth != null &&
                    request.auth.uid in resource.data.users;
      allow delete: if request.auth != null &&
                    (request.auth.uid in resource.data.users || isModerator());
    }
    
    // Friend requests - users can send/receive friend requests
    match /friend_requests/{requestId} {
      allow read: if request.auth != null &&
                  (request.auth.uid == resource.data.fromUserId || 
                   request.auth.uid == resource.data.toUserId);
      allow create: if request.auth != null &&
                    request.auth.uid == request.resource.data.fromUserId;
      allow update: if request.auth != null &&
                    request.auth.uid == resource.data.toUserId;
      allow delete: if request.auth != null &&
                    (request.auth.uid == resource.data.fromUserId || 
                     request.auth.uid == resource.data.toUserId ||
                     isModerator());
    }
  }
}
```

## Role-Based Permissions

### **User Roles:**
- **user** (default): Can create/edit own posts, like/dislike, reply
- **moderator**: All user permissions + can edit/delete any posts/replies, ban users, view reports
- **admin**: All moderator permissions + can assign moderator roles, manage other admins

### **Moderation Capabilities:**
- Edit/Delete any post or reply (even if not the author)
- Ban/Unban users (prevents them from posting)
- View and manage reported content
- Access moderation logs and user analytics
- Assign moderator roles (admin only)

## How to Apply These Rules

1. Go to your Firebase Console
2. Select your project
3. Click on "Firestore Database" in the left sidebar
4. Click on the "Rules" tab
5. Replace the existing rules with the rules above
6. Click "Publish"

## Setting Up Your Admin Account

After applying the security rules, you'll need to manually set your account as an admin:

### **Method 1: Through Firebase Console (Recommended)**
1. Go to Firebase Console → Firestore Database
2. Find the `users` collection
3. Locate your user document (using your user ID)
4. Edit the document and add a field:
   - Field: `role`
   - Type: `string`
   - Value: `admin`

### **Method 2: Through the Website (After implementing the code)**
1. Log in to your account
2. Use the browser console to run:
   ```javascript
   // This will be implemented in the next steps
   window.assignAdminRole();
   ```

## What These Rules Do

- **Enhanced Security**: Proper role-based access control
- **Moderation Powers**: Moderators can edit/delete any content
- **User Management**: Admins can assign roles, moderators can ban users
- **Audit Trail**: All moderation actions are logged
- **Reporting System**: Users can report content, moderators can review

## Collections Created

The system will create these new collections:

### Forum & Moderation:
- `moderation_logs` - Track all moderation actions
- `user_bans` - Store banned user information
- `reports` - Store user reports about content

### License System:
- `license_keys` - Store all generated license keys and their status
- `user_licenses` - Track user license information and hardware binding
- `key_generation_log` - Log all key generation activities (admin only)
- `key_validation_log` - Log all license validation attempts for security monitoring

### Messaging System:
- `conversations` - Store conversation metadata and participant lists
- `messages` - Store individual messages within conversations

### Friends System:
- `friendships` - Store confirmed friend relationships between users
- `friend_requests` - Store pending friend requests

After applying these rules, you'll have a full role-based moderation system, comprehensive license management system, secure messaging, AND friends functionality! 