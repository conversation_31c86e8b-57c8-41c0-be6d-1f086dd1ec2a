// Example: Electron Auto-Updater with GitHub Releases
const { app, BrowserWindow, dialog } = require('electron');
const { autoUpdater } = require('electron-updater');

// Configure auto-updater
autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'your-github-username',
  repo: 'armory-x',
  private: false // Set to true for private repos
});

// Auto-updater events
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for updates...');
});

autoUpdater.on('update-available', (info) => {
  dialog.showMessageBox({
    type: 'info',
    title: 'Update Available',
    message: `Version ${info.version} is available. It will be downloaded in the background.`,
    buttons: ['OK']
  });
});

autoUpdater.on('update-not-available', () => {
  console.log('No updates available');
});

autoUpdater.on('error', (err) => {
  dialog.showErrorBox('Update Error', err.toString());
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = `Download speed: ${progressObj.bytesPerSecond}`;
  log_message = log_message + ` - Downloaded ${progressObj.percent}%`;
  log_message = log_message + ` (${progressObj.transferred}/${progressObj.total})`;
  console.log(log_message);
  
  // Send progress to renderer
  BrowserWindow.getFocusedWindow()?.webContents.send('download-progress', progressObj);
});

autoUpdater.on('update-downloaded', (info) => {
  dialog.showMessageBox({
    type: 'info',
    title: 'Update Ready',
    message: 'Update downloaded. The application will restart to apply the update.',
    buttons: ['Restart Now', 'Later']
  }).then((result) => {
    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  });
});

// Check for updates on app ready
app.whenReady().then(() => {
  // Check for updates after a delay
  setTimeout(() => {
    autoUpdater.checkForUpdatesAndNotify();
  }, 3000);
  
  // Check for updates every hour
  setInterval(() => {
    autoUpdater.checkForUpdates();
  }, 60 * 60 * 1000);
});

// Manual update check (can be triggered from menu)
function checkForUpdates() {
  autoUpdater.checkForUpdatesAndNotify();
}

module.exports = { checkForUpdates }; 