/**
 * Mod Manager module for Armory X
 * Extracted from main.js for better maintainability
 * Handles all mod-related operations including scanning, categorization, installation, and management
 */

const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { shell } = require('electron');
const chokidar = require('chokidar');

class ModManager {
  constructor(dependencies = {}) {
    this.systemInfo = dependencies.systemInfo;
    this.modSetsDir = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'ArmoryX_ModSets');
    this.modSetsConfigPath = path.join(this.modSetsDir, 'modsets_config.json');
    this.minecraftDir = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
    this.fileWatchers = [];
    this.autoSaveTimeout = null;
    this.watchingDirectories = false;
  }

  /**
   * Set system info instance
   * @param {SystemInfo} systemInfo - SystemInfo instance
   */
  setSystemInfo(systemInfo) {
    this.systemInfo = systemInfo;
  }

  /**
   * Get mod category based on file name and game type
   * @param {string} fileName - Mod file name
   * @param {string} gameId - Game identifier
   * @param {string} filePath - Full file path (optional)
   * @returns {string} Mod category
   */
  getModCategory(fileName, gameId, filePath = '') {
    const ext = path.extname(fileName).toLowerCase();
    const name = fileName.toLowerCase();
    
    if (gameId === 'minecraft') {
      // Check folder-based categorization first (more reliable)
      if (filePath.includes('shaderpacks')) {
        return 'Shaders';
      }
      if (filePath.includes('resourcepacks')) {
        return 'Resource Packs';
      }
      
      // Continue with simplified name-based detection for mods folder
      
      // Shaders
      if (name.includes('shader') || name.includes('seus') || name.includes('bsl') ||
          name.includes('complementary') || name.includes('sildurs') || name.includes('chocapic') ||
          name.includes('kuda') || name.includes('continuum')) {
        return 'Shaders';
      }
      
      // Resource Packs
      if (name.includes('texture') || name.includes('resource') || name.includes('pack') || 
          name.includes('faithful') || name.includes('sphax') || name.includes('dokucraft') ||
          name.includes('graphics') || name.includes('visual')) {
        return 'Resource Packs';
      }
      
      // Storage & Organization
      if (name.includes('jei') || name.includes('nei') || name.includes('inventory') || 
          name.includes('storage') || name.includes('chest') || name.includes('backpack') ||
          name.includes('bag') || name.includes('drawer') || name.includes('cabinet') ||
          name.includes('sorting') || name.includes('refined') || name.includes('ae2') ||
          name.includes('applied') || name.includes('energistics') || name.includes('organize')) {
        return 'Storage & Organization';
      }
      
      // Default to Mods for everything else
      return 'Mods';
    }
    
    // For other games, return simple category
    return 'Mods';
  }

  /**
   * Get all available drives (wrapper to system info)
   * @returns {Array<string>} Array of drive letters
   */
  async getAllDrives() {
    if (this.systemInfo) {
      return this.systemInfo.getAllDrives();
    }
    // Fallback if systemInfo not available
    return ['C'];
  }

  /**
   * Get mods for a specific game
   * @param {Object} event - IPC event object
   * @param {string} gameId - Game identifier
   * @returns {Object} Mods result object
   */
  async getModsForGame(event, gameId) {
    console.log(`📦 Getting mods for game: ${gameId}`);
    
    // Check if this is a custom game
    if (gameId && gameId.startsWith('custom_')) {
      console.log(`🎮 Processing custom game: ${gameId}`);
      
      // Get custom game data from renderer
      const customGameData = await event.sender.executeJavaScript(`
        customGames.find(game => game.id === '${gameId}')
      `).catch(() => null);
      
      if (!customGameData || !customGameData.modFolders) {
        console.log(`❌ No custom game data or mod folders found for ${gameId}`);
        return { 
          mods: [], 
          total: 0,
          path: null,
          isDemo: true,
          checkedPaths: []
        };
      }
      
      console.log(`🔍 Found custom game: ${customGameData.name} with ${customGameData.modFolders.length} mod folders`);
      
      // Get supported file types for this custom game
      const supportedFileTypes = customGameData.supportedFileTypes || ['.zip', '.rar', '.7z'];
      console.log(`📋 Supported file types: ${supportedFileTypes.join(', ')}`);
      
      let foundMods = [];
      let usedPath = null;
      const checkedPaths = [];
      
      // Check each mod folder defined for this custom game
      for (const modFolder of customGameData.modFolders) {
        const modPath = modFolder.path;
        checkedPaths.push(modPath);
        console.log(`   Checking custom mod folder: ${modPath}`);
        
        try {
          if (await fs.pathExists(modPath)) {
            console.log(`   ✅ Path exists: ${modPath}`);
            
            const files = await fs.readdir(modPath);
            const mods = [];
            
            for (const file of files) {
              const filePath = path.join(modPath, file);
              const stats = await fs.stat(filePath);
              
              if (stats.isFile()) {
                const ext = path.extname(file).toLowerCase();
                
                if (supportedFileTypes.includes(ext)) {
                  mods.push({
                    name: file,
                    path: filePath,
                    size: stats.size,
                    modified: stats.mtime,
                    category: modFolder.name || 'Mods'
                  });
                }
              }
            }
            
            if (mods.length > 0) {
              console.log(`   🎯 Found ${mods.length} mods in: ${modPath}`);
              foundMods.push(...mods);
              if (!usedPath) usedPath = modPath; // Use first path with mods as primary
            } else {
              console.log(`   📁 Path exists but no valid mod files found: ${modPath}`);
            }
          } else {
            console.log(`   ❌ Path does not exist: ${modPath}`);
          }
        } catch (error) {
          console.log(`   ⚠️ Error checking path ${modPath}:`, error.message);
        }
      }
      
      if (foundMods.length > 0) {
        console.log(`✅ Successfully found ${foundMods.length} mods for custom game ${customGameData.name}`);
        return { 
          mods: foundMods, 
          total: foundMods.length,
          path: usedPath,
          isDemo: false
        };
      } else {
        console.log(`❌ No mods found for custom game ${customGameData.name}`);
        return { 
          mods: [], 
          total: 0,
          path: null,
          isDemo: true,
          checkedPaths: checkedPaths
        };
      }
    }
    
    // Handle preset games (existing code)
    // Get all available drives for Schedule 1
    const availableDrives = await this.getAllDrives();
    
    // Generate Schedule 1 paths for all drives (Steam-only locations)
    const schedule1Paths = [];
    for (const drive of availableDrives) {
      schedule1Paths.push(
        // Steam library paths only (where Schedule I is actually installed)
        path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
      );
    }
    
    // Special handling for Minecraft - scan multiple folders separately
    if (gameId === 'minecraft') {
      console.log(`🎮 Scanning Minecraft folders separately for comprehensive mod detection`);
      
      const minecraftFolders = [
        { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'), name: 'Mods' },
        { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'shaderpacks'), name: 'Shaders' },
        { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'resourcepacks'), name: 'Resource Packs' }
      ];
      
      let allMinecraftMods = [];
      let foundAnyMods = false;
      let usedPaths = [];
      
      for (const folder of minecraftFolders) {
        console.log(`   🔍 Checking ${folder.name}: ${folder.path}`);
        
        try {
          if (await fs.pathExists(folder.path)) {
            console.log(`   ✅ ${folder.name} folder exists`);
            
            const files = await fs.readdir(folder.path);
            const mods = [];
            
            for (const file of files) {
              const filePath = path.join(folder.path, file);
              const stats = await fs.stat(filePath);
              
              if (stats.isFile()) {
                const ext = path.extname(file).toLowerCase();
                const validExtensions = ['.jar', '.zip', '.rar', '.7z'];
                
                if (validExtensions.includes(ext)) {
                  mods.push({
                    name: file,
                    path: filePath,
                    size: stats.size,
                    modified: stats.mtime,
                    category: this.getModCategory(file, gameId, filePath)
                  });
                }
              }
            }
            
            if (mods.length > 0) {
              console.log(`   🎯 Found ${mods.length} items in ${folder.name}: ${folder.path}`);
              allMinecraftMods = allMinecraftMods.concat(mods);
              foundAnyMods = true;
              usedPaths.push(folder.path);
            } else {
              console.log(`   📁 ${folder.name} folder exists but no valid files found`);
            }
          } else {
            console.log(`   ❌ ${folder.name} folder does not exist: ${folder.path}`);
          }
        } catch (error) {
          console.log(`   ⚠️ Error checking ${folder.name} folder:`, error.message);
        }
      }
      
      if (foundAnyMods) {
        console.log(`✅ Successfully found ${allMinecraftMods.length} total Minecraft items across ${usedPaths.length} folders`);
        return { 
          mods: allMinecraftMods, 
          total: allMinecraftMods.length,
          path: usedPaths.join('; '),
          isDemo: false
        };
      } else {
        console.log(`❌ No Minecraft mods found in any folder`);
        return { 
          mods: [], 
          total: 0,
          path: null,
          isDemo: true,
          checkedPaths: minecraftFolders.map(f => f.path)
        };
      }
    }

    // Handle other games with original logic
    const gameModPaths = {
      fs22: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join(os.homedir(), 'OneDrive', 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join('C:', 'Users', os.userInfo().username, 'Documents', 'My Games', 'FarmingSimulator2022', 'mods')
      ],
      fs25: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
        path.join(os.homedir(), 'OneDrive', 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
        path.join('C:', 'Users', os.userInfo().username, 'Documents', 'My Games', 'FarmingSimulator2025', 'mods')
      ],
      schedule1: schedule1Paths
    };
    
    const possiblePaths = gameModPaths[gameId] || [];
    console.log(`🔍 Checking ${possiblePaths.length} possible paths for ${gameId}:`);
    
    let foundMods = [];
    let usedPath = null;
    
    // Check each possible path
    for (const modPath of possiblePaths) {
      console.log(`   Checking: ${modPath}`);
      
      try {
        if (await fs.pathExists(modPath)) {
          console.log(`   ✅ Path exists: ${modPath}`);
          
          const files = await fs.readdir(modPath);
          const mods = [];
          
          for (const file of files) {
            const filePath = path.join(modPath, file);
            const stats = await fs.stat(filePath);
            
            if (stats.isFile()) {
              // Filter for common mod file extensions based on game
              const ext = path.extname(file).toLowerCase();
              let validExtensions = [];
              
              switch(gameId) {
                case 'fs22':
                  validExtensions = ['.zip', '.rar', '.7z', '.mod'];
                  break;
                case 'schedule1':
                  validExtensions = ['.dll', '.zip', '.rar', '.7z', '.mod'];
                  break;
                default:
                  validExtensions = ['.jar', '.zip', '.rar', '.7z', '.mod', '.dll'];
              }
              
              if (validExtensions.includes(ext)) {
                mods.push({
                  name: file,
                  path: filePath,
                  size: stats.size,
                  modified: stats.mtime,
                  category: this.getModCategory(file, gameId, filePath)
                });
              }
            }
          }
          
          if (mods.length > 0) {
            console.log(`   🎯 Found ${mods.length} mods in: ${modPath}`);
            foundMods = mods;
            usedPath = modPath;
            break; // Use the first path that has mods
          } else {
            console.log(`   📁 Path exists but no valid mod files found: ${modPath}`);
          }
        } else {
          console.log(`   ❌ Path does not exist: ${modPath}`);
        }
      } catch (error) {
        console.log(`   ⚠️ Error checking path ${modPath}:`, error.message);
      }
    }
    
    if (foundMods.length > 0) {
      console.log(`✅ Successfully found ${foundMods.length} mods for ${gameId} in: ${usedPath}`);
      return { 
        mods: foundMods, 
        total: foundMods.length,
        path: usedPath,
        isDemo: false
      };
    } else {
      console.log(`❌ No mods found for ${gameId} in any of the checked paths`);
      return { 
        mods: [], 
        total: 0,
        path: null,
        isDemo: true,
        checkedPaths: possiblePaths
      };
    }
  }

  /**
   * Open mod folder for a specific game
   * @param {Object} event - IPC event object
   * @param {string} gameId - Game identifier
   * @returns {Object} Operation result
   */
  async openModFolder(event, gameId) {
    console.log(`📁 Opening mod folder for: ${gameId}`);
    
    // Check if this is a custom game
    if (gameId && gameId.startsWith('custom_')) {
      console.log(`🎮 Opening mod folder for custom game: ${gameId}`);
      
      // Get custom game data from renderer
      const customGameData = await event.sender.executeJavaScript(`
        customGames.find(game => game.id === '${gameId}')
      `).catch(() => null);
      
      if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
        console.log(`❌ No custom game data or mod folders found for ${gameId}`);
        return { success: false, message: 'No mod folders configured for this custom game' };
      }
      
      // Try to open the first available mod folder
      for (const modFolder of customGameData.modFolders) {
        const modPath = modFolder.path;
        console.log(`   Trying to open: ${modPath}`);
        
        try {
          if (await fs.pathExists(modPath)) {
            shell.openPath(modPath);
            console.log(`✅ Opened custom game mod folder: ${modPath}`);
            return { success: true, path: modPath };
          }
        } catch (error) {
          console.log(`❌ Error opening path ${modPath}:`, error.message);
        }
      }
      
      // If no folder exists, try to create the first one
      const primaryModFolder = customGameData.modFolders[0];
      if (primaryModFolder) {
        try {
          await fs.ensureDir(primaryModFolder.path);
          shell.openPath(primaryModFolder.path);
          console.log(`✅ Created and opened custom game mod folder: ${primaryModFolder.path}`);
          return { success: true, path: primaryModFolder.path, created: true };
        } catch (error) {
          console.log(`❌ Error creating folder ${primaryModFolder.path}:`, error.message);
          return { success: false, message: `Failed to create mod folder: ${error.message}` };
        }
      }
      
      return { success: false, message: 'No valid mod folder paths found for this custom game' };
    }
    
    // Handle preset games with proper path detection
    let possiblePaths = [];
    
    if (gameId === 'schedule1') {
      // For Schedule I, use the same logic as scanning and installation
      const availableDrives = await this.getAllDrives();
      
      // Generate Schedule 1 paths for all drives (Steam locations)
      for (const drive of availableDrives) {
        possiblePaths.push(
          path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
        );
      }
      
      // Add fallback to Documents (but it should be last priority)
      possiblePaths.push(
        path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
        path.join(os.homedir(), 'Documents', 'Schedule 1')
      );
    } else {
          // Handle other preset games
    const gameModPaths = {
      minecraft: [
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft')
      ],
      fs22: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022')
      ],
      fs25: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025')
      ]
    };
      
      possiblePaths = gameModPaths[gameId] || [];
    }
    
    // Try to find an existing path
    for (const modPath of possiblePaths) {
      try {
        if (await fs.pathExists(modPath)) {
          shell.openPath(modPath);
          console.log(`✅ Opened folder: ${modPath}`);
          return { success: true, path: modPath };
        }
      } catch (error) {
        console.log(`❌ Error opening path ${modPath}:`, error.message);
      }
    }
    
    // If no path exists, create the primary mod directory
    const primaryPath = possiblePaths[0];
    if (primaryPath) {
      try {
        await fs.ensureDir(primaryPath);
        shell.openPath(primaryPath);
        console.log(`✅ Created and opened folder: ${primaryPath}`);
        return { success: true, path: primaryPath, created: true };
      } catch (error) {
        console.log(`❌ Error creating folder ${primaryPath}:`, error.message);
        return { success: false, message: `Failed to create mod folder: ${error.message}` };
      }
    }
    
    return { success: false, message: 'No valid mod paths configured for this game' };
  }

  /**
   * Get mod directories information
   * @param {Object} event - IPC event object
   * @param {string} gameId - Game identifier (optional)
   * @returns {Object} Mod directories info
   */
  async getModDirectories(event, gameId) {
    const gameModPaths = {
      minecraft: [
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
        path.join(os.homedir(), 'curseforge', 'minecraft', 'Instances'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'PrismLauncher', 'instances')
      ],
      fs22: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods')
      ],
      schedule1: [
        path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
        path.join(os.homedir(), 'AppData', 'LocalLow', 'Schedule 1', 'mods')
      ]
    };
    
    const result = {};
    
    if (gameId) {
      // Get info for specific game
      const paths = gameModPaths[gameId] || [];
      result[gameId] = {
        paths: paths,
        existing: []
      };
      
      for (const modPath of paths) {
        try {
          if (await fs.pathExists(modPath)) {
            result[gameId].existing.push(modPath);
          }
        } catch (error) {
          console.log(`Error checking path ${modPath}:`, error.message);
        }
      }
    } else {
      // Get info for all games
      for (const [game, paths] of Object.entries(gameModPaths)) {
        result[game] = {
          paths: paths,
          existing: []
        };
        
        for (const modPath of paths) {
          try {
            if (await fs.pathExists(modPath)) {
              result[game].existing.push(modPath);
            }
          } catch (error) {
            console.log(`Error checking path ${modPath}:`, error.message);
          }
        }
      }
    }
    
    return result;
  }

  /**
   * Create test mods for development/testing
   * @param {Object} event - IPC event object
   * @param {string} gameId - Game identifier
   * @returns {Object} Creation result
   */
  async createTestMods(event, gameId) {
    console.log(`🧪 Creating test mods for: ${gameId}`);
    
    const gameModPaths = {
      minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      fs25: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
      schedule1: path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods')
    };
    
    const modPath = gameModPaths[gameId];
    if (!modPath) {
      return { success: false, message: 'Invalid game ID' };
    }
    
    try {
      // Ensure the directory exists
      await fs.ensureDir(modPath);
      
      const testMods = {
        minecraft: [
          { name: 'TestMod-OptiFine-HD.jar', size: 2500000 },
          { name: 'JustEnoughItems-1.20.1.jar', size: 1800000 },
          { name: 'BiomesOPlenty-1.20.1.jar', size: 3200000 },
          { name: 'Waila-1.8.26.jar', size: 450000 },
          { name: 'IronChests-1.20.1.jar', size: 890000 }
        ],
        fs22: [
          { name: 'TestTractor-JohnDeere8R.zip', size: 15000000 },
          { name: 'FS22_RealisticVehicles.zip', size: 8500000 },
          { name: 'FS22_SeasonsMod.zip', size: 45000000 }
        ],
        schedule1: [
          { name: 'EnhancedChemistry.zip', size: 25600000 },
          { name: 'BusinessExpansion.zip', size: 18900000 },
          { name: 'SecurityUpgrade.zip', size: 31200000 }
        ]
      };
      
      const modsToCreate = testMods[gameId] || [];
      let createdCount = 0;
      
      for (const mod of modsToCreate) {
        const filePath = path.join(modPath, mod.name);
        
        // Check if file already exists
        if (await fs.pathExists(filePath)) {
          console.log(`   Skipping existing file: ${mod.name}`);
          continue;
        }
        
        // Create a dummy file with some content
        const content = `# Test Mod File: ${mod.name}\n` +
                       `# Created by Armory X for testing purposes\n` +
                       `# Game: ${gameId}\n` +
                       `# Size: ${mod.size} bytes\n` +
                       `# Created: ${new Date().toISOString()}\n\n` +
                       `This is a test mod file created for testing the mod manager functionality.\n` +
                       `${'='.repeat(Math.floor(mod.size / 100))}`;
        
        await fs.writeFile(filePath, content);
        console.log(`   ✅ Created test mod: ${mod.name}`);
        createdCount++;
      }
      
      console.log(`✅ Created ${createdCount} test mods in: ${modPath}`);
      
      return {
        success: true,
        message: `Created ${createdCount} test mods`,
        createdCount,
        skipped: modsToCreate.length - createdCount,
        path: modPath
      };
      
    } catch (error) {
      console.error(`❌ Error creating test mods:`, error);
      return {
        success: false,
        message: `Failed to create test mods: ${error.message}`
      };
    }
  }

  /**
   * Delete a mod file
   * @param {Object} event - IPC event object
   * @param {string} modPath - Path to mod file
   * @returns {Object} Deletion result
   */
  async deleteMod(event, modPath) {
    try {
      console.log(`🗑️ Deleting mod: ${modPath}`);
      
      // Check if file exists
      if (await fs.pathExists(modPath)) {
        // Move to trash/recycle bin instead of permanent deletion
        await shell.trashItem(modPath);
        console.log(`✅ Mod deleted successfully: ${modPath}`);
        
        // Auto-save current mod set after deletion
        await this.autoSaveCurrentModSet(`Deleted mod: ${path.basename(modPath)}`);
        
        return { success: true, message: 'Mod deleted successfully' };
      } else {
        console.log(`❌ Mod file not found: ${modPath}`);
        return { success: false, message: 'Mod file not found' };
      }
    } catch (error) {
      console.error(`❌ Error deleting mod: ${error.message}`);
      return { success: false, message: `Failed to delete mod: ${error.message}` };
    }
  }

  /**
   * Get game mod path for installation
   * @param {string} gameId - Game identifier
   * @param {string} category - Mod category
   * @param {Object} event - IPC event object for custom games
   * @returns {string|null} Mod installation path
   */
  async getGameModPath(gameId, category, event = null) {
    // Check if it's a custom game
    if (gameId && gameId.startsWith('custom_')) {
      console.log(`🎮 Installing mods for custom game: ${gameId}`);
      
      if (!event) {
        throw new Error('Event object required for custom games');
      }
      
      // Get custom game data from renderer
      const customGameData = await event.sender.executeJavaScript(`
        customGames.find(game => game.id === '${gameId}')
      `).catch(() => null);
      
      if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
        throw new Error('No mod folders configured for this custom game');
      }
      
      // For custom games, use the first mod folder or find a matching one
      const targetFolder = customGameData.modFolders.find(folder => 
        folder.name.toLowerCase() === category.toLowerCase()
      ) || customGameData.modFolders[0];
      
      return targetFolder.path;
    }
    
    // Handle preset games - FOR SCHEDULE I, REUSE THE SCANNING LOGIC!
    if (gameId === 'schedule1') {
      // Get all available drives for Schedule 1 (same as scanning)
      const availableDrives = await this.getAllDrives();
      
      // Generate Schedule 1 paths for all drives (Steam-only locations)
      const schedule1Paths = [];
      for (const drive of availableDrives) {
        schedule1Paths.push(
          path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
        );
      }
      
      // Check each path and use the first one that exists (same as scanning)
      for (const modPath of schedule1Paths) {
        try {
          if (await fs.pathExists(modPath)) {
            console.log(`🎯 Using Schedule I mod path: ${modPath}`);
            return modPath;
          }
        } catch (error) {
          console.log(`❌ Error checking Schedule I path ${modPath}:`, error.message);
        }
      }
      
      // Fallback to documents if not found
      const fallbackPath = path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods');
      console.log(`⚠️ Schedule I not found in Steam locations, using fallback: ${fallbackPath}`);
      return fallbackPath;
    }
    
    // Handle other preset games
    const gameModPaths = {
      minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      fs25: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods')
    };
    
    // For Minecraft, organize by simplified category
    if (gameId === 'minecraft' && category) {
      const minecraftRoot = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
      
      if (category === 'Shaders') {
        return path.join(minecraftRoot, 'shaderpacks');
      } else if (category === 'Resource Packs') {
        return path.join(minecraftRoot, 'resourcepacks');
      }
      // For 'Mods' category or any other, use regular mods folder
    }
    
    return gameModPaths[gameId];
  }

  /**
   * Install mods with individual categories
   * @param {Object} event - IPC event object
   * @param {Object} data - Installation data
   * @returns {Object} Installation result
   */
  async installModsWithCategories(event, data) {
    console.log(`📦 Installing mods with categories for game: ${data.gameId}`);
    console.log(`   Files to install: ${data.modFileData.length}`);
    
    try {
      let installedCount = 0;
      let skippedCount = 0;
      const results = [];
      const targetPaths = new Set(); // Track unique paths used
      
      for (const modData of data.modFileData) {
        const { filePath, category, fileName } = modData;
        console.log(`   📁 Processing: ${fileName} → ${category}`);
        
        // Get target directory for this file's category
        const targetDir = await this.getGameModPath(data.gameId, category, event);
        
        if (!targetDir) {
          console.log(`   ❌ No target directory found for ${fileName}`);
          results.push({ name: fileName, status: 'failed', reason: 'No target directory configured' });
          continue;
        }
        
        // Ensure target directory exists
        await fs.ensureDir(targetDir);
        targetPaths.add(targetDir);
        console.log(`   📁 Target directory for ${fileName}: ${targetDir}`);
        
        const targetPath = path.join(targetDir, fileName);
        
        try {
          // Check if source file exists
          if (await fs.pathExists(filePath)) {
            // Check if target already exists
            if (await fs.pathExists(targetPath)) {
              console.log(`   ⚠️ Skipping existing file: ${fileName}`);
              skippedCount++;
              results.push({ name: fileName, status: 'skipped', reason: 'File already exists' });
              continue;
            }
            
            // Copy the file
            await fs.copy(filePath, targetPath);
            console.log(`   ✅ Installed: ${fileName} → ${category}`);
            installedCount++;
            results.push({ name: fileName, status: 'installed', category: category });
            
          } else {
            console.log(`   ❌ Source file not found: ${filePath}`);
            results.push({ name: fileName, status: 'failed', reason: 'Source file not found' });
          }
        } catch (fileError) {
          console.log(`   ❌ Error installing ${fileName}: ${fileError.message}`);
          results.push({ name: fileName, status: 'failed', reason: fileError.message });
        }
      }
      
      console.log(`✅ Mod installation complete: ${installedCount} installed, ${skippedCount} skipped`);
      
      // Auto-save current mod set if changes were made
      if (installedCount > 0) {
        await this.autoSaveCurrentModSet(`Installed ${installedCount} mod(s) with categories`);
      }
      
      return {
        success: true,
        message: `Installed ${installedCount} mod${installedCount !== 1 ? 's' : ''}${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
        installedMods: installedCount,
        skippedMods: skippedCount,
        results: results,
        targetPaths: Array.from(targetPaths)
      };
      
    } catch (error) {
      console.error(`❌ Error installing mods with categories:`, error);
      return {
        success: false,
        message: `Failed to install mods: ${error.message}`
      };
    }
  }

  /**
   * Install mods (legacy - keeping for compatibility)
   * @param {Object} event - IPC event object
   * @param {Object} data - Installation data
   * @returns {Object} Installation result
   */
  async installMods(event, data) {
    console.log(`📦 Installing mods for game: ${data.gameId}`);
    console.log(`   Files to install: ${data.filePaths.length}`);
    console.log(`   Target category: ${data.category}`);
    
    try {
      const baseTargetDir = await this.getGameModPath(data.gameId, data.category, event);
      
      if (baseTargetDir === undefined) {
        return { success: false, message: 'Invalid game ID or no mod path configured' };
      }
      
      let installedCount = 0;
      let skippedCount = 0;
      const results = [];
      
      for (const filePath of data.filePaths) {
        const fileName = path.basename(filePath);
        
        // Use the target directory from getGameModPath
        const targetDir = baseTargetDir;
        
        // Ensure target directory exists
        await fs.ensureDir(targetDir);
        console.log(`📁 Target directory for ${fileName}: ${targetDir}`);
        
        const targetPath = path.join(targetDir, fileName);
        
        try {
          // Check if source file exists
          if (await fs.pathExists(filePath)) {
            // Check if target already exists
            if (await fs.pathExists(targetPath)) {
              console.log(`   ⚠️ Skipping existing file: ${fileName}`);
              skippedCount++;
              results.push({ name: fileName, status: 'skipped', reason: 'File already exists' });
              continue;
            }
            
            // Copy the file
            await fs.copy(filePath, targetPath);
            console.log(`   ✅ Installed: ${fileName}`);
            installedCount++;
            results.push({ name: fileName, status: 'installed' });
            
          } else {
            console.log(`   ❌ Source file not found: ${filePath}`);
            results.push({ name: fileName, status: 'failed', reason: 'Source file not found' });
          }
        } catch (fileError) {
          console.log(`   ❌ Error installing ${fileName}: ${fileError.message}`);
          results.push({ name: fileName, status: 'failed', reason: fileError.message });
        }
      }
      
      console.log(`✅ Mod installation complete: ${installedCount} installed, ${skippedCount} skipped`);
      
      // Auto-save current mod set if changes were made
      if (installedCount > 0) {
        await this.autoSaveCurrentModSet(`Installed ${installedCount} mod(s)`);
      }
      
      return {
        success: true,
        message: `Installed ${installedCount} mod${installedCount !== 1 ? 's' : ''}${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
        installedMods: installedCount,
        skippedMods: skippedCount,
        results: results,
        targetPath: baseTargetDir || 'Multiple locations'
      };
      
    } catch (error) {
      console.error(`❌ Error installing mods:`, error);
      return {
        success: false,
        message: `Failed to install mods: ${error.message}`
      };
    }
  }

  /**
   * Add mods (legacy - keeping for compatibility)
   * @param {Object} event - IPC event object
   * @param {Object} data - Mod data
   * @returns {Object} Add result
   */
  async addMods(event, data) {
    console.log(`➕ Adding mods for game: ${data.gameId}`);
    console.log(`   Files to add: ${data.files.length}`);
    
    const gameModPaths = {
      minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      fs25: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
      schedule1: path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods')
    };
    
    const targetDir = gameModPaths[data.gameId];
    if (!targetDir) {
      return { success: false, message: 'Invalid game ID' };
    }
    
    try {
      // Ensure target directory exists
      await fs.ensureDir(targetDir);
      
      let addedCount = 0;
      let skippedCount = 0;
      const results = [];
      
      for (const fileInfo of data.files) {
        const sourcePath = fileInfo.path;
        const targetPath = path.join(targetDir, fileInfo.name);
        
        // Check if source file exists (for real files)
        if (sourcePath && await fs.pathExists(sourcePath)) {
          // Check if target already exists
          if (await fs.pathExists(targetPath)) {
            console.log(`   ⚠️ Skipping existing file: ${fileInfo.name}`);
            skippedCount++;
            results.push({ name: fileInfo.name, status: 'skipped', reason: 'File already exists' });
            continue;
          }
          
          // Copy the file
          await fs.copy(sourcePath, targetPath);
          console.log(`   ✅ Added: ${fileInfo.name}`);
          addedCount++;
          results.push({ name: fileInfo.name, status: 'added' });
          
        } else {
          console.log(`   ❌ Source file not found: ${fileInfo.name}`);
          results.push({ name: fileInfo.name, status: 'failed', reason: 'Source file not found' });
        }
      }
      
      console.log(`✅ Add mods complete: ${addedCount} added, ${skippedCount} skipped`);
      
      // Auto-save current mod set if changes were made
      if (addedCount > 0) {
        await this.autoSaveCurrentModSet(`Added ${addedCount} mod(s)`);
      }
      
      return {
        success: true,
        message: `Added ${addedCount} mods${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
        added: addedCount,
        skipped: skippedCount,
        results: results
      };
      
    } catch (error) {
      console.error(`❌ Error adding mods:`, error);
      return {
        success: false,
        message: `Failed to add mods: ${error.message}`
      };
    }
  }

  /**
   * Initialize Mod Sets directory and config
   * @returns {Promise<void>}
   */
  async initModSets() {
    try {
      await fs.ensureDir(this.modSetsDir);
      
      // Create default config if it doesn't exist
      if (!await fs.pathExists(this.modSetsConfigPath)) {
        const defaultConfig = {
          currentModSet: null,
          modSets: [],
          created: new Date().toISOString()
        };
        await fs.writeFile(this.modSetsConfigPath, JSON.stringify(defaultConfig, null, 2));
        console.log('✅ Initialized Mod Sets configuration');
      } else {
        // Check if there's an active mod set and start file watching
        const config = JSON.parse(await fs.readFile(this.modSetsConfigPath, 'utf8'));
        if (config.currentModSet) {
          console.log('🔄 Resuming file watching for active mod set');
          await this.startFileWatching();
        }
      }
    } catch (error) {
      console.error('❌ Error initializing Mod Sets:', error);
    }
  }

  /**
   * Get all available Mod Sets
   * @returns {Promise<Object>} Mod Sets configuration
   */
  async getModSets() {
    try {
      await this.initModSets();
      
      if (await fs.pathExists(this.modSetsConfigPath)) {
        const config = await fs.readFile(this.modSetsConfigPath, 'utf8');
        return JSON.parse(config);
      }
      
      return { currentModSet: null, modSets: [] };
    } catch (error) {
      console.error('❌ Error getting Mod Sets:', error);
      return { currentModSet: null, modSets: [] };
    }
  }

  /**
   * Create a new Mod Set from current mods/shaders/resource packs
   * @param {Object} event - IPC event object
   * @param {Object} modSetData - Mod Set information
   * @returns {Promise<Object>} Creation result
   */
  async createModSet(event, modSetData) {
    console.log(`📦 Creating new Mod Set: ${modSetData.name}${modSetData.startFresh ? ' (Start Fresh)' : ''}`);
    
    try {
      await this.initModSets();
      
      const { name, description = '', startFresh = false } = modSetData;
      const modSetId = name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const modSetPath = path.join(this.modSetsDir, `${modSetId}_ModSet`);
      
      // Create mod set directory structure
      await fs.ensureDir(modSetPath);
      await fs.ensureDir(path.join(modSetPath, 'mods'));
      await fs.ensureDir(path.join(modSetPath, 'shaderpacks'));
      await fs.ensureDir(path.join(modSetPath, 'resourcepacks'));
      
      let totalFiles = 0;
      
      if (startFresh) {
        // Start fresh - just create empty mod set (don't touch current directories yet)
        console.log('📁 Creating empty Mod Set (Start Fresh mode)');
        console.log('✅ Created empty Mod Set - directories will be cleared when you switch to it');
        
      } else {
        // Copy current files to the new mod set
        const sourceDirectories = [
          { source: path.join(this.minecraftDir, 'mods'), target: path.join(modSetPath, 'mods') },
          { source: path.join(this.minecraftDir, 'shaderpacks'), target: path.join(modSetPath, 'shaderpacks') },
          { source: path.join(this.minecraftDir, 'resourcepacks'), target: path.join(modSetPath, 'resourcepacks') }
        ];
        
        for (const { source, target } of sourceDirectories) {
          if (await fs.pathExists(source)) {
            const files = await fs.readdir(source);
            for (const file of files) {
              const sourcePath = path.join(source, file);
              const targetPath = path.join(target, file);
              
              if ((await fs.stat(sourcePath)).isFile()) {
                await fs.copy(sourcePath, targetPath);
                totalFiles++;
              }
            }
          }
        }
      }
      
      // Create mod set metadata
      const modSetInfo = {
        id: modSetId,
        name: name,
        description: description,
        created: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        fileCount: totalFiles,
        path: modSetPath,
        startFresh: startFresh || false
      };
      
      // Save mod set info file
      await fs.writeFile(
        path.join(modSetPath, 'modset_info.json'), 
        JSON.stringify(modSetInfo, null, 2)
      );
      
      // Update main config
      const config = await this.getModSets();
      config.modSets.push(modSetInfo);
      await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
      
      console.log(`✅ Created Mod Set "${name}" with ${totalFiles} files`);
      
      return {
        success: true,
        message: `Created Mod Set "${name}" with ${totalFiles} files`,
        modSet: modSetInfo
      };
      
    } catch (error) {
      console.error('❌ Error creating Mod Set:', error);
      return {
        success: false,
        message: `Failed to create Mod Set: ${error.message}`
      };
    }
  }

  /**
   * Switch to a different Mod Set
   * @param {Object} event - IPC event object
   * @param {Object} switchData - Switch information
   * @returns {Promise<Object>} Switch result
   */
  async switchModSet(event, switchData) {
    console.log(`🔄 Switching to Mod Set: ${switchData.modSetId}`);
    
    try {
      await this.initModSets();
      const config = await this.getModSets();
      
      // Find the target mod set
      const targetModSet = config.modSets.find(ms => ms.id === switchData.modSetId);
      if (!targetModSet) {
        return { success: false, message: 'Mod Set not found' };
      }
      
      // Save current state to previous mod set if there was one active
      if (config.currentModSet) {
        console.log(`💾 Saving current state to: ${config.currentModSet}`);
        await this.saveCurrentStateToModSet(config.currentModSet);
      }
      
      // Clear current directories
      console.log('🧹 Clearing current mod directories');
      await this.clearCurrentModDirectories();
      
      // Declare loadResult variable
      let loadResult;
      
      // Check if this is a "start fresh" mod set
      if (targetModSet.startFresh) {
        console.log('📁 This is a Start Fresh mod set - directories will remain empty');
        
        // Mark as no longer "start fresh" since it's now been activated
        targetModSet.startFresh = false;
        await fs.writeFile(
          path.join(targetModSet.path, 'modset_info.json'), 
          JSON.stringify(targetModSet, null, 2)
        );
        
        // Update in main config too
        const config = await this.getModSets();
        const configModSet = config.modSets.find(ms => ms.id === targetModSet.id);
        if (configModSet) {
          configModSet.startFresh = false;
        }
        
        loadResult = { success: true, filesLoaded: 0 };
      } else {
        // Load new mod set normally
        console.log(`📂 Loading Mod Set: ${targetModSet.name}`);
        loadResult = await this.loadModSetToActive(targetModSet);
      }
      
      if (loadResult.success) {
        // Update current mod set in config
        config.currentModSet = switchData.modSetId;
        
        // Update last used time
        const modSet = config.modSets.find(ms => ms.id === switchData.modSetId);
        if (modSet) {
          modSet.lastUsed = new Date().toISOString();
        }
        
        await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
        
        // Start file watching for real-time saving
        await this.startFileWatching();
        
        console.log(`✅ Successfully switched to Mod Set: ${targetModSet.name}`);
        
        return {
          success: true,
          message: `Switched to Mod Set "${targetModSet.name}"`,
          filesLoaded: loadResult.filesLoaded,
          modSet: targetModSet
        };
      } else {
        return loadResult;
      }
      
    } catch (error) {
      console.error('❌ Error switching Mod Set:', error);
      return {
        success: false,
        message: `Failed to switch Mod Set: ${error.message}`
      };
    }
  }

  /**
   * Save current mod state to a specific mod set
   * @param {string} modSetId - Mod Set ID to save to
   * @returns {Promise<Object>} Save result
   */
  async saveCurrentStateToModSet(modSetId) {
    try {
      const config = await this.getModSets();
      const modSet = config.modSets.find(ms => ms.id === modSetId);
      
      if (!modSet) {
        throw new Error('Mod Set not found');
      }
      
      const modSetPath = modSet.path;
      
      // Clear existing mod set files
      const directories = ['mods', 'shaderpacks', 'resourcepacks'];
      for (const dir of directories) {
        const dirPath = path.join(modSetPath, dir);
        if (await fs.pathExists(dirPath)) {
          await fs.emptyDir(dirPath);
        }
      }
      
      // Copy current files to mod set
      let totalFiles = 0;
      for (const dir of directories) {
        const sourcePath = path.join(this.minecraftDir, dir);
        const targetPath = path.join(modSetPath, dir);
        
        if (await fs.pathExists(sourcePath)) {
          const files = await fs.readdir(sourcePath);
          for (const file of files) {
            const sourceFile = path.join(sourcePath, file);
            const targetFile = path.join(targetPath, file);
            
            if ((await fs.stat(sourceFile)).isFile()) {
              await fs.copy(sourceFile, targetFile);
              totalFiles++;
            }
          }
        }
      }
      
      // Update mod set file count
      modSet.fileCount = totalFiles;
      modSet.lastUsed = new Date().toISOString();
      
      // Update mod set info file
      await fs.writeFile(
        path.join(modSetPath, 'modset_info.json'), 
        JSON.stringify(modSet, null, 2)
      );
      
      console.log(`💾 Saved ${totalFiles} files to Mod Set: ${modSet.name}`);
      
      return { success: true, filesSaved: totalFiles };
      
    } catch (error) {
      console.error('❌ Error saving current state to Mod Set:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Clear current mod directories
   * @returns {Promise<void>}
   */
  async clearCurrentModDirectories() {
    const directories = [
      path.join(this.minecraftDir, 'mods'),
      path.join(this.minecraftDir, 'shaderpacks'),
      path.join(this.minecraftDir, 'resourcepacks')
    ];
    
    for (const dir of directories) {
      if (await fs.pathExists(dir)) {
        await fs.emptyDir(dir);
        console.log(`🧹 Cleared: ${path.basename(dir)}`);
      }
    }
  }

  /**
   * Auto-save current mod set when changes are detected
   * @param {string} reason - Reason for the auto-save
   * @returns {Promise<void>}
   */
  async autoSaveCurrentModSet(reason = 'File change detected') {
    try {
      const config = await this.getModSets();
      if (!config.currentModSet) {
        console.log('📝 No active mod set to auto-save');
        return;
      }
      
      console.log(`💾 Auto-saving current mod set: ${reason}`);
      const result = await this.saveCurrentStateToModSet(config.currentModSet);
      
      if (result.success) {
        console.log(`✅ Auto-save completed: ${result.filesSaved} files saved`);
      } else {
        console.error('❌ Auto-save failed:', result.message);
      }
    } catch (error) {
      console.error('❌ Error during auto-save:', error);
    }
  }

  /**
   * Start watching mod directories for changes
   * @returns {Promise<void>}
   */
  async startFileWatching() {
    if (this.watchingDirectories) {
      console.log('📁 File watching already active');
      return;
    }

    try {
      const watchDirectories = [
        path.join(this.minecraftDir, 'mods'),
        path.join(this.minecraftDir, 'shaderpacks'),
        path.join(this.minecraftDir, 'resourcepacks')
      ];

      // Ensure directories exist before watching
      for (const dir of watchDirectories) {
        await fs.ensureDir(dir);
      }

      // Initialize file watchers
      for (const dir of watchDirectories) {
        const watcher = chokidar.watch(dir, {
          persistent: true,
          ignoreInitial: true,
          depth: 0, // Only watch direct files, not subdirectories
          ignored: /(^|[\/\\])\../, // Ignore hidden files
        });

        watcher
          .on('add', (filePath) => {
            console.log(`📄 File added: ${path.basename(filePath)}`);
            this.debouncedAutoSave(`File added: ${path.basename(filePath)}`);
          })
          .on('unlink', (filePath) => {
            console.log(`🗑️ File removed: ${path.basename(filePath)}`);
            this.debouncedAutoSave(`File removed: ${path.basename(filePath)}`);
          })
          .on('change', (filePath) => {
            console.log(`✏️ File modified: ${path.basename(filePath)}`);
            this.debouncedAutoSave(`File modified: ${path.basename(filePath)}`);
          })
          .on('error', (error) => {
            console.error(`❌ File watcher error for ${dir}:`, error);
          });

        this.fileWatchers.push(watcher);
      }

      this.watchingDirectories = true;
      console.log('👀 Started watching mod directories for real-time saving');

    } catch (error) {
      console.error('❌ Error starting file watchers:', error);
    }
  }

  /**
   * Stop watching mod directories
   * @returns {Promise<void>}
   */
  async stopFileWatching() {
    if (!this.watchingDirectories) {
      return;
    }

    try {
      // Close all file watchers
      for (const watcher of this.fileWatchers) {
        await watcher.close();
      }

      this.fileWatchers = [];
      this.watchingDirectories = false;

      // Clear any pending auto-save
      if (this.autoSaveTimeout) {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = null;
      }

      console.log('⏹️ Stopped watching mod directories');

    } catch (error) {
      console.error('❌ Error stopping file watchers:', error);
    }
  }

  /**
   * Debounced auto-save to prevent excessive saves
   * @param {string} reason - Reason for the save
   */
  debouncedAutoSave(reason) {
    // Clear existing timeout
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    // Set new timeout for 2 seconds
    this.autoSaveTimeout = setTimeout(() => {
      this.autoSaveCurrentModSet(reason);
    }, 2000);
  }

  /**
   * Load a mod set to active directories
   * @param {Object} modSet - Mod Set information
   * @returns {Promise<Object>} Load result
   */
  async loadModSetToActive(modSet) {
    try {
      const modSetPath = modSet.path;
      let totalFiles = 0;
      
      const directories = [
        { source: path.join(modSetPath, 'mods'), target: path.join(this.minecraftDir, 'mods') },
        { source: path.join(modSetPath, 'shaderpacks'), target: path.join(this.minecraftDir, 'shaderpacks') },
        { source: path.join(modSetPath, 'resourcepacks'), target: path.join(this.minecraftDir, 'resourcepacks') }
      ];
      
      // Ensure target directories exist
      for (const { target } of directories) {
        await fs.ensureDir(target);
      }
      
      // Copy files from mod set to active directories
      for (const { source, target } of directories) {
        if (await fs.pathExists(source)) {
          const files = await fs.readdir(source);
          for (const file of files) {
            const sourceFile = path.join(source, file);
            const targetFile = path.join(target, file);
            
            if ((await fs.stat(sourceFile)).isFile()) {
              await fs.copy(sourceFile, targetFile);
              totalFiles++;
            }
          }
          console.log(`📂 Loaded ${path.basename(source)} files`);
        }
      }
      
      return { success: true, filesLoaded: totalFiles };
      
    } catch (error) {
      console.error('❌ Error loading Mod Set to active directories:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Attempt to recover lost mod sets from backup directories
   * @param {Object} event - IPC event object
   * @returns {Promise<Object>} Recovery result
   */
  async recoverLostModSets(event) {
    console.log('🔍 Scanning for recoverable mod set data...');
    
    try {
      await this.initModSets();
      const config = await this.getModSets();
      let recoveredCount = 0;
      const recoveredSets = [];
      
      // Check if mod sets directory exists
      if (!await fs.pathExists(this.modSetsDir)) {
        return { success: false, message: 'No mod sets directory found' };
      }
      
      // Scan for mod set directories
      const items = await fs.readdir(this.modSetsDir);
      
      for (const item of items) {
        const itemPath = path.join(this.modSetsDir, item);
        const stat = await fs.stat(itemPath);
        
        if (stat.isDirectory() && item.endsWith('_ModSet')) {
          // Check if this mod set exists in config
          const modSetId = item.replace('_ModSet', '');
          const existsInConfig = config.modSets.some(ms => ms.id === modSetId);
          
          if (!existsInConfig) {
            // Try to recover this mod set
            const infoPath = path.join(itemPath, 'modset_info.json');
            let modSetInfo;
            
            if (await fs.pathExists(infoPath)) {
              // Load existing info
              modSetInfo = JSON.parse(await fs.readFile(infoPath, 'utf8'));
            } else {
              // Create info from directory name and contents
              const name = modSetId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              
              // Count files in subdirectories
              let fileCount = 0;
              const subDirs = ['mods', 'shaderpacks', 'resourcepacks'];
              for (const subDir of subDirs) {
                const subDirPath = path.join(itemPath, subDir);
                if (await fs.pathExists(subDirPath)) {
                  const files = await fs.readdir(subDirPath);
                  fileCount += files.filter(f => f !== '.gitkeep').length;
                }
              }
              
              modSetInfo = {
                id: modSetId,
                name: name,
                description: 'Recovered mod set',
                created: new Date().toISOString(),
                lastUsed: new Date().toISOString(),
                fileCount: fileCount,
                path: itemPath,
                recovered: true
              };
              
              // Save the recovered info
              await fs.writeFile(infoPath, JSON.stringify(modSetInfo, null, 2));
            }
            
            // Add to config
            config.modSets.push(modSetInfo);
            recoveredSets.push(modSetInfo);
            recoveredCount++;
            
            console.log(`✅ Recovered mod set: ${modSetInfo.name} (${modSetInfo.fileCount} files)`);
          }
        }
      }
      
      if (recoveredCount > 0) {
        // Save updated config
        await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
        
        return {
          success: true,
          message: `Recovered ${recoveredCount} mod set(s)`,
          recoveredCount: recoveredCount,
          recoveredSets: recoveredSets
        };
      } else {
        return {
          success: false,
          message: 'No recoverable mod sets found'
        };
      }
      
    } catch (error) {
      console.error('❌ Error during mod set recovery:', error);
      return {
        success: false,
        message: `Recovery failed: ${error.message}`
      };
    }
  }

  /**
   * Deactivate current mod set and stop file watching
   * @param {Object} event - IPC event object
   * @returns {Promise<Object>} Deactivation result
   */
  async deactivateModSet(event) {
    console.log('⚪ Deactivating current Mod Set');
    
    try {
      await this.initModSets();
      const config = await this.getModSets();
      
      if (!config.currentModSet) {
        return { success: true, message: 'No active Mod Set to deactivate' };
      }
      
      const currentModSetName = config.modSets.find(ms => ms.id === config.currentModSet)?.name || 'Unknown';
      
      // Save current state before deactivation
      console.log('💾 Saving current state before deactivation');
      await this.saveCurrentStateToModSet(config.currentModSet);
      
      // Stop file watching
      await this.stopFileWatching();
      
      // Clear current mod set
      config.currentModSet = null;
      await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
      
      console.log('✅ Successfully deactivated Mod Set');
      
      return {
        success: true,
        message: `Deactivated "${currentModSetName}" and stopped real-time saving`
      };
      
    } catch (error) {
      console.error('❌ Error deactivating Mod Set:', error);
      return {
        success: false,
        message: `Failed to deactivate Mod Set: ${error.message}`
      };
    }
  }

  /**
   * Delete a Mod Set
   * @param {Object} event - IPC event object
   * @param {Object} deleteData - Delete information
   * @returns {Promise<Object>} Delete result
   */
  async deleteModSet(event, deleteData) {
    console.log(`🗑️ Deleting Mod Set: ${deleteData.modSetId}`);
    
    try {
      const config = await this.getModSets();
      const modSetIndex = config.modSets.findIndex(ms => ms.id === deleteData.modSetId);
      
      if (modSetIndex === -1) {
        return { success: false, message: 'Mod Set not found' };
      }
      
      const modSet = config.modSets[modSetIndex];
      
      // Don't allow deleting currently active mod set
      if (config.currentModSet === deleteData.modSetId) {
        return { success: false, message: 'Cannot delete currently active Mod Set' };
      }
      
      // Remove mod set directory
      if (await fs.pathExists(modSet.path)) {
        await fs.remove(modSet.path);
        console.log(`🗑️ Removed Mod Set directory: ${modSet.path}`);
      }
      
      // Remove from config
      config.modSets.splice(modSetIndex, 1);
      await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
      
      console.log(`✅ Deleted Mod Set: ${modSet.name}`);
      
      return {
        success: true,
        message: `Deleted Mod Set "${modSet.name}"`
      };
      
    } catch (error) {
      console.error('❌ Error deleting Mod Set:', error);
      return {
        success: false,
        message: `Failed to delete Mod Set: ${error.message}`
      };
    }
  }

  /**
   * Get details of a specific Mod Set
   * @param {Object} event - IPC event object
   * @param {Object} detailsData - Request data
   * @returns {Promise<Object>} Mod Set details
   */
  async getModSetDetails(event, detailsData) {
    try {
      const config = await this.getModSets();
      const modSet = config.modSets.find(ms => ms.id === detailsData.modSetId);
      
      if (!modSet) {
        return { success: false, message: 'Mod Set not found' };
      }
      
      // Get file counts for each category
      const directories = ['mods', 'shaderpacks', 'resourcepacks'];
      const fileCounts = {};
      let totalSize = 0;
      
      for (const dir of directories) {
        const dirPath = path.join(modSet.path, dir);
        fileCounts[dir] = 0;
        
        if (await fs.pathExists(dirPath)) {
          const files = await fs.readdir(dirPath);
          for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stats = await fs.stat(filePath);
            if (stats.isFile()) {
              fileCounts[dir]++;
              totalSize += stats.size;
            }
          }
        }
      }
      
      return {
        success: true,
        modSet: {
          ...modSet,
          fileCounts: fileCounts,
          totalSize: totalSize,
          isActive: config.currentModSet === modSet.id
        }
      };
      
    } catch (error) {
      console.error('❌ Error getting Mod Set details:', error);
      return {
        success: false,
        message: `Failed to get Mod Set details: ${error.message}`
      };
    }
  }

  /**
   * Rename a Mod Set
   * @param {Object} event - IPC event object
   * @param {Object} renameData - Rename information
   * @returns {Promise<Object>} Rename result
   */
  async renameModSet(event, renameData) {
    console.log(`📝 Renaming Mod Set: ${renameData.modSetId} to "${renameData.newName}"`);
    
    try {
      const config = await this.getModSets();
      const modSet = config.modSets.find(ms => ms.id === renameData.modSetId);
      
      if (!modSet) {
        return { success: false, message: 'Mod Set not found' };
      }
      
      // Update mod set name
      modSet.name = renameData.newName;
      modSet.description = renameData.newDescription || modSet.description;
      
      // Update mod set info file
      await fs.writeFile(
        path.join(modSet.path, 'modset_info.json'), 
        JSON.stringify(modSet, null, 2)
      );
      
      // Update main config
      await fs.writeFile(this.modSetsConfigPath, JSON.stringify(config, null, 2));
      
      console.log(`✅ Renamed Mod Set to: ${renameData.newName}`);
      
      return {
        success: true,
        message: `Renamed Mod Set to "${renameData.newName}"`,
        modSet: modSet
      };
      
    } catch (error) {
      console.error('❌ Error renaming Mod Set:', error);
      return {
        success: false,
        message: `Failed to rename Mod Set: ${error.message}`
      };
    }
  }
}

module.exports = { ModManager };