# Build Modern NSIS Installer

Write-Host "Building Modern Armory X Installer..." -ForegroundColor Cyan

# Find NSIS path
$nsisPath = "${env:ProgramFiles(x86)}\NSIS\makensis.exe"
if (-not (Test-Path $nsisPath)) {
    $nsisPath = "${env:ProgramFiles}\NSIS\makensis.exe"
}

if (-not (Test-Path $nsisPath)) {
    Write-Host "ERROR: NSIS not found. Please ensure it's installed." -ForegroundColor Red
    exit 1
}

Write-Host "Found NSIS at: $nsisPath" -ForegroundColor Green

# Build the installer
Write-Host "Compiling modern installer..." -ForegroundColor Yellow
& $nsisPath "modern-installer.nsi"

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nSUCCESS! Modern installer created: ArmoryX-Setup-Modern.exe" -ForegroundColor Green
    Write-Host "`nFeatures:" -ForegroundColor Cyan
    Write-Host "  ✓ Custom dark theme UI" -ForegroundColor White
    Write-Host "  ✓ Single window experience" -ForegroundColor White
    Write-Host "  ✓ Creates Uninstall.exe" -ForegroundColor White
    Write-Host "  ✓ Desktop & Start Menu shortcuts" -ForegroundColor White
    Write-Host "`nDouble-click ArmoryX-Setup-Modern.exe to test!" -ForegroundColor Yellow
} else {
    Write-Host "ERROR: Build failed. Check the output above." -ForegroundColor Red
} 