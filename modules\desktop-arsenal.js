const { app, BrowserWindow, ipcMain, globalShortcut, screen, shell, dialog } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { exec } = require('child_process');

class DesktopArsenal {
    constructor() {
        this.mainWindow = null;
        this.overlayWindow = null;
        this.isOverlayVisible = false;
        this.hotkeysRegistered = false;
        
        // File paths
        this.appDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'ArmoryX', 'DesktopArsenal');
        this.filesPath = path.join(this.appDataPath, 'files');
        this.iconsPath = path.join(this.appDataPath, 'icons');
        this.backgroundsPath = path.join(this.appDataPath, 'backgrounds');
        this.configPath = path.join(this.appDataPath, 'config.json');
        this.layoutPath = path.join(this.appDataPath, 'layout.json');
        
        // Default settings
        this.defaultSettings = {
            overlay: {
                size: { width: 1650, height: 1000 },
                position: { x: 'center', y: 'center' },
                viewMode: 'grid', // grid, list, thumbnail
                background: null,
                sortBy: 'name', // name, date, type, custom
                autoClose: true, // Auto-close overlay when opening files
                preventFullscreen: true // Prevent overlay from opening when fullscreen apps are running
            },
            hotkeys: {
                enabled: true,
                toggleKey: 'Ctrl+Space'
            },
            fileMonitoring: {
                enabled: false,
                notificationDuration: 5000,
                autoStart: false
            },
            autoStart: false
        };
        
        this.settings = { ...this.defaultSettings };
        this.organizedFiles = new Map();
        this.categories = new Map();
        this.desktopWatcher = null;
        this.desktopPath = path.join(os.homedir(), 'Desktop');
        
        this.init();
    }

    async init() {
        try {
            await this.ensureDirectories();
            await this.loadSettings();
            await this.loadFileData();
            this.setupDefaultCategories();
            
            // Ensure settings have proper structure
            if (!this.settings.overlay) {
                this.settings.overlay = { ...this.defaultSettings.overlay };
            }
            
            // Start background cleanup scheduler
            this.scheduleBackgroundCleanup();
            
            console.log('✅ Desktop Arsenal initialized successfully');
        } catch (error) {
            console.error('❌ Desktop Arsenal initialization failed:', error);
            // Fallback to default settings if init fails
            this.settings = { ...this.defaultSettings };
        }
    }

    async ensureDirectories() {
        const dirs = [
            this.appDataPath,
            this.filesPath,
            this.iconsPath,
            this.backgroundsPath
        ];
        
        for (const dir of dirs) {
            await fs.ensureDir(dir);
        }
    }

    async loadSettings() {
        try {
            if (await fs.pathExists(this.configPath)) {
                const savedSettings = await fs.readJson(this.configPath);
                // Deep merge settings to ensure all required properties exist
                this.settings = {
                    overlay: { ...this.defaultSettings.overlay, ...savedSettings.overlay },
                    hotkeys: { ...this.defaultSettings.hotkeys, ...savedSettings.hotkeys },
                    fileMonitoring: { ...this.defaultSettings.fileMonitoring, ...savedSettings.fileMonitoring },
                    autoStart: savedSettings.autoStart || this.defaultSettings.autoStart
                };
            } else {
                this.settings = { ...this.defaultSettings };
            }
        } catch (error) {
            console.error('Error loading Desktop Arsenal settings:', error);
            this.settings = { ...this.defaultSettings };
        }
    }

    async saveSettings() {
        try {
            await fs.writeJson(this.configPath, this.settings, { spaces: 2 });
            console.log('✅ Settings saved to:', this.configPath);
        } catch (error) {
            console.error('Error saving Desktop Arsenal settings:', error);
            throw error; // Re-throw to catch in calling function
        }
    }

    async loadFileData() {
        try {
            if (await fs.pathExists(this.layoutPath)) {
                const layoutData = await fs.readJson(this.layoutPath);
                this.organizedFiles = new Map(layoutData.files || []);
                this.categories = new Map(layoutData.categories || []);
            }
        } catch (error) {
            console.error('Error loading file data:', error);
        }
    }

    async saveFileData() {
        try {
            const layoutData = {
                files: Array.from(this.organizedFiles.entries()),
                categories: Array.from(this.categories.entries()),
                lastUpdated: new Date().toISOString()
            };
            await fs.writeJson(this.layoutPath, layoutData, { spaces: 2 });
        } catch (error) {
            console.error('Error saving file data:', error);
        }
    }

    setupDefaultCategories() {
        if (this.categories.size === 0) {
            this.categories.set('documents', {
                name: 'Documents',
                icon: '📄',
                description: 'Word documents, PDFs, text files',
                extensions: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
                color: '#3b82f6'
            });
            
            this.categories.set('images', {
                name: 'Images',
                icon: '🖼️',
                description: 'Photos, screenshots, graphics',
                extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
                color: '#10b981'
            });
            
            this.categories.set('videos', {
                name: 'Videos',
                icon: '🎬',
                description: 'Video files and movies',
                extensions: ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv'],
                color: '#f59e0b'
            });
            
            this.categories.set('audio', {
                name: 'Audio',
                icon: '🎵',
                description: 'Music and sound files',
                extensions: ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
                color: '#8b5cf6'
            });
            
            this.categories.set('archives', {
                name: 'Archives',
                icon: '📦',
                description: 'Compressed files and packages',
                extensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
                color: '#ef4444'
            });
            
            this.categories.set('executables', {
                name: 'Programs',
                icon: '⚙️',
                description: 'Executable files and installers',
                extensions: ['.exe', '.msi', '.app', '.deb', '.rpm'],
                color: '#6b7280'
            });
            
            this.categories.set('other', {
                name: 'Other',
                icon: '📁',
                description: 'Other file types',
                extensions: [],
                color: '#64748b'
            });
        }
    }

    setMainWindow(window) {
        this.mainWindow = window;
    }



    async createOverlay() {
        if (this.overlayWindow) {
            this.overlayWindow.focus();
            return;
        }

        // Ensure settings are loaded
        if (!this.settings || !this.settings.overlay) {
            await this.loadSettings();
        }

        const primaryDisplay = screen.getPrimaryDisplay();
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
        
        // Calculate center position with fallback defaults
        const overlayWidth = this.settings?.overlay?.size?.width || this.defaultSettings.overlay.size.width;
        const overlayHeight = this.settings?.overlay?.size?.height || this.defaultSettings.overlay.size.height;
        
        const x = Math.round((screenWidth - overlayWidth) / 2);
        const y = Math.round((screenHeight - overlayHeight) / 2);

        this.overlayWindow = new BrowserWindow({
            width: screenWidth,
            height: screenHeight,
            x: 0,
            y: 0,
            frame: false,
            transparent: true,
            backgroundColor: '#00000000', // Fully transparent background
            resizable: false,
            minimizable: false,
            maximizable: false,
            alwaysOnTop: true,
            skipTaskbar: true,
            focusable: true,
            modal: false, // Don't make it modal so dialogs can appear on top
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                webSecurity: false // Allow loading local files
            }
        });

        // Load overlay HTML
        await this.overlayWindow.loadFile('desktop-arsenal-overlay.html');

        this.overlayWindow.on('closed', () => {
            this.overlayWindow = null;
            this.isOverlayVisible = false;
        });

        // Save size and position when changed
        this.overlayWindow.on('resized', () => {
            const [width, height] = this.overlayWindow.getSize();
            if (this.settings && this.settings.overlay) {
                this.settings.overlay.size = { width, height };
                this.saveSettings();
            }
        });

        this.overlayWindow.on('moved', () => {
            const [x, y] = this.overlayWindow.getPosition();
            if (this.settings && this.settings.overlay) {
                this.settings.overlay.position = { x, y };
                this.saveSettings();
            }
        });

        this.isOverlayVisible = true;
        console.log('✅ Desktop Arsenal overlay created');
    }

    async isFullscreenAppRunning() {
        return new Promise((resolve) => {
            if (process.platform === 'win32') {
                // PowerShell script to check for fullscreen applications
                const psScript = `
                    Add-Type @"
                    using System;
                    using System.Runtime.InteropServices;
                    using System.Drawing;
                    public class ScreenUtils {
                        [DllImport("user32.dll")]
                        public static extern IntPtr GetForegroundWindow();
                        
                        [DllImport("user32.dll")]
                        public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
                        
                        [DllImport("user32.dll")]
                        public static extern bool IsZoomed(IntPtr hWnd);
                        
                        [DllImport("user32.dll")]
                        public static extern int GetWindowLong(IntPtr hWnd, int nIndex);
                        
                        [DllImport("user32.dll", CharSet = CharSet.Auto)]
                        public static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder lpString, int nMaxCount);
                        
                        [DllImport("user32.dll", CharSet = CharSet.Auto)]
                        public static extern int GetClassName(IntPtr hWnd, System.Text.StringBuilder lpClassName, int nMaxCount);
                        
                        public struct RECT {
                            public int Left;
                            public int Top;
                            public int Right;
                            public int Bottom;
                        }
                        
                        public const int GWL_STYLE = -16;
                        public const int WS_MAXIMIZE = 0x01000000;
                        public const int WS_BORDER = 0x00800000;
                        public const int WS_CAPTION = 0x00C00000;
                    }
"@
                    
                    $foregroundWindow = [ScreenUtils]::GetForegroundWindow()
                    if ($foregroundWindow -eq [IntPtr]::Zero) {
                        return $false
                    }
                    
                    # Get window rectangle
                    $rect = New-Object ScreenUtils+RECT
                    $success = [ScreenUtils]::GetWindowRect($foregroundWindow, [ref]$rect)
                    
                    if (-not $success) {
                        return $false
                    }
                    
                    # Get screen dimensions
                    $screenWidth = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds.Width
                    $screenHeight = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds.Height
                    
                    # Get window title and class name for debugging
                    $titleBuilder = New-Object System.Text.StringBuilder(256)
                    [ScreenUtils]::GetWindowText($foregroundWindow, $titleBuilder, 256)
                    $windowTitle = $titleBuilder.ToString()
                    
                    $classBuilder = New-Object System.Text.StringBuilder(256)
                    [ScreenUtils]::GetClassName($foregroundWindow, $classBuilder, 256)
                    $className = $classBuilder.ToString()
                    
                    # Check if window covers entire screen
                    $windowWidth = $rect.Right - $rect.Left
                    $windowHeight = $rect.Bottom - $rect.Top
                    
                    # A window is considered fullscreen if it covers the entire primary screen
                    $isFullscreen = ($rect.Left -le 0) -and ($rect.Top -le 0) -and 
                                   ($windowWidth -ge $screenWidth) -and ($windowHeight -ge $screenHeight)
                    
                    # Additional check: some games/apps might not start at 0,0 but still be fullscreen
                    $isMaximizedFullscreen = [ScreenUtils]::IsZoomed($foregroundWindow) -and 
                                           ($windowWidth -ge $screenWidth) -and ($windowHeight -ge $screenHeight)
                    
                    # Check window style for borderless fullscreen
                    $style = [ScreenUtils]::GetWindowLong($foregroundWindow, [ScreenUtils]::GWL_STYLE)
                    $isBorderless = ($style -band [ScreenUtils]::WS_BORDER) -eq 0 -and 
                                   ($style -band [ScreenUtils]::WS_CAPTION) -eq 0
                    
                    $isBorderlessFullscreen = $isBorderless -and ($windowWidth -ge $screenWidth) -and ($windowHeight -ge $screenHeight)
                    
                    # Known fullscreen applications/games (common game launchers and games)
                    $fullscreenApps = @(
                        "Steam.exe",
                        "csgo.exe",
                        "cs2.exe",
                        "valorant.exe",
                        "league of legends.exe",
                        "fortnite.exe",
                        "minecraft.exe",
                        "gta5.exe",
                        "rdr2.exe",
                        "apex_legends.exe",
                        "overwatch.exe",
                        "dota2.exe",
                        "pubg.exe",
                        "destiny2.exe",
                        "cod.exe",
                        "warzone.exe",
                        "fifa23.exe",
                        "nba2k23.exe",
                        "rocket league.exe",
                        "among us.exe",
                        "discord.exe" # Discord in fullscreen mode
                    )
                    
                    # Check if current window is a known fullscreen app
                    $isKnownFullscreenApp = $fullscreenApps | Where-Object { $windowTitle -like "*$_*" }
                    
                    $result = $isFullscreen -or $isMaximizedFullscreen -or $isBorderlessFullscreen -or $isKnownFullscreenApp
                    
                    # Output debug information
                    Write-Host "Window: $windowTitle ($className)"
                    Write-Host "Dimensions: $($windowWidth)x$($windowHeight) at $($rect.Left),$($rect.Top)"
                    Write-Host "Screen: $($screenWidth)x$($screenHeight)"
                    Write-Host "Fullscreen: $result"
                    
                    return $result
                `;
                
                exec(`powershell -ExecutionPolicy Bypass -Command "${psScript}"`, (error, stdout, stderr) => {
                    if (error) {
                        console.error('Fullscreen detection error:', error.message);
                        resolve(false); // Default to false if detection fails
                    } else {
                        const result = stdout.trim();
                        const isFullscreen = result.includes('True');
                        if (isFullscreen) {
                            console.log('🎮 Fullscreen application detected, preventing overlay');
                        }
                        resolve(isFullscreen);
                    }
                });
            } else {
                resolve(false); // Not implemented for other platforms
            }
        });
    }

    async toggleOverlay() {
        try {
            if (this.isOverlayVisible && this.overlayWindow) {
                // Trigger the closing animation and save progress
                try {
                    await this.overlayWindow.webContents.executeJavaScript(`
                        // Stop periodic progress saving
                        if (typeof stopPeriodicProgressSaving === 'function') {
                            stopPeriodicProgressSaving();
                        }
                        
                        // Save final background progress before closing
                        if (typeof saveBackgroundProgress === 'function') {
                            saveBackgroundProgress();
                        }
                        
                        // Add closing animation
                        document.body.classList.add('closing');
                    `);
                    
                    // Wait for animation to complete (300ms, same as in closeOverlay)
                    await new Promise(resolve => setTimeout(resolve, 300));
                } catch (progressError) {
                    console.warn('Could not trigger closing animation:', progressError.message);
                }
                
                // Now close the window after animation
                this.overlayWindow.close();
            } else {
                // Check if fullscreen prevention is enabled and a fullscreen app is running
                if (this.settings.overlay.preventFullscreen && await this.isFullscreenAppRunning()) {
                    console.log('🎮 Fullscreen application detected, overlay blocked');
                    return;
                }
                
                await this.createOverlay();
            }
        } catch (error) {
            console.error('❌ Error toggling Desktop Arsenal overlay:', error);
            // Reset state if there's an error
            this.isOverlayVisible = false;
            this.overlayWindow = null;
        }
    }

    registerHotkeys() {
        if (this.hotkeysRegistered || !this.settings.hotkeys.enabled) return;

        try {
            const success = globalShortcut.register(this.settings.hotkeys.toggleKey, () => {
                this.toggleOverlay();
            });

            if (success) {
                this.hotkeysRegistered = true;
                console.log(`✅ Desktop Arsenal hotkey registered: ${this.settings.hotkeys.toggleKey}`);
            } else {
                console.error('❌ Failed to register Desktop Arsenal hotkey');
            }
        } catch (error) {
            console.error('❌ Error registering Desktop Arsenal hotkey:', error);
        }
    }

    unregisterHotkeys() {
        if (this.hotkeysRegistered) {
            globalShortcut.unregister(this.settings.hotkeys.toggleKey);
            this.hotkeysRegistered = false;
            console.log('✅ Desktop Arsenal hotkey unregistered');
        }
    }

    async addFileToArsenal(filePath, customName = null, customIcon = null) {
        try {
            const fileName = path.basename(filePath);
            const fileStats = await fs.stat(filePath);
            const isDirectory = fileStats.isDirectory();
            const fileExt = isDirectory ? '' : path.extname(filePath).toLowerCase();
            const fileId = Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9);
            
            // Determine category
            let category = 'other';
            if (!isDirectory) {
                for (const [catId, catData] of this.categories.entries()) {
                    if (catData.extensions.includes(fileExt)) {
                        category = catId;
                        break;
                    }
                }
            }

            // Extract and cache icon BEFORE moving the file
            let iconPath = null;
            if (!isDirectory) {
                try {
                    iconPath = await this.extractFileIcon(filePath, fileId);
                } catch (error) {
                    console.warn('Failed to extract icon for file:', fileName, error.message);
                }
            }
            // For directories, we'll use a default folder icon in the UI
            
            // Create new file path in arsenal
            const newFilePath = path.join(this.filesPath, fileId + '_' + fileName);
            
            // Move file to arsenal folder
            await fs.move(filePath, newFilePath);

            // Store file metadata
            const fileData = {
                id: fileId,
                originalPath: filePath,
                arsenalPath: newFilePath,
                originalName: fileName,
                displayName: customName || fileName,
                extension: fileExt,
                size: fileStats.size,
                dateAdded: new Date().toISOString(),
                dateModified: fileStats.mtime.toISOString(),
                category: category,
                customIcon: customIcon,
                extractedIcon: iconPath,
                description: '',
                tags: [],
                isDirectory: isDirectory
            };

            this.organizedFiles.set(fileId, fileData);
            await this.saveFileData();

            console.log(`✅ File added to Desktop Arsenal: ${fileName}`);
            return fileData;
        } catch (error) {
            console.error('❌ Error adding file to Desktop Arsenal:', error);
            throw error;
        }
    }

    async extractFileIcon(filePath, fileId) {
        return new Promise((resolve, reject) => {
            // Use PowerShell to extract icon on Windows
            if (process.platform === 'win32') {
                const iconPath = path.join(this.iconsPath, fileId + '.png');
                const fileExt = path.extname(filePath).toLowerCase();
                
                let psScript;
                if (fileExt === '.lnk') {
                    // Enhanced shortcut icon extraction with better error handling
                    psScript = `
                        Add-Type -AssemblyName System.Drawing
                        Add-Type @"
                        using System;
                        using System.Drawing;
                        using System.Runtime.InteropServices;
                        public class IconExtractor {
                            [DllImport("shell32.dll", CharSet = CharSet.Auto)]
                            public static extern IntPtr ExtractIcon(IntPtr hInst, string lpszExeFileName, int nIconIndex);
                            
                            [DllImport("user32.dll", SetLastError = true)]
                            public static extern bool DestroyIcon(IntPtr hIcon);
                            
                            [DllImport("shell32.dll", CharSet = CharSet.Auto)]
                            public static extern uint ExtractIconEx(string lpszFile, int nIconIndex, IntPtr[] phiconLarge, IntPtr[] phiconSmall, uint nIcons);
                        }
"@
                        $shell = New-Object -ComObject WScript.Shell
                        $shortcut = $shell.CreateShortcut('${filePath.replace(/'/g, "''")}')
                        $targetPath = $shortcut.TargetPath
                        $iconLocation = $shortcut.IconLocation
                        $icon = $null
                        
                        Write-Host "Shortcut target: $targetPath"
                        Write-Host "Icon location: $iconLocation"
                        
                        # First try: Use specific icon location from shortcut
                        if ($iconLocation -and $iconLocation -ne '' -and $iconLocation -ne ',0') {
                            $parts = $iconLocation.Split(',')
                            $iconPath = $parts[0].Trim()
                            $iconIndex = 0
                            if ($parts.Length -gt 1) {
                                try {
                                    $iconIndex = [int]$parts[1]
                                } catch {
                                    $iconIndex = 0
                                }
                            }
                            
                            # Resolve environment variables in icon path
                            $iconPath = [Environment]::ExpandEnvironmentVariables($iconPath)
                            
                            if (Test-Path $iconPath) {
                                Write-Host "Extracting icon from: $iconPath at index $iconIndex"
                                try {
                                    $hIcon = [IconExtractor]::ExtractIcon([IntPtr]::Zero, $iconPath, $iconIndex)
                                    if ($hIcon -ne [IntPtr]::Zero) {
                                        $icon = [System.Drawing.Icon]::FromHandle($hIcon)
                                        Write-Host "Successfully extracted icon from icon location"
                                    }
                                } catch {
                                    Write-Host "Failed to extract from icon location: $_"
                                }
                            }
                        }
                        
                        # Second try: Use target executable
                        if (-not $icon -and $targetPath -and $targetPath -ne '' -and (Test-Path $targetPath)) {
                            # Resolve environment variables in target path
                            $targetPath = [Environment]::ExpandEnvironmentVariables($targetPath)
                            
                            Write-Host "Extracting icon from target: $targetPath"
                            try {
                                $icon = [System.Drawing.Icon]::ExtractAssociatedIcon($targetPath)
                                Write-Host "Successfully extracted icon from target path"
                            } catch {
                                Write-Host "ExtractAssociatedIcon failed, trying ExtractIcon API"
                                try {
                                    $hIcon = [IconExtractor]::ExtractIcon([IntPtr]::Zero, $targetPath, 0)
                                    if ($hIcon -ne [IntPtr]::Zero) {
                                        $icon = [System.Drawing.Icon]::FromHandle($hIcon)
                                        Write-Host "Successfully extracted icon using ExtractIcon API"
                                    }
                                } catch {
                                    Write-Host "ExtractIcon API also failed: $_"
                                }
                            }
                        }
                        
                        # Third try: Use shortcut file itself
                        if (-not $icon) {
                            Write-Host "Extracting icon from shortcut file itself"
                            try {
                                $icon = [System.Drawing.Icon]::ExtractAssociatedIcon('${filePath.replace(/'/g, "''")}')
                                Write-Host "Successfully extracted icon from shortcut file"
                            } catch {
                                Write-Host "Failed to extract from shortcut file: $_"
                            }
                        }
                        
                        # Fourth try: Use system default shortcut icon
                        if (-not $icon) {
                            Write-Host "Using system default shortcut icon"
                            try {
                                $hIcon = [IconExtractor]::ExtractIcon([IntPtr]::Zero, "shell32.dll", 0)
                                if ($hIcon -ne [IntPtr]::Zero) {
                                    $icon = [System.Drawing.Icon]::FromHandle($hIcon)
                                    Write-Host "Using default system icon"
                                }
                            } catch {
                                Write-Host "Even default icon extraction failed: $_"
                            }
                        }
                        
                        if ($icon) {
                            $bmp = $icon.ToBitmap()
                            $bmp.Save('${iconPath.replace(/'/g, "''")}', [System.Drawing.Imaging.ImageFormat]::Png)
                            $bmp.Dispose()
                            $icon.Dispose()
                            Write-Host "Icon saved successfully"
                        } else {
                            Write-Host "No icon could be extracted"
                        }
                    `;
                } else {
                    // Enhanced regular file icon extraction
                    psScript = `
                        Add-Type -AssemblyName System.Drawing
                        try {
                            $icon = [System.Drawing.Icon]::ExtractAssociatedIcon('${filePath.replace(/'/g, "''")}')
                            if ($icon) {
                                $bmp = $icon.ToBitmap()
                                $bmp.Save('${iconPath.replace(/'/g, "''")}', [System.Drawing.Imaging.ImageFormat]::Png)
                                $bmp.Dispose()
                                $icon.Dispose()
                                Write-Host "Icon extracted successfully"
                            } else {
                                Write-Host "No icon found for file"
                            }
                        } catch {
                            Write-Host "Error extracting icon: $_"
                        }
                    `;
                }
                
                exec(`powershell -ExecutionPolicy Bypass -Command "${psScript}"`, (error, stdout, stderr) => {
                    if (error) {
                        console.error('Icon extraction error:', error.message);
                        console.error('PowerShell stderr:', stderr);
                        console.error('PowerShell stdout:', stdout);
                        // Don't reject, just return null for failed extractions
                        resolve(null);
                    } else {
                        console.log('PowerShell output:', stdout);
                        // Check if the icon file was created
                        fs.pathExists(iconPath).then(exists => {
                            if (exists) {
                                console.log(`✅ Icon extracted successfully for: ${path.basename(filePath)}`);
                                resolve(iconPath);
                            } else {
                                console.warn('Icon file was not created for:', filePath);
                                resolve(null);
                            }
                        });
                    }
                });
            } else {
                resolve(null);
            }
        });
    }

    async restoreFileToDesktop(fileId) {
        try {
            const fileData = this.organizedFiles.get(fileId);
            if (!fileData) {
                throw new Error('File not found in arsenal');
            }

            const desktopPath = path.join(os.homedir(), 'Desktop');
            const originalFileName = fileData.originalName;
            let restorePath = path.join(desktopPath, originalFileName);
            
            // Handle duplicate names
            let counter = 1;
            while (await fs.pathExists(restorePath)) {
                const ext = path.extname(originalFileName);
                const name = path.basename(originalFileName, ext);
                restorePath = path.join(desktopPath, `${name} (${counter})${ext}`);
                counter++;
            }

            // Move file back to desktop
            await fs.move(fileData.arsenalPath, restorePath);
            
            // Remove from arsenal
            this.organizedFiles.delete(fileId);
            await this.saveFileData();

            console.log(`✅ File restored to desktop: ${originalFileName}`);
            return restorePath;
        } catch (error) {
            console.error('❌ Error restoring file to desktop:', error);
            throw error;
        }
    }

    async deleteFileFromArsenal(fileId) {
        try {
            const fileData = this.organizedFiles.get(fileId);
            if (!fileData) {
                throw new Error('File not found in arsenal');
            }

            // Delete physical file
            if (await fs.pathExists(fileData.arsenalPath)) {
                await fs.remove(fileData.arsenalPath);
            }

            // Delete cached icon
            if (fileData.extractedIcon && await fs.pathExists(fileData.extractedIcon)) {
                await fs.remove(fileData.extractedIcon);
            }

            // Remove from arsenal
            this.organizedFiles.delete(fileId);
            await this.saveFileData();

            console.log(`✅ File deleted from Desktop Arsenal: ${fileData.originalName}`);
        } catch (error) {
            console.error('❌ Error deleting file from Desktop Arsenal:', error);
            throw error;
        }
    }

    async openFile(fileId) {
        try {
            const fileData = this.organizedFiles.get(fileId);
            if (!fileData) {
                throw new Error('File not found in arsenal');
            }

            if (await fs.pathExists(fileData.arsenalPath)) {
                await shell.openPath(fileData.arsenalPath);
                console.log(`✅ Opened file: ${fileData.displayName}`);
                
                // Auto-close overlay if setting is enabled
                if (this.settings.overlay.autoClose && this.isOverlayVisible && this.overlayWindow) {
                    setTimeout(() => {
                        this.toggleOverlay();
                    }, 100); // Small delay to ensure file opens first
                }
            } else {
                throw new Error('File not found on disk');
            }
        } catch (error) {
            console.error('❌ Error opening file:', error);
            throw error;
        }
    }

    async updateFileMetadata(fileId, updates) {
        try {
            const fileData = this.organizedFiles.get(fileId);
            if (!fileData) {
                throw new Error('File not found in arsenal');
            }

            // Update allowed fields
            const allowedFields = ['displayName', 'description', 'tags', 'category', 'customIcon'];
            for (const [key, value] of Object.entries(updates)) {
                if (allowedFields.includes(key)) {
                    fileData[key] = value;
                }
            }

            fileData.lastUpdated = new Date().toISOString();
            this.organizedFiles.set(fileId, fileData);
            await this.saveFileData();

            console.log(`✅ Updated file metadata: ${fileData.displayName}`);
            return fileData;
        } catch (error) {
            console.error('❌ Error updating file metadata:', error);
            throw error;
        }
    }

    async bulkDeleteFiles(fileIds) {
        try {
            const deletedFiles = [];
            for (const fileId of fileIds) {
                const fileData = this.organizedFiles.get(fileId);
                if (fileData) {
                    await this.deleteFileFromArsenal(fileId);
                    deletedFiles.push(fileData);
                }
            }
            console.log(`✅ Bulk deleted ${deletedFiles.length} files`);
            return deletedFiles;
        } catch (error) {
            console.error('❌ Error bulk deleting files:', error);
            throw error;
        }
    }

    async bulkRestoreFiles(fileIds) {
        try {
            const restoredFiles = [];
            for (const fileId of fileIds) {
                const restorePath = await this.restoreFileToDesktop(fileId);
                restoredFiles.push({ fileId, restorePath });
            }
            console.log(`✅ Bulk restored ${restoredFiles.length} files`);
            return restoredFiles;
        } catch (error) {
            console.error('❌ Error bulk restoring files:', error);
            throw error;
        }
    }

    async bulkCategorizeFiles(fileIds, category) {
        try {
            const updatedFiles = [];
            for (const fileId of fileIds) {
                const fileData = await this.updateFileMetadata(fileId, { category });
                updatedFiles.push(fileData);
            }
            console.log(`✅ Bulk categorized ${updatedFiles.length} files to ${category}`);
            return updatedFiles;
        } catch (error) {
            console.error('❌ Error bulk categorizing files:', error);
            throw error;
        }
    }

    async getFileThumbnail(fileId) {
        try {
            const fileData = this.organizedFiles.get(fileId);
            if (!fileData) {
                throw new Error('File not found in arsenal');
            }

            const thumbnailPath = path.join(this.iconsPath, fileId + '_thumb.png');
            
            // Check if thumbnail already exists
            if (await fs.pathExists(thumbnailPath)) {
                return thumbnailPath;
            }

            // Generate thumbnail based on file type
            if (fileData.category === 'images') {
                return await this.generateImageThumbnail(fileData.arsenalPath, thumbnailPath);
            } else if (fileData.extractedIcon) {
                return fileData.extractedIcon;
            }

            return null;
        } catch (error) {
            console.error('❌ Error getting file thumbnail:', error);
            return null;
        }
    }

    async generateImageThumbnail(imagePath, thumbnailPath) {
        try {
            // Use PowerShell to generate thumbnail on Windows
            if (process.platform === 'win32') {
                const psScript = `
                    Add-Type -AssemblyName System.Drawing
                    $image = [System.Drawing.Image]::FromFile('${imagePath.replace(/'/g, "''")}')
                    $thumb = $image.GetThumbnailImage(150, 150, $null, [IntPtr]::Zero)
                    $thumb.Save('${thumbnailPath.replace(/'/g, "''")}', [System.Drawing.Imaging.ImageFormat]::Png)
                    $image.Dispose()
                    $thumb.Dispose()
                `;
                
                await new Promise((resolve, reject) => {
                    exec(`powershell -Command "${psScript}"`, (error) => {
                        if (error) reject(error);
                        else resolve();
                    });
                });
                
                return thumbnailPath;
            }
            return null;
        } catch (error) {
            console.error('Error generating image thumbnail:', error);
            return null;
        }
    }

    async getArsenalFiles(filters = {}) {
        try {
            let files = Array.from(this.organizedFiles.values());

            // Apply filters
            if (filters.category) {
                files = files.filter(file => file.category === filters.category);
            }
            
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                files = files.filter(file => 
                    file.displayName.toLowerCase().includes(searchTerm) ||
                    file.description.toLowerCase().includes(searchTerm) ||
                    file.tags.some(tag => tag.toLowerCase().includes(searchTerm))
                );
            }

            // Apply sorting
            const sortBy = filters.sortBy || this.settings.overlay.sortBy;
            files.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return a.displayName.localeCompare(b.displayName);
                    case 'date':
                        return new Date(b.dateAdded) - new Date(a.dateAdded);
                    case 'size':
                        return b.size - a.size;
                    case 'type':
                        return a.extension.localeCompare(b.extension);
                    default:
                        return 0;
                }
            });

            return {
                files,
                total: files.length,
                categories: Array.from(this.categories.entries()),
                settings: this.settings
            };
        } catch (error) {
            console.error('❌ Error getting arsenal files:', error);
            throw error;
        }
    }

    async updateSettings(newSettings) {
        try {
            // Unregister old hotkeys if they changed
            if (newSettings.hotkeys && newSettings.hotkeys.toggleKey !== this.settings.hotkeys.toggleKey) {
                this.unregisterHotkeys();
            }

            // Deep merge settings to preserve existing nested values
            this.settings = this.deepMerge(this.settings, newSettings);
            
            // Ensure all required properties exist
            if (!this.settings.overlay) {
                this.settings.overlay = { ...this.defaultSettings.overlay };
            }
            if (!this.settings.hotkeys) {
                this.settings.hotkeys = { ...this.defaultSettings.hotkeys };
            }
            if (!this.settings.fileMonitoring) {
                this.settings.fileMonitoring = { ...this.defaultSettings.fileMonitoring };
            }
            
            await this.saveSettings();

            // Register new hotkeys
            if (this.settings.hotkeys.enabled) {
                this.registerHotkeys();
            }

            console.log('✅ Desktop Arsenal settings updated:', this.settings);
            return this.settings;
        } catch (error) {
            console.error('❌ Error updating settings:', error);
            throw error;
        }
    }

    // Deep merge utility function
    deepMerge(target, source) {
        const output = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    // If the property is an object (but not an array), merge recursively
                    if (target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
                        output[key] = this.deepMerge(target[key], source[key]);
                    } else {
                        output[key] = source[key];
                    }
                } else {
                    // Otherwise, replace the value
                    output[key] = source[key];
                }
            }
        }
        
        return output;
    }

    async getSettings() {
        return this.settings;
    }

    async getStats() {
        try {
            const files = Array.from(this.organizedFiles.values());
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            
            const categoryStats = {};
            for (const [catId, catData] of this.categories.entries()) {
                const categoryFiles = files.filter(file => file.category === catId);
                categoryStats[catId] = {
                    name: catData.name,
                    count: categoryFiles.length,
                    size: categoryFiles.reduce((sum, file) => sum + file.size, 0)
                };
            }

            return {
                totalFiles: files.length,
                totalSize,
                categoryStats,
                oldestFile: files.reduce((oldest, file) => 
                    !oldest || new Date(file.dateAdded) < new Date(oldest.dateAdded) ? file : oldest, null),
                newestFile: files.reduce((newest, file) => 
                    !newest || new Date(file.dateAdded) > new Date(newest.dateAdded) ? file : newest, null)
            };
        } catch (error) {
            console.error('❌ Error getting arsenal stats:', error);
            throw error;
        }
    }

    async startDesktopMonitoring() {
        if (!this.settings.fileMonitoring.enabled || this.desktopWatcher) {
            return;
        }

        try {
            const chokidar = require('chokidar');
            this.desktopWatcher = chokidar.watch(this.desktopPath, {
                ignoreInitial: true,
                persistent: true,
                depth: 0 // Only watch desktop, not subdirectories
            });

            this.desktopWatcher.on('add', (filePath) => {
                this.handleNewDesktopFile(filePath);
            });

            this.desktopWatcher.on('unlink', (filePath) => {
                this.handleRemovedDesktopFile(filePath);
            });

            console.log('✅ Desktop monitoring started');
        } catch (error) {
            console.error('❌ Error starting desktop monitoring:', error);
        }
    }

    stopDesktopMonitoring() {
        if (this.desktopWatcher) {
            this.desktopWatcher.close();
            this.desktopWatcher = null;
            console.log('✅ Desktop monitoring stopped');
        }
    }

    async handleNewDesktopFile(filePath) {
        try {
            const fileName = path.basename(filePath);
            const stats = await fs.stat(filePath);
            
            // Ignore if file is too small (likely temporary)
            if (stats.size < 1024) return;
            
            // Ignore certain file types that are usually temporary
            const ext = path.extname(fileName).toLowerCase();
            const ignoredExtensions = ['.tmp', '.temp', '.crdownload', '.part', '.cache'];
            if (ignoredExtensions.includes(ext)) return;
            
            // Log new file detection
            console.log(`📄 New desktop file detected: ${fileName}`);
            
            // Show notification
            await this.showFileNotification(filePath, fileName, stats);
        } catch (error) {
            console.error('Error handling new desktop file:', error);
        }
    }
    
    async showFileNotification(filePath, fileName, stats) {
        try {
            // Create notification window
            const { BrowserWindow, screen: electronScreen } = require('electron');
            const primaryDisplay = electronScreen.getPrimaryDisplay();
            const { width: screenWidth } = primaryDisplay.workAreaSize;
            
            const notificationWindow = new BrowserWindow({
                width: 400,
                height: 150,
                x: screenWidth - 420,
                y: 20,
                frame: false,
                transparent: true,
                alwaysOnTop: true,
                skipTaskbar: true,
                resizable: false,
                webPreferences: {
                    nodeIntegration: true,
                    contextIsolation: false
                }
            });
            
            // Create notification HTML
            const notificationHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body {
                            margin: 0;
                            padding: 0;
                            font-family: 'Inter', -apple-system, sans-serif;
                            background: rgba(17, 17, 17, 0.95);
                            border: 2px solid #00b4ff;
                            border-radius: 12px;
                            overflow: hidden;
                            backdrop-filter: blur(10px);
                            animation: slideIn 0.3s ease-out;
                        }
                        
                        @keyframes slideIn {
                            from {
                                transform: translateX(100%);
                                opacity: 0;
                            }
                            to {
                                transform: translateX(0);
                                opacity: 1;
                            }
                        }
                        
                        .notification-content {
                            padding: 20px;
                            color: #fff;
                        }
                        
                        .notification-header {
                            display: flex;
                            align-items: center;
                            margin-bottom: 15px;
                        }
                        
                        .notification-icon {
                            font-size: 24px;
                            margin-right: 12px;
                        }
                        
                        .notification-title {
                            font-size: 16px;
                            font-weight: 600;
                        }
                        
                        .notification-body {
                            font-size: 14px;
                            color: #a0a0a0;
                            margin-bottom: 15px;
                        }
                        
                        .notification-actions {
                            display: flex;
                            gap: 10px;
                        }
                        
                        .btn {
                            padding: 8px 16px;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 14px;
                            font-weight: 500;
                            transition: all 0.2s ease;
                        }
                        
                        .btn-primary {
                            background: #00b4ff;
                            color: white;
                        }
                        
                        .btn-primary:hover {
                            background: #0099e6;
                        }
                        
                        .btn-secondary {
                            background: #333;
                            color: white;
                        }
                        
                        .btn-secondary:hover {
                            background: #444;
                        }
                    </style>
                </head>
                <body>
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-icon">📄</div>
                            <div class="notification-title">New File Detected</div>
                        </div>
                        <div class="notification-body">
                            ${fileName} (${this.formatFileSize(stats.size)})
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-primary" onclick="addToArsenal()">Add to Arsenal</button>
                            <button class="btn btn-secondary" onclick="dismiss()">Dismiss</button>
                        </div>
                    </div>
                    <script>
                        const { ipcRenderer } = require('electron');
                        
                        function addToArsenal() {
                            ipcRenderer.send('desktop-arsenal-add-file-from-notification', '${filePath.replace(/\\/g, '\\\\')}');
                            window.close();
                        }
                        
                        function dismiss() {
                            window.close();
                        }
                        
                        // Auto-dismiss after duration
                        setTimeout(() => {
                            window.close();
                        }, ${this.settings.fileMonitoring.notificationDuration || 5000});
                    </script>
                </body>
                </html>
            `;
            
            notificationWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(notificationHTML)}`);
            
            // Store reference to close later if needed
            if (!this.notificationWindows) {
                this.notificationWindows = new Set();
            }
            this.notificationWindows.add(notificationWindow);
            
            notificationWindow.on('closed', () => {
                this.notificationWindows.delete(notificationWindow);
            });
            
        } catch (error) {
            console.error('Error showing file notification:', error);
        }
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async handleRemovedDesktopFile(filePath) {
        // Check if this file was in our arsenal and might have been restored
        const fileName = path.basename(filePath);
        console.log(`🗑️ Desktop file removed: ${fileName}`);
    }

    async addCustomCategory(categoryData) {
        try {
            const categoryId = categoryData.name.toLowerCase().replace(/\s+/g, '-');
            this.categories.set(categoryId, {
                ...categoryData,
                id: categoryId,
                custom: true,
                dateCreated: new Date().toISOString()
            });
            
            await this.saveFileData();
            console.log(`✅ Added custom category: ${categoryData.name}`);
            return categoryId;
        } catch (error) {
            console.error('❌ Error adding custom category:', error);
            throw error;
        }
    }

    async removeCustomCategory(categoryId) {
        try {
            const category = this.categories.get(categoryId);
            if (!category || !category.custom) {
                throw new Error('Cannot remove built-in category');
            }

            // Move all files in this category to 'other'
            for (const [fileId, fileData] of this.organizedFiles.entries()) {
                if (fileData.category === categoryId) {
                    fileData.category = 'other';
                    this.organizedFiles.set(fileId, fileData);
                }
            }

            this.categories.delete(categoryId);
            await this.saveFileData();
            console.log(`✅ Removed custom category: ${category.name}`);
        } catch (error) {
            console.error('❌ Error removing custom category:', error);
            throw error;
        }
    }

    async exportSettings() {
        try {
            const exportData = {
                settings: this.settings,
                categories: Array.from(this.categories.entries()),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };
            
            const exportPath = path.join(this.appDataPath, 'export_' + Date.now() + '.json');
            await fs.writeJson(exportPath, exportData, { spaces: 2 });
            
            console.log('✅ Settings exported successfully');
            return exportPath;
        } catch (error) {
            console.error('❌ Error exporting settings:', error);
            throw error;
        }
    }

    async importSettings(importPath) {
        try {
            const importData = await fs.readJson(importPath);
            
            if (importData.settings) {
                this.settings = { ...this.defaultSettings, ...importData.settings };
                await this.saveSettings();
            }
            
            if (importData.categories) {
                for (const [catId, catData] of importData.categories) {
                    if (catData.custom) {
                        this.categories.set(catId, catData);
                    }
                }
                await this.saveFileData();
            }
            
            console.log('✅ Settings imported successfully');
        } catch (error) {
            console.error('❌ Error importing settings:', error);
            throw error;
        }
    }

    async saveBackgroundImage(imageData) {
        try {
            console.log('saveBackgroundImage called with:', { 
                name: imageData?.name, 
                dataLength: imageData?.data?.length,
                dataPreview: imageData?.data?.substring(0, 100) 
            });
            
            const { name, data } = imageData;
            
            if (!data) {
                throw new Error('No data provided for background image');
            }
            
            // Generate unique filename
            const timestamp = Date.now();
            const ext = path.extname(name);
            const filename = `background_${timestamp}${ext}`;
            const filePath = path.join(this.backgroundsPath, filename);
            
            // Convert data URL to buffer
            let buffer;
            if (data.includes(',')) {
                // Standard data URL format
                const base64Data = data.split(',')[1];
                if (!base64Data) {
                    throw new Error('Invalid data URL format');
                }
                buffer = Buffer.from(base64Data, 'base64');
            } else if (data.startsWith('data:')) {
                throw new Error('Invalid data URL - missing comma separator');
            } else {
                // Assume it's already base64 data
                buffer = Buffer.from(data, 'base64');
            }
            
            // Save file
            await fs.writeFile(filePath, buffer);
            
            console.log(`✅ Background image saved: ${name} (as ${filename})`);
            return { path: filePath, originalName: name };
        } catch (error) {
            console.error('❌ Error saving background image:', error);
            throw error;
        }
    }
    
    async saveBackgroundFromPath(fileData) {
        try {
            const { sourcePath, name } = fileData;
            
            console.log('saveBackgroundFromPath called with:', { sourcePath, name });
            
            if (!sourcePath || !await fs.pathExists(sourcePath)) {
                throw new Error('Source file not found');
            }
            
            // Generate unique filename
            const timestamp = Date.now();
            const ext = path.extname(name);
            const filename = `background_${timestamp}${ext}`;
            const destPath = path.join(this.backgroundsPath, filename);
            
            // Copy file directly
            await fs.copy(sourcePath, destPath);
            
            console.log(`✅ Background file copied: ${name} (as ${filename})`);
            return { path: destPath, originalName: name };
        } catch (error) {
            console.error('❌ Error saving background from path:', error);
            throw error;
        }
    }
    
    async setAutoStart(enabled) {
        try {
            if (process.platform === 'win32') {
                const Registry = require('winreg');
                const appPath = process.execPath;
                const appName = 'ArmoryX';
                
                const regKey = new Registry({
                    hive: Registry.HKCU,
                    key: '\\Software\\Microsoft\\Windows\\CurrentVersion\\Run'
                });
                
                if (enabled) {
                    // Add registry entry with --startup flag
                    await new Promise((resolve, reject) => {
                        regKey.set(appName, Registry.REG_SZ, `"${appPath}" --startup`, (err) => {
                            if (err) reject(err);
                            else resolve();
                        });
                    });
                    console.log('✅ Auto-start enabled for Desktop Arsenal');
                } else {
                    // Remove registry entry
                    await new Promise((resolve, reject) => {
                        regKey.remove(appName, (err) => {
                            if (err) {
                                // Check if the error is because the key doesn't exist
                                if (err.message && err.message.includes('unable to find the specified registry key')) {
                                    console.log('⚠️ Registry key not found, nothing to remove');
                                    resolve();
                                } else {
                                    reject(err);
                                }
                            } else {
                                resolve();
                            }
                        });
                    });
                    console.log('✅ Auto-start disabled for Desktop Arsenal');
                }
                
                // Update settings
                this.settings.autoStart = enabled;
                await this.saveSettings();
                
                return true;
            } else {
                console.warn('⚠️ Auto-start is only supported on Windows');
                return false;
            }
        } catch (error) {
            console.error('❌ Error setting auto-start:', error);
            throw error;
        }
    }
    
    async checkAutoStart() {
        try {
            if (process.platform === 'win32') {
                const Registry = require('winreg');
                const appName = 'ArmoryX';
                
                const regKey = new Registry({
                    hive: Registry.HKCU,
                    key: '\\Software\\Microsoft\\Windows\\CurrentVersion\\Run'
                });
                
                return new Promise((resolve) => {
                    regKey.get(appName, (err, item) => {
                        if (err || !item) {
                            resolve(false);
                        } else {
                            resolve(true);
                        }
                    });
                });
            }
            return false;
        } catch (error) {
            console.error('❌ Error checking auto-start:', error);
            return false;
        }
    }
    
    async cleanupBackgrounds() {
        try {
            // Get current background path from settings
            const currentBackgroundPath = this.settings.overlay?.background;
            
            // List all files in backgrounds directory
            const backgroundFiles = await fs.readdir(this.backgroundsPath);
            
            // Find files to delete (all except the current one)
            const filesToDelete = [];
            for (const file of backgroundFiles) {
                const filePath = path.join(this.backgroundsPath, file);
                
                // Skip if this is the current background
                if (currentBackgroundPath && path.resolve(filePath) === path.resolve(currentBackgroundPath)) {
                    continue;
                }
                
                // Check if file exists and get its stats
                if (await fs.pathExists(filePath)) {
                    const stats = await fs.stat(filePath);
                    
                    // Delete files older than 7 days or if we have too many files
                    const isOld = (Date.now() - stats.mtime.getTime()) > (7 * 24 * 60 * 60 * 1000); // 7 days
                    const shouldDelete = isOld || backgroundFiles.length > 10; // Keep max 10 files
                    
                    if (shouldDelete) {
                        filesToDelete.push(filePath);
                    }
                }
            }
            
            // Delete the files
            let deletedCount = 0;
            let totalSize = 0;
            
            for (const filePath of filesToDelete) {
                try {
                    const stats = await fs.stat(filePath);
                    totalSize += stats.size;
                    await fs.remove(filePath);
                    deletedCount++;
                } catch (error) {
                    console.warn(`Failed to delete background file: ${filePath}`, error.message);
                }
            }
            
            if (deletedCount > 0) {
                const sizeMB = (totalSize / 1024 / 1024).toFixed(2);
                console.log(`✅ Cleaned up ${deletedCount} background files (${sizeMB} MB freed)`);
            }
            
            return { deletedCount, totalSize };
        } catch (error) {
            console.error('❌ Error cleaning up backgrounds:', error);
            return { deletedCount: 0, totalSize: 0 };
        }
    }
    
    async scheduleBackgroundCleanup() {
        // Auto-cleanup backgrounds every hour
        setInterval(async () => {
            await this.cleanupBackgrounds();
        }, 60 * 60 * 1000); // 1 hour
        
        // Also cleanup on startup
        setTimeout(async () => {
            await this.cleanupBackgrounds();
        }, 10000); // 10 seconds after startup
    }

    // Cleanup and shutdown
    cleanup() {
        this.unregisterHotkeys();
        this.stopDesktopMonitoring();
        
        if (this.overlayWindow) {
            this.overlayWindow.close();
        }
        
        // Close any open notification windows
        if (this.notificationWindows) {
            this.notificationWindows.forEach(window => {
                if (!window.isDestroyed()) {
                    window.close();
                }
            });
            this.notificationWindows.clear();
        }
        
        console.log('✅ Desktop Arsenal cleanup completed');
    }
}

module.exports = { DesktopArsenal };