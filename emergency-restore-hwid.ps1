# ========================================
#   EMERGENCY HWID RESTORE SCRIPT
#   FOR ARMORY X
# ========================================
# This script restores your original hardware identifiers
# Use ONLY if the normal restore function is not working

Write-Host "========================================" -ForegroundColor Yellow
Write-Host "   EMERGENCY HWID RESTORE SCRIPT" -ForegroundColor Red
Write-Host "   FOR ARMORY X" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "WARNING: This will restore your original hardware identifiers." -ForegroundColor Red
Write-Host "These values were captured from your test script results." -ForegroundColor Yellow
Write-Host ""

# Confirm with user
$confirmation = Read-Host "Do you want to proceed with emergency restore? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit
}

Write-Host ""
Write-Host "Restoring original HWID values..." -ForegroundColor Green

try {
    # Restore System Machine GUID
    Write-Host "1. Restoring System Machine GUID..." -ForegroundColor Cyan
    reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography" /v "MachineGuid" /t REG_SZ /d "9b25c922-95ef-463e-9945-abe4b9e9ec7f" /f
    
    # Restore Hardware Profile GUID
    Write-Host "2. Restoring Hardware Profile GUID..." -ForegroundColor Cyan
    reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001" /v "HwProfileGuid" /t REG_SZ /d "a5e79318-2ee9-4cf4-9812-7798f1b0e48c" /f
    
    # Restore Computer Hardware ID
    Write-Host "3. Restoring Computer Hardware ID..." -ForegroundColor Cyan
    reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation" /v "ComputerHardwareId" /t REG_SZ /d "90R8DDD7HXKL0EO9" /f
    
    # Restore Build GUID
    Write-Host "4. Restoring Build GUID..." -ForegroundColor Cyan
    reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v "BuildGUID" /t REG_SZ /d "1219d4c9-448e-4de5-b8aa-4a884e48b11b" /f
    
    # Restore Computer Hardware IDs
    Write-Host "5. Restoring Computer Hardware IDs..." -ForegroundColor Cyan
    reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation" /v "ComputerHardwareIds" /t REG_MULTI_SZ /d "03CPTO73111HLZ2X" /f
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   EMERGENCY RESTORE COMPLETED!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your original hardware identifiers have been restored." -ForegroundColor Green
    Write-Host "You may need to restart your computer for all changes to take effect." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "You can verify the restoration by running the test script:" -ForegroundColor Cyan
    Write-Host "  .\check-hwid-values.ps1" -ForegroundColor White
    
} catch {
    Write-Host ""
    Write-Host "ERROR: Failed to restore hardware identifiers!" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please ensure you are running this script as Administrator." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 