{"$schema": "../armory-x/node_modules/@tauri-apps/cli/schema.json", "productName": "Armory X Installer", "version": "1.0.0", "identifier": "com.armoryx.installer", "build": {"frontendDist": "frontend", "beforeDevCommand": "", "beforeBuildCommand": ""}, "bundle": {"active": true, "targets": ["nsis"], "icon": ["../armory-x/icons/icon.ico"], "windows": {"nsis": {"headerImage": "assets/header.bmp", "sidebarImage": "assets/sidebar.bmp"}}}, "app": {"windows": [{"label": "main", "title": "Armory X Setup", "width": 800, "height": 700, "resizable": false, "decorations": false, "center": true, "transparent": true, "alwaysOnTop": true, "shadow": false}]}}