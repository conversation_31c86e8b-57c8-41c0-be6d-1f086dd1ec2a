# Armory X Splash Screen & Login System

## Overview

The Armory X application now features a professional splash screen with login functionality that appears when the application starts. This provides a secure entry point and enhances the user experience with a modern, animated interface.

## Features

### 🎨 Visual Design
- **Modern Dark Theme**: Consistent with Armory X branding
- **Animated Background**: Floating particles, grid overlay, and animated orbs
- **Professional Typography**: Clean Inter font with gradient text effects
- **Responsive Design**: Works on different screen sizes
- **Smooth Animations**: Loading states, hover effects, and transitions

### 🔐 Authentication System
- **Login Form**: Username and password authentication
- **Remember Me**: Option to save login session
- **Password Toggle**: Show/hide password visibility
- **Demo Credentials**: Built-in test accounts for development
- **Loading States**: Visual feedback during login process

### 🚀 Loading Experience
- **Progress Tracking**: Real-time loading progress with percentage
- **Status Messages**: Step-by-step loading information
- **Smooth Transitions**: Seamless transition to main application
- **Connection Status**: System connection monitoring

## Demo Login Credentials

For testing purposes, you can use any of these credentials:

| Username | Password | Level |
|----------|----------|--------|
| `demo`   | `demo123` | User |
| `admin`  | `admin`   | Admin |
| `test`   | `test123` | User |
| `user`   | `password` | User |

## How to Use

### 1. Application Startup
1. Launch Armory X
2. The splash screen will appear automatically
3. Wait for the "Ready" connection status

### 2. Login Process
1. Enter your username and password
2. (Optional) Check "Remember me" to save your session
3. Click "Sign In" or press Enter
4. Wait for authentication to complete
5. The main application will load automatically

### 3. Quick Demo Login
- Click on the demo credentials box to auto-fill the form
- Or manually enter: `demo` / `demo123`

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `Enter` | Submit login form |
| `Escape` | Clear notifications |
| `F1` | Show help dialog |
| `F5` | Reload splash screen |
| `Ctrl+L` | Focus username field |
| `Ctrl+R` | Reload application |

## Technical Implementation

### Files Structure
```
├── splash.html          # Splash screen HTML
├── splash.css           # Splash screen styles
├── splash.js            # Splash screen logic
├── main.js              # Modified to show splash first
└── SPLASH_SCREEN_GUIDE.md # This documentation
```

### Authentication Flow
1. **Splash Window**: Creates frameless, transparent window
2. **User Input**: Validates credentials against demo accounts
3. **Authentication**: Simulates 1.5s API call with loading animation
4. **Success**: Sends IPC message to main process
5. **Main Window**: Creates main application window
6. **Cleanup**: Closes splash window and initializes app

### Session Management
- **Remember Me**: Stores encrypted auth token in localStorage
- **Auto-Login**: Automatically logs in returning users
- **Session Timeout**: Clears expired sessions
- **Secure Storage**: Uses Electron's secure storage when available

## Customization

### 1. Modify Demo Credentials
Edit the `authenticateUser` function in `splash.js`:

```javascript
const demoCredentials = [
    { username: 'your-user', password: 'your-pass' },
    // Add more accounts as needed
];
```

### 2. Change Visual Theme
Update CSS variables in `splash.css`:

```css
:root {
    --primary-color: #your-color;
    --background-dark: #your-bg;
    /* Modify other variables */
}
```

### 3. Add Real Authentication
Replace the `authenticateUser` function with real API calls:

```javascript
async function authenticateUser(username, password) {
    const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
    });
    return await response.json();
}
```

### 4. Modify Loading Steps
Update the `loadingSteps` array in `splash.js`:

```javascript
const loadingSteps = [
    { message: 'Your custom message...', duration: 1000 },
    // Add more steps as needed
];
```

## Security Considerations

### Current Implementation (Demo)
- ⚠️ **Demo Mode**: Uses hardcoded credentials for testing
- ⚠️ **Local Storage**: Stores tokens in browser storage
- ⚠️ **No Encryption**: Passwords are stored in plain text

### Production Recommendations
- 🔐 **Real Authentication**: Implement proper API authentication
- 🔐 **Token Security**: Use encrypted, time-limited tokens
- 🔐 **HTTPS**: Always use secure connections
- 🔐 **Password Hashing**: Never store plain text passwords
- 🔐 **Rate Limiting**: Prevent brute force attacks

## Troubleshooting

### Common Issues

**Splash screen doesn't appear:**
- Check if `splash.html` exists in the project root
- Verify Electron window creation in `main.js`
- Check console for JavaScript errors

**Login fails with valid credentials:**
- Verify credentials match those in `authenticateUser` function
- Check browser console for error messages
- Ensure IPC communication is working

**Main app doesn't load after login:**
- Check if `splash-login-complete` IPC event is properly handled
- Verify main window creation in `main.js`
- Look for errors in the main process console

**Remember me doesn't work:**
- Check if localStorage is available
- Verify auth data is properly saved/retrieved
- Clear browser data if corrupted

### Debug Mode
Enable debug logging by adding to `splash.js`:

```javascript
const DEBUG = true;
if (DEBUG) {
    console.log('Debug info:', data);
}
```

## Future Enhancements

### Planned Features
- [ ] Two-factor authentication support
- [ ] Social login integration (Google, Microsoft)
- [ ] Biometric authentication (fingerprint, face)
- [ ] Custom branding options
- [ ] Multi-language support
- [ ] Offline mode support

### Performance Optimizations
- [ ] Lazy loading of resources
- [ ] Reduced animation complexity option
- [ ] Memory usage optimization
- [ ] Faster startup times

## Support

For issues or questions about the splash screen:

1. Check this documentation first
2. Review the console for error messages
3. Verify all files are in the correct location
4. Test with demo credentials
5. Check the main process logs

---

**Note**: This splash screen system is designed for the Armory X Electron application. Modifications may be needed for different environments or authentication systems. 