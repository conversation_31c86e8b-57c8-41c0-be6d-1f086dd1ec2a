// Debug logging
console.log('🚀 Armory X Electron loading...');

// Global state
let isCleanupRunning = false;
let progressInterval = null;
let currentSettings = null;
let currentLicense = null;
let selectedGameId = null;
let ipcRenderer = null;

// Initialize Electron IPC if available
function initializeElectronIPC() {
    if (typeof require !== 'undefined') {
        try {
            const { ipcRenderer: electronIpcRenderer } = require('electron');
            
            // Set up global ipcRenderer for backward compatibility
            ipcRenderer = electronIpcRenderer;
            window.ipcRenderer = electronIpcRenderer;
            
            // Set up electronAPI for file/folder selection
            window.electronAPI = {
                selectFile: (options) => ipcRenderer.invoke('select-file', options),
                selectFolder: (options) => ipcRenderer.invoke('select-folder-dialog', options),
                openModFolder: (gameId) => ipcRenderer.invoke('open-mod-folder', gameId),
                getMods: (gameId) => ipcRenderer.invoke('get-mods-for-game', gameId),
                showInFolder: (filePath) => ipcRenderer.invoke('show-in-folder', filePath),
                deleteMod: (modPath) => ipcRenderer.invoke('delete-mod', modPath),
                extractIcon: (filePath) => ipcRenderer.invoke('extract-icon', filePath),
                getFileIcon: (filePath) => ipcRenderer.invoke('get-file-icon', filePath), // Alternative method
                openDownloadBrowser: (url) => ipcRenderer.invoke('open-download-browser', url),
                categorizeDownload: (data) => ipcRenderer.invoke('categorize-download', data),
                cancelDownload: (downloadId) => ipcRenderer.invoke('cancel-download', downloadId),
                getActiveDownloads: () => ipcRenderer.invoke('get-active-downloads'),
                openExternal: (url) => ipcRenderer.invoke('open-external', url),
                on: (channel, callback) => ipcRenderer.on(channel, (event, ...args) => callback(...args)),
                removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
            };
            
            // Set up window control handlers
            window.minimizeWindow = () => ipcRenderer.send('minimize-window');
            window.maximizeWindow = () => ipcRenderer.send('maximize-window');
            window.closeWindow = () => ipcRenderer.send('close-window');
            
            console.log('✅ Electron IPC initialized');
            console.log('📋 Available IPC functions:', Object.keys(window.electronAPI));
            
            // Test if icon extraction functions are available
            if (typeof window.electronAPI.extractIcon === 'function') {
                console.log('✅ extractIcon function available');
            } else {
                console.log('⚠️ extractIcon function not available');
            }
            
            if (typeof window.electronAPI.getFileIcon === 'function') {
                console.log('✅ getFileIcon function available');
            } else {
                console.log('⚠️ getFileIcon function not available');
            }
            
        } catch (error) {
            console.log('⚠️ Electron IPC not available:', error.message);
        }
    }
}

/**
 * Format bytes to human readable string
 * @param {number} bytes - Number of bytes
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Store custom games and settings in localStorage
let customGames = JSON.parse(localStorage.getItem('armoryXCustomGames') || '[]');
let modManagerSettings = JSON.parse(localStorage.getItem('armoryXModManagerSettings') || '{"hidePresetGames": false, "sortBy": "name", "filterBy": "all"}');

// Preset games definition
const presetGames = [
    {
        id: 'minecraft',
        name: 'Minecraft',
        description: 'Mods, Shaders & Resource Packs',
        icon: '🟫',
        image: 'assets/Games/minecraft_cover.jpg',
        isPreset: true,
        dateAdded: '2024-01-01',
        lastPlayed: null
    },
    {
        id: 'fs22',
        name: 'Farming Simulator 22',
        description: 'Vehicle & Equipment Mods',
        icon: '🚜',
        image: 'assets/Games/fs22_cover.jpg',
        isPreset: true,
        dateAdded: '2024-01-01',
        lastPlayed: null
    },
    {
        id: 'fs25',
        name: 'Farming Simulator 25',
        description: 'Vehicle & Equipment Mods',
        icon: '🚜',
        image: 'assets/Games/fs25_cover.jpg',
        isPreset: true,
        dateAdded: '2024-01-01',
        lastPlayed: null
    },
    {
        id: 'schedule1',
        name: 'Schedule 1',
        description: 'Drug Production & Business Mods',
        icon: '🧪',
        image: 'assets/Games/schedule1_cover.jpg',
        isPreset: true,
        dateAdded: '2024-01-01',
        lastPlayed: null
    }
];

// DOM Ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM Content Loaded - initializing app...');
    
    // Initialize Electron IPC first
    initializeElectronIPC();
    
    // Initialize download events
    initializeDownloadEvents();
    
    // Initialize the application
    initializeApp();
    setupEventListeners();
    setupKeyboardShortcuts();
    
    // Start system monitoring
    updateSystemStats();
    setInterval(updateSystemStats, 3000); // Update every 3 seconds
    
    // Handle responsive design
    handleResponsiveSidebar();
    window.addEventListener('resize', handleResponsiveSidebar);

    // Load settings on app start
    loadSettings();
    loadSystemInfo();
    
    console.log('✅ App initialization complete!');
});

// Initialize Application
function initializeApp() {
    console.log('🚀 Armory X Desktop initialized');
    
    // Set initial tab
    switchTab('dashboard');
    
    console.log('✅ Running in Electron environment');
}

// Setup Event Listeners
function setupEventListeners() {
    // Menu toggle
    document.getElementById('menu-toggle').addEventListener('click', toggleSidebar);

    // Navigation (handled by onclick attributes, but we can add this for robustness)
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', () => {
            // The onclick attribute handles the switch, this ensures the sidebar closes
            const sidebar = document.getElementById('sidebar');
            if (!sidebar.classList.contains('collapsed')) {
                // Add a small delay to allow the tab switch to feel responsive
                setTimeout(toggleSidebar, 50);
            }
        });
    });

    // Make tool functions globally accessible for HTML onclick handlers
    window.toggleAutoClicker = toggleAutoClicker;
    window.toggleAntiRecoil = toggleAntiRecoil;
    window.toggleKeySequence = toggleKeySequence;
    window.toggleHWIDSpoofer = toggleHWIDSpoofer;
    window.createHWIDBackup = createHWIDBackup;
    window.restoreHWIDValues = restoreHWIDValues;
    window.showHWIDInfo = showHWIDInfo;
    window.spoofMACAddresses = spoofMACAddresses;
    window.showComingSoonMessage = showComingSoonMessage;
}

// Sidebar Management
function toggleSidebar() {
    console.log('🍔 Toggle sidebar called');
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');
    
    if (sidebar && menuToggle) {
        sidebar.classList.toggle('collapsed');
        menuToggle.classList.toggle('open');
        console.log('✅ Sidebar toggled');
    } else {
        console.error('❌ Sidebar or menu toggle not found');
    }
}

// Tab Management
function switchTab(tabName) {
    console.log(`🔄 Switching to tab: ${tabName}`);
    
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Activate selected tab
    const activeNavItem = document.querySelector(`[onclick*="${tabName}"]`);
    const activeContent = document.getElementById(`${tabName}-tab`);
    
    if (activeNavItem) {
        activeNavItem.classList.add('active');
        
        // Update current tab indicator in header
        const tabDisplayName = activeNavItem.getAttribute('data-tab');
        const tabIndicator = document.getElementById('current-tab-name');
        if (tabIndicator && tabDisplayName) {
            tabIndicator.textContent = tabDisplayName;
        }
        console.log(`✅ Activated nav item for: ${tabName}`);
    } else {
        console.warn(`❌ Could not find nav item for: ${tabName}`);
    }
    
    if (activeContent) {
        activeContent.classList.add('active');
        console.log(`✅ Activated content for: ${tabName}`);
    } else {
        console.warn(`❌ Could not find content for: ${tabName}-tab`);
    }
    
    // Tab-specific initialization
    switch(tabName) {
        case 'dashboard':
            initDashboardTab();
            break;
        case 'cleanup':
            initCleanupTab();
            break;
        case 'modmanager':
            initModManagerTab();
            break;
        case 'tools':
            initToolsTab();
            break;
        case 'license':
            initLicenseTab();
            break;
        case 'settings':
            initSettingsTab();
            break;
        case 'account':
            initAccountTab();
            break;
        default:
            console.warn(`❌ Unknown tab: ${tabName}`);
    }
}

// System monitoring
async function updateSystemStats() {
    try {
        const stats = await ipcRenderer.invoke('get-system-stats');
        
        // Update sidebar stats
        const memoryElement = document.getElementById('memory-usage');
        const cpuElement = document.getElementById('cpu-usage');
        
        if (memoryElement && stats) {
            memoryElement.textContent = `${stats.memory}%`;
        }
        
        if (cpuElement && stats) {
            cpuElement.textContent = `${stats.cpu}%`;
        }
        
        // Update dashboard stats if visible
        const dashCpu = document.getElementById('dash-cpu');
        const dashMemory = document.getElementById('dash-memory');
        
        if (dashCpu && stats) {
            dashCpu.textContent = `${stats.cpu}%`;
        }
        
        if (dashMemory && stats) {
            dashMemory.textContent = `${stats.memory}%`;
        }
    } catch (error) {
        console.error('Failed to get system stats:', error);
    }
}

// Load system information
async function loadSystemInfo() {
    try {
        const systemInfo = await ipcRenderer.invoke('get-system-info');
        
        if (systemInfo) {
            const platformElement = document.getElementById('system-platform');
            const cpuElement = document.getElementById('system-cpu');
            const memoryElement = document.getElementById('system-memory');
            
            if (platformElement) {
                platformElement.textContent = `${systemInfo.os.platform} ${systemInfo.os.release}`;
            }
            
            if (cpuElement) {
                cpuElement.textContent = `${systemInfo.cpu.brand} (${systemInfo.cpu.cores} cores)`;
            }
            
            if (memoryElement) {
                const totalGB = Math.round(systemInfo.memory.total / (1024 * 1024 * 1024));
                memoryElement.textContent = `${totalGB} GB`;
            }
        }
    } catch (error) {
        console.error('Failed to get system info:', error);
    }
}

// Auto-collapse sidebar on mobile/small screens
function handleResponsiveSidebar() {
    const sidebar = document.getElementById('sidebar');
    const menuToggle = document.getElementById('menu-toggle');
    
    if (window.innerWidth < 768) {
        if (sidebar && !sidebar.classList.contains('collapsed')) {
            sidebar.classList.add('collapsed');
        }
        if (menuToggle && menuToggle.classList.contains('open')) {
            menuToggle.classList.remove('open');
        }
    }
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Prevent shortcuts when typing in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        // Navigation shortcuts
        if (e.key >= '1' && e.key <= '6') {
            const tabs = ['dashboard', 'cleanup', 'modmanager', 'tools', 'license', 'settings'];
            const tabIndex = parseInt(e.key) - 1;
            if (tabs[tabIndex]) {
                switchTab(tabs[tabIndex]);
            }
        }
        
        // Global shortcuts
        switch(e.key) {
            case 'F1':
                e.preventDefault();
                showHelpDialog();
                break;
            case 'F5':
                e.preventDefault();
                if (selectedGameId) {
                    refreshMods();
                } else {
                    showNotification('Switch to Mod Manager and select a game to refresh mods', 'info');
                }
                break;
            case 'F11':
                e.preventDefault();
                maximizeWindow();
                break;
            case 'F12':
                e.preventDefault();
                showDebugPanel();
                break;
            case 'Escape':
                e.preventDefault();
                const modal = document.querySelector('.custom-modal-overlay');
                if (modal) {
                    closeCustomModal();
                }
                break;
        }
        
        // Ctrl combinations
        if (e.ctrlKey) {
            switch(e.key) {
                case 'r':
                    e.preventDefault();
                    if (selectedGameId) {
                        refreshMods();
                    }
                    break;
                case 'd':
                    e.preventDefault();
                    showModDirectories();
                    break;
                case 't':
                    e.preventDefault();
                    if (selectedGameId) {
                        createTestMods();
                    } else {
                        showNotification('Please select a game first to create test mods', 'warning');
                    }
                    break;
                case 'm':
                    e.preventDefault();
                    minimizeWindow();
                    break;
                case 'q':
                    e.preventDefault();
                    closeWindow();
                    break;
            }
        }
    });
}

// Professional Help Dialog
function showHelpDialog() {
    showCustomModal({
        title: '⌨️ Keyboard Shortcuts & Help',
        content: `
            <div class="help-content">
                <div class="help-section">
                    <h4>Navigation</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <span class="shortcut-key">1-6</span>
                            <span class="shortcut-desc">Switch between tabs</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">ESC</span>
                            <span class="shortcut-desc">Close modal dialogs</span>
                        </div>
                    </div>
                </div>
                
                <div class="help-section">
                    <h4>Mod Manager</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <span class="shortcut-key">F5</span>
                            <span class="shortcut-desc">Refresh current game mods</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">Ctrl+R</span>
                            <span class="shortcut-desc">Refresh mods (alternative)</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">Ctrl+D</span>
                            <span class="shortcut-desc">Show mod directories info</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">Ctrl+T</span>
                            <span class="shortcut-desc">Create test mods</span>
                        </div>
                    </div>
                </div>
                
                <div class="help-section">
                    <h4>Window Controls</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <span class="shortcut-key">Ctrl+M</span>
                            <span class="shortcut-desc">Minimize window</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">F11</span>
                            <span class="shortcut-desc">Toggle maximize</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">Ctrl+Q</span>
                            <span class="shortcut-desc">Close application</span>
                        </div>
                    </div>
                </div>
                
                <div class="help-section">
                    <h4>Debug & Development</h4>
                    <div class="shortcut-list">
                        <div class="shortcut-item">
                            <span class="shortcut-key">F12</span>
                            <span class="shortcut-desc">Show debug panel</span>
                        </div>
                        <div class="shortcut-item">
                            <span class="shortcut-key">F1</span>
                            <span class="shortcut-desc">Show this help dialog</span>
                        </div>
                    </div>
                </div>
            </div>
        `,
        type: 'info',
        buttons: [
            {
                text: 'Close',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            }
        ]
    });
}

// Professional Debug Panel
function showDebugPanel() {
    const debugInfo = {
        version: '2.0.0',
        platform: navigator.platform,
        userAgent: navigator.userAgent.split(' ').slice(-2).join(' '),
        screenResolution: `${screen.width}x${screen.height}`,
        windowSize: `${window.innerWidth}x${window.innerHeight}`,
        selectedGame: selectedGameId || 'None',
        totalMods: document.querySelectorAll('.mod-item').length,
        lastRefresh: new Date().toLocaleString()
    };
    
    const debugContent = `
        <div class="debug-panel">
            <div class="debug-section">
                <h4>🔧 System Information</h4>
                <div class="debug-info-grid">
                    <div class="debug-info-item">
                        <span class="debug-label">Version:</span>
                        <span class="debug-value">${debugInfo.version}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Platform:</span>
                        <span class="debug-value">${debugInfo.platform}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Browser:</span>
                        <span class="debug-value">${debugInfo.userAgent}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Screen:</span>
                        <span class="debug-value">${debugInfo.screenResolution}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Window:</span>
                        <span class="debug-value">${debugInfo.windowSize}</span>
                    </div>
                </div>
            </div>
            
            <div class="debug-section">
                <h4>🎮 Mod Manager Status</h4>
                <div class="debug-info-grid">
                    <div class="debug-info-item">
                        <span class="debug-label">Selected Game:</span>
                        <span class="debug-value">${debugInfo.selectedGame}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Loaded Mods:</span>
                        <span class="debug-value">${debugInfo.totalMods}</span>
                    </div>
                    <div class="debug-info-item">
                        <span class="debug-label">Last Refresh:</span>
                        <span class="debug-value">${debugInfo.lastRefresh}</span>
                    </div>
                </div>
            </div>
            
            <div class="debug-section">
                <h4>🛠️ Debug Actions</h4>
                <div class="debug-actions">
                    <button class="btn btn-primary debug-action-btn" onclick="showModDirectories(); closeCustomModal();">
                        📁 Show Mod Directories
                    </button>
                    <button class="btn btn-secondary debug-action-btn" onclick="createTestMods(); closeCustomModal();" ${!selectedGameId ? 'disabled' : ''}>
                        🧪 Create Test Mods
                    </button>
                    <button class="btn btn-tertiary debug-action-btn" onclick="refreshMods(); closeCustomModal();" ${!selectedGameId ? 'disabled' : ''}>
                        🔄 Refresh Mods
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Account Page
function openAccountPage() {
    console.log('👤 Opening account page');
    switchTab('account');
}

// Make openAccountPage globally accessible
window.openAccountPage = openAccountPage;

// Window Controls
async function minimizeWindow() {
    console.log('🔽 Minimize window called');
    if (ipcRenderer) {
        ipcRenderer.send('minimize-window');
    } else {
        console.log('⚠️ Window controls not available');
    }
}

async function maximizeWindow() {
    console.log('📐 Maximize window called');
    if (ipcRenderer) {
        ipcRenderer.send('maximize-window');
    } else {
        console.log('⚠️ Window controls not available');
    }
}

async function closeWindow() {
    console.log('❌ Close window called');
    if (ipcRenderer) {
        ipcRenderer.send('close-window');
    } else {
        console.log('⚠️ Window controls not available');
    }
}

// Make window control functions globally accessible
window.minimizeWindow = minimizeWindow;
window.maximizeWindow = maximizeWindow;
window.closeWindow = closeWindow;

// === DASHBOARD TAB FUNCTIONS ===

function initDashboardTab() {
    console.log('🏠 Dashboard initialization delegated to dashboard.js');
    // The new widget-based dashboard is now handled by dashboard.js
}

// === CLEANUP TAB FUNCTIONS ===

function initCleanupTab() {
    updateCleanupStatus();
    updateDesktopArsenalStats();
}

async function startCleanup() {
    console.log('🧹 Start cleanup called');
    if (isCleanupRunning) return;
    
    isCleanupRunning = true;
    showLoadingState(true);
    updateCleanupStatus();
    
    try {
        // Start progress monitoring
        startProgressMonitoring();
        
        // Call Electron backend
        const result = await ipcRenderer.invoke('clean-junk-files');
        handleCleanupResult(result);
    } catch (error) {
        console.error('Cleanup failed:', error);
        showNotification('Cleanup failed: ' + error, 'error');
    } finally {
        isCleanupRunning = false;
        showLoadingState(false);
        stopProgressMonitoring();
        updateCleanupStatus();
    }
}

async function scanSystem() {
    console.log('🔍 Scan system called');
    showLoadingState(true);
    updateStatusIndicator('Scanning...', 'warning');
    
    try {
        // Simulate scan for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const demoResults = {
            files_found: 1250,
            size_estimate: '2.4 GB',
            categories: [
                { name: 'Temporary Files', count: 450, size: '890 MB' },
                { name: 'Browser Cache', count: 320, size: '740 MB' },
                { name: 'Gaming Cache', count: 280, size: '512 MB' },
                { name: 'System Cache', count: 200, size: '310 MB' }
            ]
        };
        
        showScanResults(demoResults);
        updateStatusIndicator('Scan Complete', 'success');
    } catch (error) {
        console.error('Scan failed:', error);
        updateStatusIndicator('Scan Failed', 'error');
    } finally {
        showLoadingState(false);
    }
}

function startProgressMonitoring() {
    progressInterval = setInterval(async () => {
        try {
            const progress = await ipcRenderer.invoke('get-cleanup-progress');
            updateProgressDisplay(progress);
        } catch (error) {
            console.error('Failed to get progress:', error);
        }
    }, 500);
}

function stopProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

function updateProgressDisplay(progress) {
    const progressFill = document.getElementById('progress-fill');
    const currentOperation = document.getElementById('current-operation');
    const progressStats = document.getElementById('progress-stats');
    
    if (progress) {
        // Calculate progress percentage
        const percentage = progress.is_running ? 
            Math.min((progress.files_processed / 1000) * 100, 95) : 100;
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (currentOperation) {
            currentOperation.textContent = progress.current_operation || 'Processing...';
        }
        
        if (progressStats) {
            const sizeText = formatFileSize(progress.bytes_freed);
            progressStats.textContent = `${progress.files_processed} files processed • ${sizeText} freed`;
        }
    }
}

function handleCleanupResult(result) {
    if (result.success) {
        const message = `✅ ${result.message}`;
        showNotification(message, 'success');
        updateStatusIndicator('Cleanup Complete', 'success');
        
        // Update final progress
        updateProgressDisplay({
            is_running: false,
            files_processed: result.files_deleted || 0,
            bytes_freed: result.bytes_freed || 0,
            current_operation: 'Complete'
        });
    } else {
        showNotification('❌ ' + result.message, 'error');
        updateStatusIndicator('Cleanup Failed', 'error');
    }
}

function updateCleanupStatus() {
    const statusText = isCleanupRunning ? 'Cleaning in progress...' : 'Ready to clean';
    const statusType = isCleanupRunning ? 'running' : 'ready';
    updateStatusIndicator(statusText, statusType);
}

function updateStatusIndicator(text, type) {
    const indicator = document.getElementById('status-indicator');
    if (indicator) {
        const dot = indicator.querySelector('.status-dot');
        const textElement = indicator.querySelector('.status-text');
        
        if (dot) {
            dot.className = `status-dot ${type}`;
        }
        
        if (textElement) {
            textElement.textContent = text;
        }
    }
}

// === DESKTOP ARSENAL FUNCTIONS ===

async function openDesktopArsenal() {
    console.log('🛡️ Opening Desktop Arsenal...');
    try {
        // TEMPORARILY DISABLED FOR TESTING - Show premium overlay first
        // showPremiumFeatureOverlay('Desktop Arsenal', 'Organize and hide your desktop files in a beautiful, searchable interface.');
        
        // ENABLED FOR TESTING - Uncomment when premium system is implemented
        await ipcRenderer.invoke('desktop-arsenal-toggle-overlay');
    } catch (error) {
        console.error('❌ Failed to open Desktop Arsenal:', error);
        showNotification('Failed to open Desktop Arsenal', 'error');
    }
}

async function updateDesktopArsenalStats() {
    try {
        const stats = await ipcRenderer.invoke('desktop-arsenal-get-stats');
        const statsElement = document.getElementById('arsenal-stats');
        
        if (statsElement && stats) {
            const fileCount = stats.totalFiles || 0;
            const sizeFormatted = formatFileSize(stats.totalSize || 0);
            statsElement.textContent = `${fileCount} files organized (${sizeFormatted})`;
        }
    } catch (error) {
        console.log('📊 Desktop Arsenal stats not available (premium feature)');
        const statsElement = document.getElementById('arsenal-stats');
        if (statsElement) {
            statsElement.textContent = '0 files organized';
        }
    }
}

function showPremiumFeatureOverlay(featureName, description) {
    showCustomModal({
        title: '👑 Premium Feature',
        content: `
            <div style="text-align: center; padding: 20px;">
                <div style="font-size: 4rem; margin-bottom: 20px;">🛡️</div>
                <h3 style="color: #ffd700; margin-bottom: 16px;">${featureName}</h3>
                <p style="margin-bottom: 24px; font-size: 16px; line-height: 1.6;">${description}</p>
                
                <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 12px; padding: 20px; margin: 20px 0;">
                    <h4 style="color: #ffd700; margin-bottom: 12px;">✨ Premium Features Include:</h4>
                    <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                        <li>🎯 Desktop widget for quick access</li>
                        <li>🔥 Global hotkey support (Ctrl+Space)</li>
                        <li>📁 Intelligent file categorization</li>
                        <li>🔍 Advanced search and filtering</li>
                        <li>🎨 Customizable themes and layouts</li>
                        <li>📊 File organization analytics</li>
                    </ul>
                </div>
                
                <p style="font-size: 14px; color: #a0a0a0; margin-top: 20px;">
                    Upgrade to Premium to unlock this and many other powerful features.
                </p>
            </div>
        `,
        type: 'info',
        buttons: [
            {
                text: '💳 Upgrade to Premium',
                class: 'btn-primary',
                action: () => {
                    closeCustomModal();
                    switchTab('license');
                }
            },
            {
                text: 'Maybe Later',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            }
        ]
    });
}

// === MOD MANAGER TAB FUNCTIONS ===

function initModManagerTab() {
    console.log('🎮 Initializing Mod Manager tab');
    
    // Initialize global variables
    if (!window.customGames) {
        window.customGames = [];
        console.log('🔧 Initialized empty customGames array');
    }
    
    // Try to load custom games from localStorage
    try {
        const savedGames = localStorage.getItem('armoryXCustomGames');
        if (savedGames) {
            window.customGames = JSON.parse(savedGames);
            console.log('✅ Loaded', window.customGames.length, 'custom games from localStorage');
        }
    } catch (error) {
        console.error('❌ Error loading custom games from localStorage:', error);
        window.customGames = [];
    }
    
    // Ensure customGames is accessible globally
    if (typeof customGames === 'undefined') {
        window.customGames = window.customGames || [];
    }
    
    // Only show initial view if we're not currently viewing a specific game
    const modContent = document.getElementById('mod-content');
    const mainView = document.getElementById('mod-manager-main');
    
    // Always clear any transition states when switching to this tab
    if (modContent) {
        modContent.style.transition = '';
        modContent.style.transform = '';
    }
    if (mainView) {
        mainView.style.transition = '';
        mainView.style.transform = '';
    }
    
    if (selectedGameId && modContent && modContent.classList.contains('active')) {
        // We're already viewing a game, don't reset to the main view
        console.log('🎮 Preserving current game view for:', selectedGameId);
        return;
    }
    
    showModManagerInitial();
}

function showModManagerInitial() {
    // Updated to show the new main library view
    const mainView = document.getElementById('mod-manager-main');
    const modContent = document.getElementById('mod-content');
    
    if (mainView) {
        mainView.style.display = 'block';
        mainView.style.opacity = '1';
        mainView.style.transition = '';
        mainView.classList.add('active');
    }
    
    if (modContent) {
        modContent.style.display = 'none';
        modContent.style.opacity = '0';
        modContent.style.transition = '';
        modContent.classList.remove('active');
    }
    
    // Load settings and populate the library
    loadModManagerSettings();
    populateGameLibrary();
}

function loadModManagerSettings() {
    // Load saved settings and apply them to UI
    const hidePresetCheckbox = document.getElementById('hide-preset-games');
    const sortSelect = document.getElementById('game-sort');
    const filterSelect = document.getElementById('game-filter');
    
    if (hidePresetCheckbox) hidePresetCheckbox.checked = modManagerSettings.hidePresetGames;
    if (sortSelect) sortSelect.value = modManagerSettings.sortBy;
    if (filterSelect) filterSelect.value = modManagerSettings.filterBy;
}

function saveModManagerSettings() {
    localStorage.setItem('armoryXModManagerSettings', JSON.stringify(modManagerSettings));
}

function populateGameLibrary(searchTerm = '') {
    const gamesGrid = document.getElementById('games-grid');
    if (!gamesGrid) return;
    
    console.log('🎮 Populating game library with search term:', searchTerm);
    
    // Convert custom games for display with proper icon handling
            const convertedCustomGames = customGames.map(game => {
        let iconPath = './assets/Games/steam_game.png'; // Default fallback
        let isSmallIcon = false; // Track if this is a small extracted icon
        
        if (game.iconPath) {
            if (game.iconPath.startsWith('data:image/')) {
                // Base64 data URL from extracted icon - likely small
                iconPath = game.iconPath;
                isSmallIcon = true;
            } else if (game.iconPath.startsWith('./') || game.iconPath.startsWith('/') || game.iconPath.includes(':')) {
                // Already a proper path (relative or absolute)
                iconPath = game.iconPath;
                // Check if it's an .ico file (likely small) or extracted icon path
                if (game.iconPath.toLowerCase().endsWith('.ico') || game.iconPath.includes('extracted_icon')) {
                    isSmallIcon = true;
                }
            } else if (game.iconPath.startsWith('assets/')) {
                // Fix old relative paths without ./
                iconPath = './' + game.iconPath;
            } else {
                // Assume it's a full path from file dialog
                iconPath = game.iconPath;
                // Check if it's an .ico file (likely small)
                if (game.iconPath.toLowerCase().endsWith('.ico')) {
                    isSmallIcon = true;
                }
            }
        }
        
        console.log('🎮 Game icon for', game.name, ':', iconPath, isSmallIcon ? '(small icon)' : '');
        
        return {
            id: game.id,
            name: game.name,
            description: 'Custom Game',
            image: iconPath,
            isSmallIcon: isSmallIcon, // Add flag for small icons
            isPreset: false,
            isCustom: true,
            lastPlayed: game.lastPlayed,
            dateAdded: game.dateAdded
        };
    });
    
    // Combine preset and custom games
    let allGames = [...presetGames, ...convertedCustomGames];
    
    // Filter games based on current settings
    let filteredGames = filterGamesList(allGames);
    
    // Apply search filter if provided
    if (searchTerm) {
        filteredGames = filteredGames.filter(game => 
            game.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }
    
    // Sort games
    filteredGames = sortGamesList(filteredGames);
    
    // Generate HTML for games
    if (filteredGames.length === 0) {
        gamesGrid.innerHTML = `
            <div class="no-games-message">
                <div class="no-games-icon">🎮</div>
                <h3>No games found</h3>
                <p>Try adjusting your filters or add some custom games to get started!</p>
            </div>
        `;
    } else {
        gamesGrid.innerHTML = filteredGames.map(game => `
            <div class="steam-game-card ${game.isPreset ? 'preset-game' : 'custom-game'} ${window.deleteMode && !game.isPreset ? 'delete-mode' : ''}" 
                 onclick="${window.deleteMode && !game.isPreset ? `handleGameDelete('${game.id}', '${game.name.replace(/'/g, "\\'")}')` : `selectGame('${game.id}')`}" 
                 data-game-id="${game.id}">
                <div class="game-image-container${game.isSmallIcon ? ' small-icon' : ''}">
                    <img src="${game.image}" alt="${game.name}" class="game-cover" 
                         onerror="this.src='./assets/Games/steam_game.png'; if(this.src.includes('steam_game.png')) this.style.opacity='0.7';">
                    <div class="game-overlay">
                        <div class="game-type-badge ${game.isPreset ? 'preset-badge' : 'custom-badge'}">
                            ${game.isPreset ? 'PRESET' : 'CUSTOM'}
                        </div>
                        ${window.deleteMode && !game.isPreset ? `
                            <div class="delete-mode-center-overlay">
                                <div class="delete-icon-large">🗑️</div>
                                <div class="delete-text-large">Click to Remove</div>
                            </div>
                        ` : `
                            ${!game.isPreset ? `
                                <div class="edit-button-pro" data-game-id="${game.id}">
                                    <span class="edit-icon">✏️</span>
                                    <span class="edit-text">Edit</span>
                                </div>
                            ` : ''}
                            <div class="play-button">
                                <span class="play-icon">▶</span>
                            </div>
                        `}
                    </div>
                </div>
                <div class="game-info">
                    <h4 class="game-title">${game.name}</h4>
                    <p class="game-description">${game.description || 'Custom Game'}</p>
                    ${game.lastPlayed ? `<span class="last-played">Last played: ${formatDate(game.lastPlayed)}</span>` : ''}
                </div>
            </div>
        `).join('');
        
        // Add event listeners for edit buttons after creating the HTML
        setTimeout(() => {
            document.querySelectorAll('.edit-button-pro').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const gameId = this.getAttribute('data-game-id');
                    console.log('🔧 Edit button clicked for game:', gameId);
                    
                    try {
                        editCustomGame(gameId);
                    } catch (error) {
                        console.error('❌ Error calling editCustomGame:', error);
                        showNotification('Error opening edit dialog: ' + error.message, 'error');
                    }
                });
            });
            
            console.log('✅ Edit button event listeners attached to', document.querySelectorAll('.edit-button-pro').length, 'buttons');
        }, 100); // Increased timeout to ensure DOM is ready
    }
    
    updateGameCount(filteredGames.length);
}

function filterGamesList(games) {
    let filtered = [...games];
    
    // Apply hide preset games setting
    if (modManagerSettings.hidePresetGames) {
        filtered = filtered.filter(game => !game.isPreset);
    }
    
    // Apply filter dropdown
    switch (modManagerSettings.filterBy) {
        case 'preset':
            filtered = filtered.filter(game => game.isPreset);
            break;
        case 'custom':
            filtered = filtered.filter(game => !game.isPreset);
            break;
        case 'all':
        default:
            // Show all (already filtered by hidePresetGames if needed)
            break;
    }
    
    return filtered;
}

function sortGamesList(games) {
    const sorted = [...games];
    
    switch (modManagerSettings.sortBy) {
        case 'name':
            return sorted.sort((a, b) => a.name.localeCompare(b.name));
        case 'recent':
            return sorted.sort((a, b) => {
                const aTime = a.lastPlayed ? new Date(a.lastPlayed).getTime() : 0;
                const bTime = b.lastPlayed ? new Date(b.lastPlayed).getTime() : 0;
                return bTime - aTime;
            });
        case 'dateAdded':
            return sorted.sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded));
        case 'type':
            return sorted.sort((a, b) => {
                if (a.isPreset && !b.isPreset) return -1;
                if (!a.isPreset && b.isPreset) return 1;
                return a.name.localeCompare(b.name);
            });
        default:
            return sorted;
    }
}

function sortGames() {
    const sortSelect = document.getElementById('game-sort');
    if (sortSelect) {
        modManagerSettings.sortBy = sortSelect.value;
        saveModManagerSettings();
        populateGameLibrary();
        console.log('🔄 Games sorted by:', modManagerSettings.sortBy);
    }
}

function filterGames() {
    const filterSelect = document.getElementById('game-filter');
    if (filterSelect) {
        modManagerSettings.filterBy = filterSelect.value;
        saveModManagerSettings();
        populateGameLibrary();
        console.log('🔍 Games filtered by:', modManagerSettings.filterBy);
    }
}

function togglePresetGames() {
    const hidePresetCheckbox = document.getElementById('hide-preset-games');
    if (hidePresetCheckbox) {
        modManagerSettings.hidePresetGames = hidePresetCheckbox.checked;
        saveModManagerSettings();
        populateGameLibrary();
        console.log('👁️ Preset games hidden:', modManagerSettings.hidePresetGames);
    }
}

function updateGameCount(count = null) {
    const gameCountElement = document.getElementById('game-count');
    if (!gameCountElement) return;
    
    if (count === null) {
        // Calculate the count based on current filters
        let allGames = [...presetGames, ...customGames];
        let filteredGames = filterGamesList(allGames);
        count = filteredGames.length;
    }
    
    const totalGames = presetGames.length + customGames.length;
    const gameText = count === 1 ? 'game' : 'games';
    const totalText = totalGames === 1 ? 'game' : 'games';
    
    if (count === totalGames) {
        gameCountElement.innerHTML = `<span>${count} ${gameText} in library</span>`;
    } else {
        gameCountElement.innerHTML = `<span>${count} of ${totalGames} ${totalText} shown</span>`;
    }
}

// Game library search functions
function filterGameLibrary() {
    const searchInput = document.getElementById('game-search');
    const clearButton = document.getElementById('game-search-clear');
    
    if (searchInput) {
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        // Show/hide clear button with smooth animation
        if (clearButton) {
            if (searchTerm) {
                clearButton.style.display = 'block';
                clearButton.style.opacity = '1';
            } else {
                clearButton.style.opacity = '0';
                setTimeout(() => {
                    if (!searchInput.value.trim()) {
                        clearButton.style.display = 'none';
                    }
                }, 150);
            }
        }
        
        // Re-populate library with search filter
        populateGameLibrary(searchTerm);
    }
}

function clearGameSearch() {
    const searchInput = document.getElementById('game-search');
    const clearButton = document.getElementById('game-search-clear');
    
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus(); // Keep focus for better UX
    }
    
    if (clearButton) {
        clearButton.style.opacity = '0';
        setTimeout(() => clearButton.style.display = 'none', 150);
    }
    
    // Re-populate library without search filter
    populateGameLibrary();
}

function backToGameLibrary() {
    // Clear the selected game ID
    selectedGameId = null;
    
    // Clear mod data
    window.currentModsByCategory = null;
    window.currentModsTotal = 0;
    
    const mainView = document.getElementById('mod-manager-main');
    const modContent = document.getElementById('mod-content');
    
    if (mainView && modContent) {
        // Clear any existing transitions
        mainView.style.transition = '';
        modContent.style.transition = '';
        animateViewTransition(modContent, mainView, 'slide-out-right', 'slide-in-left');
    }
    
    populateGameLibrary();
}

// === SIMPLIFIED VIEW TRANSITION ===
let isTransitioning = false;

function animateViewTransition(fromEl, toEl, outClass = 'slide-out-left', inClass = 'slide-in-right') {
    if (!fromEl || !toEl || isTransitioning) return;
    
    isTransitioning = true;
    console.log('🎬 Simple transition from', fromEl.id, 'to', toEl.id);

    // Immediately hide destination and show source
    toEl.style.display = 'none';
    toEl.style.opacity = '0';
    toEl.classList.remove('active');
    
    fromEl.style.display = 'block';
    fromEl.style.opacity = '1';
    fromEl.classList.add('active');
    
    // Simple fade out source, then fade in destination
    fromEl.style.transition = 'opacity 0.3s ease';
    fromEl.style.opacity = '0';
    
    setTimeout(() => {
        // Hide source completely
        fromEl.style.display = 'none';
        fromEl.classList.remove('active');
        
        // Show destination
        toEl.style.display = 'block';
        toEl.style.transition = 'opacity 0.3s ease';
        toEl.classList.add('active');
        
        // Use requestAnimationFrame for smooth opacity change
        requestAnimationFrame(() => {
            toEl.style.opacity = '1';
            
            // Initialize particles if needed
            if (toEl.id === 'mod-content' && !document.getElementById('mod-content-particles')) {
                injectParticlesOnModContent();
            }
        });
        
        // Reset transition state
        setTimeout(() => {
            isTransitioning = false;
            // Clean up inline styles
            fromEl.style.transition = '';
            toEl.style.transition = '';
            console.log('🎬 Transition completed');
        }, 300);
        
    }, 300);
}
// === END VIEW TRANSITION ===

// --- Inject tsParticles for beautiful background effect ---
function injectParticlesOnModContent() {
    if (!window.tsParticles) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/tsparticles@3.3.0/tsparticles.bundle.min.js';
        script.async = true;
        script.onload = () => setupModContentParticles();
        document.head.appendChild(script);
    } else {
        setupModContentParticles();
    }
}

function setupModContentParticles() {
    const modContent = document.getElementById('mod-content');
    if (!modContent) return;
    if (document.getElementById('mod-content-particles')) return;
    const particlesDiv = document.createElement('div');
    particlesDiv.id = 'mod-content-particles';
    particlesDiv.style.position = 'absolute';
    particlesDiv.style.top = 0;
    particlesDiv.style.left = 0;
    particlesDiv.style.width = '100%';
    particlesDiv.style.height = '100%';
    particlesDiv.style.zIndex = 0;
    particlesDiv.style.pointerEvents = 'none';
    modContent.prepend(particlesDiv);
    window.tsParticles.load('mod-content-particles', {
        fullScreen: { enable: false },
        background: { color: 'transparent' },
        particles: {
            number: { value: 40 },
            color: { value: ['#3b82f6', '#06b6d4', '#fff'] },
            shape: { type: 'circle' },
            opacity: { value: 0.18 },
            size: { value: { min: 2, max: 6 } },
            move: { enable: true, speed: 0.7, direction: 'none', random: true, straight: false, outModes: { default: 'out' } },
            links: { enable: true, distance: 120, color: '#3b82f6', opacity: 0.12, width: 1 }
        },
        interactivity: { events: { onHover: { enable: false }, onClick: { enable: false } } },
        detectRetina: true
    });
}

/**
 * Initialize download event listeners
 */
function initializeDownloadEvents() {
    if (!window.electronAPI) return;
    
    // Listen for download events
    window.electronAPI.onDownloadStarted = (callback) => {
        window.electronAPI.on('download-started', callback);
    };
    
    window.electronAPI.onDownloadProgress = (callback) => {
        window.electronAPI.on('download-progress', callback);
    };
    
    window.electronAPI.onDownloadCompleted = (callback) => {
        window.electronAPI.on('download-completed', callback);
    };
    
    window.electronAPI.onDownloadError = (callback) => {
        window.electronAPI.on('download-error', callback);
    };
    
    window.electronAPI.onDownloadCancelled = (callback) => {
        window.electronAPI.on('download-cancelled', callback);
    };
    
    window.electronAPI.onShowDownloadCategorization = (callback) => {
        window.electronAPI.on('show-download-categorization', callback);
    };
    
    window.electronAPI.onDownloadInstalled = (callback) => {
        window.electronAPI.on('download-installed', callback);
    };
    
    window.electronAPI.onDownloadFailed = (callback) => {
        window.electronAPI.on('download-failed', callback);
    };
    
    // Set up download event handlers
    if (window.electronAPI.onDownloadStarted) {
        window.electronAPI.onDownloadStarted((data) => {
            console.log('📥 Download started:', data);
            showDownloadProgressNotification(data);
        });
    }
    
    if (window.electronAPI.onDownloadProgress) {
        window.electronAPI.onDownloadProgress((data) => {
            updateDownloadProgress(data);
        });
    }
    
    if (window.electronAPI.onDownloadCompleted) {
        window.electronAPI.onDownloadCompleted((data) => {
            console.log('✅ Download completed:', data);
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onDownloadError) {
        window.electronAPI.onDownloadError((data) => {
            console.error('❌ Download error:', data);
            showNotification(`Download failed: ${data.error}`, 'error');
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onDownloadCancelled) {
        window.electronAPI.onDownloadCancelled((data) => {
            console.log('🚫 Download cancelled:', data);
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onShowDownloadCategorization) {
        window.electronAPI.onShowDownloadCategorization((data) => {
            console.log('🎯 Showing categorization dialog:', data);
            showDownloadCategorizationDialog(data);
        });
    }
    
    if (window.electronAPI.onDownloadInstalled) {
        window.electronAPI.onDownloadInstalled((data) => {
            console.log('✅ Download installed:', data);
            showNotification(`✅ ${data.filename} installed as ${data.category}! ${data.installedMods ? `(${data.installedMods} file${data.installedMods !== 1 ? 's' : ''})` : ''}`, 'success');
            
            // Refresh mods if we're currently viewing Minecraft mods
            if (selectedGameId === 'minecraft') {
                setTimeout(() => refreshMods(), 1000);
            }
        });
    }
    
    if (window.electronAPI.onDownloadFailed) {
        window.electronAPI.onDownloadFailed((data) => {
            console.error('❌ Download failed:', data);
            showNotification(`❌ Download failed: ${data.filename} (${data.reason})`, 'error');
        });
    }
}

/**
 * Show download progress notification
 * @param {Object} downloadInfo - Download information
 */
function showDownloadProgressNotification(downloadInfo) {
    const progressId = `download-progress-${downloadInfo.id}`;
    
    // Create progress notification container if it doesn't exist
    let progressContainer = document.getElementById('download-progress-container');
    if (!progressContainer) {
        progressContainer = document.createElement('div');
        progressContainer.id = 'download-progress-container';
        progressContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            width: 350px;
            pointer-events: none;
        `;
        document.body.appendChild(progressContainer);
    }
    
    // Create progress notification
    const progressElement = document.createElement('div');
    progressElement.id = progressId;
    progressElement.style.cssText = `
        background: linear-gradient(135deg, #1f2937, #111827);
        border: 2px solid rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        color: #f9fafb;
        font-size: 0.9rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        pointer-events: auto;
        animation: slideInRight 0.3s ease;
    `;
    
    progressElement.innerHTML = `
        <div class="download-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
            <span class="download-title" style="font-weight: 600; color: #3b82f6;">📥 Downloading</span>
            <button onclick="cancelDownload('${downloadInfo.id}')" style="
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                color: #fca5a5;
                border-radius: 4px;
                padding: 0.2rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
            ">Cancel</button>
        </div>
        <div class="download-filename" style="color: #e2e8f0; margin-bottom: 0.5rem; word-break: break-all;">
            ${downloadInfo.filename}
        </div>
        <div class="download-progress-bar" style="
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        ">
            <div class="progress-fill" style="
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #3b82f6, #6366f1);
                border-radius: 4px;
                transition: width 0.3s ease;
            "></div>
        </div>
        <div class="download-stats" style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #94a3b8;">
            <span class="download-percentage">0%</span>
            <span class="download-speed">Calculating...</span>
        </div>
    `;
    
    progressContainer.appendChild(progressElement);
    
    // Auto-hide after 30 seconds if still showing
    setTimeout(() => {
        if (document.getElementById(progressId)) {
            hideDownloadProgress(downloadInfo.id);
        }
    }, 30000);
}

/**
 * Update download progress
 * @param {Object} downloadInfo - Download information
 */
function updateDownloadProgress(downloadInfo) {
    const progressId = `download-progress-${downloadInfo.id}`;
    const progressElement = document.getElementById(progressId);
    
    if (!progressElement) return;
    
    const progressFill = progressElement.querySelector('.progress-fill');
    const percentageSpan = progressElement.querySelector('.download-percentage');
    const speedSpan = progressElement.querySelector('.download-speed');
    
    if (progressFill) {
        progressFill.style.width = `${downloadInfo.progress}%`;
    }
    
    if (percentageSpan) {
        percentageSpan.textContent = `${downloadInfo.progress}%`;
    }
    
    if (speedSpan && downloadInfo.speed > 0) {
        const speedMB = (downloadInfo.speed / (1024 * 1024)).toFixed(1);
        const timeRemaining = downloadInfo.timeRemaining > 0 ? 
            ` - ${Math.round(downloadInfo.timeRemaining)}s remaining` : '';
        speedSpan.textContent = `${speedMB} MB/s${timeRemaining}`;
    }
}

/**
 * Hide download progress notification
 * @param {string} downloadId - Download ID
 */
function hideDownloadProgress(downloadId) {
    const progressId = `download-progress-${downloadId}`;
    const progressElement = document.getElementById(progressId);
    
    if (progressElement) {
        progressElement.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (progressElement.parentNode) {
                progressElement.parentNode.removeChild(progressElement);
            }
        }, 300);
    }
}

/**
 * Cancel a download
 * @param {string} downloadId - Download ID to cancel
 */
async function cancelDownload(downloadId) {
    try {
        if (window.electronAPI && window.electronAPI.cancelDownload) {
            await window.electronAPI.cancelDownload(downloadId);
            showNotification('Download cancelled', 'info');
        }
    } catch (error) {
        console.error('❌ Error cancelling download:', error);
        showNotification('Failed to cancel download', 'error');
    }
}

/**
 * Show download categorization dialog
 * @param {Object} data - Categorization data
 */
function showDownloadCategorizationDialog(data) {
    const { downloadInfo, suggestedCategory } = data;
    
    const content = `
        <div class="categorization-container">
            <div class="file-info">
                <div class="file-icon">📦</div>
                <div class="file-details">
                    <h3 class="file-name">${downloadInfo.filename}</h3>
                    <p class="file-size">${formatBytes(downloadInfo.totalBytes)}</p>
                </div>
            </div>
            
            <div class="categorization-section">
                <p class="categorization-text">Please select what type of file this is so Armory X can install it to the correct location:</p>
                
                <div class="category-options">
                    <label class="category-option ${suggestedCategory === 'Mods' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Mods" ${suggestedCategory === 'Mods' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">🧩</span>
                            <div class="option-text">
                                <strong>Mod</strong>
                                <small>Game modification (.jar files)</small>
                            </div>
                        </div>
                    </label>
                    
                    <label class="category-option ${suggestedCategory === 'Resource Packs' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Resource Packs" ${suggestedCategory === 'Resource Packs' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">🎨</span>
                            <div class="option-text">
                                <strong>Resource Pack</strong>
                                <small>Texture and sound packs</small>
                            </div>
                        </div>
                    </label>
                    
                    <label class="category-option ${suggestedCategory === 'Shaders' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Shaders" ${suggestedCategory === 'Shaders' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">✨</span>
                            <div class="option-text">
                                <strong>Shader Pack</strong>
                                <small>Visual enhancement shaders</small>
                            </div>
                        </div>
                    </label>
                </div>
                
                ${suggestedCategory ? `
                    <div class="ai-suggestion">
                        <span class="suggestion-icon">🤖</span>
                        <span class="suggestion-text">AI suggests: <strong>${suggestedCategory}</strong> (pre-selected)</span>
                    </div>
                ` : ''}
            </div>
        </div>
        
        <style>
            .categorization-container {
                padding: 1rem 0;
            }
            
            .file-info {
                display: flex;
                align-items: center;
                padding: 1.5rem;
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
                border-radius: 12px;
                border: 1px solid rgba(59, 130, 246, 0.2);
                margin-bottom: 2rem;
            }
            
            .file-icon {
                font-size: 3rem;
                margin-right: 1rem;
            }
            
            .file-name {
                color: #f8fafc;
                margin: 0 0 0.5rem 0;
                font-size: 1.2rem;
                font-weight: 600;
                word-break: break-all;
            }
            
            .file-size {
                color: #94a3b8;
                margin: 0;
                font-size: 0.9rem;
            }
            
            .categorization-text {
                color: #e2e8f0;
                margin-bottom: 1.5rem;
                line-height: 1.5;
            }
            
            .category-options {
                display: flex;
                flex-direction: column;
                gap: 0.8rem;
                margin-bottom: 1.5rem;
            }
            
            .category-option {
                display: block;
                cursor: pointer;
                padding: 1rem;
                background: linear-gradient(135deg, #1f2937, #111827);
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                transition: all 0.3s ease;
            }
            
            .category-option:hover {
                border-color: rgba(59, 130, 246, 0.3);
                background: linear-gradient(135deg, #374151, #1f2937);
            }
            
            .category-option.suggested {
                border-color: rgba(16, 185, 129, 0.5);
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
            }
            
            .category-option input[type="radio"] {
                display: none;
            }
            
            .category-option input[type="radio"]:checked + .option-content {
                color: #3b82f6;
            }
            
            .option-content {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            
            .option-icon {
                font-size: 1.5rem;
            }
            
            .option-text strong {
                display: block;
                color: #f8fafc;
                font-size: 1rem;
                margin-bottom: 0.2rem;
            }
            
            .option-text small {
                color: #94a3b8;
                font-size: 0.85rem;
            }
            
            .ai-suggestion {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.8rem;
                background: rgba(16, 185, 129, 0.1);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: 6px;
                color: #d1fae5;
                font-size: 0.9rem;
            }
            
            .suggestion-icon {
                font-size: 1.1rem;
            }
            
            .suggestion-text strong {
                color: #10b981;
            }
        </style>
    `;
    
    showCustomModal({
        title: '🎯 Categorize Downloaded File',
        content: content,
        type: 'info',
        customClass: 'download-categorization-modal',
        buttons: [
            {
                text: 'Install',
                class: 'btn-primary',
                action: () => installCategorizedDownload(downloadInfo)
            },
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    closeCustomModal();
                    cancelDownload(downloadInfo.id);
                }
            }
        ]
    });
}

/**
 * Install categorized download
 * @param {Object} downloadInfo - Download information
 */
async function installCategorizedDownload(downloadInfo) {
    try {
        // Get selected category
        const selectedCategory = document.querySelector('input[name="file-category"]:checked');
        
        if (!selectedCategory) {
            showNotification('Please select a category first', 'warning');
            return;
        }
        
        const category = selectedCategory.value;
        console.log('📦 Installing download with category:', category);
        
        // Close the modal
        closeCustomModal();
        
        // Show installing notification
        showNotification(`Installing ${downloadInfo.filename} as ${category}...`, 'info');
        
        // Send categorization to main process
        if (window.electronAPI && window.electronAPI.categorizeDownload) {
            const result = await window.electronAPI.categorizeDownload({
                downloadInfo: downloadInfo,
                category: category
            });
            
            if (!result.success) {
                showNotification(`❌ Installation failed: ${result.message}`, 'error');
            }
            // Success notification will be sent via download-installed event
        } else {
            showNotification('❌ Installation not available in demo mode', 'error');
        }
        
    } catch (error) {
        console.error('❌ Error installing categorized download:', error);
        showNotification('❌ Failed to install download', 'error');
    }
}

// Updated selectGame function to include animation
async function selectGame(gameId) {
    console.log('🎮 Selecting game:', gameId);
    
    // Clear any existing mod data first to prevent stale data issues
    window.currentModsByCategory = null;
    window.currentModsTotal = 0;
    
    // Clear the mod display immediately
    const modGrid = document.getElementById('mod-grid');
    if (modGrid) {
        modGrid.innerHTML = `
            <div class="loading-message">
                <div class="loading-spinner"></div>
                <p>Loading mods...</p>
            </div>
        `;
    }
    
    // Update mod count to 0 while loading
    updateModCount(0);
    
    // Set the selected game ID
    selectedGameId = gameId;
    
    // Update last played time for the game
    const gameIndex = customGames.findIndex(g => g.id === gameId);
    if (gameIndex !== -1) {
        customGames[gameIndex].lastPlayed = new Date().toISOString();
        localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
    }
    
    // Hide main library view with animation and show mod content
    const mainView = document.getElementById('mod-manager-main');
    const modContent = document.getElementById('mod-content');
    if (mainView && modContent) {
        // Clear any existing transitions
        mainView.style.transition = '';
        modContent.style.transition = '';
        animateViewTransition(mainView, modContent, 'slide-out-left', 'slide-in-right');
    }
    
    // Update the selected game title
    const titleElement = document.getElementById('selected-game-title');
    const gameName = getGameDisplayName(gameId);
    if (titleElement) {
        titleElement.textContent = `🎮 ${gameName} Mods`;
    }
    
    // Show/hide Minecraft-specific features
    const minecraftModSetsSection = document.getElementById('minecraft-modsets-section');
    const minecraftOnlyButtons = document.querySelectorAll('.minecraft-only');
    
    if (gameId === 'minecraft') {
        // Set data attribute for CSS targeting
        if (modContent) {
            modContent.setAttribute('data-game', 'minecraft');
        }
        
        // Show Minecraft Mod Sets section
        if (minecraftModSetsSection) {
            minecraftModSetsSection.style.display = 'block';
        }
        
        // Show Minecraft-only buttons
        minecraftOnlyButtons.forEach(btn => {
            btn.style.display = 'flex';
        });
        
        // Load and display current Mod Sets information
        loadModSets();
    } else {
        // Hide Minecraft-specific elements for other games
        if (modContent) {
            modContent.removeAttribute('data-game');
        }
        
        if (minecraftModSetsSection) {
            minecraftModSetsSection.style.display = 'none';
        }
        
        minecraftOnlyButtons.forEach(btn => {
            btn.style.display = 'none';
        });
    }
    
    // Clear search and filters
    const searchInput = document.getElementById('mod-search');
    const clearButton = document.getElementById('mod-search-clear');
    if (searchInput) {
        searchInput.value = '';
        searchInput.style.borderColor = '';
        searchInput.style.boxShadow = '';
    }
    if (clearButton) {
        clearButton.style.display = 'none';
    }
    
    try {
        // Get mods for the selected game
        let modsData;
        
        if (window.electronAPI && window.electronAPI.getMods) {
            console.log('📡 Using Electron API to get mods');
            modsData = await window.electronAPI.getMods(gameId);
        } else if (ipcRenderer) {
            console.log('📡 Using IPC Renderer to get mods');
            modsData = await ipcRenderer.invoke('get-mods-for-game', gameId);
        } else {
            console.log('🌐 No Electron API available, using demo data');
            modsData = { mods: [], total: 0, isDemo: true };
        }
        
        console.log('📦 Received mods data:', modsData);
        
        if (modsData && modsData.mods) {
            displayMods(modsData.mods, gameId);
            
            if (modsData.mods.length > 0) {
                showNotification(`Loaded ${modsData.mods.length} mods for ${gameName}`, 'success');
            } else if (modsData.isDemo) {
                showNotification(`Demo: No mods found for ${gameName}`, 'info');
            } else {
                showNotification(`No mods found for ${gameName}`, 'info');
            }
        } else {
            console.error('❌ Invalid mods data received');
            displayMods([], gameId);
            showNotification(`Failed to load mods for ${gameName}`, 'error');
        }
        
    } catch (error) {
        console.error('❌ Error loading mods:', error);
        
        // Show error state in mod grid instead of empty array
        const modGrid = document.getElementById('mod-grid');
        if (modGrid) {
            modGrid.innerHTML = `
                <div class="error-message" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                    <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                    <h3>Failed to Load Mods</h3>
                    <p>Error: ${error.message}</p>
                    <div style="margin-top: 20px;">
                        <button class="btn btn-primary" onclick="refreshModsWithTransition('${gameId}')" style="margin-right: 8px;">
                            <span class="btn-icon">🔄</span>
                            Retry
                        </button>
                        <button class="btn btn-secondary" onclick="backToGameLibrary()">
                            <span class="btn-icon">←</span>
                            Back to Library
                        </button>
                    </div>
                </div>
            `;
        }
        
        // Update mod count to show error state
        updateModCount(0);
        
        showNotification(`Error loading mods: ${error.message}`, 'error');
    }
}

// Missing button functions
function showAddGameDialog() {
    console.log('➕ Showing add game dialog');
    
    // Ensure customGames array is initialized
    if (!customGames) {
        console.log('🔧 Initializing customGames array');
        customGames = [];
    }
    
    const addGameContent = `
        <div class="add-game-form">
            <div class="form-section">
                <label for="custom-game-path">Game Executable Location:</label>
                <div class="path-input-group">
                    <input type="text" id="custom-game-path" class="modal-input" placeholder="Click Browse to select..." readonly />
                    <button class="btn btn-secondary" onclick="browseGameExecutable()">Browse</button>
                </div>
                <p class="form-help">Select the main executable file or shortcut for your game</p>
            </div>
            
            <div class="form-section">
                <label for="custom-game-name">Game Name:</label>
                <input type="text" id="custom-game-name" class="modal-input" placeholder="Enter game name..." />
            </div>
            
            <div class="form-section">
                <label for="custom-game-icon">Game Icon (Optional):</label>
                <div class="path-input-group">
                    <input type="text" id="custom-game-icon" class="modal-input" placeholder="Using default icon..." readonly />
                    <button class="btn btn-secondary" onclick="browseGameIcon()">Browse</button>
                </div>
                <div class="icon-preview" id="icon-preview" style="display: none; margin-top: 10px;">
                    <img id="icon-preview-img" src="" alt="Icon Preview" style="width: 32px; height: 32px; border-radius: 4px; border: 1px solid #374151;">
                    <span style="margin-left: 8px; color: #9ca3af; font-size: 12px;">Icon Preview</span>
                </div>
                <p class="form-help">💡 <strong>Tip:</strong> Icon extraction may not work for all files. Use Browse to manually select a custom .png, .jpg, or .ico icon file if needed.</p>
            </div>
            
            <div class="form-section">
                <label>Mod Folders:</label>
                <p class="form-help">Add folders where mods for this game will be stored</p>
                <div id="custom-mod-folders">
                    <div class="mod-folder-item">
                        <input type="text" class="modal-input" placeholder="Folder name (e.g., Mods)" data-type="name" />
                        <div class="path-input-group">
                            <input type="text" class="modal-input" placeholder="Click Browse to select folder..." readonly data-type="path" />
                            <button class="btn btn-secondary" onclick="browseModFolder(this)">Browse</button>
                        </div>
                        <button class="btn btn-danger" onclick="removeModFolder(this)" style="display: none;">Remove</button>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="addModFolder()">+ Add Another Folder</button>
            </div>
            
            <div class="file-types-section">
                <label>Supported File Types:</label>
                <div class="file-types-dropdown" id="file-types-dropdown">
                    <div class="dropdown-header" onclick="toggleFileTypesDropdown()">
                        <span class="selected-text">All Types</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="dropdown-options" id="file-types-options">
                        <div class="file-type-option">
                            <input type="checkbox" id="type-all" value="all" checked>
                            <span>All Types (*.* - All file types)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-zip" value=".zip">
                            <span>.zip files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-rar" value=".rar">
                            <span>.rar files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-7z" value=".7z">
                            <span>.7z files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-jar" value=".jar">
                            <span>.jar files (Java/Minecraft)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-exe" value=".exe">
                            <span>.exe files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-dll" value=".dll">
                            <span>.dll files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-pak" value=".pak">
                            <span>.pak files (Unreal Engine)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-mod" value=".mod">
                            <span>.mod files</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    try {
        showCustomModal({
            title: 'Add Custom Game',
            content: addGameContent,
            type: 'info',
            preventOutsideClose: true, // Prevent accidental closing
            buttons: [
                {
                    text: 'Cancel',
                    class: 'btn-secondary',
                    action: () => closeCustomModal()
                },
                {
                    text: 'Add Game',
                    class: 'btn-primary',
                    action: () => saveCustomGame()
                }
            ]
        });
        
        console.log('✅ Add game modal opened successfully');
    } catch (error) {
        console.error('❌ Error showing add game modal:', error);
        showNotification('Error opening add game dialog: ' + error.message, 'error');
    }
}

function showRemoveGameDialog() {
    console.log('🗑️ Toggling delete mode');
    toggleDeleteMode();
}

// New function to handle game deletion in remove mode
function handleGameDelete(gameId, gameName) {
    console.log('🗑️ Attempting to delete game:', gameId, gameName);
    confirmDeleteGame(gameId, gameName);
}

function showModManagerHelp() {
    console.log('❓ Showing mod manager help');
    
    try {
        showCustomModal({
            title: '🎮 Mod Manager Help & Guide',
            content: `
                <div class="help-content">
                    <div class="help-section">
                        <h4>🎯 Getting Started</h4>
                        <p>The Mod Manager helps you organize and manage game modifications. You can use preset games or add your own custom games.</p>
                    </div>
                    
                    <div class="help-section">
                        <h4>🎮 Preset Games</h4>
                        <p>Built-in support for popular games:</p>
                        <ul>
                            <li><strong>Minecraft</strong> - Mods, Shaders, Resource Packs</li>
                            <li><strong>Farming Simulator 22</strong> - Vehicle & Equipment Mods</li>
                            <li><strong>Schedule 1</strong> - Drug Production & Business Mods</li>
                        </ul>
                    </div>
                    
                    <div class="help-section">
                        <h4>➕ Adding Custom Games</h4>
                        <p>Click "Add Game" to add your own games:</p>
                        <ol>
                            <li>Enter the game name</li>
                            <li>Select the game executable file</li>
                            <li>Choose a custom icon (optional)</li>
                            <li>Add mod folder locations</li>
                            <li>Select supported file types for mods</li>
                        </ol>
                    </div>
                    
                    <div class="help-section">
                        <h4>✏️ Editing Custom Games</h4>
                        <p>To edit a custom game:</p>
                        <ul>
                            <li>Hover over any custom game card</li>
                            <li>Click the "Edit" button that appears</li>
                            <li>Modify settings and click "Save Changes"</li>
                        </ul>
                    </div>
                    
                    <div class="help-section">
                        <h4>🗑️ Removing Custom Games</h4>
                        <p>To remove custom games:</p>
                        <ol>
                            <li>Click the "Remove" button in the top toolbar</li>
                            <li>This activates Remove Mode (button turns green)</li>
                            <li>Click on any custom game card to remove it</li>
                            <li>Click "Exit Remove Mode" when finished</li>
                        </ol>
                        <p><strong>Note:</strong> Only custom games can be removed. Preset games are protected.</p>
                    </div>
                    
                    <div class="help-section">
                        <h4>🔍 Sorting & Filtering</h4>
                        <p>Use the controls to organize your library:</p>
                        <ul>
                            <li><strong>Sort by:</strong> Name, Recently Played, Date Added, Type</li>
                            <li><strong>Filter:</strong> All Games, Preset Only, Custom Only</li>
                            <li><strong>Hide Preset:</strong> Focus on just your custom games</li>
                            <li><strong>Search:</strong> Type in the search bar to find specific games</li>
                        </ul>
                    </div>
                    
                    <div class="help-section">
                        <h4>🔧 Managing Mods</h4>
                        <p>Click on any game to manage its mods:</p>
                        <ul>
                            <li>View all installed mods organized by category</li>
                            <li>Add new mods by dragging files or using the "Add Mods" button</li>
                            <li>Search through your mod collection</li>
                            <li>Open mod folders directly from the interface</li>
                            <li>Delete unwanted mods with confirmation dialogs</li>
                        </ul>
                    </div>
                    
                    <div class="help-section">
                        <h4>🎯 Tips & Tricks</h4>
                        <ul>
                            <li>Use descriptive names for your custom games</li>
                            <li>Organize mods into appropriate folders</li>
                            <li>Regular cleanup helps maintain good performance</li>
                            <li>Backup important mods before making changes</li>
                            <li>Use the search function to quickly find specific mods</li>
                        </ul>
                    </div>
                    
                    <div class="help-section">
                        <h4>⚠️ Troubleshooting</h4>
                        <p>If you encounter issues:</p>
                        <ul>
                            <li>Make sure game executables are accessible</li>
                            <li>Check that mod folder paths are correct</li>
                            <li>Verify file permissions for mod directories</li>
                            <li>Restart the application if buttons stop responding</li>
                        </ul>
                    </div>
                </div>
            `,
            type: 'info',
            buttons: [
                {
                    text: 'Close',
                    class: 'btn-secondary',
                    action: () => closeCustomModal()
                }
            ]
        });
        
        console.log('✅ Help modal opened successfully');
    } catch (error) {
        console.error('❌ Error showing help modal:', error);
        showNotification('Error opening help dialog: ' + error.message, 'error');
    }
}

function confirmRemoveGame(gameId, gameName) {
    showCustomModal({
        title: 'Confirm Removal',
        content: `
            <div class="delete-game-content">
                <div class="warning-icon">⚠️</div>
                <div class="delete-message">
                    <p>Are you sure you want to remove <strong>"${gameName}"</strong>?</p>
                    <p>This will only remove the game configuration from Armory X. Your actual game files and mods will not be deleted.</p>
                </div>
            </div>
        `,
        type: 'warning',
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Remove Game',
                class: 'btn-danger',
                action: () => removeCustomGame(gameId, gameName)
            }
        ]
    });
}

function removeCustomGame(gameId, gameName) {
    console.log('🗑️ Removing custom game:', gameId, gameName);
    
    // Ensure customGames array exists
    if (!customGames || !Array.isArray(customGames)) {
        console.error('❌ customGames array not found');
        showNotification('Error: Custom games data not found', 'error');
        return;
    }
    
    const gameIndex = customGames.findIndex(g => g.id === gameId);
    
    if (gameIndex !== -1) {
        // Remove the game from the array
        const removedGame = customGames.splice(gameIndex, 1)[0];
        
        // Save updated list to localStorage
        try {
            localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
            console.log('✅ Updated localStorage after removing game');
        } catch (error) {
            console.error('❌ Error saving to localStorage:', error);
        }
        
        // Exit delete mode after successful deletion
        if (window.deleteMode) {
            toggleDeleteMode();
        }
        
        // Refresh the game library display
        populateGameLibrary();
        
        // Close any open modal
        closeCustomModal();
        
        // Show success notification
        showNotification(`Game "${gameName}" has been removed successfully!`, 'success');
        console.log('✅ Custom game removed successfully:', removedGame);
    } else {
        console.error('❌ Game not found in customGames array:', gameId);
        showNotification('Error: Game not found', 'error');
    }
}

// Helper functions for add game dialog
function addModFolder() {
    const container = document.getElementById('custom-mod-folders');
    if (!container) return;
    
    const newFolder = document.createElement('div');
    newFolder.className = 'mod-folder-item';
    newFolder.innerHTML = `
        <input type="text" class="modal-input" placeholder="Folder name (e.g., Shaders)" data-type="name" />
        <div class="path-input-group">
            <input type="text" class="modal-input" placeholder="Click Browse to select folder..." readonly data-type="path" />
            <button class="btn btn-secondary" onclick="browseModFolder(this)">Browse</button>
        </div>
        <button class="btn btn-danger" onclick="removeModFolder(this)">Remove</button>
    `;
    
    container.appendChild(newFolder);
    
    // Show remove button on all items now
    container.querySelectorAll('.btn-danger').forEach(btn => {
        btn.style.display = 'inline-block';
    });
}

function removeModFolder(button) {
    const folderItem = button.closest('.mod-folder-item');
    const container = document.getElementById('custom-mod-folders');
    
    if (folderItem && container) {
        folderItem.remove();
        
        // Hide remove button if only one item left
        const remaining = container.querySelectorAll('.mod-folder-item');
        if (remaining.length === 1) {
            const removeBtn = remaining[0].querySelector('.btn-danger');
            if (removeBtn) removeBtn.style.display = 'none';
        }
    }
}

function browseGameExecutable() {
    // For Electron environment, use IPC to open native file dialog
    if (window.electronAPI && window.electronAPI.selectFile) {
        window.electronAPI.selectFile({
            filters: [
                { name: 'Executable Files', extensions: ['exe'] },
                { name: 'Shortcuts', extensions: ['lnk'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            properties: ['openFile']
        }).then(async result => {
            if (result && !result.canceled && result.filePaths && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];
                const pathInput = document.getElementById('custom-game-path');
                const nameInput = document.getElementById('custom-game-name');
                const iconInput = document.getElementById('custom-game-icon');
                
                pathInput.value = selectedPath;
                
                // Auto-fill game name if empty
                if (!nameInput.value.trim()) {
                    const fileName = selectedPath.split(/[/\\]/).pop();
                    let gameName = fileName.replace(/\.[^/.]+$/, ''); // Remove extension
                    
                    // Clean up common patterns in game names
                    gameName = gameName.replace(/[-_]/g, ' ') // Replace dashes/underscores with spaces
                                 .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize words
                    
                    nameInput.value = gameName;
                }
                
                // Debug available Electron API functions
                console.log('🔍 Available Electron API functions:', Object.keys(window.electronAPI || {}));
                
                // Try multiple approaches for icon handling
                await handleIconExtraction(selectedPath, iconInput);
            }
        }).catch(error => {
            console.error('Error selecting file:', error);
            showNotification('Error selecting file: ' + error.message, 'error');
        });
    } else {
        // Fallback for web or environments without native file dialog
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.exe,.lnk'; // Accept both executables and shortcuts
        fileInput.style.display = 'none';
        
        fileInput.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const pathInput = document.getElementById('custom-game-path');
                const iconInput = document.getElementById('custom-game-icon');
                
                // In web mode, we can only get the filename
                pathInput.value = file.name;
                pathInput.title = 'Web mode: Only filename available. Use desktop app for full paths.';
                
                // Auto-fill game name if empty
                const nameInput = document.getElementById('custom-game-name');
                if (!nameInput.value.trim()) {
                    let gameName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
                    
                    // Clean up common patterns in game names
                    gameName = gameName.replace(/[-_]/g, ' ') // Replace dashes/underscores with spaces
                                 .replace(/\b\w/g, l => l.toUpperCase()); // Capitalize words
                    
                    nameInput.value = gameName;
                }
                
                // Web mode icon handling
                iconInput.value = '';
                iconInput.placeholder = 'Web mode: Use Browse button to select custom icon';
                showNotification('Please use the Browse button to select a custom icon if desired', 'info');
            }
            document.body.removeChild(fileInput);
        };
        
        document.body.appendChild(fileInput);
        fileInput.click();
    }
}

// New function to handle icon extraction with multiple fallback approaches
async function handleIconExtraction(selectedPath, iconInput) {
    const iconPreview = document.getElementById('icon-preview');
    const iconPreviewImg = document.getElementById('icon-preview-img');
    
    console.log('🎨 Starting icon extraction for:', selectedPath);
    
    // Check for Xbox/Windows Store games (problematic)
    const isXboxGame = selectedPath.toLowerCase().includes('windowsapps') ||
                      selectedPath.toLowerCase().includes('microsoft.') ||
                      selectedPath.toLowerCase().includes('xbox') ||
                      selectedPath.toLowerCase().includes('packages\\') ||
                      selectedPath.includes('C:\\Program Files\\WindowsApps');
    
    if (isXboxGame) {
        console.log('🎮 Xbox/Windows Store game detected - using special handling');
        iconInput.value = './assets/Games/xbox_game.png';
        iconInput.placeholder = 'Xbox/Windows Store game detected';
        
        if (iconPreview && iconPreviewImg) {
            iconPreviewImg.src = './assets/Games/xbox_game.png';
            iconPreview.style.display = 'flex';
            iconPreview.style.alignItems = 'center';
        }
        
        showNotification('Xbox/Windows Store game detected - using Xbox controller icon', 'info');
        
        // Store that this is an Xbox game for later confirmation
        iconInput.dataset.isXboxGame = 'true';
        return;
    }
    
    // Check for Steam SHORTCUTS only (not actual executables in Steam directories)
    const isSteamShortcut = selectedPath.toLowerCase().endsWith('.url') && 
                           selectedPath.toLowerCase().includes('steam');
    
    if (isSteamShortcut) {
        console.log('🎮 Steam shortcut detected - using controller icon');
        iconInput.value = './assets/Games/steam_game.png';
        iconInput.placeholder = 'Steam shortcut detected - controller icon';
        
        if (iconPreview && iconPreviewImg) {
            iconPreviewImg.src = './assets/Games/steam_game.png';
            iconPreview.style.display = 'flex';
            iconPreview.style.alignItems = 'center';
        }
        
        showNotification('Steam shortcut detected - using controller icon', 'info');
        
        // Store that this is a Steam shortcut for later confirmation
        iconInput.dataset.isSteamGame = 'true';
        return;
    }
    
    // Method 1: Try Electron API extraction for non-Steam games
    if (window.electronAPI && typeof window.electronAPI.extractIcon === 'function') {
        try {
            console.log('🔧 Attempting Electron API icon extraction...');
            const iconResult = await window.electronAPI.extractIcon(selectedPath);
            
                    if (iconResult && iconResult.success && iconResult.iconData) {
            iconInput.value = iconResult.iconData;
            iconInput.placeholder = 'Icon extracted from file';
            
            // Mark as small icon for extracted icons
            iconInput.dataset.isSmallIcon = 'true';
            
            if (iconPreview && iconPreviewImg) {
                iconPreviewImg.src = iconResult.iconData;
                iconPreview.style.display = 'flex';
                iconPreview.style.alignItems = 'center';
            }
            
            console.log('✅ Successfully extracted icon via Electron API (marked as small)');
            showNotification('Icon extracted successfully!', 'success');
            return; // Success, exit early
            } else {
                console.log('⚠️ Electron API extraction returned no usable icon');
            }
        } catch (error) {
            console.error('❌ Electron API extraction failed:', error);
        }
    } else {
        console.log('⚠️ Electron API extractIcon function not available');
    }
    
    // Method 2: Try file path based approach for .ico files
    if (selectedPath.toLowerCase().endsWith('.ico')) {
        try {
            console.log('🖼️ Attempting direct .ico file usage...');
            iconInput.value = selectedPath;
            iconInput.placeholder = 'Using .ico file directly';
            
            if (iconPreview && iconPreviewImg) {
                iconPreviewImg.src = `file://${selectedPath}`;
                iconPreview.style.display = 'flex';
                iconPreview.style.alignItems = 'center';
            }
            
            console.log('✅ Using .ico file directly');
            showNotification('Using .ico file as game icon', 'success');
            return;
        } catch (error) {
            console.error('❌ Direct .ico file usage failed:', error);
        }
    }
    
    // Method 3: Try Windows shell integration (if available)
    if (window.electronAPI && typeof window.electronAPI.getFileIcon === 'function') {
        try {
            console.log('🔧 Attempting Windows shell icon extraction...');
            const iconResult = await window.electronAPI.getFileIcon(selectedPath);
            
            if (iconResult && iconResult.success && iconResult.iconData) {
                iconInput.value = iconResult.iconData;
                iconInput.placeholder = 'Icon extracted via shell';
                
                if (iconPreview && iconPreviewImg) {
                    iconPreviewImg.src = iconResult.iconData;
                    iconPreview.style.display = 'flex';
                    iconPreview.style.alignItems = 'center';
                }
                
                console.log('✅ Successfully extracted icon via shell');
                showNotification('Icon extracted via Windows shell!', 'success');
                return;
            }
        } catch (error) {
            console.error('❌ Shell icon extraction failed:', error);
        }
    }
    
    // Method 4: Smart defaults based on file type/name
    const fileName = selectedPath.split(/[/\\]/).pop().toLowerCase();
    let defaultIcon = null;
    
    // Check for common game patterns
    if (fileName.includes('minecraft') || selectedPath.includes('minecraft')) {
        defaultIcon = './assets/Games/minecraft_cover.jpg';
    } else if (fileName.includes('epic') || selectedPath.includes('epic')) {
        defaultIcon = './assets/Games/steam_game.png'; // Use generic controller for Epic
    }
    
    if (defaultIcon) {
        iconInput.value = defaultIcon;
        iconInput.placeholder = 'Using smart default icon';
        
        if (iconPreview && iconPreviewImg) {
            iconPreviewImg.src = defaultIcon;
            iconPreview.style.display = 'flex';
            iconPreview.style.alignItems = 'center';
        }
        
        console.log('✅ Using smart default icon based on path');
        showNotification('Using default icon based on game type detected', 'info');
        return;
    }
    
    // Method 5: Ultimate fallback - suggest manual icon selection
    console.log('📝 All automatic methods failed, suggesting manual selection');
    iconInput.value = '';
    iconInput.placeholder = 'No icon found - use Browse to select custom icon';
    
    if (iconPreview) {
        iconPreview.style.display = 'none';
    }
    
    const fileType = selectedPath.endsWith('.lnk') ? 'shortcut' : 'executable';
    showNotification(`${fileType} selected - please use Browse button to add a custom icon if desired`, 'info');
}

function browseGameIcon() {
    console.log('🖼️ Browse game icon clicked');
    
    if (window.electronAPI && window.electronAPI.selectFile) {
        window.electronAPI.selectFile({
            title: 'Select Game Icon',
            filters: [
                { name: 'Image Files', extensions: ['png', 'jpg', 'jpeg', 'ico', 'gif', 'svg'] },
                { name: 'Icon Files', extensions: ['ico'] },
                { name: 'PNG Images', extensions: ['png'] },
                { name: 'JPEG Images', extensions: ['jpg', 'jpeg'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        }).then(result => {
            if (result && result.filePaths && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];
                const iconInput = document.getElementById('custom-game-icon');
                const iconPreview = document.getElementById('icon-preview');
                const iconPreviewImg = document.getElementById('icon-preview-img');
                
                console.log('✅ Icon file selected:', selectedPath);
                
                // Set the icon path
                iconInput.value = selectedPath;
                iconInput.placeholder = 'Custom icon selected';
                
                // Mark .ico files as small icons
                if (selectedPath.toLowerCase().endsWith('.ico')) {
                    iconInput.dataset.isSmallIcon = 'true';
                    console.log('🎯 Marked .ico file as small icon');
                }
                
                // Show preview
                if (iconPreview && iconPreviewImg) {
                    iconPreviewImg.src = `file://${selectedPath}`;
                    iconPreview.style.display = 'flex';
                    iconPreview.style.alignItems = 'center';
                    
                    // Handle image load errors
                    iconPreviewImg.onerror = function() {
                        console.log('⚠️ Could not preview selected icon file');
                        iconPreview.style.display = 'none';
                        showNotification('Icon selected but preview unavailable', 'info');
                    };
                    
                    iconPreviewImg.onload = function() {
                        console.log('✅ Icon preview loaded successfully');
                        showNotification('Custom icon selected successfully!', 'success');
                    };
                } else {
                    // No preview available, just confirm selection
                    showNotification('Custom icon selected successfully!', 'success');
                }
            }
        }).catch(error => {
            console.error('Error selecting icon:', error);
            showNotification('Error selecting icon: ' + error.message, 'error');
        });
    } else {
        // Fallback for web or environments without native file dialog
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*,.ico'; // Accept image files and .ico
        fileInput.style.display = 'none';
        
        fileInput.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                const iconInput = document.getElementById('custom-game-icon');
                const iconPreview = document.getElementById('icon-preview');
                const iconPreviewImg = document.getElementById('icon-preview-img');
                
                console.log('✅ Icon file selected via web dialog:', file.name);
                
                // In web mode, we need to create a blob URL for the file
                const reader = new FileReader();
                reader.onload = function(e) {
                    const dataUrl = e.target.result;
                    
                    iconInput.value = file.name; // Just store filename in web mode
                    iconInput.placeholder = 'Custom icon selected (web mode)';
                    iconInput.title = 'Web mode: Icon data stored locally';
                    
                    // Show preview
                    if (iconPreview && iconPreviewImg) {
                        iconPreviewImg.src = dataUrl;
                        iconPreview.style.display = 'flex';
                        iconPreview.style.alignItems = 'center';
                        
                        iconPreviewImg.onload = function() {
                            console.log('✅ Icon preview loaded successfully');
                            showNotification('Custom icon selected successfully!', 'success');
                        };
                        
                        iconPreviewImg.onerror = function() {
                            console.log('⚠️ Could not preview selected icon file');
                            iconPreview.style.display = 'none';
                            showNotification('Icon selected but preview unavailable', 'info');
                        };
                    } else {
                        showNotification('Custom icon selected successfully!', 'success');
                    }
                    
                    // Store the data URL for later use
                    iconInput.dataset.iconData = dataUrl;
                };
                
                reader.readAsDataURL(file);
            }
            document.body.removeChild(fileInput);
        };
        
        document.body.appendChild(fileInput);
        fileInput.click();
    }
}

function browseModFolder(button) {
    // For Electron environment, use IPC to open native folder dialog
    if (window.electronAPI && window.electronAPI.selectFolder) {
        window.electronAPI.selectFolder({
            properties: ['openDirectory']
        }).then(result => {
            if (result && !result.canceled && result.filePaths && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];
                const folderItem = button.closest('.mod-folder-item');
                const pathInput = folderItem.querySelector('[data-type="path"]');
                
                pathInput.value = selectedPath;
                
                console.log('✅ Selected mod folder:', selectedPath);
            }
        }).catch(error => {
            console.error('Error selecting folder:', error);
            showNotification('Error selecting folder: ' + error.message, 'error');
        });
    } else {
        // Fallback for web environments (limited functionality)
        const folderInput = document.createElement('input');
        folderInput.type = 'file';
        folderInput.webkitdirectory = true;
        folderInput.style.display = 'none';
        
        folderInput.onchange = function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                const folderItem = button.closest('.mod-folder-item');
                const pathInput = folderItem.querySelector('[data-type="path"]');
                
                // Get the folder path from the first file
                const firstFile = files[0];
                const folderPath = firstFile.webkitRelativePath.split('/')[0];
                
                pathInput.value = folderPath;
                pathInput.title = 'Web mode: Only relative path available. Use desktop app for full paths.';
                
                console.log('📁 Selected folder (web mode):', folderPath);
            }
            document.body.removeChild(folderInput);
        };
        
        document.body.appendChild(folderInput);
        folderInput.click();
    }
}

// Save new custom game
function saveCustomGame() {
    console.log('💾 Saving new custom game');
    
    const name = document.getElementById('custom-game-name').value.trim();
    const executablePath = document.getElementById('custom-game-path').value.trim();
    const iconPath = document.getElementById('custom-game-icon').value.trim();
    const iconInput = document.getElementById('custom-game-icon');
    
    if (!name) {
        showNotification('Please enter a game name', 'warning');
        return;
    }
    
    if (!executablePath) {
        showNotification('Please select the game executable', 'warning');
        return;
    }
    
    // Collect selected file types from the form
    const fileTypeCheckboxes = document.querySelectorAll('#file-types-options input[type="checkbox"]:checked');
    let supportedFileTypes = Array.from(fileTypeCheckboxes).map(cb => cb.value);
    
    // Handle "All Types" selection - expand to include all common extensions
    if (supportedFileTypes.includes('all')) {
        supportedFileTypes = ['.zip', '.rar', '.7z', '.jar', '.exe', '.dll', '.pak', '.mod', '.msi', '.deb', '.rpm', '.tar', '.gz', '.bz2', '.xz', '.lzma', '.cab', '.iso', '.dmg', '.pkg', '.apk', '.ipa'];
    }
    
    // Ensure we have at least some file types selected
    if (supportedFileTypes.length === 0) {
        supportedFileTypes = ['.zip', '.rar', '.7z', '.jar', '.exe', '.dll', '.pak', '.mod']; // Default fallback
    }
    
    console.log('🔧 Selected file types for new game:', supportedFileTypes);
    
    // Collect mod folders from the form
    const folderItems = document.querySelectorAll('.mod-folder-item');
    const modFolders = [];
    
    console.log('🗂️ Found', folderItems.length, 'mod folder items in add game form');
    
    folderItems.forEach((item, index) => {
        const nameInput = item.querySelector('[data-type="name"]');
        const pathInput = item.querySelector('[data-type="path"]');
        
        if (nameInput && pathInput) {
            const name = nameInput.value.trim();
            const path = pathInput.value.trim();
            
            console.log(`📁 Folder ${index + 1}: name="${name}", path="${path}"`);
            
            // Include folder even if path is empty (user can set it later)
            if (name) {
                modFolders.push({
                    name: name,
                    path: path
                });
            }
        }
    });
    
    // Ensure we have at least one mod folder
    if (modFolders.length === 0) {
        console.log('⚠️ No mod folders collected, adding default');
        modFolders.push({ name: 'Mods', path: '' });
    }
    
    console.log('🗂️ Final mod folders for new game:', JSON.stringify(modFolders, null, 2));
    
    // Check if icon is marked as small
    const gameIconInput = document.getElementById('custom-game-icon');
    const isSmallIcon = gameIconInput?.dataset?.isSmallIcon === 'true' || 
                       (iconPath && (iconPath.startsWith('data:image/') || iconPath.toLowerCase().endsWith('.ico')));

    // Create the new game object
    const newGame = {
        id: generateGameId(),
        name: name,
        executablePath: executablePath,
        iconPath: iconPath || './assets/Games/steam_game.png', // Default controller icon
        isSmallIcon: isSmallIcon, // Store the small icon flag
        isCustom: true,
        dateAdded: new Date().toISOString(),
        modFolders: modFolders, // Use collected mod folders
        supportedFileTypes: supportedFileTypes // Use selected file types
    };
    
    console.log('🎮 Complete new game object before saving:', JSON.stringify(newGame, null, 2));
    
    // Add to customGames array
    customGames.push(newGame);
    
    // Save to localStorage
    try {
        localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
        console.log('✅ Custom game saved to localStorage');
    } catch (error) {
        console.error('❌ Error saving to localStorage:', error);
    }
    
    // Close the modal
    closeCustomModal();
    
    // Refresh the mod manager display
    populateGameLibrary();
    
    // Always show game type confirmation dialog for ALL games
    console.log('🎮 Showing game type confirmation for:', name);
    showGameTypeConfirmation(newGame);
}

// Show Steam game confirmation dialog
function showSteamGameConfirmation(game) {
    console.log('🎮 Showing Steam game confirmation for:', game.name);
    
    showCustomModal({
        title: '🎮 Steam Game Detected',
        content: `
            <div class="steam-confirmation">
                <div class="confirmation-icon">🎮</div>
                <h3>Is "${game.name}" a Steam game?</h3>
                <p>We detected this might be a Steam game. Steam shortcuts don't contain extractable icons, so we've used a controller icon as default.</p>
                
                <div class="steam-options">
                    <div class="option-explanation">
                        <strong>For the best experience:</strong>
                        <ul>
                            <li>🎯 <strong>Steam shortcuts</strong> work fine but use the controller icon</li>
                            <li>🔍 <strong>Actual .exe files</strong> allow icon extraction and better integration</li>
                            <li>🎨 You can always set a <strong>custom icon</strong> later by editing the game</li>
                        </ul>
                    </div>
                </div>
                
                <div class="confirmation-buttons">
                    <button class="btn btn-secondary" onclick="confirmSteamGameSelection('${game.id}', false)">
                        ✅ Keep as-is (controller icon is fine)
                    </button>
                    <button class="btn btn-primary" onclick="confirmSteamGameSelection('${game.id}', true)">
                        🔍 Let me select the actual .exe file instead
                    </button>
                </div>
            </div>
        `,
        type: 'info'
    });
}

// Show Xbox game confirmation dialog  
function showXboxGameConfirmation(game) {
    console.log('🎮 Showing Xbox game confirmation for:', game.name);
    
    showCustomModal({
        title: '🎮 Xbox/Windows Store Game Detected',
        content: `
            <div class="xbox-confirmation">
                <div class="confirmation-icon">🎮</div>
                <h3>"${game.name}" appears to be an Xbox/Windows Store game</h3>
                <p>Xbox and Windows Store games require special handling due to their protected file system locations.</p>
                
                <div class="xbox-info">
                    <div class="info-box warning">
                        <strong>⚠️ Important:</strong>
                        <ul>
                            <li>Xbox/Windows Store games are stored in protected directories</li>
                            <li>Icon extraction may not work reliably</li>
                            <li>We've set an Xbox controller icon as default</li>
                            <li>The game should still launch correctly</li>
                        </ul>
                    </div>
                </div>
                
                <div class="confirmation-buttons">
                    <button class="btn btn-primary" onclick="confirmXboxGameSelection('${game.id}')">
                        ✅ Got it, continue with Xbox controller icon
                    </button>
                </div>
            </div>
        `,
        type: 'warning'
    });
}

// Handle Steam game confirmation response
function confirmSteamGameSelection(gameId, shouldReselect) {
    const game = customGames.find(g => g.id === gameId);
    if (!game) {
        console.error('❌ Game not found for confirmation:', gameId);
        return;
    }
    
    closeCustomModal();
    
    if (shouldReselect) {
        // User wants to go back and select the actual .exe file
        console.log('🔍 User chose to reselect Steam game executable');
        
        showCustomModal({
            title: '🔍 Select Actual Steam Game Executable',
            content: `
                <div class="steam-reselect">
                    <h3>Finding the actual "${game.name}" executable</h3>
                    <p>Steam games are typically located in:</p>
                    
                    <div class="path-examples">
                        <div class="path-box">
                            <strong>Common Steam Locations:</strong>
                            <code>C:\\Program Files (x86)\\Steam\\steamapps\\common\\</code>
                            <code>C:\\Program Files\\Steam\\steamapps\\common\\</code>
                            <code>[Your Steam Library]\\steamapps\\common\\</code>
                        </div>
                        
                        <div class="path-box">
                            <strong>Look for:</strong>
                            <ul>
                                <li>📂 A folder named similar to "${game.name}"</li>
                                <li>📄 The main .exe file (usually obvious)</li>
                                <li>🚫 Avoid launcher.exe, updater.exe, or uninstall.exe</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="reselect-buttons">
                        <button class="btn btn-primary" onclick="reselectSteamExecutable('${gameId}')">
                            📁 Browse for actual .exe file
                        </button>
                        <button class="btn btn-secondary" onclick="cancelSteamReselection('${gameId}')">
                            ⬅️ Go back (keep current selection)
                        </button>
                    </div>
                </div>
            `,
            type: 'info'
        });
    } else {
        // User is happy with the controller icon
        console.log('✅ User confirmed Steam game with controller icon');
        showNotification(`✅ Steam game "${game.name}" added successfully with controller icon!`, 'success');
    }
}

// Handle Xbox game confirmation
function confirmXboxGameSelection(gameId) {
    const game = customGames.find(g => g.id === gameId);
    if (!game) {
        console.error('❌ Game not found for confirmation:', gameId);
        return;
    }
    
    closeCustomModal();
    console.log('✅ User confirmed Xbox game');
    showNotification(`✅ Xbox game "${game.name}" added successfully!`, 'success');
}

// Reselect Steam executable
function reselectSteamExecutable(gameId) {
    console.log('🔍 Reselecting Steam executable for game:', gameId);
    
    closeCustomModal();
    
    // Open file browser specifically for Steam executables
    if (window.electronAPI && window.electronAPI.selectFile) {
        window.electronAPI.selectFile({
            title: 'Select Actual Steam Game Executable',
            filters: [
                { name: 'Executable Files', extensions: ['exe'] },
                { name: 'All Files', extensions: ['*'] }
            ],
            defaultPath: 'C:\\Program Files (x86)\\Steam\\steamapps\\common\\'
        }).then(result => {
            if (result && result.filePaths && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];
                
                // Update the game with the new path
                const game = customGames.find(g => g.id === gameId);
                if (game) {
                    game.executablePath = selectedPath;
                    
                    // Try to extract icon from the actual executable
                    handleIconExtractionForExistingGame(game, selectedPath).then(() => {
                                                 // Save updated game
                         localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
                         populateGameLibrary(); // Refresh display
                        
                        showNotification(`✅ Updated "${game.name}" with actual executable!`, 'success');
                    });
                }
            }
        }).catch(error => {
            console.error('Error reselecting executable:', error);
            showNotification('Error selecting file: ' + error.message, 'error');
        });
    }
}

// Cancel Steam reselection
function cancelSteamReselection(gameId) {
    const game = customGames.find(g => g.id === gameId);
    if (game) {
        closeCustomModal();
        showNotification(`✅ Steam game "${game.name}" added successfully with controller icon!`, 'success');
    }
}

// Handle icon extraction for existing games
async function handleIconExtractionForExistingGame(game, executablePath) {
    console.log('🎨 Updating icon for existing game:', game.name);
    
    if (window.electronAPI && typeof window.electronAPI.extractIcon === 'function') {
        try {
            const iconResult = await window.electronAPI.extractIcon(executablePath);
            
                    if (iconResult && iconResult.success && iconResult.iconData) {
            game.iconPath = iconResult.iconData;
            game.isSmallIcon = true; // Mark extracted icons as small
            console.log('✅ Successfully updated icon for', game.name, '(marked as small)');
        } else {
            console.log('⚠️ Could not extract icon, keeping controller icon');
        }
        } catch (error) {
            console.error('❌ Error extracting icon for existing game:', error);
        }
    }
}

// File types dropdown functionality
function toggleFileTypesDropdown() {
    const dropdown = document.getElementById('file-types-dropdown');
    const options = document.getElementById('file-types-options');
    
    if (dropdown && options) {
        dropdown.classList.toggle('open');
        
        if (dropdown.classList.contains('open')) {
            options.style.display = 'block';
            updateFileTypesDisplay();
        } else {
            options.style.display = 'none';
        }
    }
}

function updateFileTypesDisplay() {
    const checkboxes = document.querySelectorAll('#file-types-options input[type="checkbox"]:checked');
    const selectedText = document.querySelector('#file-types-dropdown .selected-text');
    
    if (selectedText) {
        if (checkboxes.length === 0) {
            selectedText.textContent = 'No file types selected';
        } else {
            // Check if "All Types" is selected
            const allTypesSelected = Array.from(checkboxes).some(cb => cb.value === 'all');
            
            if (allTypesSelected) {
                selectedText.textContent = 'All Types';
            } else if (checkboxes.length === 1) {
                const selectedCheckbox = checkboxes[0];
                selectedText.textContent = selectedCheckbox.nextElementSibling.textContent;
            } else if (checkboxes.length <= 3) {
                const types = Array.from(checkboxes)
                    .filter(cb => cb.value !== 'all')
                    .map(cb => cb.value)
                    .join(', ');
                selectedText.textContent = types;
            } else {
                const nonAllCount = Array.from(checkboxes).filter(cb => cb.value !== 'all').length;
                selectedText.textContent = `${nonAllCount} file types selected`;
            }
        }
    }
}

// Add event listeners for file type checkboxes
document.addEventListener('change', function(e) {
    if (e.target.matches('#file-types-options input[type="checkbox"]')) {
        const clickedCheckbox = e.target;
        const allTypesCheckbox = document.getElementById('type-all');
        
        if (clickedCheckbox.value === 'all') {
            // If "All Types" was selected, uncheck all other options
            if (clickedCheckbox.checked) {
                document.querySelectorAll('#file-types-options input[type="checkbox"]:not(#type-all)').forEach(cb => {
                    cb.checked = false;
                });
            }
        } else {
            // If any individual type was selected, uncheck "All Types"
            if (clickedCheckbox.checked && allTypesCheckbox) {
                allTypesCheckbox.checked = false;
            }
        }
        
        updateFileTypesDisplay();
    }
});

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    const dropdown = document.getElementById('file-types-dropdown');
    if (dropdown && !dropdown.contains(e.target)) {
        dropdown.classList.remove('open');
        const options = document.getElementById('file-types-options');
        if (options) options.style.display = 'none';
    }
});

// Legacy function support - keeping these for backward compatibility
function showPresetGames() {
    console.log('🎮 showPresetGames called - redirecting to main library');
    showModManagerInitial();
}

function backToModManagerOptions() {
    backToGameLibrary();
}

function showGameSelectionView(title) {
    showModManagerInitial();
}

// === UTILITY FUNCTIONS ===

function showLoadingState(show) {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = show ? 'flex' : 'none';
    }
}

function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
    
    // Animate in
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function showScanResults(results) {
    const scanResults = document.getElementById('scan-results');
    const filesFound = document.getElementById('files-found');
    const sizeEstimate = document.getElementById('size-estimate');
    const categoriesContainer = document.getElementById('results-categories');
    
    if (filesFound) filesFound.textContent = results.files_found;
    if (sizeEstimate) sizeEstimate.textContent = results.size_estimate;
    
    if (categoriesContainer && results.categories) {
        categoriesContainer.innerHTML = results.categories.map(category => `
            <div class="result-category">
                <div class="category-name">${category.name}</div>
                <div class="category-stats">
                    <span class="category-count">${category.count} files</span>
                    <span class="category-size">${category.size}</span>
                </div>
            </div>
        `).join('');
    }
    
    if (scanResults) {
        scanResults.style.display = 'block';
    }
}

function showConfirmDialog(title, message) {
    return new Promise((resolve) => {
        showCustomModal({
            title: title,
            content: `<p class="confirm-message">${message}</p>`,
            type: 'warning',
            buttons: [
                {
                    text: 'Cancel',
                    class: 'btn-secondary',
                    action: () => {
                        closeCustomModal();
                        resolve(false);
                    }
                },
                {
                    text: 'Confirm',
                    class: 'btn-danger',
                    action: () => {
                        closeCustomModal();
                        resolve(true);
                    }
                }
            ]
        });
    });
}

// Professional custom modal system
function showCustomModal({ title, content, type = 'info', buttons = [], preventOutsideClose = true, customClass = '', showCloseButton = null }) {
    // Remove any existing modal
    closeCustomModal();
    
    const modal = document.createElement('div');
    modal.className = 'custom-modal-overlay';
    modal.id = 'custom-modal-overlay';
    
    const typeClasses = {
        info: 'info',
        success: 'success', 
        warning: 'warning',
        error: 'error'
    };
    
    const typeIcons = {
        info: '💡',
        success: '✅',
        warning: '⚠️', 
        error: '❌'
    };
    
    // Auto-determine if close button should be shown
    // Only show if no buttons are provided OR explicitly requested
    const shouldShowCloseButton = showCloseButton !== null ? showCloseButton : buttons.length === 0;
    
    modal.innerHTML = `
        <div class="custom-modal-container ${customClass}">
            <div class="custom-modal-header ${typeClasses[type] || 'info'}">
                <span class="modal-icon">${typeIcons[type] || '💡'}</span>
                <h3 class="modal-title">${title}</h3>
                ${shouldShowCloseButton ? '<button class="modal-close-btn" onclick="closeCustomModal()">✕</button>' : ''}
            </div>
            <div class="custom-modal-body">
                ${content}
            </div>
            <div class="custom-modal-footer" style="position: relative;">
                ${buttons.map((button, index) => `
                    <button class="btn ${button.class || 'btn-secondary'}" 
                            data-button-index="${index}"
                            ${button.disabled ? 'disabled' : ''}>
                        ${button.text}
                    </button>
                `).join('')}
            </div>
        </div>
    `;
    
    // Handle escape key and outside clicks
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeCustomModal();
        }
    };
    
    // Only add outside click handler if not prevented
    const clickHandler = (e) => {
        if (!preventOutsideClose && e.target === modal) {
            closeCustomModal();
        }
    };
    
    modal.addEventListener('click', clickHandler);
    document.addEventListener('keydown', escapeHandler);
    
    // Store handlers for cleanup
    modal._escapeHandler = escapeHandler;
    modal._clickHandler = clickHandler;
    
    // Prevent body scroll without affecting layout
    const scrollY = window.scrollY;
    document.body.classList.add('modal-open');
    document.body.style.overflow = 'hidden';
    document.body.style.paddingRight = '0px';
    document.body.dataset.scrollY = scrollY;
    
    document.body.appendChild(modal);
    
    // Add button click handlers after modal is in DOM
    buttons.forEach((button, index) => {
        const btnElement = modal.querySelector(`[data-button-index="${index}"]`);
        if (btnElement && button.action) {
            btnElement.addEventListener('click', button.action);
        }
    });
    
    // Add animation classes for proper display
    setTimeout(() => {
        modal.classList.add('active');
        const modalContainer = modal.querySelector('.custom-modal-container');
        if (modalContainer) {
            modalContainer.classList.add('active');
        }
        
        // Ensure beta warning maintains its layout after modal animation
        const betaWarning = document.querySelector('.beta-warning-banner');
        if (betaWarning) {
            betaWarning.style.display = 'flex';
            betaWarning.style.alignItems = 'center';
        }
    }, 10);
}

function closeCustomModal() {
    const modal = document.getElementById('custom-modal-overlay');
    if (modal) {
        // Clean up event listeners
        if (modal._escapeHandler) {
            document.removeEventListener('keydown', modal._escapeHandler);
        }
        if (modal._clickHandler) {
            modal.removeEventListener('click', modal._clickHandler);
        }
        
        // Animate out
        modal.classList.remove('active');
        const modalContainer = modal.querySelector('.custom-modal-container');
        if (modalContainer) {
            modalContainer.classList.remove('active');
        }
        
        // Maintain beta warning layout during modal close
        const betaWarning = document.querySelector('.beta-warning-banner');
        if (betaWarning) {
            betaWarning.style.transition = 'none';
            betaWarning.style.display = 'flex';
            betaWarning.style.alignItems = 'center';
        }
        
        // Remove after animation
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
            
            // Restore body styles and scroll position
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Restore scroll position
            const scrollY = document.body.dataset.scrollY;
            if (scrollY) {
                window.scrollTo(0, parseInt(scrollY));
                delete document.body.dataset.scrollY;
            }
            
            // Reset beta warning transition after modal is fully closed
            const betaWarning = document.querySelector('.beta-warning-banner');
            if (betaWarning) {
                betaWarning.style.transition = '';
            }
        }, 300);
        
        console.log('✅ Modal closed and removed');
    }
}

// Additional functions for new HTML structure
function testFunction() {
    console.log('✅ Test function called - JavaScript is working!');
    showNotification('JavaScript is working! Functions are accessible.', 'success');
}

function purchaseLicense() {
    showNotification('Purchase license functionality coming soon', 'info');
}

function exportSettings() {
    showNotification('Export settings functionality coming soon', 'info');
}

function openTask(taskId) {
    console.log('Opening task:', taskId);
    showNotification(`${taskId} functionality coming soon`, 'info');
}

// Show coming soon message for additional tools
function showComingSoonMessage(toolName) {
    showCustomModal({
        title: `🚧 ${toolName}`,
        content: `
            <div class="coming-soon-content">
                <div class="coming-soon-icon">🔧</div>
                <h3>Feature Coming Soon!</h3>
                <p><strong>${toolName}</strong> is currently under development and will be available in a future update.</p>
                
                <div class="feature-preview">
                    <h4>What to expect:</h4>
                    <ul>
                        <li>Professional-grade system utilities</li>
                        <li>Advanced automation capabilities</li>
                        <li>Seamless integration with Armory X</li>
                        <li>User-friendly interface with powerful features</li>
                    </ul>
                </div>
                
                <div class="stay-updated">
                    <p>📧 Stay updated with the latest features and releases!</p>
                </div>
            </div>
            
            <style>
                .coming-soon-content {
                    text-align: center;
                    padding: 20px;
                }
                
                .coming-soon-icon {
                    font-size: 64px;
                    margin-bottom: 20px;
                    opacity: 0.8;
                }
                
                .coming-soon-content h3 {
                    color: #06b6d4;
                    margin-bottom: 16px;
                    font-size: 24px;
                }
                
                .coming-soon-content p {
                    color: #9ca3af;
                    margin-bottom: 24px;
                    line-height: 1.6;
                }
                
                .feature-preview {
                    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
                    border: 1px solid #334155;
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: left;
                }
                
                .feature-preview h4 {
                    color: #06b6d4;
                    margin-bottom: 12px;
                    font-size: 16px;
                }
                
                .feature-preview ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }
                
                .feature-preview li {
                    color: #d1d5db;
                    padding: 8px 0;
                    padding-left: 24px;
                    position: relative;
                    line-height: 1.5;
                }
                
                .feature-preview li::before {
                    content: '✨';
                    position: absolute;
                    left: 0;
                    top: 8px;
                }
                
                .stay-updated {
                    background: rgba(6, 182, 212, 0.1);
                    border: 1px solid rgba(6, 182, 212, 0.3);
                    border-radius: 8px;
                    padding: 16px;
                    margin-top: 20px;
                }
                
                .stay-updated p {
                    margin: 0;
                    color: #06b6d4;
                    font-weight: 600;
                }
            </style>
        `,
        type: 'info',
        buttons: [
            {
                text: 'Got It!',
                class: 'btn-primary',
                action: () => closeCustomModal()
            }
        ]
    });
}

// Basic settings functions
function loadSettings() {
    console.log('⚙️ Loading settings...');
    // Settings loading functionality can be added here later
}

// Debug functions for mod manager
async function showModDirectories() {
    showNotification('Debug: Mod directories info coming soon', 'info');
}

async function createTestMods() {
    if (!selectedGameId) {
        showNotification('Please select a game first', 'warning');
        return;
    }
    showNotification('Debug: Test mod creation coming soon', 'info');
}

// Initialization functions for tabs that don't exist yet
function initToolsTab() {
    console.log('🔧 Tools tab initialized');
    
    // Initialize tool states and UI listeners
    initializeToolStates();
    
    // Initialize HWID spoofer status
    initializeHWIDSpooferStatus();
}

// Tool states are now managed by the main process through automation-tools.js

function initializeToolStates() {
    // Set up IPC listeners for tool status updates
    if (ipcRenderer) {
        ipcRenderer.on('auto-clicker-status-changed', (event, data) => {
            updateToolUI('autoClicker', data);
        });
        
        ipcRenderer.on('anti-recoil-status-changed', (event, data) => {
            updateToolUI('antiRecoil', data);
        });
        
        ipcRenderer.on('key-sequence-status-changed', (event, data) => {
            updateToolUI('keySequence', data);
        });
        
        ipcRenderer.on('hwid-spoofer-status-changed', (event, data) => {
            hwidSpooferState.active = data.active;
            hwidSpooferState.spoofedCount = data.successCount || 0;
            updateHWIDSpooferUI();
            
            // Show advanced options when active
            const advancedOptions = document.getElementById('hwid-advanced-options');
            if (advancedOptions) {
                advancedOptions.style.display = data.active ? 'block' : 'none';
            }
        });
    }

    // Update UI elements to reflect default values
    const intervalSlider = document.getElementById('click-interval');
    const intervalValue = document.getElementById('interval-value');
    const cpsDisplay = document.getElementById('cps-display');
    
    if (intervalSlider && intervalValue && cpsDisplay) {
        intervalSlider.addEventListener('input', (e) => {
            const interval = parseInt(e.target.value);
            intervalValue.textContent = interval;
            const cps = Math.round(1000 / interval);
            cpsDisplay.textContent = `≈ ${cps} CPS`;
        });
    }
    
    // Anti-recoil strength slider
    const strengthSlider = document.getElementById('recoil-strength');
    const strengthValue = document.getElementById('strength-value');
    
    if (strengthSlider && strengthValue) {
        strengthSlider.addEventListener('input', (e) => {
            const strength = parseInt(e.target.value);
            strengthValue.textContent = strength;
            toolStates.antiRecoil.settings.strength = strength;
        });
    }
    
    // Key sequence delay slider
    const keyDelaySlider = document.getElementById('key-delay');
    const keyDelayValue = document.getElementById('key-delay-value');
    
    if (keyDelaySlider && keyDelayValue) {
        keyDelaySlider.addEventListener('input', (e) => {
            const delay = parseInt(e.target.value);
            keyDelayValue.textContent = delay;
        });
    }
}

// Update tool UI based on status changes from main process
function updateToolUI(toolName, data) {
    const toolMappings = {
        autoClicker: {
            status: 'auto-clicker-status',
            button: 'auto-clicker-toggle'
        },
        antiRecoil: {
            status: 'anti-recoil-status',
            button: 'anti-recoil-toggle'
        },
        keySequence: {
            status: 'key-sequence-status',
            button: 'key-sequence-toggle'
        }
    };

    const mapping = toolMappings[toolName];
    if (!mapping) return;

    const status = document.getElementById(mapping.status);
    const button = document.getElementById(mapping.button);

    if (status && button) {
        if (data.active) {
            // Tool is active
            status.textContent = 'Active';
            status.className = 'tool-status active';
            
            // Update button based on tool type
            switch (toolName) {
                case 'autoClicker':
                    button.textContent = 'Disable Auto-Clicker';
                    break;
                case 'antiRecoil':
                    button.textContent = 'Disable Anti-Recoil';
                    break;
                case 'keySequence':
                    button.textContent = 'Disable Key Sequence';
                    break;
            }
            button.className = 'btn btn-danger';
        } else {
            // Tool is inactive
            status.textContent = 'Inactive';
            status.className = 'tool-status inactive';
            
            // Update button based on tool type
            switch (toolName) {
                case 'autoClicker':
                    button.textContent = 'Enable Auto-Clicker';
                    break;
                case 'antiRecoil':
                    button.textContent = 'Enable Anti-Recoil';
                    break;
                case 'keySequence':
                    button.textContent = 'Enable Key Sequence';
                    break;
            }
            button.className = 'btn btn-primary';
        }
    }
}

// Hotkey listeners are now handled globally by the main process through automation-tools.js

// Auto-Clicker functionality
async function toggleAutoClicker() {
    try {
        // Get current settings from UI
        const settings = {
            button: document.getElementById('mouse-button-select')?.value || 'left',
            clickType: document.getElementById('click-type-select')?.value || 'single',
            interval: parseInt(document.getElementById('click-interval')?.value) || 100,
            ludicrousMode: document.getElementById('ludicrous-mode')?.checked || false,
            hotkey: document.getElementById('auto-clicker-hotkey')?.value || 'F6'
        };

        // Call main process to toggle auto-clicker
        const result = await ipcRenderer.invoke('automation-toggle-tool', 'autoClicker');
        
        if (result.success) {
            // Update settings if needed
            await ipcRenderer.invoke('automation-update-settings', 'autoClicker', settings);
            showNotification(result.message, 'success');
        } else {
            showNotification(`Auto-Clicker error: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Auto-clicker toggle error:', error);
        showNotification('Failed to toggle auto-clicker', 'error');
    }
}

// Anti-Recoil functionality
async function toggleAntiRecoil() {
    try {
        // Get current settings from UI
        const settings = {
            strength: parseInt(document.getElementById('recoil-strength')?.value) || 5,
            hotkey: document.getElementById('anti-recoil-hotkey')?.value || 'F7'
        };

        // Call main process to toggle anti-recoil
        const result = await ipcRenderer.invoke('automation-toggle-tool', 'antiRecoil');
        
        if (result.success) {
            // Update settings if needed
            await ipcRenderer.invoke('automation-update-settings', 'antiRecoil', settings);
            showNotification(result.message, 'success');
        } else {
            showNotification(`Anti-Recoil error: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Anti-recoil toggle error:', error);
        showNotification('Failed to toggle anti-recoil', 'error');
    }
}

// Key Sequence functionality
async function toggleKeySequence() {
    try {
        // Get current settings from UI
        const settings = {
            keys: document.getElementById('key-sequence-input')?.value || 'A,S,D,W',
            delay: parseInt(document.getElementById('key-delay')?.value) || 100,
            hotkey: document.getElementById('key-sequence-hotkey')?.value || 'F8'
        };

        // Call main process to toggle key sequence
        const result = await ipcRenderer.invoke('automation-toggle-tool', 'keySequence');
        
        if (result.success) {
            // Update settings if needed
            await ipcRenderer.invoke('automation-update-settings', 'keySequence', settings);
            showNotification(result.message, 'success');
        } else {
            showNotification(`Key Sequence error: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('Key sequence toggle error:', error);
        showNotification('Failed to toggle key sequence', 'error');
    }
}

// HWID Spoofer functionality
let hwidSpooferState = {
    active: false,
    hasBackup: false,
    spoofedCount: 0,
    kernelMode: false,
    kernelAvailable: false
};

async function toggleHWIDSpoofer() {
    try {
        // Check kernel mode preference
        const kernelModeCheckbox = document.getElementById('hwid-kernel-mode');
        hwidSpooferState.kernelMode = kernelModeCheckbox && kernelModeCheckbox.checked;
        
        // Check if backup exists before enabling
        if (!hwidSpooferState.active && !hwidSpooferState.hasBackup) {
            const protectionLevel = hwidSpooferState.kernelMode ? 'kernel-level' : 'registry-level';
            const confirmed = await showConfirmDialog(
                'HWID Spoofer - First Time Setup',
                `This will create a backup of your original hardware identifiers and then enable ${protectionLevel} spoofing. Your original values will be safely stored and can be restored at any time.`,
                'info'
            );
            
            if (!confirmed) return;
            
            showNotification('Creating backup of original values...', 'info');
            
            // Create backup first (use kernel backup if kernel mode selected)
            const backupResult = hwidSpooferState.kernelMode ? 
                await ipcRenderer.invoke('kernel-hwid-backup-values') :
                await ipcRenderer.invoke('hwid-spoofer-backup-values');
                
            if (!backupResult.success) {
                showNotification(`Failed to create backup: ${backupResult.message}`, 'error');
                return;
            }
            
            hwidSpooferState.hasBackup = true;
            showNotification('Backup created successfully', 'success');
        } else if (hwidSpooferState.active) {
            const protectionLevel = hwidSpooferState.kernelMode ? 'kernel-level' : 'registry-level';
            const confirmed = await showConfirmDialog(
                'Disable HWID Spoofing',
                `Are you sure you want to restore your original hardware identifiers? This will disable ${protectionLevel} spoofing.`,
                'warning'
            );
            
            if (!confirmed) return;
        }
        
        // Use appropriate spoofing method based on kernel mode
        const result = hwidSpooferState.kernelMode ? 
            await ipcRenderer.invoke('kernel-hwid-toggle') :
            await ipcRenderer.invoke('hwid-spoofer-toggle');
        
        if (result.success) {
            hwidSpooferState.active = !hwidSpooferState.active;
            
            // Force update backup status from appropriate service
            if (hwidSpooferState.kernelMode) {
                const kernelStatus = await ipcRenderer.invoke('kernel-hwid-get-status');
                const kernelDetailedStatus = await ipcRenderer.invoke('kernel-hwid-get-detailed-status');
                
                if (kernelDetailedStatus || kernelStatus) {
                    hwidSpooferState.hasBackup = (kernelDetailedStatus && kernelDetailedStatus.hasBackup) || 
                                                 (kernelStatus && kernelStatus.originalValues !== null);
                    hwidSpooferState.spoofedCount = kernelDetailedStatus && kernelDetailedStatus.spoofedValues ? 
                                                   Object.keys(kernelDetailedStatus.spoofedValues).length : 0;
                    hwidSpooferState.active = (kernelDetailedStatus && kernelDetailedStatus.isActive) || 
                                             (kernelStatus && kernelStatus.isActive);
                }
            } else {
                const registryStatus = await ipcRenderer.invoke('hwid-spoofer-get-status');
                if (registryStatus) {
                    hwidSpooferState.hasBackup = registryStatus.hasBackup;
                    hwidSpooferState.spoofedCount = registryStatus.spoofedValues ? Object.keys(registryStatus.spoofedValues).length : 0;
                    hwidSpooferState.active = registryStatus.active;
                }
            }
            
            // Force immediate UI update
            updateHWIDSpooferUI();
            showNotification(result.message, 'success');
            
            console.log('HWID Spoofer State Updated:', hwidSpooferState); // Debug logging
            
            // Show advanced options when active
            const advancedOptions = document.getElementById('hwid-advanced-options');
            if (advancedOptions) {
                advancedOptions.style.display = hwidSpooferState.active ? 'block' : 'none';
                console.log('Advanced options display:', advancedOptions.style.display); // Debug logging
            }
        } else {
            showNotification(`HWID Spoofer error: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('HWID spoofer toggle error:', error);
        showNotification('Failed to toggle HWID spoofer', 'error');
    }
}

async function createHWIDBackup() {
    try {
        showNotification('Creating HWID backup...', 'info');
        const result = await ipcRenderer.invoke('hwid-spoofer-backup-values');
        
        if (result.success) {
            hwidSpooferState.hasBackup = true;
            updateHWIDSpooferUI();
            showNotification(result.message, 'success');
        } else {
            showNotification(`Backup failed: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('HWID backup error:', error);
        showNotification('Failed to create HWID backup', 'error');
    }
}

async function restoreHWIDValues() {
    try {
        const protectionLevel = hwidSpooferState.kernelMode ? 'kernel-level' : 'registry-level';
        const confirmed = await showConfirmDialog(
            'Restore Original HWID Values',
            `Are you sure you want to restore your original HWID values? This will deactivate ${protectionLevel} spoofing.`
        );
        
        if (!confirmed) return;
        
        showNotification('Restoring original HWID values...', 'info');
        
        // Use appropriate restore method based on current mode
        const result = hwidSpooferState.kernelMode ?
            await ipcRenderer.invoke('kernel-hwid-restore-values') :
            await ipcRenderer.invoke('hwid-spoofer-restore-values');
        
        if (result.success) {
            hwidSpooferState.active = false;
            hwidSpooferState.kernelMode = false;
            
            // Reset kernel checkbox
            const kernelCheckbox = document.getElementById('hwid-kernel-mode');
            if (kernelCheckbox) {
                kernelCheckbox.checked = false;
                kernelCheckbox.disabled = !hwidSpooferState.kernelAvailable;
            }
            
            // Update status from backend to reflect actual state
            const registryStatus = await ipcRenderer.invoke('hwid-spoofer-get-status');
            const kernelDetailedStatus = await ipcRenderer.invoke('kernel-hwid-get-detailed-status');
            
            hwidSpooferState.hasBackup = (registryStatus && registryStatus.hasBackup) || 
                                        (kernelDetailedStatus && kernelDetailedStatus.hasBackup);
            hwidSpooferState.spoofedCount = 0;
            
            updateHWIDSpooferUI();
            showNotification(result.message, 'success');
            
            // Hide advanced options
            const advancedOptions = document.getElementById('hwid-advanced-options');
            if (advancedOptions) {
                advancedOptions.style.display = 'none';
            }
        } else {
            showNotification(`Restore failed: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('HWID restore error:', error);
        showNotification('Failed to restore HWID values', 'error');
    }
}

async function showHWIDInfo() {
    try {
        showNotification('Loading HWID information...', 'info');
        
        // Use appropriate method based on current mode
        const systemInfo = await ipcRenderer.invoke('hwid-spoofer-get-system-info');
            
        const registryStatus = await ipcRenderer.invoke('hwid-spoofer-get-status');
        const kernelStatus = await ipcRenderer.invoke('kernel-hwid-get-status');
        const kernelDetailedStatus = await ipcRenderer.invoke('kernel-hwid-get-detailed-status');
        const kernelAvailability = await ipcRenderer.invoke('kernel-hwid-check-availability');
        
        // Use the appropriate status based on current mode
        const status = hwidSpooferState.kernelMode ? 
            (kernelDetailedStatus || kernelStatus) : 
            registryStatus;
            
        const comparison = await ipcRenderer.invoke('hwid-spoofer-get-comparison');
        
        let content = `
            <div class="hwid-info-content">
                <div class="info-section">
                    <h4>🖥️ System Information</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Platform:</span>
                            <span class="info-value">${systemInfo.platform}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Hostname:</span>
                            <span class="info-value">${systemInfo.hostname}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Username:</span>
                            <span class="info-value">${systemInfo.username}</span>
                        </div>
                        ${systemInfo.manufacturer ? `
                        <div class="info-item">
                            <span class="info-label">Manufacturer:</span>
                            <span class="info-value">${systemInfo.manufacturer}</span>
                        </div>
                        ` : ''}
                        ${systemInfo.model ? `
                        <div class="info-item">
                            <span class="info-label">Model:</span>
                            <span class="info-value">${systemInfo.model}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="info-section">
                    <h4>🛡️ Spoofer Status</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Status:</span>
                            <span class="info-value ${(status.active || status.isActive) ? 'active' : 'inactive'}">
                                ${(status.active || status.isActive) ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Backup Available:</span>
                            <span class="info-value ${(status.hasBackup || (status.originalValues !== null)) ? 'success' : 'warning'}">
                                ${(status.hasBackup || (status.originalValues !== null)) ? 'Yes' : 'No'}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Currently Modified:</span>
                            <span class="info-value ${(comparison.success && comparison.comparison.isCurrentlyModified) || (status.active || status.isActive) ? 'modified' : 'original'}">
                                ${(comparison.success && comparison.comparison.isCurrentlyModified) || (status.active || status.isActive) ? 'Yes' : 'No'}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="info-section">
                    <h4>🔐 Kernel-Level Protection</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">Driver Status:</span>
                            <span class="info-value ${kernelAvailability.success ? 'success' : 'warning'}">
                                ${kernelAvailability.success ? 'Available' : 
                                  kernelAvailability.requiresAdmin ? 'Needs Admin Rights' :
                                  kernelAvailability.requiresDriver ? 'Driver Missing' : 'Unavailable'}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Kernel Mode:</span>
                            <span class="info-value ${(kernelStatus && kernelStatus.isActive) || (kernelDetailedStatus && kernelDetailedStatus.isActive) ? 'active' : 'inactive'}">
                                ${(kernelStatus && kernelStatus.isActive) || (kernelDetailedStatus && kernelDetailedStatus.isActive) ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Protection Level:</span>
                            <span class="info-value ${hwidSpooferState.kernelMode ? 'success' : 'info'}">
                                ${hwidSpooferState.kernelMode ? 'Kernel-Level (99.9%)' : 'Registry-Level (95%)'}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Driver Loaded:</span>
                            <span class="info-value ${(kernelStatus && kernelStatus.driverLoaded) || (kernelDetailedStatus && kernelDetailedStatus.driverLoaded) ? 'success' : 'inactive'}">
                                ${(kernelStatus && kernelStatus.driverLoaded) || (kernelDetailedStatus && kernelDetailedStatus.driverLoaded) ? 'Yes' : 'No'}
                            </span>
                        </div>
                    </div>
                </div>
                
                ${comparison.success ? `
                <div class="info-section">
                    <h4>🔍 Hardware ID Information</h4>
                    ${comparison.comparison.hasBackup ? `
                    <div class="comparison-header">
                        <p><strong>Comparison Mode:</strong> Showing current vs original values</p>
                    </div>
                    ` : `
                    <div class="comparison-header">
                        <p><strong>Current Values Only:</strong> No backup available for comparison. Enable spoofing once to create a backup.</p>
                    </div>
                    `}
                    <div class="hwid-comparison-grid">
                        ${Object.entries(comparison.comparison.values).map(([key, data]) => `
                            <div class="hwid-comparison-item ${data.isModified ? 'modified' : 'original'}">
                                <div class="hwid-label">
                                    <span class="hwid-name">${data.displayName}</span>
                                    <span class="hwid-status ${data.isModified ? 'status-modified' : 'status-original'}">
                                        ${comparison.comparison.hasBackup ? 
                                            (data.isModified ? '🟡 Modified' : '🟢 Original') : 
                                            '📋 Current'
                                        }
                                    </span>
                                </div>
                                <div class="hwid-values">
                                    <div class="hwid-value-row">
                                        <span class="value-type">Current:</span>
                                        <span class="value-data ${data.isModified ? 'modified-value' : 'original-value'}">
                                            ${data.current || 'N/A'}
                                        </span>
                                    </div>
                                    ${data.original && comparison.comparison.hasBackup ? `
                                    <div class="hwid-value-row">
                                        <span class="value-type">Original:</span>
                                        <span class="value-data original-value">
                                            ${data.original}
                                        </span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : `
                <div class="info-section">
                    <h4>🔍 Hardware ID Information</h4>
                    <div class="error-message">
                        <p>Unable to retrieve hardware information. Please ensure the application is running with administrator privileges.</p>
                    </div>
                </div>
                `}
                
                <div class="info-section">
                    <h4>ℹ️ Privacy Information</h4>
                    <div class="privacy-note">
                        <p>This tool modifies system hardware identifiers to protect your privacy from malicious actors who might try to track your device. Your original values are safely backed up and can be restored at any time.</p>
                        <p><strong>Note:</strong> Some games may require a restart to recognize the new hardware identifiers.</p>
                        ${comparison.success && comparison.comparison.isCurrentlyModified ? `
                        <p><strong>Current Status:</strong> Your hardware IDs are currently modified. Click "Restore Original Values" to revert to your original hardware identifiers.</p>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        
        showCustomModal({
            title: '🛡️ HWID Spoofer Information',
            content: content,
            type: 'info',
            customClass: 'hwid-info-modal',
            buttons: [
                {
                    text: 'Refresh',
                    class: 'btn-tertiary',
                    action: () => {
                        closeCustomModal();
                        setTimeout(() => showHWIDInfo(), 100);
                    }
                },
                {
                    text: 'Close',
                    class: 'btn-secondary',
                    action: () => closeCustomModal()
                }
            ]
        });
    } catch (error) {
        console.error('HWID info error:', error);
        showNotification('Failed to get HWID information', 'error');
    }
}

async function spoofMACAddresses() {
    try {
        showNotification('Checking network adapters...', 'info');
        const result = await ipcRenderer.invoke('hwid-spoofer-spoof-mac-addresses');
        
        if (result.success) {
            let content = `
                <div class="mac-spoof-content">
                    <div class="warning-section">
                        <div class="warning-icon">⚠️</div>
                        <div class="warning-text">
                            <h4>Advanced Feature</h4>
                            <p>MAC address spoofing requires elevated privileges and a system restart to take effect. This feature is more complex and may require additional system configuration.</p>
                        </div>
                    </div>
                    
                    ${result.adapters ? `
                    <div class="adapters-section">
                        <h4>📡 Network Adapters Found:</h4>
                        <div class="adapters-list">
                            ${result.adapters.map(adapter => `
                                <div class="adapter-item">
                                    <span class="adapter-name">${adapter.name}</span>
                                    <span class="adapter-mac">${adapter.mac}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                    
                    <div class="info-section">
                        <p><strong>Current Status:</strong> ${result.message}</p>
                        <p>This feature is under development and may require additional system-level permissions.</p>
                    </div>
                </div>
            `;
            
            showCustomModal({
                title: '📡 MAC Address Spoofing',
                content: content,
                type: 'warning',
                customClass: 'mac-spoof-modal',
                buttons: [
                    {
                        text: 'Close',
                        class: 'btn-secondary',
                        action: () => closeCustomModal()
                    }
                ]
            });
        } else {
            showNotification(`MAC spoofing failed: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('MAC spoofing error:', error);
        showNotification('Failed to access MAC address spoofing', 'error');
    }
}

function updateHWIDSpooferUI() {
    console.log('Updating HWID UI with state:', hwidSpooferState); // Debug logging
    
    const status = document.getElementById('hwid-spoofer-status');
    const button = document.getElementById('hwid-spoofer-toggle');
    const backupStatus = document.getElementById('hwid-backup-status');
    const spoofedCount = document.getElementById('hwid-spoofed-count');
    const protectionLevel = document.getElementById('hwid-protection-level');
    const kernelCheckbox = document.getElementById('hwid-kernel-mode');
    const advancedOptions = document.getElementById('hwid-advanced-options');
    
    console.log('Found elements:', { status, button, backupStatus, spoofedCount, protectionLevel }); // Debug logging
    
    if (status && button) {
        if (hwidSpooferState.active) {
            status.textContent = 'ACTIVE';
            status.className = 'tool-status active';
            button.textContent = 'Disable HWID Spoofing';
            button.className = 'btn btn-danger';
            console.log('UI updated to ACTIVE state'); // Debug logging
            
            // Disable kernel checkbox when active
            if (kernelCheckbox) {
                kernelCheckbox.disabled = true;
            }
        } else {
            status.textContent = 'INACTIVE';
            status.className = 'tool-status inactive';
            button.textContent = 'Enable HWID Spoofing';
            button.className = 'btn btn-primary';
            console.log('UI updated to INACTIVE state'); // Debug logging
            
            // Enable kernel checkbox when inactive (if kernel is available)
            if (kernelCheckbox) {
                kernelCheckbox.disabled = !hwidSpooferState.kernelAvailable;
            }
        }
    } else {
        console.error('Could not find status or button elements!'); // Debug logging
    }
    
    if (backupStatus) {
        backupStatus.textContent = hwidSpooferState.hasBackup ? 'Available' : 'Auto-backup on first use';
        backupStatus.className = hwidSpooferState.hasBackup ? 'status-success' : 'status-info';
    }
    
    if (spoofedCount) {
        spoofedCount.textContent = hwidSpooferState.spoofedCount;
    }
    
    if (protectionLevel) {
        const level = hwidSpooferState.kernelMode ? 'Kernel-Level' : 'Registry-Level';
        protectionLevel.textContent = level;
        protectionLevel.className = hwidSpooferState.kernelMode ? 'status-success' : 'status-info';
    }
    
    // Show/hide advanced options based on active state
    if (advancedOptions) {
        advancedOptions.style.display = hwidSpooferState.active ? 'block' : 'none';
        console.log('Advanced options display:', advancedOptions.style.display); // Debug logging
    }
}

// Initialize HWID spoofer status when tools tab is loaded
async function initializeHWIDSpooferStatus() {
    try {
        // Give the backend time to initialize state detection
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Check kernel availability first - allow it to be available even if driver needs setup
        try {
            const kernelAvailability = await ipcRenderer.invoke('kernel-hwid-check-availability');
            // Consider kernel available if it's just missing driver/admin, not completely broken
            hwidSpooferState.kernelAvailable = kernelAvailability.success || 
                                              kernelAvailability.requiresAdmin || 
                                              kernelAvailability.requiresDriver;
            console.log('Kernel availability:', hwidSpooferState.kernelAvailable, kernelAvailability);
        } catch (error) {
            console.error('Failed to check kernel availability:', error);
            hwidSpooferState.kernelAvailable = true; // Allow user to try kernel mode
        }
        
        // Check both registry and kernel status to determine current state
        const registryStatus = await ipcRenderer.invoke('hwid-spoofer-get-status');
        const kernelStatus = await ipcRenderer.invoke('kernel-hwid-get-status');
        const kernelDetailedStatus = await ipcRenderer.invoke('kernel-hwid-get-detailed-status');
        
        console.log('Registry status:', registryStatus);
        console.log('Kernel status:', kernelStatus);
        console.log('Kernel detailed status:', kernelDetailedStatus);
        
        // Determine if kernel mode was used based on which one is active
        const isKernelActive = (kernelStatus && kernelStatus.isActive) || (kernelDetailedStatus && kernelDetailedStatus.isActive);
        const isRegistryActive = registryStatus && registryStatus.active;
        
        if (isKernelActive) {
            hwidSpooferState.active = true;
            hwidSpooferState.kernelMode = true;
            hwidSpooferState.hasBackup = (kernelDetailedStatus && kernelDetailedStatus.hasBackup) || 
                                        (kernelStatus && kernelStatus.originalValues !== null);
            hwidSpooferState.spoofedCount = kernelDetailedStatus && kernelDetailedStatus.spoofedValues ? 
                                          Object.keys(kernelDetailedStatus.spoofedValues).length : 0;
            
            // Set kernel checkbox
            const kernelCheckbox = document.getElementById('hwid-kernel-mode');
            if (kernelCheckbox) {
                kernelCheckbox.checked = true;
            }
        } else if (isRegistryActive) {
            hwidSpooferState.active = true;
            hwidSpooferState.kernelMode = false;
            hwidSpooferState.hasBackup = registryStatus.hasBackup;
            hwidSpooferState.spoofedCount = registryStatus.spoofedValues ? Object.keys(registryStatus.spoofedValues).length : 0;
        } else {
            hwidSpooferState.active = false;
            hwidSpooferState.kernelMode = false;
            hwidSpooferState.hasBackup = (registryStatus && registryStatus.hasBackup) || 
                                        (kernelDetailedStatus && kernelDetailedStatus.hasBackup);
            hwidSpooferState.spoofedCount = 0;
        }
        
        updateHWIDSpooferUI();
        
        // Show notification if spoof was detected from previous session
        if (hwidSpooferState.active && hwidSpooferState.hasBackup) {
            const modeText = hwidSpooferState.kernelMode ? 'kernel-level' : 'registry-level';
            showNotification(`${modeText} HWID spoofing detected from previous session - Status: ACTIVE`, 'info');
        }
        
        // Add event listener for kernel mode checkbox
        const kernelCheckbox = document.getElementById('hwid-kernel-mode');
        if (kernelCheckbox) {
            kernelCheckbox.addEventListener('change', async function() {
                hwidSpooferState.kernelMode = this.checked;
                
                // Update backup status based on selected mode
                if (hwidSpooferState.kernelMode) {
                    // Check if kernel backup exists
                    const kernelStatus = await ipcRenderer.invoke('kernel-hwid-get-status');
                    const kernelDetailedStatus = await ipcRenderer.invoke('kernel-hwid-get-detailed-status');
                    hwidSpooferState.hasBackup = (kernelDetailedStatus && kernelDetailedStatus.hasBackup) || 
                                                (kernelStatus && kernelStatus.originalValues !== null);
                } else {
                    // Check if registry backup exists
                    const registryStatus = await ipcRenderer.invoke('hwid-spoofer-get-status');
                    hwidSpooferState.hasBackup = registryStatus && registryStatus.hasBackup;
                }
                
                updateHWIDSpooferUI();
            });
        }
    } catch (error) {
        console.error('Failed to initialize HWID spoofer status:', error);
    }
}



function initLicenseTab() {
    console.log('📄 License tab initialized');
}

function initSettingsTab() {
    console.log('⚙️ Settings tab initialized');
    
    // Load and initialize browser settings
    initBrowserSettings();
}

/**
 * Initialize browser settings functionality
 */
async function initBrowserSettings() {
    try {
        // Load current settings
        if (window.electronAPI && window.electronAPI.loadSettings) {
            const settings = await window.electronAPI.loadSettings();
            
            // Update UI with current settings
            updateBrowserSettingsUI(settings);
            
            // Set up event listeners
            setupBrowserSettingsEvents();
        }
    } catch (error) {
        console.error('Error initializing browser settings:', error);
    }
}

/**
 * Update browser settings UI with current values
 */
function updateBrowserSettingsUI(settings) {
    const browserSettings = settings.browser || {};
    
    // Default site selection
    const defaultSiteSelect = document.getElementById('default-site-select');
    if (defaultSiteSelect) {
        defaultSiteSelect.value = browserSettings.defaultSite || 'modrinth';
        
        // Show/hide custom URL input based on selection
        const customUrlDiv = document.getElementById('custom-default-url');
        const customUrlInput = document.getElementById('custom-default-url-input');
        if (defaultSiteSelect.value === 'custom') {
            customUrlDiv.style.display = 'block';
            if (customUrlInput) {
                customUrlInput.value = browserSettings.customDefaultUrl || '';
            }
        } else {
            customUrlDiv.style.display = 'none';
        }
    }
    
    // Other settings
    const showQuickNavToggle = document.getElementById('show-quick-nav');
    if (showQuickNavToggle) {
        showQuickNavToggle.checked = browserSettings.showQuickNav !== false;
    }
    
    const enableAdBlockerToggle = document.getElementById('enable-ad-blocker');
    if (enableAdBlockerToggle) {
        enableAdBlockerToggle.checked = browserSettings.enableAdBlocker !== false;
    }
    
    const autoOpenDownloadsToggle = document.getElementById('auto-open-downloads');
    if (autoOpenDownloadsToggle) {
        autoOpenDownloadsToggle.checked = browserSettings.autoOpenDownloads !== false;
    }
}

/**
 * Set up event listeners for browser settings
 */
function setupBrowserSettingsEvents() {
    // Default site selection
    const defaultSiteSelect = document.getElementById('default-site-select');
    if (defaultSiteSelect) {
        defaultSiteSelect.addEventListener('change', function() {
            const customUrlDiv = document.getElementById('custom-default-url');
            if (this.value === 'custom') {
                customUrlDiv.style.display = 'block';
            } else {
                customUrlDiv.style.display = 'none';
            }
            saveBrowserSettings();
        });
    }
    
    // Custom URL input
    const customUrlInput = document.getElementById('custom-default-url-input');
    if (customUrlInput) {
        customUrlInput.addEventListener('change', saveBrowserSettings);
    }
    
    // Toggle settings
    const toggles = [
        'show-quick-nav',
        'enable-ad-blocker', 
        'auto-open-downloads'
    ];
    
    toggles.forEach(toggleId => {
        const toggle = document.getElementById(toggleId);
        if (toggle) {
            toggle.addEventListener('change', saveBrowserSettings);
        }
    });
}

/**
 * Save browser settings
 */
async function saveBrowserSettings() {
    try {
        if (!window.electronAPI || !window.electronAPI.loadSettings || !window.electronAPI.saveSettings) {
            console.error('Settings API not available');
            return;
        }
        
        // Load current settings
        const settings = await window.electronAPI.loadSettings();
        
        // Update browser settings
        if (!settings.browser) {
            settings.browser = {};
        }
        
        // Get values from UI
        const defaultSiteSelect = document.getElementById('default-site-select');
        const customUrlInput = document.getElementById('custom-default-url-input');
        const showQuickNavToggle = document.getElementById('show-quick-nav');
        const enableAdBlockerToggle = document.getElementById('enable-ad-blocker');
        const autoOpenDownloadsToggle = document.getElementById('auto-open-downloads');
        
        if (defaultSiteSelect) {
            settings.browser.defaultSite = defaultSiteSelect.value;
        }
        
        if (customUrlInput) {
            settings.browser.customDefaultUrl = customUrlInput.value;
        }
        
        if (showQuickNavToggle) {
            settings.browser.showQuickNav = showQuickNavToggle.checked;
        }
        
        if (enableAdBlockerToggle) {
            settings.browser.enableAdBlocker = enableAdBlockerToggle.checked;
        }
        
        if (autoOpenDownloadsToggle) {
            settings.browser.autoOpenDownloads = autoOpenDownloadsToggle.checked;
        }
        
        // Save settings
        const result = await window.electronAPI.saveSettings(settings);
        
        if (result.success) {
            console.log('✅ Browser settings saved successfully');
            
            // Show brief success notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            notification.textContent = '✅ Browser settings saved!';
            
            document.body.appendChild(notification);
            setTimeout(() => notification.style.opacity = '1', 10);
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 2000);
            
        } else {
            console.error('❌ Failed to save browser settings:', result.message);
        }
        
    } catch (error) {
        console.error('Error saving browser settings:', error);
    }
}

/**
 * Show settings section (navigation between settings tabs)
 */
function showSettingsSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.settings-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all tabs
    const tabs = document.querySelectorAll('.settings-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected section
    const targetSection = document.getElementById(`${sectionName}-settings`);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Add active class to clicked tab
    const targetTab = document.querySelector(`[data-section="${sectionName}"]`);
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    console.log(`🎛️ Switched to ${sectionName} settings`);
}

// Clean no-mods message for when no real mods are found
function displayNoModsMessage(gameId) {
    const modGrid = document.getElementById('mod-grid');
    const modTabs = document.getElementById('mod-tabs');
    if (!modGrid) return;
    
    // Hide tabs for all games when no mods
    if (modTabs) modTabs.style.display = 'none';
    
    const gameName = getGameDisplayName(gameId);
    
    modGrid.innerHTML = `
        <div class="no-mods-container">
            <div class="no-mods-icon">📦</div>
            <h3>No Mods Found for ${gameName}</h3>
            <p>Get started by adding some mods to your ${gameName} installation.</p>
            
            <div class="no-mods-actions">
                <button class="btn btn-primary" onclick="showAddModDialog()">
                    <span class="btn-icon">➕</span>
                    Add Mods
                </button>
                <button class="btn btn-secondary" onclick="refreshMods()">
                    <span class="btn-icon">🔄</span>
                    Refresh
                </button>
                <button class="btn btn-tertiary" onclick="openModFolder()">
                    <span class="btn-icon">📁</span>
                    Open Mod Folder
                </button>
            </div>
            
            <div class="no-mods-help">
                <p><strong>💡 Tip:</strong> Place your mod files in the game's mods folder, or use the "Add Mods" button to install them automatically.</p>
            </div>
        </div>
    `;
    
    // Update mod count
    updateModCount(0);
}

// Standard mods display for non-Minecraft games
function displayStandardMods(mods, gameId, totalCount) {
    const modGrid = document.getElementById('mod-grid');
    const modTabs = document.getElementById('mod-tabs');
    if (!modGrid) return;
    
    // Hide tabs for non-Minecraft games
    if (modTabs) modTabs.style.display = 'none';
    
    modGrid.innerHTML = `
        <div class="mod-list-content">
            ${mods.map(mod => {
                // Extract mod name from filename if name field is missing
                const modName = mod.name || (mod.path ? mod.path.split('/').pop().split('\\').pop() : 'Unknown Mod');
                const escapedPath = escapeFilePath(mod.path);
                const escapedName = modName.replace(/'/g, "\\'");
                
                return `
                    <div class="mod-item">
                        <div class="mod-info">
                            <h4>${modName}</h4>
                            <p>Size: ${formatFileSize(mod.size)} • Modified: ${formatModDate(mod.modified)}</p>
                        </div>
                        <div class="mod-actions">
                            <button class="btn btn-danger btn-sm" onclick="showDeleteModDialog('${escapedPath}', '${escapedName}')" title="Delete mod">
                                <span class="btn-icon">🗑️</span>
                                Delete
                            </button>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;
}

console.log('✅ Armory X Electron JavaScript loaded');

// Initialize global variables
window.currentModsByCategory = null;
window.currentModsTotal = 0;

// Update category counts for Minecraft mods
function updateCategoryCounts() {
    if (!window.currentModsByCategory) return;
    
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        const category = item.getAttribute('data-category');
        const countElement = item.querySelector('.category-count');
        
        if (category && countElement && window.currentModsByCategory[category]) {
            const mods = window.currentModsByCategory[category];
            countElement.textContent = `${mods.length} mods`;
        }
    });
}



// Professional Add Mod Dialog using Custom Modal System
function showAddModDialog() {
    if (!selectedGameId) {
        showNotification('Please select a game first', 'warning');
        return;
    }
    
    // Determine supported file types based on the selected game
    const supportedFileTypes = getGameSupportedFileTypes(selectedGameId);
    const acceptString = supportedFileTypes.join(',');
    const formatTags = supportedFileTypes.map(ext => `<span class="format-tag">${ext}</span>`).join('');
    
    // Generate category options based on selected game
    let categoryOptions = '<option value="">📋 Select a category...</option>';
    
    if (selectedGameId === 'minecraft') {
        categoryOptions += `
            <option value="Mods">🎮 Mods</option>
            <option value="Shaders">✨ Shaders</option>
            <option value="Resource Packs">🎨 Resource Packs</option>
        `;
    }
    
    const content = `
        <div class="add-mod-content">
            <div class="mod-upload-section">
                <div class="upload-instructions">
                    <div class="instructions-icon">📋</div>
                    <h4>How to Add Mods</h4>
                    <div class="step-list">
                        <div class="step">
                            <span class="step-number">1</span>
                            <span class="step-text">Click "Browse Files" below to select your mod files</span>
                        </div>
                        <div class="step">
                            <span class="step-number">2</span>
                            <span class="step-text">Choose categories for each mod</span>
                        </div>
                        <div class="step">
                            <span class="step-number">3</span>
                            <span class="step-text">Click "Add Mods" to install them</span>
                        </div>
                    </div>
                    <div class="supported-formats-info">
                        <strong>Supported Mod Types:</strong>
                        <div class="format-tags-container">
                            ${formatTags}
                        </div>
                    </div>
                </div>
                
                <div class="file-browse-section">
                    <button class="btn-browse-files" onclick="triggerFileInput()">
                        <span class="browse-icon">📁</span>
                        <span>Browse Files</span>
                    </button>
                    <p class="browse-help">💡 You can select multiple mod files at once using Ctrl+Click or Shift+Click</p>
                </div>
                
                <input type="file" id="professional-mod-files" multiple accept="${acceptString}" style="display: none;" onchange="handleProfessionalModFiles()">
            </div>
            
            <div id="professional-selected-files" class="professional-selected-files">
                <!-- Selected files with individual category selection will be shown here -->
            </div>
        </div>
    `;
    
    showCustomModal({
        title: '➕ Add New Mods',
        content: content,
        customClass: 'add-mods-modal-wide',
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    selectedModFiles = [];
                    closeCustomModal();
                }
            },
            {
                text: 'Add Mods',
                class: 'btn-primary',
                action: () => addSelectedMods()
            }
        ],
        showCloseButton: false
    });
}

// Helper function to get supported file types for each game
function getGameSupportedFileTypes(gameId) {
    const presetGameTypes = {
        'minecraft': ['.jar', '.zip'],
        'fs22': ['.zip'],
        'schedule1': ['.dll']
    };
    
    // Check if it's a preset game
    if (presetGameTypes[gameId]) {
        return presetGameTypes[gameId];
    }
    
    // For custom games, check the customGames array
    if (gameId && gameId.startsWith('custom_') && typeof customGames !== 'undefined') {
        const customGame = customGames.find(game => game.id === gameId);
        if (customGame && customGame.supportedFileTypes) {
            return customGame.supportedFileTypes;
        }
    }
    
    // Default fallback
    return ['.zip', '.rar', '.7z', '.jar', '.dll', '.mod'];
}

// Function to actually add the selected mods
async function addSelectedMods() {
    if (!selectedModFiles || selectedModFiles.length === 0) {
        showNotification('Please select some mod files first', 'warning');
        return;
    }
    
    if (!selectedGameId) {
        showNotification('No game selected', 'error');
        return;
    }
    
    try {
        console.log(`📦 Adding ${selectedModFiles.length} mods to ${selectedGameId}`);
        
        // Check if we're in an Electron environment
        const isElectron = window.electronAPI || window.require || (typeof require !== 'undefined');
        
        if (!isElectron) {
            showNotification('Mod installation requires the desktop app', 'info');
            return;
        }
        
        // Validate that each file has a category selected
        const categorySelects = document.querySelectorAll('.file-category-select');
        const fileCategories = [];
        const missingCategories = [];
        
        categorySelects.forEach((select, index) => {
            const category = select.value;
            if (!category || category === '') {
                missingCategories.push(selectedModFiles[index].name);
            } else {
                fileCategories.push({
                    file: selectedModFiles[index],
                    category: category
                });
            }
        });
        
        // Show inline warning if any files are missing categories
        if (missingCategories.length > 0) {
            // Remove any existing warning popup
            const existingPopup = document.querySelector('.category-warning-popup');
            if (existingPopup) {
                existingPopup.remove();
            }
            
            // Highlight files missing categories
            categorySelects.forEach((select, index) => {
                const fileItem = select.closest('.file-item-professional');
                if (!select.value || select.value === '') {
                    fileItem.classList.add('missing-category');
                } else {
                    fileItem.classList.remove('missing-category');
                }
            });
            
            // Find the Add Mods button in the modal footer
            const addModsButton = document.querySelector('.custom-modal-footer .btn-primary');
            if (addModsButton) {
                // Add shake animation to button
                addModsButton.classList.add('btn-shake');
                setTimeout(() => {
                    addModsButton.classList.remove('btn-shake');
                }, 500);
                
                // Create and show warning popup above button
                const warningPopup = document.createElement('div');
                warningPopup.className = 'category-warning-popup';
                warningPopup.textContent = `⚠️ Please assign categories to ${missingCategories.length} file${missingCategories.length !== 1 ? 's' : ''}`;
                
                // Insert popup before the button
                addModsButton.parentNode.insertBefore(warningPopup, addModsButton);
                
                // Show popup with animation
                setTimeout(() => {
                    warningPopup.classList.add('show');
                }, 10);
                
                // Auto-hide popup after 4 seconds
                setTimeout(() => {
                    if (warningPopup.parentNode) {
                        warningPopup.classList.remove('show');
                        setTimeout(() => {
                            if (warningPopup.parentNode) {
                                warningPopup.remove();
                            }
                        }, 300);
                    }
                }, 4000);
            }
            
            console.log(`⚠️ Missing categories for: ${missingCategories.join(', ')}`);
            return;
        }
        
        // Clear any category highlighting if all categories are now selected
        document.querySelectorAll('.file-item-professional').forEach(item => {
            item.classList.remove('missing-category');
        });
        
        // Prepare file data with individual categories
        const modFileData = fileCategories.map(item => {
            let filePath;
            if (item.file.path) {
                // Electron/Node.js File object with path
                filePath = item.file.path;
            } else if (item.file.webkitRelativePath) {
                // Web File object with relative path
                filePath = item.file.webkitRelativePath;
            } else {
                // Fallback to name (though this won't work for actual installation)
                filePath = item.file.name;
            }
            
            return {
                filePath: filePath,
                category: item.category,
                fileName: item.file.name
            };
        });
        
        console.log('🎯 Installing mods with categories:', modFileData);
        
        let result;
        try {
            if (window.electronAPI && window.electronAPI.installModsWithCategories) {
                result = await window.electronAPI.installModsWithCategories({
                    gameId: selectedGameId,
                    modFileData: modFileData
                });
            } else if (window.ipcRenderer) {
                result = await window.ipcRenderer.invoke('install-mods-with-categories', {
                    gameId: selectedGameId,
                    modFileData: modFileData
                });
            } else {
                const { ipcRenderer } = require('electron');
                result = await ipcRenderer.invoke('install-mods-with-categories', {
                    gameId: selectedGameId,
                    modFileData: modFileData
                });
            }
        } catch (error) {
            console.log('⚠️ New handler not available, using legacy installation');
            // Fallback to legacy handler if new one isn't available
            if (fileCategories.length === 1) {
                const singleFile = fileCategories[0];
                result = await window.ipcRenderer.invoke('install-mods', {
                    gameId: selectedGameId,
                    filePaths: [singleFile.file.path || singleFile.file.name],
                    category: singleFile.category
                });
            } else {
                throw new Error('Multiple files with different categories require updated backend');
            }
        }
        
        console.log('🎯 Installation result:', result);
        
        if (result && result.success) {
            const installedCount = result.installedMods || selectedModFiles.length;
            showNotification(`Successfully installed ${installedCount} mod${installedCount !== 1 ? 's' : ''}!`, 'success');
            
            // Clear selected files and close modal
            selectedModFiles = [];
            closeCustomModal();
            
            // Refresh the mod list to show new mods with smooth transition
            console.log('🔄 Refreshing mods after installation...');
            await refreshModsWithTransition(selectedGameId);
            
            console.log('✅ Mod installation completed successfully');
        } else {
            const errorMessage = result?.message || 'Failed to install mods';
            showNotification(errorMessage, 'error');
            console.error('❌ Mod installation failed:', result);
        }
        
    } catch (error) {
        console.error('❌ Error installing mods:', error);
        showNotification(`Failed to install mods: ${error.message}`, 'error');
    }
}

function triggerFileInput() {
    document.getElementById('professional-mod-files').click();
}

let selectedModFiles = [];

function handleProfessionalModFiles() {
    const fileInput = document.getElementById('professional-mod-files');
    if (!fileInput) {
        console.error('❌ File input not found');
        return;
    }
    
    const files = Array.from(fileInput.files);
    const selectedFilesContainer = document.getElementById('professional-selected-files');
    const uploadArea = document.getElementById('upload-area');
    
    // Add null checks to prevent errors
    if (!selectedFilesContainer) {
        console.error('❌ Selected files container not found');
        return;
    }
    
    selectedModFiles = files;
    
    if (files.length === 0) {
        selectedFilesContainer.innerHTML = '';
        if (uploadArea) {
            uploadArea.classList.remove('has-files');
        }
        return;
    }
    
    if (uploadArea) {
        uploadArea.classList.add('has-files');
    }
    
    // Generate category options based on selected game
    let categoryOptions = '<option value="">📋 Select category...</option>';
    
    if (selectedGameId === 'minecraft') {
        categoryOptions += `
            <option value="Mods">🎮 Mods</option>
            <option value="Shaders">✨ Shaders</option>
            <option value="Resource Packs">🎨 Resource Packs</option>
        `;
    } else {
        categoryOptions += `<option value="Mods">🎮 Mods</option>`;
    }
    
    selectedFilesContainer.innerHTML = `
        <div class="files-preview-professional">
            <div class="files-header">
                <h4>📦 Selected Files (${files.length})</h4>
                <button class="btn-clear-all" onclick="clearAllFiles()">Clear All</button>
            </div>
            
            <div class="bulk-category-section">
                <div class="bulk-category-wrapper">
                    <label class="bulk-category-label">🚀 Apply to All:</label>
                    <select class="bulk-category-select" id="bulk-category-select" onchange="applyBulkCategory()">
                        <option value="">Select category for all files...</option>
                        ${categoryOptions.replace('<option value="">📋 Select category...</option>', '')}
                    </select>
                    <button class="btn btn-secondary btn-sm" onclick="clearBulkCategory()" title="Clear all categories">
                        Clear All
                    </button>
                </div>
                <p class="bulk-category-help">💡 Select a category to apply it to all files at once, or set individual categories below.</p>
            </div>
            
            <div class="file-list-professional">
                ${files.map((file, index) => `
                    <div class="file-item-professional" data-index="${index}">
                        <div class="file-icon">${getFileIcon(file.name)}</div>
                        <div class="file-details">
                            <span class="file-name">${file.name}</span>
                            <span class="file-size">${formatFileSize(file.size)}</span>
                        </div>
                        <div class="file-category-wrapper">
                            <label class="category-label">Category:</label>
                            <select class="file-category-select" data-file-index="${index}">
                                ${categoryOptions}
                            </select>
                        </div>
                        <button class="btn-remove-file" onclick="removeFile(${index})" title="Remove file">×</button>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function getFileIcon(fileName) {
    const ext = fileName.split('.').pop().toLowerCase();
    const icons = {
        'jar': '☕',
        'zip': '📦',
        'rar': '📋',
        '7z': '🗜️',
        'mod': '🎮'
    };
    return icons[ext] || '📄';
}

function clearAllFiles() {
    selectedModFiles = [];
    document.getElementById('professional-mod-files').value = '';
    document.getElementById('professional-selected-files').innerHTML = '';
    document.getElementById('upload-area').classList.remove('has-files');
}

function removeFile(index) {
    const fileInput = document.getElementById('professional-mod-files');
    const dt = new DataTransfer();
    
    selectedModFiles.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });
    
    selectedModFiles.splice(index, 1);
    fileInput.files = dt.files;
    handleProfessionalModFiles();
}

// Apply bulk category to all files
function applyBulkCategory() {
    const bulkSelect = document.getElementById('bulk-category-select');
    const selectedCategory = bulkSelect.value;
    
    if (selectedCategory) {
        // Apply to all individual file selects
        const fileSelects = document.querySelectorAll('.file-category-select');
        fileSelects.forEach(select => {
            select.value = selectedCategory;
            // Remove any missing category highlighting
            const fileItem = select.closest('.file-item-professional');
            if (fileItem) {
                fileItem.classList.remove('missing-category');
            }
        });
        
        console.log(`📦 Applied category "${selectedCategory}" to ${fileSelects.length} files`);
        showNotification(`Applied "${selectedCategory}" to all ${fileSelects.length} files`, 'success');
    }
}

// Clear all category selections
function clearBulkCategory() {
    // Clear bulk selector
    const bulkSelect = document.getElementById('bulk-category-select');
    if (bulkSelect) {
        bulkSelect.value = '';
    }
    
    // Clear all individual file selects
    const fileSelects = document.querySelectorAll('.file-category-select');
    fileSelects.forEach(select => {
        select.value = '';
        // Remove any missing category highlighting
        const fileItem = select.closest('.file-item-professional');
        if (fileItem) {
            fileItem.classList.remove('missing-category');
        }
    });
    
    console.log(`🧹 Cleared categories for ${fileSelects.length} files`);
    showNotification(`Cleared categories for all ${fileSelects.length} files`, 'info');
}

// Professional Delete Confirmation Dialog
function showDeleteModDialog(modPath, modName) {
    // Convert escaped path back to normal path for processing
    const actualPath = modPath.replace(/\\\\/g, '\\');
    const displayPath = displayFilePath(actualPath);
    
    showCustomModal({
        title: '🗑️ Delete Mod',
        content: `
            <div class="delete-mod-content">
                <div class="warning-icon">⚠️</div>
                <div class="delete-message">
                    <p>Are you sure you want to permanently delete this mod?</p>
                </div>
                <div class="mod-to-delete-professional">
                    <div class="mod-delete-info">
                        <h4>${modName}</h4>
                        <p class="mod-path-delete">${displayPath}</p>
                    </div>
                </div>
                <div class="warning-message-professional">
                    <span class="warning-icon-small">⚠️</span>
                    <span>This action cannot be undone!</span>
                </div>
            </div>
        `,
        type: 'warning',
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Delete Mod',
                class: 'btn-danger',
                action: () => confirmDeleteMod(actualPath, modName)
            }
        ]
    });
}

// Enhanced search functionality
function clearSearch() {
    const searchInput = document.getElementById('mod-search');
    const clearButton = document.getElementById('mod-search-clear');
    
    if (searchInput) {
        searchInput.value = '';
        searchInput.style.borderColor = '';
        searchInput.style.boxShadow = '';
        searchInput.focus(); // Keep focus for better UX
    }
    if (clearButton) {
        clearButton.style.display = 'none';
    }
    
    // If we're in categorized view, return to appropriate view
    const categoryModsContainer = document.getElementById('category-mods-container');
    const categoriesExist = window.currentModsByCategory && categoryModsContainer;
    
    if (categoriesExist) {
        // Check if any category is currently selected
        const activeCategory = document.querySelector('.category-item.active');
        
        if (activeCategory) {
            // Return to the selected category
            const categoryName = activeCategory.getAttribute('data-category');
            updateCategoryDisplay(categoryName);
        } else {
            // No category selected, show all categories combined
            showAllCategoriesCombined();
        }
        
        // Reset category counts
        updateCategoryCounts();
        
        // Update filter stats with total count
        const filterStats = document.getElementById('filter-stats');
        if (filterStats) {
            const totalMods = Object.values(window.currentModsByCategory).reduce((sum, mods) => sum + mods.length, 0);
            const statsText = filterStats.querySelector('.stats-text');
            if (statsText) {
                statsText.textContent = `${totalMods} mods total`;
                statsText.style.color = '#94a3b8';
            }
        }
    } else {
        filterMods();
    }
}

function filterMods() {
    const searchInput = document.getElementById('mod-search');
    const clearButton = document.getElementById('mod-search-clear');
    const filterStats = document.getElementById('filter-stats');
    const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
    
    // Show/hide clear button
    if (clearButton) {
        clearButton.style.display = searchTerm ? 'block' : 'none';
    }
    
    // Add visual feedback to search input
    if (searchInput) {
        if (searchTerm) {
            searchInput.style.borderColor = 'rgba(59, 130, 246, 0.5)';
            searchInput.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.1)';
        } else {
            searchInput.style.borderColor = '';
            searchInput.style.boxShadow = '';
        }
    }
    
    // Check if we're actually in a categorized view by looking for the DOM elements
    const categoryModsContainer = document.getElementById('category-mods-container');
    const categoriesExist = window.currentModsByCategory && categoryModsContainer;
    
    if (categoriesExist) {
        filterMinecraftCategorizedMods(searchTerm, filterStats);
    } else {
        // Standard filtering for non-categorized games
        filterStandardMods(searchTerm, filterStats);
    }
}

function filterMinecraftCategorizedMods(searchTerm, filterStats) {
    if (!window.currentModsByCategory) return;
    
    // Double-check we have the required DOM elements
    const categoryModsContainer = document.getElementById('category-mods-container');
    if (!categoryModsContainer) {
        console.warn('Category container not found, falling back to standard filtering');
        filterStandardMods(searchTerm, filterStats);
        return;
    }
    
    let totalMods = 0;
    let totalVisible = 0;
    const filteredCategories = {};
    
    // Filter across ALL categories
    for (const [category, mods] of Object.entries(window.currentModsByCategory)) {
        totalMods += mods.length;
        const filteredMods = mods.filter(mod => {
            const modName = mod.name || '';
            const modPath = mod.path || '';
            return !searchTerm || 
                   modName.toLowerCase().includes(searchTerm) || 
                   modPath.toLowerCase().includes(searchTerm);
        });
        
        if (filteredMods.length > 0) {
            filteredCategories[category] = filteredMods;
            totalVisible += filteredMods.length;
        }
    }
    
    // Update the display based on search results
    if (searchTerm && Object.keys(filteredCategories).length > 0) {
        // Show cross-category search results
        displayCrossCategoryResults(filteredCategories, searchTerm);
    } else if (!searchTerm) {
        // No search term - show original categorized view
        const activeCategory = Object.keys(window.currentModsByCategory)[0];
        updateCategoryDisplay(activeCategory);
    } else {
        // Search term but no results
        const container = document.getElementById('category-mods-container');
        if (container) {
            container.innerHTML = '<div class="no-results">No mods found matching your search.</div>';
        }
    }
    
    // Update category counts in sidebar
    updateCategoryCountsWithSearch(searchTerm);
    
    // Update filter stats with correct totals
    if (filterStats) {
        const statsText = filterStats.querySelector('.stats-text');
        if (statsText) {
            if (searchTerm) {
                statsText.textContent = `Showing ${totalVisible} of ${totalMods} mods`;
                statsText.style.color = totalVisible > 0 ? '#10b981' : '#ef4444';
            } else {
                statsText.textContent = `${totalMods} mods total`;
                statsText.style.color = '#94a3b8';
            }
        }
    }
}

function filterStandardMods(searchTerm, filterStats) {
    const modItems = document.querySelectorAll('.mod-item');
    let visibleCount = 0;
    let totalCount = window.currentModsTotal || modItems.length;
    
    modItems.forEach(item => {
        const modNameElement = item.querySelector('h4');
        const modPathElement = item.querySelector('.mod-path');
        
        const modName = modNameElement ? modNameElement.textContent.toLowerCase() : '';
        const modPath = modPathElement ? modPathElement.textContent.toLowerCase() : '';
        
        const isVisible = !searchTerm || 
                         modName.includes(searchTerm) || 
                         modPath.includes(searchTerm);
        
        if (isVisible) {
            item.style.display = ''; // Reset to original CSS display value
        } else {
            item.style.display = 'none';
        }
        if (isVisible) visibleCount++;
    });
    
    // Update filter stats
    if (filterStats) {
        const statsText = filterStats.querySelector('.stats-text');
        if (statsText) {
            if (searchTerm) {
                statsText.textContent = `Showing ${visibleCount} of ${totalCount} mods`;
                statsText.style.color = visibleCount > 0 ? '#10b981' : '#ef4444';
            } else {
                statsText.textContent = `${totalCount} mods total`;
                statsText.style.color = '#94a3b8';
            }
        }
    }
}

function displayCrossCategoryResults(filteredCategories, searchTerm) {
    const categoryModsContainer = document.getElementById('category-mods-container');
    if (!categoryModsContainer) return;

    let resultsHTML = '';
    
    for (const [category, mods] of Object.entries(filteredCategories)) {
        resultsHTML += `
            <div class="search-category-section">
                <h4 class="search-category-header">
                    <span class="category-icon">${getCategoryIcon(category)}</span>
                    ${category} (${mods.length} mods)
                </h4>
                ${mods.map(mod => {
                    const modName = mod.name || (mod.path ? mod.path.split('/').pop().split('\\').pop() : 'Unknown Mod');
                    const escapedPath = escapeFilePath(mod.path);
                    const escapedName = modName.replace(/'/g, "\\'");
                    
                    return `
                        <div class="mod-item">
                            <div class="mod-info">
                                <h4>${modName}</h4>
                                <p>Size: ${formatFileSize(mod.size)} • Modified: ${formatModDate(mod.last_modified || mod.modified)}</p>
                            </div>
                            <div class="mod-actions">
                                <button class="btn btn-danger btn-sm" onclick="showDeleteModDialog('${escapedPath}', '${escapedName}')" title="Delete mod">
                                    <span class="btn-icon">🗑️</span>
                                    Delete
                                </button>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }
    
    categoryModsContainer.innerHTML = resultsHTML;
}

function updateCategoryDisplay(category) {
    const categoryMods = window.currentModsByCategory[category];
    const activeCategoryTitle = document.getElementById('active-category-title');
    const activeCategoryDescription = document.getElementById('active-category-description');
    const activeCategoryCount = document.getElementById('active-category-count');
    const categoryModsContainer = document.getElementById('category-mods-container');
    
    if (activeCategoryTitle) {
        activeCategoryTitle.innerHTML = `${getCategoryIcon(category)} ${category}`;
    }
    if (activeCategoryDescription) {
        activeCategoryDescription.textContent = getCategoryDescription(category);
    }
    if (activeCategoryCount) {
        activeCategoryCount.textContent = `${categoryMods.length} mods`;
    }
    if (categoryModsContainer) {
        categoryModsContainer.innerHTML = generateCategoryModsHTML(categoryMods);
    }
}

function updateCategoryCountsWithSearch(searchTerm) {
    if (!window.currentModsByCategory) return;
    
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        const category = item.getAttribute('data-category');
        const countElement = item.querySelector('.category-count');
        
        if (category && countElement && window.currentModsByCategory[category]) {
            const mods = window.currentModsByCategory[category];
            if (searchTerm) {
                // Count filtered mods in this category
                const filteredCount = mods.filter(mod => {
                    const modName = mod.name || '';
                    const modPath = mod.path || '';
                    return modName.toLowerCase().includes(searchTerm) || 
                           modPath.toLowerCase().includes(searchTerm);
                }).length;
                countElement.textContent = `${filteredCount} mods`;
            } else {
                // Show original count
                countElement.textContent = `${mods.length} mods`;
            }
        }
    });
}

// Update mod count display
function updateModCount(count) {
    const modCountElement = document.getElementById('mod-count');
    if (modCountElement) {
        modCountElement.textContent = `${count} mod${count !== 1 ? 's' : ''} loaded`;
    }
}

// Update filter stats display
function updateFilterStats(visibleCount, totalCount) {
    const filterStats = document.getElementById('filter-stats');
    if (filterStats) {
        const statsText = filterStats.querySelector('.stats-text');
        if (statsText) {
            if (visibleCount === totalCount) {
                statsText.textContent = `${totalCount} mods total`;
                statsText.style.color = '#94a3b8';
            } else {
                statsText.textContent = `Showing ${visibleCount} of ${totalCount} mods`;
                statsText.style.color = visibleCount > 0 ? '#10b981' : '#ef4444';
            }
        }
    }
}

async function confirmDeleteMod(modPath, modName) {
    try {
        console.log(`🗑️ Deleting mod: ${modName}`);
        
        // Check if we're in an Electron environment
        const isElectron = window.electronAPI || window.require || (typeof require !== 'undefined');
        
        if (isElectron) {
            let result;
            
            if (window.electronAPI && window.electronAPI.deleteMod) {
                result = await window.electronAPI.deleteMod(modPath);
            } else if (window.ipcRenderer) {
                result = await window.ipcRenderer.invoke('delete-mod', modPath);
            } else {
                const { ipcRenderer } = require('electron');
                result = await ipcRenderer.invoke('delete-mod', modPath);
            }
            
            if (result.success) {
                showNotification(`Successfully deleted "${modName}"`, 'success');
                closeCustomModal();
                
                // Ensure we have a selected game before refreshing
                if (!selectedGameId) {
                    console.error('❌ No selected game ID for refresh');
                    showNotification('Error: No game selected for refresh', 'error');
                    return;
                }
                
                console.log('🔄 Refreshing mods after deletion...');
                
                // Show loading state in the mod grid immediately
                const modGrid = document.getElementById('mod-grid');
                if (modGrid) {
                    modGrid.innerHTML = `
                        <div class="loading-message" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <div class="loading-spinner" style="margin: 0 auto 16px auto;"></div>
                            <p>Refreshing mods...</p>
                        </div>
                    `;
                }
                
                // Small delay to ensure UI updates, then refresh with smooth transition
                setTimeout(async () => {
                    try {
                        await refreshModsWithTransition(selectedGameId);
                        console.log('✅ Mod list refreshed smoothly after deletion');
                    } catch (refreshError) {
                        console.error('❌ Error refreshing mods after deletion:', refreshError);
                        showNotification('Warning: Mod deleted but failed to refresh list', 'warning');
                        
                        // Fallback: try to reload the current mod data
                        if (modGrid) {
                            modGrid.innerHTML = `
                                <div class="error-message" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                                    <h3>Refresh Failed</h3>
                                    <p>Mod was deleted successfully, but failed to refresh the list.</p>
                                    <button class="btn btn-primary" onclick="refreshModsWithTransition('${selectedGameId}')">
                                        <span class="btn-icon">🔄</span>
                                        Try Again
                                    </button>
                                </div>
                            `;
                        }
                    }
                }, 100);
                
            } else {
                showNotification(result.message || 'Failed to delete mod', 'error');
            }
        } else {
            // Web mode fallback
            console.log('🌐 Web mode - cannot delete mods directly');
            showNotification('Mod deletion functionality requires the desktop app', 'info');
            closeCustomModal();
        }
        
    } catch (error) {
        console.error('Error deleting mod:', error);
        showNotification('Failed to delete mod: ' + error.message, 'error');
        closeCustomModal();
    }
}

// Enhanced keyboard shortcuts (keeping useful ones, removing debug ones)
document.addEventListener('keydown', function(event) {
    // Ctrl+F for search focus
    if (event.ctrlKey && event.key === 'f') {
        event.preventDefault();
        const searchInput = document.getElementById('mod-search');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
    
    // Escape to close dialogs
    if (event.key === 'Escape') {
        const addDialog = document.getElementById('add-mod-dialog');
        const deleteDialog = document.getElementById('delete-mod-dialog');
        
        if (addDialog.style.display === 'flex') {
            closeCustomModal();
        } else if (deleteDialog.style.display === 'flex') {
            closeCustomModal();
        }
    }
    
    // Ctrl+R for refresh (prevent browser refresh)
    if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        refreshMods();
    }
});

// Helper functions for the professional mod manager
function getGameDisplayName(gameId) {
    const names = {
        'minecraft': 'Minecraft',
        'fs22': 'Farming Simulator 22',
        'fs25': 'Farming Simulator 25',
        'schedule1': 'Schedule 1'
    };
    
    // Handle preset games
    if (names[gameId]) {
        return names[gameId];
    }
    
    // Handle custom games - look up the actual name from customGames array
    if (gameId && gameId.startsWith('custom_')) {
        const customGame = customGames.find(game => game.id === gameId);
        if (customGame && customGame.name) {
            return customGame.name;
        }
    }
    
    // Fallback to gameId if not found
    return gameId;
}

function formatModDate(dateString) {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

async function refreshMods() {
    if (selectedGameId) {
        console.log('🔄 Refreshing mods for:', selectedGameId);
        await refreshModsWithTransition(selectedGameId);
        showNotification('Mods refreshed successfully', 'success');
    } else {
        showNotification('Please select a game first', 'warning');
    }
}

async function openModFolder() {
    if (selectedGameId) {
        try {
            console.log(`📁 Opening mod folder for: ${selectedGameId}`);
            
            // Check if we're in an Electron environment
            const isElectron = window.electronAPI || window.require || (typeof require !== 'undefined');
            
            if (isElectron) {
                let result;
                
                if (window.electronAPI && window.electronAPI.openModFolder) {
                    result = await window.electronAPI.openModFolder(selectedGameId);
                } else if (window.ipcRenderer) {
                    result = await window.ipcRenderer.invoke('open-mod-folder', selectedGameId);
                } else {
                    const { ipcRenderer } = require('electron');
                    result = await ipcRenderer.invoke('open-mod-folder', selectedGameId);
                }
                
                console.log('📁 Open folder result:', result);
                
                if (result.success) {
                    const message = result.created ? 
                        `Created and opened mod folder: ${result.path}` : 
                        `Opened mod folder: ${result.path}`;
                    showNotification(message, 'success');
                } else {
                    showNotification(result.message || 'Failed to open mod folder', 'error');
                }
            } else {
                // Web mode fallback
                console.log('🌐 Web mode - cannot open mod folder directly');
                showNotification('Mod folder functionality requires the desktop app', 'info');
            }
        } catch (error) {
            console.error('Error opening mod folder:', error);
            showNotification('Failed to open mod folder: ' + error.message, 'error');
        }
    } else {
        showNotification('Please select a game first', 'warning');
    }
}

async function showModInFolder(modPath) {
    try {
        // Check if we're in an Electron environment
        const isElectron = window.electronAPI || window.require || (typeof require !== 'undefined');
        
        if (isElectron) {
            let result;
            
            if (window.electronAPI && window.electronAPI.showInFolder) {
                result = await window.electronAPI.showInFolder(modPath);
            } else if (window.ipcRenderer) {
                result = await window.ipcRenderer.invoke('show-in-folder', modPath);
            } else {
                const { ipcRenderer } = require('electron');
                result = await ipcRenderer.invoke('show-in-folder', modPath);
            }
            
            if (result.success) {
                showNotification('File shown in folder', 'success');
            } else {
                showNotification('Failed to show file in folder', 'error');
            }
        } else {
            // Web mode fallback
            console.log('🌐 Web mode - cannot show file in folder directly');
            showNotification('Show in folder functionality requires the desktop app', 'info');
        }
    } catch (error) {
        console.error('Error showing mod in folder:', error);
        showNotification('Failed to show file in folder', 'error');
    }
}

// Enhanced displayMods function with professional styling
function displayMods(mods, gameId) {
    const modGrid = document.getElementById('mod-grid');
    const modTabs = document.getElementById('mod-tabs');
    if (!modGrid) return;
    
    console.log('🎮 displayMods called with:', { 
        gameId, 
        modsType: typeof mods, 
        modsLength: mods?.length, 
        modsArray: Array.isArray(mods),
        mods: mods 
    });
    
    // Ensure mods is an array
    if (!mods || !Array.isArray(mods)) {
        console.log('⚠️ Invalid mods data, treating as empty array');
        mods = [];
    }
    
    // Update mod count and store globally for filter functions
    updateModCount(mods.length);
    window.currentModsTotal = mods.length;
    
    if (mods.length === 0) {
        // Clear global state for no mods case
        window.currentModsByCategory = null;
        window.currentModsTotal = 0;
        
        modGrid.innerHTML = `
            <div class="no-mods-message">
                <div class="no-mods-icon">📦</div>
                <h3>No mods found</h3>
                <p>No mods found for ${getGameDisplayName(gameId)}. Install some mods to get started!</p>
                <button class="btn btn-primary" onclick="showAddModDialog()">
                    <span class="btn-icon">➕</span>
                    Add Mods
                </button>
            </div>
        `;
        if (modTabs) modTabs.style.display = 'none';
        
        // Important: Update filter stats to show 0 mods
        updateFilterStats(0, 0);
        return;
    }
    
    // Group mods by category
    const modsByCategory = {};
    mods.forEach(mod => {
        const category = mod.category || 'Mods';
        if (!modsByCategory[category]) {
            modsByCategory[category] = [];
        }
        modsByCategory[category].push(mod);
    });
    
    // Special handling for Minecraft with multiple categories
    if (gameId === 'minecraft' && Object.keys(modsByCategory).length > 1) {
        displayMinecraftModsWithTabs(modsByCategory, mods.length);
        // Update category counts for multi-category display
        updateCategoryCounts();
    } else {
        // Standard display for single category games
        // Clear the categorized mods global state for non-categorized games
        window.currentModsByCategory = null;
        displayStandardMods(mods, gameId, mods.length);
        if (modTabs) modTabs.style.display = 'none';
    }
    
    // Update filter stats
    updateFilterStats(mods.length, mods.length);
}

function displayMinecraftModsWithTabs(modsByCategory, totalCount) {
    const modTabs = document.getElementById('mod-tabs');
    const modGrid = document.getElementById('mod-grid');
    if (!modTabs || !modGrid) return;
    
    const categories = Object.keys(modsByCategory);
    
    // Hide the original tabs container
    modTabs.style.display = 'none';
    
    // Store the categorized mods globally for category switching
    window.currentModsByCategory = modsByCategory;
    
    // Combine all mods from all categories for initial display
    const allMods = [];
    for (const [category, mods] of Object.entries(modsByCategory)) {
        allMods.push(...mods);
    }
    
    // Create vertical sidebar layout
    modGrid.innerHTML = `
        <div class="mod-sidebar-layout">
            <div class="mod-categories-sidebar">
                <div class="categories-header">
                    <h4>📂 Categories</h4>
                    <span class="total-categories">${categories.length} categories</span>
                </div>
                <div class="categories-list">
                    ${categories.map((category, index) => `
                        <div class="category-item" 
                             onclick="switchToCategory('${category}')" 
                             data-category="${category}">
                            <div class="category-icon">${getCategoryIcon(category)}</div>
                            <div class="category-info">
                                <span class="category-name">${category}</span>
                                <span class="category-count">${modsByCategory[category].length} mods</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="mod-content-area">
                <div class="category-content-header">
                    <div class="active-category-info">
                        <h3 id="active-category-title">📦 All Mods</h3>
                        <p id="active-category-description">All mods, shaders, and resource packs from all categories</p>
                    </div>
                    <div class="category-stats">
                        <span id="active-category-count">${allMods.length} mods</span>
                    </div>
                </div>
                
                <div class="category-mods-container" id="category-mods-container">
                    ${generateCategoryModsHTML(allMods)}
                </div>
            </div>
        </div>
    `;
}

// Helper function to generate mods HTML for a category
function generateCategoryModsHTML(categoryMods) {
    return categoryMods.map(mod => {
        // Extract mod name from filename if name field is missing
        const modName = mod.name || (mod.path ? mod.path.split('/').pop().split('\\').pop() : 'Unknown Mod');
        const escapedPath = escapeFilePath(mod.path);
        const escapedName = modName.replace(/'/g, "\\'");
        
        return `
            <div class="mod-item">
                <div class="mod-info">
                    <h4>${modName}</h4>
                    <p>Size: ${formatFileSize(mod.size)} • Modified: ${formatModDate(mod.last_modified || mod.modified)}</p>
                </div>
                <div class="mod-actions">
                    <button class="btn btn-danger btn-sm" onclick="showDeleteModDialog('${escapedPath}', '${escapedName}')" title="Delete mod">
                        <span class="btn-icon">🗑️</span>
                        Delete
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

// New function to switch between categories
function switchToCategory(category) {
    if (!window.currentModsByCategory) return;
    
    const selectedCategoryItem = document.querySelector(`[data-category="${category}"]`);
    
    // Check if this category is already selected
    if (selectedCategoryItem && selectedCategoryItem.classList.contains('active')) {
        // Unselect all categories and show all mods
        document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('active');
        });
        showAllCategoriesCombined();
        return;
    }
    
    // Update sidebar active state
    document.querySelectorAll('.category-item').forEach(item => {
        item.classList.remove('active');
    });
    
    if (selectedCategoryItem) {
        selectedCategoryItem.classList.add('active');
    }
    
    // Update content area
    const categoryMods = window.currentModsByCategory[category];
    const activeCategoryTitle = document.getElementById('active-category-title');
    const activeCategoryDescription = document.getElementById('active-category-description');
    const activeCategoryCount = document.getElementById('active-category-count');
    const categoryModsContainer = document.getElementById('category-mods-container');
    
    if (activeCategoryTitle) {
        activeCategoryTitle.innerHTML = `${getCategoryIcon(category)} ${category}`;
    }
    
    if (activeCategoryDescription) {
        activeCategoryDescription.textContent = getCategoryDescription(category);
    }
    
    if (activeCategoryCount) {
        activeCategoryCount.textContent = `${categoryMods.length} mods`;
    }
    
    if (categoryModsContainer) {
        categoryModsContainer.innerHTML = generateCategoryModsHTML(categoryMods);
    }
    
    // Update filter stats - always show total mod count, not category count
    updateFilterStats(categoryMods.length, window.currentModsTotal || 0);
}

// Function to show all mods from all categories combined
function showAllCategoriesCombined() {
    if (!window.currentModsByCategory) return;
    
    // Combine all mods from all categories
    const allMods = [];
    for (const [category, mods] of Object.entries(window.currentModsByCategory)) {
        allMods.push(...mods);
    }
    
    // Update content area to show all mods
    const activeCategoryTitle = document.getElementById('active-category-title');
    const activeCategoryDescription = document.getElementById('active-category-description');
    const activeCategoryCount = document.getElementById('active-category-count');
    const categoryModsContainer = document.getElementById('category-mods-container');
    
    if (activeCategoryTitle) {
        activeCategoryTitle.innerHTML = `📦 All Mods`;
    }
    
    if (activeCategoryDescription) {
        activeCategoryDescription.textContent = 'All mods, shaders, and resource packs from all categories';
    }
    
    if (activeCategoryCount) {
        activeCategoryCount.textContent = `${allMods.length} mods`;
    }
    
    if (categoryModsContainer) {
        categoryModsContainer.innerHTML = generateCategoryModsHTML(allMods);
    }
    
    // Update filter stats to show all mods
    updateFilterStats(allMods.length, window.currentModsTotal || 0);
}

function getCategoryIcon(category) {
    const icons = {
        'Performance & Optimization': '⚡',
        'Graphics & Shaders': '🎨',
        'Storage & Organization': '📦',
        'Technology & Machinery': '⚙️',
        'Magic & Enchanting': '🔮',
        'World Generation & Biomes': '🌍',
        'Food & Agriculture': '🌾',
        'Building & Decoration': '🏗️',
        'Transportation': '🚗',
        'Combat & Weapons': '⚔️',
        'Adventure & Exploration': '🗺️',
        'Animals & Pets': '🐕',
        'Utilities & Quality of Life': '🛠️',
        'API & Core Libraries': '📚',
        'Multiplayer & Social': '👥',
        'Miscellaneous': '📄'
    };
    return icons[category] || '📄';
}

function getCategoryDescription(category) {
    const descriptions = {
        'Performance & Optimization': 'Performance enhancers, FPS boosters, and optimization mods',
        'Graphics & Shaders': 'Visual enhancements, shaders, lighting, and graphics improvements',
        'Storage & Organization': 'Inventory management, storage solutions, and item organization systems',
        'Technology & Machinery': 'Industrial mods, automation, machinery, and technical systems',
        'Magic & Enchanting': 'Magical systems, spells, enchantments, and mystical content',
        'World Generation & Biomes': 'World generation, biomes, dimensions, and terrain modifications',
        'Food & Agriculture': 'Farming, cooking, food systems, and agricultural content',
        'Building & Decoration': 'Building blocks, furniture, decoration, and architectural elements',
        'Transportation': 'Vehicles, travel methods, teleportation, and movement systems',
        'Combat & Weapons': 'Weapons, armor, combat systems, and battle enhancements',
        'Adventure & Exploration': 'Quests, dungeons, exploration content, and adventure features',
        'Animals & Pets': 'Animals, pets, creatures, and wildlife additions',
        'Utilities & Quality of Life': 'Helper tools, UI improvements, and quality of life enhancements',
        'API & Core Libraries': 'Core libraries, APIs, frameworks, and mod dependencies',
        'Multiplayer & Social': 'Multiplayer features, social systems, and server enhancements',
        'Miscellaneous': 'Other mods that don\'t fit into specific categories'
    };
    return descriptions[category] || 'Various modifications for your game';
}

// Delete mode functionality
window.deleteMode = false;

function toggleDeleteMode() {
    window.deleteMode = !window.deleteMode;
    
    // Find the remove button by looking for the one that calls showRemoveGameDialog
    const removeButton = document.querySelector('[onclick*="showRemoveGameDialog"]');
    
    if (removeButton) {
        if (window.deleteMode) {
            // Update button to show exit delete mode
            removeButton.innerHTML = `
                <span class="btn-icon">✅</span>
                <span>Exit Remove Mode</span>
            `;
            removeButton.className = 'btn-compact btn-success';
            removeButton.title = 'Exit Remove Mode';
        } else {
            // Reset button to normal remove mode
            removeButton.innerHTML = `
                <span class="btn-icon">🗑️</span>
                <span>Remove</span>
            `;
            removeButton.className = 'btn-compact btn-secondary';
            removeButton.title = 'Remove Custom Game';
        }
    }
    
    // Re-populate the game library to update the display
    populateGameLibrary();
    
    if (window.deleteMode) {
        showNotification('🗑️ Remove mode activated - Click on custom games to remove them', 'warning');
        
        // Add global visual indicator to body
        const body = document.body;
        body.classList.add('delete-mode-active');
        
    } else {
        showNotification('✅ Remove mode deactivated', 'success');
        
        // Remove global visual indicator
        const body = document.body;
        body.classList.remove('delete-mode-active');
    }
}

// Confirm delete game function
function confirmDeleteGame(gameId, gameName) {
    showCustomModal({
        title: '🗑️ Remove Custom Game',
        content: `
            <div class="delete-game-content">
                <div class="warning-icon">⚠️</div>
                <div class="delete-message">
                    <p>Are you sure you want to remove <strong>"${gameName}"</strong>?</p>
                    <p>This will only remove the game configuration from Armory X. Your actual game files and mods will not be deleted.</p>
                </div>
            </div>
        `,
        type: 'warning',
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Remove Game',
                class: 'btn-danger',
                action: () => {
                    removeCustomGame(gameId, gameName);
                    closeCustomModal();
                }
            }
        ]
    });
}

// Edit custom game function
function editCustomGame(gameId) {
    console.log('✏️ Edit custom game called for:', gameId);
    
    // Ensure customGames array exists
    if (!customGames || !Array.isArray(customGames)) {
        console.error('❌ customGames array not found or invalid');
        showNotification('Error: Custom games data not found', 'error');
        return;
    }
    
    const game = customGames.find(g => g.id === gameId);
    if (!game) {
        console.error('❌ Game not found:', gameId);
        showNotification('Game not found', 'error');
        return;
    }
    
    console.log('✅ Found game to edit:', game.name);
    console.log('🔍 Game data:', JSON.stringify(game, null, 2));
    
    // Prepare safe values for form
    const safeName = (game.name || '').replace(/"/g, '&quot;');
    const safePath = (game.executablePath || '').replace(/"/g, '&quot;');
    const safeIcon = (game.iconPath || '').replace(/"/g, '&quot;');
    
    // Ensure modFolders exists and debug the data
    console.log('🐛 Raw game data in editCustomGame:', JSON.stringify(game, null, 2));
    const modFolders = game.modFolders || [{ name: 'Mods', path: '' }];
    const supportedFileTypes = game.supportedFileTypes || ['.zip', '.rar', '.7z', '.jar', '.exe', '.dll', '.pak', '.mod'];
    
    console.log('🗂️ Mod folders for edit:', modFolders);
    console.log('🔧 File types for edit:', supportedFileTypes);
    
    // Show the same dialog as add game but with pre-filled data
    const addGameContent = `
        <div class="add-game-form">
            <div class="form-section">
                <label for="custom-game-path">Game Executable Location:</label>
                <div class="path-input-group">
                    <input type="text" id="custom-game-path" class="modal-input" placeholder="Click Browse to select..." readonly value="${safePath}" />
                    <button class="btn btn-secondary" onclick="browseGameExecutable()">Browse</button>
                </div>
                <p class="form-help">Select the main executable file or shortcut for your game</p>
            </div>
            
            <div class="form-section">
                <label for="custom-game-name">Game Name:</label>
                <input type="text" id="custom-game-name" class="modal-input" placeholder="Enter game name..." value="${safeName}" />
            </div>
            
            <div class="form-section">
                <label for="custom-game-icon">Game Icon (Optional):</label>
                <div class="path-input-group">
                    <input type="text" id="custom-game-icon" class="modal-input" placeholder="Using default icon..." readonly value="${safeIcon}" />
                    <button class="btn btn-secondary" onclick="browseGameIcon()">Browse</button>
                </div>
                <div class="icon-preview" id="icon-preview" style="${game.iconPath ? 'display: flex; align-items: center; margin-top: 10px;' : 'display: none; margin-top: 10px;'}">
                    <img id="icon-preview-img" src="${safeIcon}" alt="Icon Preview" style="width: 32px; height: 32px; border-radius: 4px; border: 1px solid #374151;">
                    <span style="margin-left: 8px; color: #9ca3af; font-size: 12px;">Icon Preview</span>
                </div>
                <p class="form-help">💡 <strong>Tip:</strong> Icon extraction may not work for all files. Use Browse to manually select a custom .png, .jpg, or .ico icon file if needed.</p>
            </div>
            
            <div class="form-section">
                <label>Mod Folders:</label>
                <p class="form-help">Add folders where mods for this game are stored</p>
                <div id="custom-mod-folders">
                    <!-- Will be populated by JavaScript -->
                </div>
                <button class="btn btn-secondary" onclick="addModFolder()">+ Add Another Folder</button>
            </div>
            
            <div class="file-types-section">
                <label>Supported File Types:</label>
                <div class="file-types-dropdown" id="file-types-dropdown">
                    <div class="dropdown-header" onclick="toggleFileTypesDropdown()">
                        <span class="selected-text">Loading...</span>
                        <span class="dropdown-arrow">▼</span>
                    </div>
                    <div class="dropdown-options" id="file-types-options">
                        <div class="file-type-option">
                            <input type="checkbox" id="type-all" value="all">
                            <span>All Types (*.* - All file types)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-zip" value=".zip">
                            <span>.zip files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-rar" value=".rar">
                            <span>.rar files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-7z" value=".7z">
                            <span>.7z files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-jar" value=".jar">
                            <span>.jar files (Java/Minecraft)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-exe" value=".exe">
                            <span>.exe files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-dll" value=".dll">
                            <span>.dll files</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-pak" value=".pak">
                            <span>.pak files (Unreal Engine)</span>
                        </div>
                        <div class="file-type-option">
                            <input type="checkbox" id="type-mod" value=".mod">
                            <span>.mod files</span>
                        </div>
                    </div>
                </div>
                <p class="form-help">💡 <strong>Tip:</strong> Select the file types your mods use. If unsure, choose "All Types".</p>
            </div>
        </div>
    `;
    
    try {
        showCustomModal({
            title: `✏️ Edit ${game.name}`,
            content: addGameContent,
            type: 'info',
            preventOutsideClose: true,
            buttons: [
                {
                    text: 'Cancel',
                    class: 'btn-secondary',
                    action: () => closeCustomModal()
                },
                {
                    text: 'Save Changes',
                    class: 'btn-primary',
                    action: () => saveEditedGame(gameId)
                }
            ]
        });
        
        console.log('✅ Edit modal opened successfully');
        
        // Populate file types and mod folders after modal is created
        setTimeout(() => {
            populateFileTypesForEdit(supportedFileTypes);
            populateModFoldersForEdit(modFolders);
        }, 100);
    } catch (error) {
        console.error('❌ Error showing modal:', error);
        showNotification('Error opening edit dialog: ' + error.message, 'error');
    }
}

// Save edited game function
function saveEditedGame(gameId) {
    console.log('💾 Saving edited game:', gameId);
    
    const name = document.getElementById('custom-game-name').value.trim();
    const executablePath = document.getElementById('custom-game-path').value.trim();
    const iconPath = document.getElementById('custom-game-icon').value.trim();
    
    if (!name) {
        showNotification('Please enter a game name', 'warning');
        return;
    }
    
    if (!executablePath) {
        showNotification('Please select the game executable', 'warning');
        return;
    }
    
    // Collect mod folders from the form
    const folderItems = document.querySelectorAll('.mod-folder-item');
    const modFolders = [];
    
    folderItems.forEach(item => {
        const nameInput = item.querySelector('[data-type="name"]');
        const pathInput = item.querySelector('[data-type="path"]');
        
        if (nameInput && pathInput) {
            const name = nameInput.value.trim();
            const path = pathInput.value.trim();
            
            // Include folder even if path is empty (user can set it later)
            if (name) {
                modFolders.push({
                    name: name,
                    path: path
                });
            }
        }
    });
    
    // Ensure we have at least one mod folder
    if (modFolders.length === 0) {
        modFolders.push({ name: 'Mods', path: '' });
    }
    
    console.log('🗂️ Collected mod folders:', modFolders);
    
    // Collect selected file types from the form
    const fileTypeCheckboxes = document.querySelectorAll('#file-types-options input[type="checkbox"]:checked');
    let supportedFileTypes = Array.from(fileTypeCheckboxes).map(cb => cb.value);
    
    // Handle "All Types" selection - expand to include all common extensions
    if (supportedFileTypes.includes('all')) {
        supportedFileTypes = ['.zip', '.rar', '.7z', '.jar', '.exe', '.dll', '.pak', '.mod', '.msi', '.deb', '.rpm', '.tar', '.gz', '.bz2', '.xz', '.lzma', '.cab', '.iso', '.dmg', '.pkg', '.apk', '.ipa'];
    }
    
    // Ensure we have at least some file types selected
    if (supportedFileTypes.length === 0) {
        const existingGame = customGames.find(g => g.id === gameId);
        supportedFileTypes = existingGame?.supportedFileTypes || ['.zip', '.rar', '.7z', '.jar', '.exe', '.dll', '.pak', '.mod'];
    }
    
    console.log('🔧 Selected file types:', supportedFileTypes);
    
    // Find and update the game
    const gameIndex = customGames.findIndex(g => g.id === gameId);
    if (gameIndex !== -1) {
        // Check if icon is marked as small for edited game
        const editIconInput = document.getElementById('custom-game-icon');
        const isSmallIcon = editIconInput?.dataset?.isSmallIcon === 'true' || 
                           (iconPath && (iconPath.startsWith('data:image/') || iconPath.toLowerCase().endsWith('.ico'))) ||
                           customGames[gameIndex].isSmallIcon; // Preserve existing flag if no new icon
        
        // Create a new updated game object
        const updatedGame = {
            ...customGames[gameIndex],
            name: name,
            executablePath: executablePath,
            iconPath: iconPath || './assets/Games/steam_game.png',
            isSmallIcon: isSmallIcon, // Update the small icon flag
            modFolders: modFolders,
            supportedFileTypes: supportedFileTypes,
            lastModified: new Date().toISOString()
        };
        
        // Replace the game in the array
        customGames[gameIndex] = updatedGame;
        
        // Save to localStorage
        try {
            localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
            console.log('✅ Saved updated game to localStorage');
        } catch (error) {
            console.error('❌ Error saving to localStorage:', error);
        }
        
        populateGameLibrary();
        closeCustomModal();
        showNotification(`Game "${name}" updated successfully!`, 'success');
        console.log('✅ Custom game updated:', updatedGame);
    } else {
        console.error('❌ Game not found for editing:', gameId);
        showNotification('Game not found', 'error');
    }
}

// Generate unique game ID
function generateGameId() {
    return `custom_game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Show game type confirmation dialog for ALL games
function showGameTypeConfirmation(game) {
    console.log('🎮 Showing game type confirmation for:', game.name);
    
    showCustomModal({
        title: '🎮 Game Type Confirmation',
        content: `
            <div class="game-type-confirmation">
                <div class="confirmation-icon">🎮</div>
                <h3>What type of game is "${game.name}"?</h3>
                
                <div class="game-type-options">
                    <div class="type-option-explanation">
                        <p>Please confirm the game type to ensure the best experience:</p>
                    </div>
                    
                    <div class="type-options-grid">
                        <div class="type-option" onclick="selectGameType('steam-keep')">
                            <div class="type-icon">🎮</div>
                            <div class="type-title">Steam Game Shortcut</div>
                            <div class="type-description">
                                Will use a default card image, can be changed to a custom image if desired.
                            </div>
                        </div>
                        
                        <div class="type-option" onclick="selectGameType('steam-exe')">
                            <div class="type-icon">🔍</div>
                            <div class="type-title">Steam Game Shortcut but I want to use the game's icon</div>
                            <div class="type-description">
                                Let me browse for the actual game executable to extract its icon instead of using the shortcut.
                            </div>
                        </div>
                        
                        <div class="type-option" onclick="selectGameType('regular')">
                            <div class="type-icon">🎯</div>
                            <div class="type-title">Regular Game</div>
                            <div class="type-description">
                                Standard game or executable. Icon extraction will be attempted and if you desire, just like the other options, a custom image can be used.
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="confirmation-note">
                    <strong>💡 Note:</strong> You can always change the icon later by editing the game.
                </div>
            </div>
        `,
        type: 'info',
        preventOutsideClose: true
    });
    
    // Store the game data for the selection handlers
    window.currentGameForConfirmation = game;
}

// Handle game type selection
function selectGameType(type) {
    const game = window.currentGameForConfirmation;
    if (!game) {
        console.error('❌ No game data found for type selection');
        return;
    }
    
    console.log('🎯 User selected game type:', type, 'for game:', game.name);
    
    closeCustomModal();
    
    switch (type) {
        case 'steam-keep':
            // Find the existing game in the array to preserve all data
            const gameIndex = customGames.findIndex(g => g.id === game.id);
            if (gameIndex !== -1) {
                // Only update the icon, preserve everything else (mod folders, file types, etc.)
                customGames[gameIndex].iconPath = './assets/Games/steam_game.png';
                
                localStorage.setItem('armoryXCustomGames', JSON.stringify(customGames));
                populateGameLibrary(); // Refresh display
                
                console.log('🎮 Steam game added with controller icon');
                console.log('🗂️ Mod folders preserved:', customGames[gameIndex].modFolders);
                console.log('🔧 File types preserved:', customGames[gameIndex].supportedFileTypes);
                
                showNotification(`✅ Steam game "${game.name}" added successfully with controller icon!`, 'success');
            } else {
                console.error('❌ Game not found in array for Steam update');
                showNotification(`✅ Custom game "${game.name}" added successfully!`, 'success');
            }
            break;
            
        case 'steam-exe':
            // User wants to find the actual executable
            const gameIndex2 = customGames.findIndex(g => g.id === game.id);
            if (gameIndex2 !== -1) {
                console.log('🔍 User chose to reselect Steam game executable');
                reselectSteamExecutable(game.id);
            }
            break;
            
        case 'regular':
        default:
            // Just show success notification for regular games
            showNotification(`✅ Custom game "${game.name}" added successfully!`, 'success');
            break;
    }
    
    // Clean up
    window.currentGameForConfirmation = null;
}

// Populate file types for edit modal
function populateFileTypesForEdit(gameFileTypes) {
    console.log('🔧 Populating file types for edit:', gameFileTypes);
    
    // Check if we have "all types" (indicated by a large number of types)
    const hasAllTypes = gameFileTypes && gameFileTypes.length > 10;
    
    // Set checkboxes based on current game file types
    const allCheckbox = document.getElementById('type-all');
    if (allCheckbox) {
        allCheckbox.checked = hasAllTypes;
    }
    
    // Individual file type checkboxes
    const fileTypeMap = {
        'type-zip': '.zip',
        'type-rar': '.rar',
        'type-7z': '.7z',
        'type-jar': '.jar',
        'type-exe': '.exe',
        'type-dll': '.dll',
        'type-pak': '.pak',
        'type-mod': '.mod'
    };
    
    Object.entries(fileTypeMap).forEach(([checkboxId, fileType]) => {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
            if (hasAllTypes) {
                checkbox.checked = false; // When "All Types" is selected, individual types are unchecked
            } else {
                checkbox.checked = gameFileTypes && gameFileTypes.includes(fileType);
            }
        }
    });
    
    // Update the display text
    updateFileTypesDisplay();
}

// Populate mod folders for edit modal
function populateModFoldersForEdit(gameFolders) {
    console.log('🗂️ Populating mod folders for edit - input data:', JSON.stringify(gameFolders, null, 2));
    
    const modFoldersContainer = document.getElementById('custom-mod-folders');
    if (!modFoldersContainer) {
        console.error('❌ Mod folders container not found');
        return;
    }
    
    console.log('✅ Found mod folders container element');
    
    // Clear existing content
    modFoldersContainer.innerHTML = '';
    
    // Ensure we have at least one folder
    const folders = gameFolders && gameFolders.length > 0 ? gameFolders : [{ name: 'Mods', path: '' }];
    console.log('🔧 Using folders for population:', JSON.stringify(folders, null, 2));
    
    folders.forEach((folder, index) => {
        console.log(`📁 Creating folder ${index + 1}: name="${folder.name}", path="${folder.path}"`);
        
        const folderHTML = `
            <div class="mod-folder-item">
                <input type="text" class="modal-input" placeholder="Folder name (e.g., Mods)" data-type="name" value="${(folder.name || '').replace(/"/g, '&quot;')}" />
                <div class="path-input-group">
                    <input type="text" class="modal-input" placeholder="Click Browse to select folder..." readonly data-type="path" value="${(folder.path || '').replace(/"/g, '&quot;')}" />
                    <button class="btn btn-secondary" onclick="browseModFolder(this)">Browse</button>
                </div>
                <button class="btn btn-danger" onclick="removeModFolder(this)" style="${index === 0 && folders.length === 1 ? 'display: none;' : 'display: inline-block;'}">Remove</button>
            </div>
        `;
        modFoldersContainer.insertAdjacentHTML('beforeend', folderHTML);
    });
    
    console.log('✅ Populated', folders.length, 'mod folders');
    
    // Verify the inputs were populated correctly
    setTimeout(() => {
        const nameInputs = document.querySelectorAll('[data-type="name"]');
        const pathInputs = document.querySelectorAll('[data-type="path"]');
        console.log('🔍 Verification - Name inputs found:', nameInputs.length);
        console.log('🔍 Verification - Path inputs found:', pathInputs.length);
        
        nameInputs.forEach((input, index) => {
            console.log(`📝 Name input ${index + 1} value:`, input.value);
        });
        
        pathInputs.forEach((input, index) => {
            console.log(`📂 Path input ${index + 1} value:`, input.value);
        });
    }, 50);
}

// Helper function to properly escape file paths for HTML and JavaScript
function escapeFilePath(filePath) {
    if (!filePath) return '';
    // Replace single backslashes with double backslashes for proper display and handling
    return filePath.replace(/\\/g, '\\\\');
}

// Helper function to display file paths properly
function displayFilePath(filePath) {
    if (!filePath) return '';
    // Just replace backslashes with forward slashes for display (more readable)
    return filePath.replace(/\\/g, '/');
}

// --- Particle burst effect for card selection ---
function createParticleBurst(card) {
    // Remove any existing burst
    const oldBurst = card.querySelector('.particle-burst');
    if (oldBurst) oldBurst.remove();
    const burst = document.createElement('div');
    burst.className = 'particle-burst';
    for (let i = 0; i < 18; i++) {
        const p = document.createElement('div');
        p.className = 'particle';
        const angle = (i / 18) * 2 * Math.PI;
        const radius = 60 + Math.random() * 30;
        const dx = Math.cos(angle) * radius;
        const dy = Math.sin(angle) * radius;
        p.style.setProperty('--dx', `${dx}px`);
        p.style.setProperty('--dy', `${dy}px`);
        burst.appendChild(p);
    }
    card.appendChild(burst);
    setTimeout(() => burst.remove(), 700);
}

// Inject anime.js for future advanced animation
(function injectAnimeJS() {
    if (!window.anime) {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js';
        script.async = true;
        document.head.appendChild(script);
    }
})();

// Patch selectGame to trigger particle burst and card pop
const _originalSelectGame = window.selectGame;
window.selectGame = async function(gameId) {
    // Remove .selected from all cards
    document.querySelectorAll('.steam-game-card.selected').forEach(card => card.classList.remove('selected'));
    // Find the card for this game
    const card = document.querySelector(`.steam-game-card[data-game-id="${gameId}"]`);
    if (card) {
        card.classList.add('selected');
        createParticleBurst(card);
    }
    return _originalSelectGame.apply(this, arguments);
};

// Add a smooth refresh function with slide transitions
async function refreshModsWithTransition(gameId = null) {
    const targetGameId = gameId || selectedGameId;
    if (!targetGameId) {
        console.warn('⚠️ No game selected for refresh');
        return;
    }
    
    console.log('🔄 Starting smooth mod refresh for:', targetGameId);
    
    const modGrid = document.getElementById('mod-grid');
    if (!modGrid) return;
    
    // Add slide-out animation to current content
    modGrid.classList.add('refresh-slide-out');
    
    // Wait for slide-out animation to complete
    await new Promise(resolve => {
        const handleSlideOut = () => {
            modGrid.removeEventListener('animationend', handleSlideOut);
            resolve();
        };
        modGrid.addEventListener('animationend', handleSlideOut);
        
        // Fallback timeout in case animation doesn't fire
        setTimeout(resolve, 400);
    });
    
    try {
        // Load new mod data while content is hidden
        let modsData;
        
        if (window.electronAPI && window.electronAPI.getMods) {
            modsData = await window.electronAPI.getMods(targetGameId);
        } else if (ipcRenderer) {
            modsData = await ipcRenderer.invoke('get-mods-for-game', targetGameId);
        } else {
            modsData = { mods: [], total: 0, isDemo: true };
        }
        
        console.log('✅ Loaded fresh mod data:', modsData);
        
        // Clean up slide-out class and display new content
        modGrid.classList.remove('refresh-slide-out');
        
        // Display the new mods
        if (modsData && modsData.mods) {
            displayMods(modsData.mods, targetGameId);
        } else {
            displayMods([], targetGameId);
        }
        
        // Add slide-in animation to new content
        modGrid.classList.add('refresh-slide-in');
        
        // Clean up slide-in class after animation
        setTimeout(() => {
            modGrid.classList.remove('refresh-slide-in');
            console.log('✨ Smooth refresh completed');
        }, 400);
        
    } catch (error) {
        console.error('❌ Error during smooth refresh:', error);
        // Remove slide-out class and show error
        modGrid.classList.remove('refresh-slide-out');
        modGrid.innerHTML = `
            <div class="error-message" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                <h3>Refresh Failed</h3>
                <p>Error: ${error.message}</p>
                <button class="btn btn-primary" onclick="refreshModsWithTransition('${targetGameId}')">
                    <span class="btn-icon">🔄</span>
                    Try Again
                </button>
            </div>
        `;
    }
}

// Emergency restore function
async function emergencyRestore() {
    try {
        const confirmed = await showConfirmDialog(
            'Emergency HWID Restore',
            'This will attempt to restore your original hardware identifiers using emergency methods. This should only be used if the normal restore function is not working.\n\nAre you sure you want to proceed?',
            'warning'
        );
        
        if (!confirmed) return;
        
        showNotification('Performing emergency HWID restoration...', 'info');
        
        // First try the normal restore method
        let result = await ipcRenderer.invoke('hwid-spoofer-restore-values');
        
        if (!result.success) {
            // If normal restore fails, try emergency restore methods
            showNotification('Normal restore failed, trying emergency methods...', 'warning');
            
            // Create emergency restore script
            const emergencyScript = `
# Emergency HWID Restore Script
# This will reset hardware identifiers to system defaults

# Reset System Machine GUID (use your original from the test script)
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography" /v "MachineGuid" /t REG_SZ /d "9b25c922-95ef-463e-9945-abe4b9e9ec7f" /f

# Reset Hardware Profile GUID to system default
reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\IDConfigDB\\Hardware Profiles\\0001" /v "HwProfileGuid" /t REG_SZ /d "{dca0a88f-d137-11ef-bb34-806e6f6e6963}" /f

# Reset Computer Hardware ID
reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation" /v "ComputerHardwareId" /t REG_SZ /d "{2276c983-0a3b-5c8e-ada2-d4cb603b7dde}" /f

# Reset Build GUID
reg add "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion" /v "BuildGUID" /t REG_SZ /d "ffffffff-ffff-ffff-ffff-ffffffffffff" /f

# Reset Computer Hardware IDs
reg add "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation" /v "ComputerHardwareIds" /t REG_MULTI_SZ /d "03CPTO73111HLZ2X" /f

echo Emergency restore completed!
pause
            `;
            
            showCustomModal({
                title: '🚨 Emergency Restore Instructions',
                content: `
                    <div class="emergency-restore-content">
                        <div class="warning-section">
                            <div class="warning-icon">⚠️</div>
                            <div class="warning-text">
                                <h4>Manual Emergency Restore Required</h4>
                                <p>The automatic restore has failed. You will need to perform a manual restore using these values from your test script:</p>
                            </div>
                        </div>
                        
                        <div class="emergency-values">
                            <h5>Your Original HWID Values:</h5>
                            <div class="value-list">
                                <div class="value-item">
                                    <strong>System Machine GUID:</strong><br>
                                    <code>9b25c922-95ef-463e-9945-abe4b9e9ec7f</code>
                                </div>
                                <div class="value-item">
                                    <strong>Hardware Profile GUID:</strong><br>
                                    <code>a5e79318-2ee9-4cf4-9812-7798f1b0e48c</code>
                                </div>
                                <div class="value-item">
                                    <strong>Computer Hardware ID:</strong><br>
                                    <code>90R8DDD7HXKL0EO9</code>
                                </div>
                                <div class="value-item">
                                    <strong>Build GUID:</strong><br>
                                    <code>1219d4c9-448e-4de5-b8aa-4a884e48b11b</code>
                                </div>
                                <div class="value-item">
                                    <strong>Computer Hardware IDs:</strong><br>
                                    <code>03CPTO73111HLZ2X</code>
                                </div>
                            </div>
                        </div>
                        
                        <div class="emergency-instructions">
                            <h5>Manual Restore Steps:</h5>
                            <ol>
                                <li>Run PowerShell as Administrator</li>
                                <li>Copy and paste the registry commands from the emergency restore script</li>
                                <li>Run the test script to verify restoration</li>
                                <li>Restart your computer if needed</li>
                            </ol>
                        </div>
                        
                        <div class="emergency-note">
                            <p><strong>Note:</strong> These are your original values from the test script you ran earlier. If you need the emergency restore script file, it will be created in your ArmoryX folder.</p>
                        </div>
                    </div>
                `,
                type: 'warning',
                customClass: 'emergency-restore-modal',
                buttons: [
                    {
                        text: 'Create Emergency Script',
                        class: 'btn-warning',
                        action: async () => {
                            try {
                                // Create emergency restore script file
                                await window.electronAPI.saveEmergencyScript(emergencyScript);
                                showNotification('Emergency restore script created in ArmoryX folder', 'success');
                            } catch (error) {
                                console.error('Failed to create emergency script:', error);
                                showNotification('Failed to create emergency script', 'error');
                            }
                        }
                    },
                    {
                        text: 'Close',
                        class: 'btn-secondary',
                        action: () => closeCustomModal()
                    }
                ]
            });
            
            return;
        }
        
        // Normal restore succeeded
        hwidSpooferState.active = false;
        hwidSpooferState.hasBackup = result.hasBackup || false;
        updateHWIDSpooferUI();
        showNotification('Emergency restore completed successfully', 'success');
        
        // Hide advanced options
        const advancedOptions = document.getElementById('hwid-advanced-options');
        if (advancedOptions) {
            advancedOptions.style.display = 'none';
        }
        
    } catch (error) {
        console.error('Emergency restore error:', error);
        showNotification('Emergency restore failed', 'error');
    }
}

async function showMaxModeExplanation() {
    const content = `
        <div class="max-mode-explanation">
            <div class="explanation-header">
                <div class="explanation-icon">⚡</div>
                <h3>MAX MODE - True Kernel-Level Spoofing</h3>
                <p>Ultimate hardware protection with advanced anti-cheat evasion</p>
            </div>
            
            <div class="mode-comparison">
                <div class="mode-section">
                    <h4>🔧 Standard Mode (Default)</h4>
                    <div class="mode-features">
                        <div class="feature-item">✅ <strong>Registry-Level Spoofing</strong> - Modifies Windows registry entries</div>
                        <div class="feature-item">✅ <strong>95% Protection</strong> - Effective against most games and applications</div>
                        <div class="feature-item">✅ <strong>Hardware Identifier Changes</strong> - SMBIOS, disk serials, network adapters, GPU</div>
                        <div class="feature-item">✅ <strong>Fast & Reliable</strong> - Immediate effect with high stability</div>
                        <div class="feature-item">✅ <strong>Easy Restoration</strong> - Simple backup and restore system</div>
                    </div>
                </div>
                
                <div class="mode-section max-mode">
                    <h4>⚡ MAX MODE (Advanced)</h4>
                    <div class="mode-features">
                        <div class="feature-item">✅ <strong>Kernel-Level Function Hooking</strong> - Intercepts system calls</div>
                        <div class="feature-item">✅ <strong>99.9% Protection</strong> - Bypasses advanced anti-cheat systems</div>
                        <div class="feature-item">✅ <strong>Memory Structure Modification</strong> - Direct hardware info manipulation</div>
                        <div class="feature-item">✅ <strong>Anti-Cheat Evasion</strong> - Advanced techniques for EAC, BattlEye, Vanguard</div>
                        <div class="feature-item">✅ <strong>Live Query Interception</strong> - Hooks hardware detection APIs</div>
                        <div class="feature-item">⚠️ <strong>Higher Complexity</strong> - Requires system-level permissions</div>
                    </div>
                </div>
            </div>
            
            <div class="technical-details">
                <h4>🔬 Technical Implementation</h4>
                <div class="tech-grid">
                    <div class="tech-item">
                        <strong>Standard Mode:</strong>
                        <ul>
                            <li>Modifies registry keys directly</li>
                            <li>Changes stored hardware values</li>
                            <li>Works with existing system APIs</li>
                        </ul>
                    </div>
                    <div class="tech-item">
                        <strong>MAX MODE:</strong>
                        <ul>
                            <li>Hooks NtQuerySystemInformation</li>
                            <li>Intercepts WMI queries</li>
                            <li>Modifies kernel memory structures</li>
                            <li>Real-time hardware spoofing</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="recommendation">
                <div class="rec-icon">💡</div>
                <div class="rec-content">
                    <h4>Recommendation</h4>
                    <p><strong>Standard Mode</strong> is sufficient for 95% of use cases including most games and applications.</p>
                    <p><strong>MAX MODE</strong> should only be used when facing advanced anti-cheat systems that specifically scan for registry-level spoofing.</p>
                </div>
            </div>
            
            <div class="warning-note">
                <div class="warning-icon">⚠️</div>
                <div class="warning-content">
                    <strong>Important:</strong> MAX MODE requires administrator privileges and uses advanced system techniques. 
                    Always ensure you have a working backup before enabling.
                </div>
            </div>
        </div>
        
        <style>
            .max-mode-explanation {
                max-width: 800px;
                margin: 0 auto;
            }
            
            .explanation-header {
                text-align: center;
                margin-bottom: 2rem;
            }
            
            .explanation-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
            }
            
            .explanation-header h3 {
                color: var(--primary-color);
                margin-bottom: 0.5rem;
                font-size: 1.5rem;
            }
            
            .explanation-header p {
                color: var(--text-secondary);
                font-size: 1rem;
            }
            
            .mode-comparison {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
                margin-bottom: 2rem;
            }
            
            .mode-section {
                background: var(--background-hover);
                border-radius: 12px;
                padding: 1.5rem;
                border: 1px solid var(--border-color);
            }
            
            .mode-section.max-mode {
                border-color: var(--primary-color);
                background: linear-gradient(135deg, var(--background-hover) 0%, rgba(0, 180, 255, 0.1) 100%);
            }
            
            .mode-section h4 {
                color: var(--text-primary);
                margin-bottom: 1rem;
                font-size: 1.1rem;
            }
            
            .mode-features {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;
            }
            
            .feature-item {
                color: var(--text-secondary);
                font-size: 0.9rem;
                line-height: 1.4;
            }
            
            .feature-item strong {
                color: var(--text-primary);
            }
            
            .technical-details {
                background: var(--background-card);
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                border: 1px solid var(--border-color);
            }
            
            .technical-details h4 {
                color: var(--text-primary);
                margin-bottom: 1rem;
            }
            
            .tech-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
            }
            
            .tech-item strong {
                color: var(--primary-color);
                display: block;
                margin-bottom: 0.5rem;
            }
            
            .tech-item ul {
                margin: 0;
                padding-left: 1.5rem;
                color: var(--text-secondary);
            }
            
            .tech-item li {
                margin-bottom: 0.25rem;
                font-size: 0.9rem;
            }
            
            .recommendation {
                background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, var(--background-hover) 100%);
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 1.5rem;
                border: 1px solid rgba(0, 255, 136, 0.3);
                display: flex;
                gap: 1rem;
            }
            
            .rec-icon {
                font-size: 2rem;
                color: var(--success-color);
            }
            
            .rec-content h4 {
                color: var(--success-color);
                margin-bottom: 0.5rem;
            }
            
            .rec-content p {
                color: var(--text-secondary);
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
                line-height: 1.4;
            }
            
            .warning-note {
                background: linear-gradient(135deg, rgba(255, 170, 0, 0.1) 0%, var(--background-hover) 100%);
                border-radius: 12px;
                padding: 1.5rem;
                border: 1px solid rgba(255, 170, 0, 0.3);
                display: flex;
                gap: 1rem;
                align-items: flex-start;
            }
            
            .warning-note .warning-icon {
                font-size: 1.5rem;
                color: var(--warning-color);
            }
            
            .warning-content {
                color: var(--text-secondary);
                font-size: 0.9rem;
                line-height: 1.4;
            }
            
            .warning-content strong {
                color: var(--warning-color);
            }
            
            @media (max-width: 768px) {
                .mode-comparison,
                .tech-grid {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
                
                .recommendation,
                .warning-note {
                    flex-direction: column;
                    text-align: center;
                }
            }
        </style>
    `;
    
    showCustomModal({
        title: '⚡ MAX MODE Explanation',
        content: content,
        type: 'info',
        customClass: 'max-mode-modal',
        buttons: [
            {
                text: 'Got it!',
                class: 'btn-primary',
                action: () => closeCustomModal()
            }
        ]
    });
}

// === ACCOUNT TAB FUNCTIONS ===

let licenseKeyVisible = false;
let currentLicenseKey = null;

async function initAccountTab() {
    console.log('👤 Initializing Account tab');
    
    // Load user data from Firebase/storage
    await loadAccountData();
}

async function loadAccountData() {
    try {
        // Get user data from storage/Firebase
        const userData = localStorage.getItem('armoryXUserData');
        if (userData) {
            const user = JSON.parse(userData);
            
            // Update account display
            document.getElementById('account-username').textContent = user.displayName || user.email.split('@')[0];
            document.getElementById('account-email').textContent = user.email;
            document.getElementById('account-email-display').textContent = user.email;
            
            // Format member since date
            const createdDate = new Date(user.createdAt || user.metadata?.creationTime || Date.now());
            document.getElementById('account-created').textContent = createdDate.toLocaleDateString();
            
            // Get license info
            const licenseInfo = await getLicenseInfo();
            updateLicenseDisplay(licenseInfo);
        }
    } catch (error) {
        console.error('Error loading account data:', error);
        showNotification('Failed to load account data', 'error');
    }
}

async function getLicenseInfo() {
    try {
        // Check for stored license key
        const storedKey = localStorage.getItem('armoryXLicenseKey');
        if (storedKey) {
            currentLicenseKey = storedKey;
            
            // Verify license status
            const response = await ipcRenderer.invoke('verify-license', storedKey);
            if (response.valid) {
                return {
                    key: storedKey,
                    type: 'Premium',
                    expiry: response.expiry || 'Lifetime',
                    active: true
                };
            }
        }
        
        return {
            key: null,
            type: 'Free',
            expiry: 'N/A',
            active: false
        };
    } catch (error) {
        console.error('Error getting license info:', error);
        return {
            key: null,
            type: 'Free',
            expiry: 'N/A',
            active: false
        };
    }
}

function updateLicenseDisplay(licenseInfo) {
    // Update license type and expiry
    document.getElementById('license-type').textContent = licenseInfo.type;
    document.getElementById('license-expiry').textContent = licenseInfo.expiry;
    
    // Update license key display
    if (licenseInfo.key) {
        const maskedKey = licenseInfo.key.replace(/(.{4})/g, '$1-').slice(0, -1);
        document.getElementById('license-key-masked').textContent = licenseKeyVisible ? maskedKey : '****-****-****-****';
    }
    
    // Update premium features display
    const upgradeSection = document.getElementById('upgrade-section');
    if (licenseInfo.active) {
        upgradeSection.style.display = 'none';
    } else {
        upgradeSection.style.display = 'block';
    }
}

function toggleLicenseVisibility() {
    licenseKeyVisible = !licenseKeyVisible;
    const visibilityIcon = document.getElementById('visibility-icon');
    visibilityIcon.textContent = licenseKeyVisible ? '🙈' : '👁️';
    
    if (currentLicenseKey) {
        const maskedKey = currentLicenseKey.replace(/(.{4})/g, '$1-').slice(0, -1);
        document.getElementById('license-key-masked').textContent = licenseKeyVisible ? maskedKey : '****-****-****-****';
    }
}

async function copyLicenseKey() {
    if (currentLicenseKey) {
        try {
            await navigator.clipboard.writeText(currentLicenseKey);
            showNotification('License key copied to clipboard', 'success');
        } catch (error) {
            console.error('Failed to copy license key:', error);
            showNotification('Failed to copy license key', 'error');
        }
    }
}

async function openRedeemDialog() {
    // Create modal for license key redemption
    const modalContent = `
        <div class="modal-header">
            <h3>🎁 Redeem License Key</h3>
            <p>Enter your license key to unlock premium features</p>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>License Key</label>
                <input type="text" 
                    id="redeem-key-input" 
                    placeholder="XXXX-XXXX-XXXX-XXXX" 
                    class="form-input"
                    style="font-family: monospace; text-transform: uppercase;"
                    maxlength="19">
                <small>Enter the license key you received after purchase</small>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeCustomModal()">Cancel</button>
            <button class="btn btn-primary" onclick="redeemLicenseKey()">Redeem Key</button>
        </div>
    `;
    
    showCustomModal(modalContent);
    
    // Auto-format license key input
    const keyInput = document.getElementById('redeem-key-input');
    keyInput.addEventListener('input', (e) => {
        let value = e.target.value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
        let formatted = value.match(/.{1,4}/g)?.join('-') || value;
        e.target.value = formatted;
    });
}

async function redeemLicenseKey() {
    const keyInput = document.getElementById('redeem-key-input');
    const key = keyInput.value.replace(/-/g, '');
    
    if (key.length !== 16) {
        showNotification('Invalid license key format', 'error');
        return;
    }
    
    try {
        showLoadingOverlay('Validating license key...');
        
        // Verify license key
        const response = await ipcRenderer.invoke('verify-license', key);
        
        if (response.valid) {
            // Store license key
            localStorage.setItem('armoryXLicenseKey', key);
            currentLicenseKey = key;
            
            // Update display
            const licenseInfo = {
                key: key,
                type: 'Premium',
                expiry: response.expiry || 'Lifetime',
                active: true
            };
            updateLicenseDisplay(licenseInfo);
            
            closeCustomModal();
            hideLoadingOverlay();
            showNotification('License key redeemed successfully! 🎉', 'success');
            
            // Refresh account data
            await loadAccountData();
        } else {
            hideLoadingOverlay();
            showNotification(response.message || 'Invalid license key', 'error');
        }
    } catch (error) {
        console.error('Error redeeming license key:', error);
        hideLoadingOverlay();
        showNotification('Failed to redeem license key', 'error');
    }
}

async function checkLicenseStatus() {
    if (!currentLicenseKey) {
        showNotification('No license key found', 'info');
        return;
    }
    
    try {
        showLoadingOverlay('Checking license status...');
        
        const response = await ipcRenderer.invoke('verify-license', currentLicenseKey);
        hideLoadingOverlay();
        
        if (response.valid) {
            showNotification('License is active and valid ✅', 'success');
        } else {
            showNotification(response.message || 'License is invalid', 'error');
        }
    } catch (error) {
        console.error('Error checking license status:', error);
        hideLoadingOverlay();
        showNotification('Failed to check license status', 'error');
    }
}

function openChangePasswordDialog() {
    const modalContent = `
        <div class="modal-header">
            <h3>🔐 Change Password</h3>
            <p>Update your account password</p>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label>Current Password</label>
                <input type="password" id="current-password" class="form-input" placeholder="Enter current password">
            </div>
            <div class="form-group">
                <label>New Password</label>
                <input type="password" id="new-password" class="form-input" placeholder="Enter new password">
                <small>At least 8 characters with numbers and symbols</small>
            </div>
            <div class="form-group">
                <label>Confirm New Password</label>
                <input type="password" id="confirm-password" class="form-input" placeholder="Confirm new password">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeCustomModal()">Cancel</button>
            <button class="btn btn-primary" onclick="changePassword()">Change Password</button>
        </div>
    `;
    
    showCustomModal(modalContent);
}

async function changePassword() {
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showNotification('Please fill in all fields', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showNotification('New passwords do not match', 'error');
        return;
    }
    
    if (newPassword.length < 8) {
        showNotification('Password must be at least 8 characters', 'error');
        return;
    }
    
    try {
        showLoadingOverlay('Changing password...');
        
        // TODO: Implement password change via Firebase
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
        
        hideLoadingOverlay();
        closeCustomModal();
        showNotification('Password changed successfully', 'success');
    } catch (error) {
        console.error('Error changing password:', error);
        hideLoadingOverlay();
        showNotification('Failed to change password', 'error');
    }
}

function toggle2FA() {
    const is2FAEnabled = document.getElementById('2fa-toggle').checked;
    
    if (is2FAEnabled) {
        showNotification('Two-factor authentication enabled', 'success');
        // TODO: Implement 2FA setup flow
    } else {
        showNotification('Two-factor authentication disabled', 'warning');
        // TODO: Implement 2FA disable flow
    }
}

function openChangeEmailDialog() {
    showNotification('Email change feature coming soon', 'info');
    // TODO: Implement email change dialog
}

function openUpgradeDialog() {
    // Open external link to purchase page
    if (shell && shell.openExternal) {
        shell.openExternal('https://your-website.com/purchase');
    } else {
        window.open('https://your-website.com/purchase', '_blank');
    }
}

async function exportAccountData() {
    try {
        const userData = localStorage.getItem('armoryXUserData');
        if (!userData) {
            showNotification('No account data to export', 'error');
            return;
        }
        
        const data = {
            ...JSON.parse(userData),
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        // Create download
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'armory-x-account-export.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('Account data exported successfully', 'success');
    } catch (error) {
        console.error('Error exporting account data:', error);
        showNotification('Failed to export account data', 'error');
    }
}

async function downloadActivityLog() {
    showNotification('Activity log feature coming soon', 'info');
    // TODO: Implement activity log download
}

function openDeleteAccountDialog() {
    const modalContent = `
        <div class="modal-header">
            <h3 style="color: var(--error-color);">⚠️ Delete Account</h3>
            <p>This action cannot be undone!</p>
        </div>
        <div class="modal-body">
            <div class="warning-box" style="background: rgba(255, 68, 68, 0.1); border: 1px solid var(--error-color); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <p style="color: var(--error-color); margin: 0;">
                    <strong>Warning:</strong> Deleting your account will permanently remove all your data, licenses, and settings.
                </p>
            </div>
            <div class="form-group">
                <label>Type "DELETE" to confirm</label>
                <input type="text" id="delete-confirm-input" class="form-input" placeholder="Type DELETE to confirm">
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeCustomModal()">Cancel</button>
            <button class="btn btn-danger" onclick="deleteAccount()">Delete Account</button>
        </div>
    `;
    
    showCustomModal(modalContent);
}

async function deleteAccount() {
    const confirmInput = document.getElementById('delete-confirm-input').value;
    
    if (confirmInput !== 'DELETE') {
        showNotification('Please type DELETE to confirm', 'error');
        return;
    }
    
    // TODO: Implement account deletion via Firebase
    showNotification('Account deletion is disabled in this demo', 'info');
    closeCustomModal();
}

// === MINECRAFT PROFILE MANAGEMENT FUNCTIONS ===

// Global variables for Minecraft profiles
let minecraftProfiles = [];
let currentMinecraftProfile = 'default';

/**
 * Load Minecraft profiles from localStorage and populate dropdown
 */
function loadMinecraftProfiles() {
    console.log('🎮 Loading Minecraft profiles');
    
    try {
        const savedProfiles = localStorage.getItem('armoryXMinecraftProfiles');
        minecraftProfiles = savedProfiles ? JSON.parse(savedProfiles) : [];
        
        // Populate profile dropdown
        const profileDropdown = document.getElementById('profile-dropdown');
        if (profileDropdown) {
            // Clear existing options except default
            profileDropdown.innerHTML = '<option value="default">Default (No Profile)</option>';
            
            // Add saved profiles
            minecraftProfiles.forEach(profile => {
                const option = document.createElement('option');
                option.value = profile.id;
                option.textContent = `${profile.name} (${profile.mcVersion} - ${profile.loader})`;
                profileDropdown.appendChild(option);
            });
            
            // Set current selection
            profileDropdown.value = currentMinecraftProfile;
            updateProfileDisplay(currentMinecraftProfile);
        }
        
        console.log('✅ Loaded', minecraftProfiles.length, 'Minecraft profiles');
    } catch (error) {
        console.error('❌ Error loading Minecraft profiles:', error);
        minecraftProfiles = [];
    }
}

/**
 * Update the profile display information
 */
function updateProfileDisplay(profileId) {
    const statusElement = document.getElementById('profile-status');
    
    if (!statusElement) return;
    
    if (profileId === 'default') {
        statusElement.textContent = 'Vanilla';
        statusElement.style.color = '#94a3b8';
    } else {
        const profile = minecraftProfiles.find(p => p.id === profileId || p.name === profileId);
        if (profile) {
            const loaderText = profile.loader ? 
                profile.loader.charAt(0).toUpperCase() + profile.loader.slice(1) : 
                'Vanilla';
            statusElement.textContent = `${profile.mcVersion} • ${loaderText}`;
            statusElement.style.color = profile.loader ? '#3b82f6' : '#94a3b8';
        }
    }
}

/**
 * Switch to a different Minecraft profile
 */
async function switchMinecraftProfile() {
    const profileDropdown = document.getElementById('profile-dropdown');
    if (!profileDropdown) return;
    
    const newProfileId = profileDropdown.value;
    
    if (newProfileId === currentMinecraftProfile) {
        return; // No change
    }
    
    console.log('🔄 Switching from profile', currentMinecraftProfile, 'to', newProfileId);
    
    try {
        // Show loading state
        showNotification('Switching profile...', 'info');
        
        // Call backend to switch profiles
        if (window.electronAPI && window.electronAPI.switchMinecraftProfile) {
            const result = await window.electronAPI.switchMinecraftProfile(currentMinecraftProfile, newProfileId);
            if (result.success) {
                currentMinecraftProfile = newProfileId;
                updateProfileDisplay(newProfileId);
                
                // Refresh mods to show the new profile's mods
                await refreshMods();
                
                const profileName = newProfileId === 'default' ? 'Default' : 
                    minecraftProfiles.find(p => p.id === newProfileId)?.name || newProfileId;
                showNotification(`✅ Switched to profile: ${profileName}`, 'success');
            } else {
                showNotification(`❌ Failed to switch profile: ${result.message}`, 'error');
                // Revert dropdown selection
                profileDropdown.value = currentMinecraftProfile;
            }
        } else {
            // Demo mode
            currentMinecraftProfile = newProfileId;
            updateProfileDisplay(newProfileId);
            showNotification('✅ Profile switched (Demo mode)', 'info');
        }
    } catch (error) {
        console.error('❌ Error switching profile:', error);
        showNotification('❌ Failed to switch profile', 'error');
        profileDropdown.value = currentMinecraftProfile;
    }
}

/**
 * Show the Setup Loader Profile dialog
 */
function showCreateMinecraftProfile() {
    console.log('📦 Showing simplified profile creation dialog');
    
    const content = `
        <div class="profile-create-form">
            <div class="form-intro">
                <p>Create a new profile to keep mods, configs, and resource packs organized.</p>
            </div>
            
            <div class="form-section">
                <label for="profile-name">Profile Name:</label>
                <input type="text" id="profile-name" class="modal-input" placeholder="e.g., My Survival World" />
            </div>
            
            <div class="form-section">
                <label for="mc-version">Minecraft Version:</label>
                <select id="mc-version" class="modal-input">
                    <option value="1.20.4">1.20.4 (Latest)</option>
                    <option value="1.20.1" selected>1.20.1 (Stable)</option>
                    <option value="1.19.4">1.19.4</option>
                    <option value="1.19.2">1.19.2</option>
                    <option value="1.18.2">1.18.2</option>
                    <option value="1.16.5">1.16.5</option>
                    <option value="1.12.2">1.12.2 (Legacy)</option>
                </select>
            </div>
            
            <div class="form-section">
                <label>Mod Loader (Optional):</label>
                <div class="loader-options">
                    <label class="radio-option">
                        <input type="radio" name="loader" value="vanilla" checked />
                        <span>Vanilla (No mods)</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="loader" value="fabric" />
                        <span>Fabric</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="loader" value="forge" />
                        <span>Forge</span>
                    </label>
                </div>
                <p class="form-help">Only select a loader if you plan to use mods</p>
            </div>
            
            <div class="form-section">
                <label for="ram-allocation">Memory:</label>
                <select id="ram-allocation" class="modal-input">
                    <option value="2G">2GB</option>
                    <option value="4G" selected>4GB (Recommended)</option>
                    <option value="6G">6GB</option>
                    <option value="8G">8GB</option>
                </select>
            </div>
        </div>
        
        <style>
            .profile-create-form {
                min-width: 400px;
            }
            
            .form-intro {
                margin-bottom: 1.5rem;
                color: #94a3b8;
                font-size: 0.95rem;
            }
            
            .profile-create-form .form-section {
                margin-bottom: 1.5rem;
            }
            
            .profile-create-form label {
                display: block;
                color: #e2e8f0;
                font-weight: 600;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }
            
            .profile-create-form .modal-input {
                width: 100%;
                padding: 0.8rem;
                background: #1f2937;
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: #f9fafb;
                font-size: 1rem;
                transition: all 0.3s ease;
            }
            
            .profile-create-form select.modal-input {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.8rem center;
                background-repeat: no-repeat;
                background-size: 16px;
                padding-right: 2.5rem;
                appearance: none;
            }
            
            .profile-create-form .modal-input:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
            }
            
            .form-help {
                color: #64748b;
                font-size: 0.85rem;
                margin-top: 0.3rem;
            }
            
            .loader-options {
                display: flex;
                gap: 0.75rem;
                margin-bottom: 0.5rem;
            }
            
            .radio-option {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .radio-option:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(59, 130, 246, 0.3);
            }
            
            .radio-option:has(input:checked) {
                background: rgba(59, 130, 246, 0.1);
                border-color: var(--primary-color);
            }
            
            .radio-option input[type="radio"] {
                margin: 0;
                width: 16px;
                height: 16px;
            }
            
            .radio-option span {
                color: #e2e8f0;
                font-size: 0.9rem;
            }
        </style>
    `;
    
    showCustomModal({
        title: '📦 Create New Profile',
        content: content,
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Create Profile',
                class: 'btn-primary',
                action: () => processProfileCreation()
            }
        ]
    });
}

/**
 * Process the profile creation form
 */
async function processProfileCreation() {
    const profileName = document.getElementById('profile-name')?.value;
    const mcVersion = document.getElementById('mc-version')?.value;
    const loaderType = document.querySelector('input[name="loader"]:checked')?.value;
    const ramAllocation = document.getElementById('ram-allocation')?.value;
    
    // Validation
    if (!profileName || profileName.trim() === '') {
        showNotification('Please enter a profile name', 'warning');
        return;
    }
    
    // Check for duplicate profile names
    if (minecraftProfiles.some(p => p.name.toLowerCase() === profileName.trim().toLowerCase())) {
        showNotification('A profile with this name already exists', 'warning');
        return;
    }
    
    console.log('📦 Creating profile:', {
        profileName: profileName.trim(),
        mcVersion,
        loaderType,
        ramAllocation
    });
    
    try {
        closeCustomModal();
        showNotification('Creating profile...', 'info');
        
        // Call backend to create profile
        if (window.electronAPI && window.electronAPI.createMinecraftProfile) {
            const result = await window.electronAPI.createMinecraftProfile({
                profileName: profileName.trim(),
                mcVersion: mcVersion,
                loader: loaderType === 'vanilla' ? null : loaderType,
                ramAllocation: ramAllocation,
                autoLauncherProfile: true
            });
            
            if (result.success) {
                // Add profile to local storage
                const newProfile = {
                    id: `profile_${Date.now()}`,
                    name: profileName.trim(),
                    loader: loaderType === 'vanilla' ? null : loaderType,
                    mcVersion: mcVersion,
                    ramAllocation: ramAllocation,
                    created: new Date().toISOString()
                };
                
                minecraftProfiles.push(newProfile);
                localStorage.setItem('armoryXMinecraftProfiles', JSON.stringify(minecraftProfiles));
                
                // Reload profiles and switch to new one
                loadMinecraftProfiles();
                document.getElementById('profile-dropdown').value = profileName.trim();
                switchMinecraftProfile();
                
                const loaderText = loaderType === 'vanilla' ? 'Vanilla' : loaderType.charAt(0).toUpperCase() + loaderType.slice(1);
                showNotification(`✅ Created "${profileName.trim()}" profile (${loaderText})!`, 'success');
            } else {
                showNotification(`❌ Failed to setup profile: ${result.message}`, 'error');
            }
        } else {
            // Demo mode - just add the profile locally
            const newProfile = {
                id: `profile_${Date.now()}`,
                name: profileName.trim(),
                loader: loaderType === 'vanilla' ? null : loaderType,
                mcVersion: mcVersion,
                ramAllocation: ramAllocation,
                created: new Date().toISOString()
            };
            
            minecraftProfiles.push(newProfile);
            localStorage.setItem('armoryXMinecraftProfiles', JSON.stringify(minecraftProfiles));
            
            loadMinecraftProfiles();
            document.getElementById('profile-dropdown').value = profileName.trim();
            switchMinecraftProfile();
            
            const loaderText = loaderType === 'vanilla' ? 'Vanilla' : loaderType.charAt(0).toUpperCase() + loaderType.slice(1);
            showNotification(`✅ Created "${profileName.trim()}" profile (${loaderText})! (Demo mode)`, 'success');
        }
    } catch (error) {
        console.error('❌ Error setting up loader profile:', error);
        showNotification('❌ Failed to setup loader profile', 'error');
    }
}

/**
 * Show the Create Profile dialog
 */
function showCreateProfileDialog() {
    console.log('➕ Showing Create Profile dialog');
    
    const content = `
        <div class="create-profile-form">
            <p class="intro-text">Create a new Minecraft profile to organize your mods by version or mod pack.</p>
            
            <div class="form-section">
                <label for="new-profile-name">Profile Name:</label>
                <input type="text" id="new-profile-name" class="modal-input" placeholder="e.g., Vanilla Plus, Tech Mods, etc." />
                <p class="form-help">Choose a descriptive name for your new profile</p>
            </div>
            
            <div class="form-section">
                <label for="copy-from-profile">Copy from existing profile (optional):</label>
                <select id="copy-from-profile" class="modal-input">
                    <option value="">Start with empty profile</option>
                    <option value="default">Copy from Default</option>
                    ${minecraftProfiles.map(profile => 
                        `<option value="${profile.id}">Copy from ${profile.name}</option>`
                    ).join('')}
                </select>
                <p class="form-help">This will copy mods and settings from an existing profile</p>
            </div>
        </div>
        
        <style>
            .create-profile-form .intro-text {
                color: #94a3b8;
                margin-bottom: 1.5rem;
                font-size: 1rem;
                line-height: 1.5;
            }
            
            .create-profile-form .form-section {
                margin-bottom: 1.5rem;
            }
            
            .create-profile-form label {
                display: block;
                color: #e2e8f0;
                font-weight: 600;
                margin-bottom: 0.5rem;
            }
            
            .create-profile-form .modal-input {
                width: 100%;
                padding: 0.8rem;
                background: linear-gradient(135deg, #1f2937, #111827);
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 8px;
                color: #f9fafb;
                font-size: 1rem;
                transition: all 0.3s ease;
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
            }
            
            .create-profile-form select.modal-input {
                background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236366f1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
                background-position: right 0.8rem center;
                background-repeat: no-repeat;
                background-size: 16px;
                padding-right: 2.5rem;
            }
            
            .create-profile-form .modal-input:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            }
            
            .create-profile-form .modal-input option {
                background: #1f2937;
                color: #f9fafb;
                padding: 0.8rem;
            }
            
            .create-profile-form .form-help {
                color: #9ca3af;
                font-size: 0.9rem;
                margin-top: 0.5rem;
                margin-bottom: 0;
            }
        </style>
    `;
    
    showCustomModal({
        title: '➕ Create New Profile',
        content: content,
        type: 'info',
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Create Profile',
                class: 'btn-primary',
                action: () => processCreateProfile()
            }
        ]
    });
}

/**
 * Process the create profile form
 */
async function processCreateProfile() {
    const profileName = document.getElementById('new-profile-name')?.value;
    const copyFromProfile = document.getElementById('copy-from-profile')?.value;
    
    if (!profileName || profileName.trim() === '') {
        showNotification('Please enter a profile name', 'warning');
        return;
    }
    
    // Check for duplicate profile names
    if (minecraftProfiles.some(p => p.name.toLowerCase() === profileName.trim().toLowerCase())) {
        showNotification('A profile with this name already exists', 'warning');
        return;
    }
    
    try {
        closeCustomModal();
        showNotification('Creating profile...', 'info');
        
        // Call backend to create the profile
        if (window.electronAPI && window.electronAPI.createMinecraftProfile) {
            const result = await window.electronAPI.createMinecraftProfile({
                name: profileName.trim(),
                copyFrom: copyFromProfile || null
            });
            
            if (result.success) {
                // Add profile to local storage
                const newProfile = {
                    id: result.profileId || `profile_${Date.now()}`,
                    name: profileName.trim(),
                    loader: 'vanilla',
                    mcVersion: 'latest',
                    ramAllocation: '4G',
                    created: new Date().toISOString()
                };
                
                minecraftProfiles.push(newProfile);
                localStorage.setItem('armoryXMinecraftProfiles', JSON.stringify(minecraftProfiles));
                
                loadMinecraftProfiles();
                showNotification(`✅ Profile "${profileName.trim()}" created successfully!`, 'success');
            } else {
                showNotification(`❌ Failed to create profile: ${result.message}`, 'error');
            }
        } else {
            // Demo mode
            const newProfile = {
                id: `profile_${Date.now()}`,
                name: profileName.trim(),
                loader: 'vanilla',
                mcVersion: 'latest',
                ramAllocation: '4G',
                created: new Date().toISOString()
            };
            
            minecraftProfiles.push(newProfile);
            localStorage.setItem('armoryXMinecraftProfiles', JSON.stringify(minecraftProfiles));
            
            loadMinecraftProfiles();
            showNotification(`✅ Profile "${profileName.trim()}" created! (Demo mode)`, 'success');
        }
    } catch (error) {
        console.error('❌ Error creating profile:', error);
        showNotification('❌ Failed to create profile', 'error');
    }
}

/**
 * Show the Manage Profiles dialog
 */
function showManageProfilesDialog() {
    console.log('⚙️ Showing Manage Profiles dialog');
    
    const profilesList = minecraftProfiles.map(profile => `
        <div class="profile-item" data-profile-id="${profile.id}">
            <div class="profile-info">
                <h4>${profile.name}</h4>
                <p>Loader: ${profile.loader} | Version: ${profile.mcVersion} | RAM: ${profile.ramAllocation}</p>
                <small>Created: ${new Date(profile.created).toLocaleDateString()}</small>
            </div>
            <div class="profile-actions">
                <button class="btn-sm btn-primary" onclick="editProfile('${profile.id}')">Edit</button>
                <button class="btn-sm btn-danger" onclick="deleteProfile('${profile.id}')">Delete</button>
            </div>
        </div>
    `).join('');
    
    const content = `
        <div class="manage-profiles-container">
            <p class="intro-text">Manage your Minecraft profiles. You can edit settings or delete profiles you no longer need.</p>
            
            <div class="profiles-list">
                ${profilesList || '<p class="no-profiles">No custom profiles created yet.</p>'}
            </div>
        </div>
        
        <style>
            .manage-profiles-container .intro-text {
                color: #94a3b8;
                margin-bottom: 1.5rem;
                font-size: 1rem;
                line-height: 1.5;
            }
            
            .profiles-list {
                max-height: 400px;
                overflow-y: auto;
            }
            
            .profile-item {
                background: linear-gradient(135deg, #374151, #1f2937);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 1rem;
                margin-bottom: 1rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: all 0.3s ease;
            }
            
            .profile-item:hover {
                border-color: rgba(59, 130, 246, 0.3);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }
            
            .profile-info h4 {
                color: #e2e8f0;
                margin: 0 0 0.5rem 0;
                font-size: 1.1rem;
            }
            
            .profile-info p {
                color: #94a3b8;
                margin: 0 0 0.3rem 0;
                font-size: 0.9rem;
            }
            
            .profile-info small {
                color: #6b7280;
                font-size: 0.8rem;
            }
            
            .profile-actions {
                display: flex;
                gap: 0.5rem;
            }
            
            .btn-sm {
                padding: 0.4rem 0.8rem;
                font-size: 0.85rem;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            
            .btn-sm.btn-primary {
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                color: white;
            }
            
            .btn-sm.btn-primary:hover {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
            }
            
            .btn-sm.btn-danger {
                background: linear-gradient(135deg, #ef4444, #dc2626);
                color: white;
            }
            
            .btn-sm.btn-danger:hover {
                background: linear-gradient(135deg, #dc2626, #b91c1c);
            }
            
            .no-profiles {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 2rem;
            }
        </style>
    `;
    
    showCustomModal({
        title: '⚙️ Manage Profiles',
        content: content,
        type: 'info',
        customClass: 'manage-profiles-modal',
        buttons: [
            {
                text: 'Close',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            }
        ]
    });
}

// === MOD SETS MANAGEMENT ===

/**
 * Load and display available Mod Sets for Minecraft
 */
async function loadModSets() {
    console.log('📦 Loading Mod Sets...');
    
    try {
        const result = await ipcRenderer.invoke('get-mod-sets');
        const dropdown = document.getElementById('modsets-dropdown');
        const statusIndicator = document.getElementById('modsets-status-indicator');
        const statusText = document.getElementById('modsets-status-text');
        const statusContainer = document.getElementById('modsets-status');
        
        if (!dropdown) return;
        
        // Clear existing options
        dropdown.innerHTML = '<option value="">No Mod Set Active</option>';
        
        if (result.modSets && result.modSets.length > 0) {
            result.modSets.forEach(modSet => {
                const option = document.createElement('option');
                option.value = modSet.id;
                option.textContent = modSet.name;
                dropdown.appendChild(option);
            });
            
            // Set current active mod set
            if (result.currentModSet) {
                dropdown.value = result.currentModSet;
                const activeModSet = result.modSets.find(ms => ms.id === result.currentModSet);
                if (activeModSet) {
                    statusIndicator.textContent = '🟢';
                    statusText.textContent = `Active: ${activeModSet.name}`;
                    statusContainer.className = 'modsets-status active';
                }
            } else {
                statusIndicator.textContent = '⚪';
                statusText.textContent = 'No active Mod Set';
                statusContainer.className = 'modsets-status';
            }
        } else {
            statusIndicator.textContent = '⚪';
            statusText.textContent = 'No Mod Sets created yet';
            statusContainer.className = 'modsets-status';
        }
        
        console.log('✅ Mod Sets loaded successfully');
    } catch (error) {
        console.error('❌ Error loading Mod Sets:', error);
        showNotification('Failed to load Mod Sets', 'error');
    }
}

/**
 * Show dialog to create a new Mod Set
 */
function showCreateModSetDialog() {
    console.log('📦 Showing Create Mod Set dialog');
    
    const content = `
        <div class="create-modset-content">
            <div class="input-group">
                <label for="modset-name">Mod Set Name</label>
                <input type="text" id="modset-name" class="modal-input" placeholder="e.g. Survival World, Creative Server, Modpack Name" maxlength="50">
            </div>
            
            <div class="input-group">
                <label for="modset-description">Description (Optional)</label>
                <textarea id="modset-description" class="modal-textarea" placeholder="Brief description of what this mod set is for..." maxlength="200"></textarea>
            </div>
            
            <div class="input-group">
                <div class="start-fresh-section">
                    <label class="start-fresh-option">
                        <input type="checkbox" id="start-fresh-checkbox" />
                        <span class="custom-checkbox"></span>
                        <div class="checkbox-content">
                            <span class="checkbox-title">🗂️ Start Fresh (Empty Folders)</span>
                            <p class="checkbox-description">Create an empty mod set without affecting your current mods. Folders will be cleared when you first switch to this set.</p>
                    </div>
                    </label>
                </div>
            </div>
            
            <div class="info-box">
                <div class="info-icon">💡</div>
                <div class="info-content">
                    <p><strong>What happens when you create a Mod Set:</strong></p>
                    <ul>
                        <li>✅ All current mods, shaders, and resource packs will be saved to this set (unless "Start Fresh" is enabled)</li>
                        <li>📁 A backup folder will be created in your Minecraft directory</li>
                        <li>🔄 You can switch between different mod sets anytime</li>
                        <li>🛡️ Your current setup will always be preserved safely</li>
                        <li>🆕 "Start Fresh" creates an empty mod set without affecting your current mods</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <style>
            .create-modset-content .input-group {
                margin-bottom: 1.5rem;
            }
            
            .create-modset-content label {
                display: block;
                color: #e2e8f0;
                font-weight: 600;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }
            
            .create-modset-content .modal-input,
            .create-modset-content .modal-textarea {
                width: 100%;
                padding: 0.75rem;
                background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
                border: 1px solid #374151;
                border-radius: 8px;
                color: #f9fafb;
                font-size: 1rem;
                transition: all 0.3s ease;
                box-sizing: border-box;
            }
            
            .create-modset-content .modal-textarea {
                min-height: 80px;
                resize: vertical;
                font-family: inherit;
            }
            
            .create-modset-content .modal-input:focus,
            .create-modset-content .modal-textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            }
            
            .info-box {
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
                border: 1px solid rgba(59, 130, 246, 0.2);
                border-radius: 10px;
                padding: 1rem;
                display: flex;
                gap: 1rem;
                margin-top: 1rem;
            }
            
            .info-icon {
                font-size: 1.5rem;
                flex-shrink: 0;
            }
            
            .info-content p {
                margin: 0 0 0.5rem 0;
                color: #e2e8f0;
                font-weight: 600;
            }
            
            .info-content ul {
                margin: 0;
                padding-left: 1rem;
                color: #94a3b8;
            }
            
            .info-content li {
                margin-bottom: 0.25rem;
                line-height: 1.4;
            }
        </style>
    `;
}

/**
 * Switch to a different Mod Set
 */
async function switchModSet() {
    const dropdown = document.getElementById('modsets-dropdown');
    if (!dropdown) return;
    
    const selectedModSetId = dropdown.value;
    
    if (!selectedModSetId) {
        console.log('⚪ Deactivating current Mod Set');
        
        // Show deactivating status
        const statusIndicator = document.getElementById('modsets-status-indicator');
        const statusText = document.getElementById('modsets-status-text');
        const statusContainer = document.getElementById('modsets-status');
        
        if (statusIndicator && statusText && statusContainer) {
            statusIndicator.textContent = '🔄';
            statusText.textContent = 'Deactivating Mod Set...';
            statusContainer.className = 'modsets-status switching';
        }
        
        showLoadingState(true);
        
        try {
            const result = await ipcRenderer.invoke('deactivate-mod-set');
            
            showLoadingState(false);
            
            if (result.success) {
                showNotification(result.message, 'success');
                
                // Update status
                if (statusIndicator && statusText && statusContainer) {
                    statusIndicator.textContent = '⚪';
                    statusText.textContent = 'No active Mod Set';
                    statusContainer.className = 'modsets-status';
                }
                
                // Refresh the mods view
                refreshMods();
            } else {
                showNotification(result.message || 'Failed to deactivate Mod Set', 'error');
            }
        } catch (error) {
            showLoadingState(false);
            console.error('❌ Error deactivating Mod Set:', error);
            showNotification('Failed to deactivate Mod Set', 'error');
        }
        
        return;
    }
    
    // Show switching status
    const statusIndicator = document.getElementById('modsets-status-indicator');
    const statusText = document.getElementById('modsets-status-text');
    const statusContainer = document.getElementById('modsets-status');
    
    if (statusIndicator && statusText && statusContainer) {
        statusIndicator.textContent = '🔄';
        statusText.textContent = 'Switching Mod Set...';
        statusContainer.className = 'modsets-status switching';
    }
    
    console.log(`🔄 Switching to Mod Set: ${selectedModSetId}`);
    showLoadingState(true);
    
    try {
        const result = await ipcRenderer.invoke('switch-mod-set', {
            modSetId: selectedModSetId
        });
        
        showLoadingState(false);
        
        if (result.success) {
            showNotification(result.message, 'success');
            
            // Update status
            if (statusIndicator && statusText && statusContainer) {
                statusIndicator.textContent = '🟢';
                statusText.textContent = `Active: ${result.modSet?.name || 'Mod Set'}`;
                statusContainer.className = 'modsets-status active';
            }
            
            // Refresh the mods view to show the new mod set
            refreshMods();
        } else {
            showNotification(result.message || 'Failed to switch Mod Set', 'error');
            
            // Reset status on failure
            if (statusIndicator && statusText && statusContainer) {
                statusIndicator.textContent = '⚪';
                statusText.textContent = 'Failed to switch';
                statusContainer.className = 'modsets-status';
            }
            
            // Reset dropdown
            loadModSets();
        }
    } catch (error) {
        showLoadingState(false);
        console.error('❌ Error switching Mod Set:', error);
        showNotification('Failed to switch Mod Set', 'error');
        
        // Reset status on error
        if (statusIndicator && statusText && statusContainer) {
            statusIndicator.textContent = '⚪';
            statusText.textContent = 'Error switching';
            statusContainer.className = 'modsets-status';
        }
        
        loadModSets();
    }
}

/**
 * Show Manage Mod Sets dialog
 */
async function showManageModSetsDialog() {
    console.log('⚙️ Showing Manage Mod Sets dialog');
    
    try {
        const result = await ipcRenderer.invoke('get-mod-sets');
        
        let modSetsList = '';
        if (result.modSets && result.modSets.length > 0) {
            modSetsList = result.modSets.map(modSet => {
                const isActive = result.currentModSet === modSet.id;
                const statusBadge = isActive ? '<span class="status-badge active">🟢 Active</span>' : '<span class="status-badge">⚪ Inactive</span>';
                
                return `
                    <div class="modset-item ${isActive ? 'active' : ''}" data-modset-id="${modSet.id}">
                        <div class="modset-info">
                            <div class="modset-header">
                                <h4>${modSet.name}</h4>
                                ${statusBadge}
                    </div>
                            <p class="modset-description">${modSet.description || 'No description'}</p>
                            <div class="modset-stats">
                                <span class="stat">📁 ${modSet.fileCount} files</span>
                                <span class="stat">📅 Created: ${new Date(modSet.created).toLocaleDateString()}</span>
                                <span class="stat">🕒 Last used: ${new Date(modSet.lastUsed).toLocaleDateString()}</span>
                    </div>
                    </div>
                        <div class="modset-actions">
                            ${!isActive ? `<button class="btn-small btn-primary" onclick="switchToModSetById('${modSet.id}')">Switch To</button>` : ''}
                            <button class="btn-small btn-secondary" onclick="renameModSet('${modSet.id}', '${modSet.name}', '${modSet.description || ''}')">Rename</button>
                            ${!isActive ? `<button class="btn-small btn-danger" onclick="deleteModSet('${modSet.id}', '${modSet.name}')">Delete</button>` : ''}
                    </div>
                    </div>
                `;
            }).join('');
        } else {
            modSetsList = `
                <div class="empty-state">
                    <div class="empty-icon">📦</div>
                    <h4>No Mod Sets Created</h4>
                    <p>Create your first mod set to organize your mods, shaders, and resource packs.</p>
                    <button class="btn btn-primary" onclick="closeCustomModal(); showCreateModSetDialog();">Create First Mod Set</button>
                    </div>
            `;
        }
        
        const content = `
            <div class="manage-modsets-content">
                <div class="modsets-list">
                    ${modSetsList}
            </div>
        </div>
        
        <style>
                .manage-modsets-content {
                    max-height: 500px;
                overflow-y: auto;
                }
                
                .modset-item {
                    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
                    border: 1px solid #4b5563;
                border-radius: 12px;
                    padding: 1rem;
                    margin-bottom: 1rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 1rem;
                    transition: all 0.3s ease;
                }
                
                .modset-item:hover {
                    border-color: #6b7280;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
                
                .modset-item.active {
                    border-color: #22c55e;
                    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
                }
                
                .modset-info {
                    flex: 1;
                    min-width: 0;
                }
                
                .modset-header {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-bottom: 0.5rem;
                }
                
                .modset-header h4 {
                margin: 0;
                    color: #f9fafb;
                    font-size: 1rem;
                    font-weight: 600;
                }
                
                .status-badge {
                    font-size: 0.75rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 12px;
                    font-weight: 600;
                    background: rgba(107, 114, 128, 0.2);
                    color: #9ca3af;
                }
                
                .status-badge.active {
                    background: rgba(34, 197, 94, 0.2);
                    color: #86efac;
                }
                
                .modset-description {
                    color: #9ca3af;
                    font-size: 0.9rem;
                    margin: 0 0 0.75rem 0;
                    line-height: 1.4;
                }
                
                .modset-stats {
                    display: flex;
                    gap: 1rem;
                    flex-wrap: wrap;
                }
                
                .stat {
                    color: #6b7280;
                    font-size: 0.8rem;
                    font-weight: 500;
                }
                
                .modset-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    flex-shrink: 0;
                }
                
                .btn-small {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.8rem;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    white-space: nowrap;
                }
                
                .btn-small.btn-primary {
                    background: #3b82f6;
                    color: white;
                }
                
                .btn-small.btn-primary:hover {
                    background: #2563eb;
                }
                
                .btn-small.btn-secondary {
                    background: #6b7280;
                    color: white;
                }
                
                .btn-small.btn-secondary:hover {
                    background: #4b5563;
                }
                
                .btn-small.btn-danger {
                    background: #ef4444;
                    color: white;
                }
                
                .btn-small.btn-danger:hover {
                    background: #dc2626;
                }
                
                .empty-state {
                    text-align: center;
                    padding: 3rem 2rem;
                    color: #6b7280;
                }
                
                .empty-icon {
                    font-size: 3rem;
                    margin-bottom: 1rem;
                }
                
                .empty-state h4 {
                    color: #9ca3af;
                    margin-bottom: 0.5rem;
                }
                
                .empty-state p {
                    margin-bottom: 1.5rem;
                line-height: 1.5;
            }
            </style>
        `;
        
        showCustomModal({
            title: '⚙️ Manage Mod Sets',
            content: content,
            buttons: [
                {
                    text: '🔍 Recover Lost Mod Sets',
                    class: 'btn-warning',
                    action: () => recoverLostModSets()
                },
                {
                    text: 'Close',
                    class: 'btn-secondary',
                    action: () => closeCustomModal()
                }
            ],
            size: 'large',
            showCloseButton: false
        });
        
    } catch (error) {
        console.error('❌ Error loading Mod Sets for management:', error);
        showNotification('Failed to load Mod Sets', 'error');
    }
}

/**
 * Attempt to recover lost mod sets
 */
async function recoverLostModSets() {
    console.log('🔍 Starting mod set recovery...');
    showLoadingState(true);
    
    try {
        const result = await ipcRenderer.invoke('recover-lost-mod-sets');
        
        showLoadingState(false);
        
        if (result.success) {
            showNotification(`✅ ${result.message}`, 'success');
            
            // Close current modal and refresh mod sets
            closeCustomModal();
            loadModSets();
            
            // Show recovered mod sets if any
            if (result.recoveredSets && result.recoveredSets.length > 0) {
                const recoveredNames = result.recoveredSets.map(ms => ms.name).join(', ');
                setTimeout(() => {
                    showNotification(`Recovered mod sets: ${recoveredNames}`, 'info');
                }, 1000);
            }
            
        } else {
            showNotification(`❌ ${result.message}`, 'error');
        }
        
    } catch (error) {
        showLoadingState(false);
        console.error('❌ Error during mod set recovery:', error);
        showNotification('Recovery failed', 'error');
    }
}

/**
 * Switch to a mod set by ID (from management dialog)
 */
async function switchToModSetById(modSetId) {
    const dropdown = document.getElementById('modsets-dropdown');
    if (dropdown) {
        dropdown.value = modSetId;
        await switchModSet();
        closeCustomModal();
    }
}

/**
 * Delete a Mod Set
 */
async function deleteModSet(modSetId, modSetName) {
    const confirmed = await showConfirmationModal(
        'Delete Mod Set',
        `Are you sure you want to delete the mod set "${modSetName}"? This action cannot be undone.`,
        () => performDeleteModSet(modSetId)
    );
}

/**
 * Perform the actual mod set deletion
 */
async function performDeleteModSet(modSetId) {
    console.log(`🗑️ Deleting Mod Set: ${modSetId}`);
    showLoadingState(true);
    
    try {
        const result = await ipcRenderer.invoke('delete-mod-set', {
            modSetId: modSetId
        });
        
        showLoadingState(false);
        
        if (result.success) {
            showNotification(result.message, 'success');
            closeCustomModal();
            loadModSets(); // Refresh the mod sets list
        } else {
            showNotification(result.message || 'Failed to delete Mod Set', 'error');
        }
    } catch (error) {
        showLoadingState(false);
        console.error('❌ Error deleting Mod Set:', error);
        showNotification('Failed to delete Mod Set', 'error');
    }
}

/**
 * Rename a Mod Set
 */
/**
 * Confirm deletion of a Mod Set
 */
function deleteModSet(modSetId, modSetName) {
    console.log(`🗑️ Confirming deletion of Mod Set: ${modSetName}`);
    
    showCustomModal({
        title: '⚠️ Delete Mod Set',
        content: `
            <div class="warning-content">
                <div class="warning-icon-large">🗑️</div>
                <h3>Delete "${modSetName}"?</h3>
                <p>This will permanently delete this mod set and all its backed up files.</p>
                <div class="warning-note">
                    <strong>⚠️ Warning:</strong> This action cannot be undone!
                </div>
            </div>
        `,
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Delete Mod Set',
                class: 'btn-danger',
                action: () => performDeleteModSet(modSetId)
            }
        ],
        size: 'medium',
        showCloseButton: false
    });
}

function renameModSet(modSetId, currentName, currentDescription) {
    const content = `
        <div class="rename-modset-content">
            <div class="input-group">
                <label for="rename-modset-name">Mod Set Name</label>
                <input type="text" id="rename-modset-name" class="modal-input" value="${currentName}" maxlength="50">
            </div>
            
            <div class="input-group">
                <label for="rename-modset-description">Description (Optional)</label>
                <textarea id="rename-modset-description" class="modal-textarea" maxlength="200">${currentDescription}</textarea>
            </div>
        </div>
        
        <style>
            .rename-modset-content .input-group {
                margin-bottom: 1.5rem;
            }
            
            .rename-modset-content label {
                display: block;
                color: #e2e8f0;
                font-weight: 600;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
            }
            
            .rename-modset-content .modal-input,
            .rename-modset-content .modal-textarea {
                width: 100%;
                padding: 0.75rem;
                background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
                border: 1px solid #374151;
                border-radius: 8px;
                color: #f9fafb;
                font-size: 1rem;
                transition: all 0.3s ease;
                box-sizing: border-box;
            }
            
            .rename-modset-content .modal-textarea {
                min-height: 80px;
                resize: vertical;
                font-family: inherit;
            }
            
            .rename-modset-content .modal-input:focus,
            .rename-modset-content .modal-textarea:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
            }
        </style>
    `;
    
    showCustomModal({
        title: '📝 Rename Mod Set',
        content: content,
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            },
            {
                text: 'Save Changes',
                class: 'btn-primary',
                action: () => performRenameModSet(modSetId)
            }
        ],
        size: 'medium',
        showCloseButton: false
    });
}

/**
 * Initialize download event listeners
 */
function initializeDownloadEvents() {
    if (!window.electronAPI) return;
    
    // Listen for download events
    window.electronAPI.onDownloadStarted = (callback) => {
        window.electronAPI.on('download-started', callback);
    };
    
    window.electronAPI.onDownloadProgress = (callback) => {
        window.electronAPI.on('download-progress', callback);
    };
    
    window.electronAPI.onDownloadCompleted = (callback) => {
        window.electronAPI.on('download-completed', callback);
    };
    
    window.electronAPI.onDownloadError = (callback) => {
        window.electronAPI.on('download-error', callback);
    };
    
    window.electronAPI.onDownloadCancelled = (callback) => {
        window.electronAPI.on('download-cancelled', callback);
    };
    
    window.electronAPI.onShowDownloadCategorization = (callback) => {
        window.electronAPI.on('show-download-categorization', callback);
    };
    
    window.electronAPI.onDownloadInstalled = (callback) => {
        window.electronAPI.on('download-installed', callback);
    };
    
    window.electronAPI.onDownloadFailed = (callback) => {
        window.electronAPI.on('download-failed', callback);
    };
    
    // Set up download event handlers
    if (window.electronAPI.onDownloadStarted) {
        window.electronAPI.onDownloadStarted((data) => {
            console.log('📥 Download started:', data);
            showDownloadProgressNotification(data);
        });
    }
    
    if (window.electronAPI.onDownloadProgress) {
        window.electronAPI.onDownloadProgress((data) => {
            updateDownloadProgress(data);
        });
    }
    
    if (window.electronAPI.onDownloadCompleted) {
        window.electronAPI.onDownloadCompleted((data) => {
            console.log('✅ Download completed:', data);
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onDownloadError) {
        window.electronAPI.onDownloadError((data) => {
            console.error('❌ Download error:', data);
            showNotification(`Download failed: ${data.error}`, 'error');
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onDownloadCancelled) {
        window.electronAPI.onDownloadCancelled((data) => {
            console.log('🚫 Download cancelled:', data);
            hideDownloadProgress(data.id);
        });
    }
    
    if (window.electronAPI.onShowDownloadCategorization) {
        window.electronAPI.onShowDownloadCategorization((data) => {
            console.log('🎯 Showing categorization dialog:', data);
            showDownloadCategorizationDialog(data);
        });
    }
    
    if (window.electronAPI.onDownloadInstalled) {
        window.electronAPI.onDownloadInstalled((data) => {
            console.log('✅ Download installed:', data);
            showNotification(`✅ ${data.filename} installed as ${data.category}! ${data.installedMods ? `(${data.installedMods} file${data.installedMods !== 1 ? 's' : ''})` : ''}`, 'success');
            
            // Refresh mods if we're currently viewing Minecraft mods
            if (selectedGameId === 'minecraft') {
                setTimeout(() => refreshMods(), 1000);
            }
        });
    }
    
    if (window.electronAPI.onDownloadFailed) {
        window.electronAPI.onDownloadFailed((data) => {
            console.error('❌ Download failed:', data);
            showNotification(`❌ Download failed: ${data.filename} (${data.reason})`, 'error');
        });
    }
}

/**
 * Show download progress notification
 * @param {Object} downloadInfo - Download information
 */
function showDownloadProgressNotification(downloadInfo) {
    const progressId = `download-progress-${downloadInfo.id}`;
    
    // Create progress notification container if it doesn't exist
    let progressContainer = document.getElementById('download-progress-container');
    if (!progressContainer) {
        progressContainer = document.createElement('div');
        progressContainer.id = 'download-progress-container';
        progressContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            width: 350px;
            pointer-events: none;
        `;
        document.body.appendChild(progressContainer);
    }
    
    // Create progress notification
    const progressElement = document.createElement('div');
    progressElement.id = progressId;
    progressElement.style.cssText = `
        background: linear-gradient(135deg, #1f2937, #111827);
        border: 2px solid rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        color: #f9fafb;
        font-size: 0.9rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        pointer-events: auto;
        animation: slideInRight 0.3s ease;
    `;
    
    progressElement.innerHTML = `
        <div class="download-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
            <span class="download-title" style="font-weight: 600; color: #3b82f6;">📥 Downloading</span>
            <button onclick="cancelDownload('${downloadInfo.id}')" style="
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                color: #fca5a5;
                border-radius: 4px;
                padding: 0.2rem 0.5rem;
                font-size: 0.8rem;
                cursor: pointer;
            ">Cancel</button>
        </div>
        <div class="download-filename" style="color: #e2e8f0; margin-bottom: 0.5rem; word-break: break-all;">
            ${downloadInfo.filename}
        </div>
        <div class="download-progress-bar" style="
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        ">
            <div class="progress-fill" style="
                width: 0%;
                height: 100%;
                background: linear-gradient(90deg, #3b82f6, #6366f1);
                border-radius: 4px;
                transition: width 0.3s ease;
            "></div>
        </div>
        <div class="download-stats" style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #94a3b8;">
            <span class="download-percentage">0%</span>
            <span class="download-speed">Calculating...</span>
        </div>
    `;
    
    progressContainer.appendChild(progressElement);
    
    // Auto-hide after 30 seconds if still showing
    setTimeout(() => {
        if (document.getElementById(progressId)) {
            hideDownloadProgress(downloadInfo.id);
        }
    }, 30000);
}

/**
 * Update download progress
 * @param {Object} downloadInfo - Download information
 */
function updateDownloadProgress(downloadInfo) {
    const progressId = `download-progress-${downloadInfo.id}`;
    const progressElement = document.getElementById(progressId);
    
    if (!progressElement) return;
    
    const progressFill = progressElement.querySelector('.progress-fill');
    const percentageSpan = progressElement.querySelector('.download-percentage');
    const speedSpan = progressElement.querySelector('.download-speed');
    
    if (progressFill) {
        progressFill.style.width = `${downloadInfo.progress}%`;
    }
    
    if (percentageSpan) {
        percentageSpan.textContent = `${downloadInfo.progress}%`;
    }
    
    if (speedSpan && downloadInfo.speed > 0) {
        const speedMB = (downloadInfo.speed / (1024 * 1024)).toFixed(1);
        const timeRemaining = downloadInfo.timeRemaining > 0 ? 
            ` - ${Math.round(downloadInfo.timeRemaining)}s remaining` : '';
        speedSpan.textContent = `${speedMB} MB/s${timeRemaining}`;
    }
}

/**
 * Hide download progress notification
 * @param {string} downloadId - Download ID
 */
function hideDownloadProgress(downloadId) {
    const progressId = `download-progress-${downloadId}`;
    const progressElement = document.getElementById(progressId);
    
    if (progressElement) {
        progressElement.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (progressElement.parentNode) {
                progressElement.parentNode.removeChild(progressElement);
            }
        }, 300);
    }
}

/**
 * Cancel a download
 * @param {string} downloadId - Download ID to cancel
 */
async function cancelDownload(downloadId) {
    try {
        if (window.electronAPI && window.electronAPI.cancelDownload) {
            await window.electronAPI.cancelDownload(downloadId);
            showNotification('Download cancelled', 'info');
        }
    } catch (error) {
        console.error('❌ Error cancelling download:', error);
        showNotification('Failed to cancel download', 'error');
    }
}

/**
 * Show download categorization dialog
 * @param {Object} data - Categorization data
 */
function showDownloadCategorizationDialog(data) {
    const { downloadInfo, suggestedCategory } = data;
    
    const content = `
        <div class="categorization-container">
            <div class="file-info">
                <div class="file-icon">📦</div>
                <div class="file-details">
                    <h3 class="file-name">${downloadInfo.filename}</h3>
                    <p class="file-size">${formatBytes(downloadInfo.totalBytes)}</p>
                </div>
            </div>
            
            <div class="categorization-section">
                <p class="categorization-text">Please select what type of file this is so Armory X can install it to the correct location:</p>
                
                <div class="category-options">
                    <label class="category-option ${suggestedCategory === 'Mods' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Mods" ${suggestedCategory === 'Mods' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">🧩</span>
                            <div class="option-text">
                                <strong>Mod</strong>
                                <small>Game modification (.jar files)</small>
                            </div>
                        </div>
                    </label>
                    
                    <label class="category-option ${suggestedCategory === 'Resource Packs' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Resource Packs" ${suggestedCategory === 'Resource Packs' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">🎨</span>
                            <div class="option-text">
                                <strong>Resource Pack</strong>
                                <small>Texture and sound packs</small>
                            </div>
                        </div>
                    </label>
                    
                    <label class="category-option ${suggestedCategory === 'Shaders' ? 'suggested' : ''}">
                        <input type="radio" name="file-category" value="Shaders" ${suggestedCategory === 'Shaders' ? 'checked' : ''}>
                        <div class="option-content">
                            <span class="option-icon">✨</span>
                            <div class="option-text">
                                <strong>Shader Pack</strong>
                                <small>Visual enhancement shaders</small>
                            </div>
                        </div>
                    </label>
                </div>
                
                ${suggestedCategory ? `
                    <div class="ai-suggestion">
                        <span class="suggestion-icon">🤖</span>
                        <span class="suggestion-text">AI suggests: <strong>${suggestedCategory}</strong> (pre-selected)</span>
                    </div>
                ` : ''}
            </div>
        </div>
        
        <style>
            .categorization-container {
                padding: 1rem 0;
            }
            
            .file-info {
                display: flex;
                align-items: center;
                padding: 1.5rem;
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
                border-radius: 12px;
                border: 1px solid rgba(59, 130, 246, 0.2);
                margin-bottom: 2rem;
            }
            
            .file-icon {
                font-size: 3rem;
                margin-right: 1rem;
            }
            
            .file-name {
                color: #f8fafc;
                margin: 0 0 0.5rem 0;
                font-size: 1.2rem;
                font-weight: 600;
                word-break: break-all;
            }
            
            .file-size {
                color: #94a3b8;
                margin: 0;
                font-size: 0.9rem;
            }
            
            .categorization-text {
                color: #e2e8f0;
                margin-bottom: 1.5rem;
                line-height: 1.5;
            }
            
            .category-options {
                display: flex;
                flex-direction: column;
                gap: 0.8rem;
                margin-bottom: 1.5rem;
            }
            
            .category-option {
                display: block;
                cursor: pointer;
                padding: 1rem;
                background: linear-gradient(135deg, #1f2937, #111827);
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                transition: all 0.3s ease;
            }
            
            .category-option:hover {
                border-color: rgba(59, 130, 246, 0.3);
                background: linear-gradient(135deg, #374151, #1f2937);
            }
            
            .category-option.suggested {
                border-color: rgba(16, 185, 129, 0.5);
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
            }
            
            .category-option input[type="radio"] {
                display: none;
            }
            
            .category-option input[type="radio"]:checked + .option-content {
                color: #3b82f6;
            }
            
            .option-content {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            
            .option-icon {
                font-size: 1.5rem;
            }
            
            .option-text strong {
                display: block;
                color: #f8fafc;
                font-size: 1rem;
                margin-bottom: 0.2rem;
            }
            
            .option-text small {
                color: #94a3b8;
                font-size: 0.85rem;
            }
            
            .ai-suggestion {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.8rem;
                background: rgba(16, 185, 129, 0.1);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: 6px;
                color: #d1fae5;
                font-size: 0.9rem;
            }
            
            .suggestion-icon {
                font-size: 1.1rem;
            }
            
            .suggestion-text strong {
                color: #10b981;
            }
        </style>
    `;
    
    showCustomModal({
        title: '🎯 Categorize Downloaded File',
        content: content,
        type: 'info',
        customClass: 'download-categorization-modal',
        buttons: [
            {
                text: 'Install',
                class: 'btn-primary',
                action: () => installCategorizedDownload(downloadInfo)
            },
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    closeCustomModal();
                    cancelDownload(downloadInfo.id);
                }
            }
        ]
    });
}

/**
 * Install categorized download
 * @param {Object} downloadInfo - Download information
 */
async function installCategorizedDownload(downloadInfo) {
    try {
        // Get selected category
        const selectedCategory = document.querySelector('input[name="file-category"]:checked');
        
        if (!selectedCategory) {
            showNotification('Please select a category first', 'warning');
            return;
        }
        
        const category = selectedCategory.value;
        console.log('📦 Installing download with category:', category);
        
        // Close the modal
        closeCustomModal();
        
        // Show installing notification
        showNotification(`Installing ${downloadInfo.filename} as ${category}...`, 'info');
        
        // Send categorization to main process
        if (window.electronAPI && window.electronAPI.categorizeDownload) {
            const result = await window.electronAPI.categorizeDownload({
                downloadInfo: downloadInfo,
                category: category
            });
            
            if (!result.success) {
                showNotification(`❌ Installation failed: ${result.message}`, 'error');
            }
            // Success notification will be sent via download-installed event
        } else {
            showNotification('❌ Installation not available in demo mode', 'error');
        }
        
    } catch (error) {
        console.error('❌ Error installing categorized download:', error);
        showNotification('❌ Failed to install download', 'error');
    }
}

/**
 * Perform the actual mod set rename
 */
async function performRenameModSet(modSetId) {
    const nameInput = document.getElementById('rename-modset-name');
    const descriptionInput = document.getElementById('rename-modset-description');
    
    if (!nameInput) return;
    
    const newName = nameInput.value.trim();
    const newDescription = descriptionInput ? descriptionInput.value.trim() : '';
    
    if (!newName) {
        showNotification('Please enter a name for the Mod Set', 'error');
        nameInput.focus();
        return;
    }
    
    console.log(`📝 Renaming Mod Set: ${modSetId} to ${newName}`);
    
    try {
        const result = await ipcRenderer.invoke('rename-mod-set', {
            modSetId: modSetId,
            newName: newName,
            newDescription: newDescription
        });
        
        if (result.success) {
            showNotification(result.message, 'success');
            closeCustomModal();
            loadModSets(); // Refresh the mod sets list
            showManageModSetsDialog(); // Refresh the management dialog
        } else {
            showNotification(result.message || 'Failed to rename Mod Set', 'error');
        }
    } catch (error) {
        console.error('❌ Error renaming Mod Set:', error);
        showNotification('Failed to rename Mod Set', 'error');
    }
}

/**
 * Show the Download Mods dialog with browser-based downloading
 */
function showDownloadModsDialog() {
    console.log('⬇️ Showing Enhanced Download Mods dialog');
    
    const content = `
        <div class="download-mods-container">
            <div class="intro-section">
                <h3>🌐 Smart Mod Downloads</h3>
                <p class="intro-text">Browse mod sites and let Armory X automatically handle the downloads for you!</p>
                
                <div class="simple-steps">
                    <div class="step">
                        <span class="step-icon">🔍</span>
                        <span class="step-text">Browse & Download</span>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <span class="step-icon">📥</span>
                        <span class="step-text">Auto-Detect</span>
                    </div>
                    <div class="step-arrow">→</div>
                    <div class="step">
                        <span class="step-icon">📁</span>
                        <span class="step-text">Auto-Install</span>
                    </div>
                </div>
            </div>
            
            <div class="quick-info">
                <div class="info-item">
                    <span class="info-icon">📦</span>
                                         <span class="info-text">Works with Modrinth, CurseForge & more</span>
                </div>
                <div class="info-item">
                    <span class="info-icon">🎯</span>
                    <span class="info-text">Automatically categorizes mods, shaders & resource packs</span>
                </div>
                <div class="info-item">
                    <span class="info-icon">💡</span>
                    <span class="info-text">Use Quick Nav buttons to switch between sites</span>
                </div>
            </div>
        </div>
        
        <style>
            .download-mods-container {
                padding: 0;
            }
            
            .intro-section {
                margin-bottom: 1.5rem;
                padding: 1.5rem;
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
                border-radius: 12px;
                border: 1px solid rgba(59, 130, 246, 0.2);
                text-align: center;
            }
            
            .intro-section h3 {
                color: #3b82f6;
                margin: 0 0 1rem 0;
                font-size: 1.4rem;
                font-weight: 600;
            }
            
            .intro-text {
                color: #e2e8f0;
                margin-bottom: 2rem;
                font-size: 1.1rem;
                line-height: 1.6;
            }
            
            .simple-steps {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 1rem;
                flex-wrap: wrap;
            }
            
            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                min-width: 120px;
            }
            
            .step-icon {
                font-size: 2rem;
                color: #3b82f6;
                background: rgba(59, 130, 246, 0.1);
                padding: 0.75rem;
                border-radius: 50%;
                border: 2px solid rgba(59, 130, 246, 0.3);
            }
            
            .step-text {
                color: #e2e8f0;
                font-weight: 600;
                font-size: 0.9rem;
                text-align: center;
            }
            
            .step-arrow {
                color: #6b7280;
                font-size: 1.5rem;
                font-weight: bold;
            }
            
            .quick-info {
                background: linear-gradient(135deg, rgba(16, 185, 129, 0.08), rgba(34, 197, 94, 0.04));
                border-radius: 12px;
                border: 1px solid rgba(16, 185, 129, 0.15);
                padding: 1.5rem;
            }
            
            .info-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1rem;
            }
            
            .info-item:last-child {
                margin-bottom: 0;
            }
            
            .info-icon {
                font-size: 1.3rem;
                color: #10b981;
                flex-shrink: 0;
            }
            
            .info-text {
                color: #d1fae5;
                font-size: 0.95rem;
                line-height: 1.4;
            }
            
            @media (max-width: 600px) {
                .simple-steps {
                    flex-direction: column;
                    gap: 1.5rem;
                }
                
                .step-arrow {
                    transform: rotate(90deg);
                }
            }
        </style>
    `;
    
    showCustomModal({
        title: '🌐 Smart Mod Downloader',
        content: content,
        customClass: 'enhanced-download-mods-modal',
        buttons: [
            {
                text: 'Open Browser',
                class: 'btn-primary',
                action: () => startBrowserDownload()
            },
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => closeCustomModal()
            }
        ],
        showCloseButton: false
    });
    
    // Website selection now uses default site from settings
    // No event handlers needed since dropdown was removed
}

/**
 * Show browser loading modal
 */
function showBrowserLoadingModal() {
    const content = `
        <div class="browser-loading-container">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            
            <h3 class="loading-title">🌐 Opening Download Browser</h3>
            <p class="loading-message">Please wait while we prepare your secure download browser...</p>
            
            <div class="loading-steps">
                <div class="step-item active" id="step-1">
                    <span class="step-icon">⏳</span>
                    <span class="step-text">Initializing browser window</span>
                </div>
                <div class="step-item" id="step-2">
                    <span class="step-icon">⏳</span>
                    <span class="step-text">Setting up download detection</span>
                </div>
                <div class="step-item" id="step-3">
                    <span class="step-icon">⏳</span>
                    <span class="step-text">Loading website</span>
                </div>
            </div>
            
            <div class="loading-tip">
                <strong>💡 What's happening:</strong> We're creating a secure, isolated browser window that can automatically detect and install your mod downloads.
            </div>
        </div>
        
        <style>
            .browser-loading-container {
                text-align: center;
                padding: 2rem;
                min-height: 300px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            
            .loading-spinner {
                position: relative;
                width: 80px;
                height: 80px;
                margin-bottom: 2rem;
            }
            
            .spinner-ring {
                position: absolute;
                width: 100%;
                height: 100%;
                border: 4px solid transparent;
                border-top-color: #3b82f6;
                border-radius: 50%;
                animation: spin 1.2s linear infinite;
            }
            
            .spinner-ring:nth-child(2) {
                width: 90%;
                height: 90%;
                top: 5%;
                left: 5%;
                border-top-color: #6366f1;
                animation-delay: -0.4s;
            }
            
            .spinner-ring:nth-child(3) {
                width: 80%;
                height: 80%;
                top: 10%;
                left: 10%;
                border-top-color: #8b5cf6;
                animation-delay: -0.8s;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .loading-title {
                color: #f8fafc;
                margin: 0 0 1rem 0;
                font-size: 1.5rem;
                font-weight: 600;
            }
            
            .loading-message {
                color: #cbd5e1;
                margin-bottom: 2rem;
                font-size: 1rem;
                line-height: 1.5;
            }
            
            .loading-steps {
                display: flex;
                flex-direction: column;
                gap: 0.8rem;
                margin-bottom: 2rem;
                text-align: left;
                min-width: 300px;
            }
            
            .step-item {
                display: flex;
                align-items: center;
                gap: 0.8rem;
                padding: 0.6rem;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 6px;
                transition: all 0.3s ease;
            }
            
            .step-item.active {
                background: rgba(59, 130, 246, 0.1);
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
            
            .step-item.completed {
                background: rgba(16, 185, 129, 0.1);
                border: 1px solid rgba(16, 185, 129, 0.3);
            }
            
            .step-icon {
                font-size: 1rem;
                min-width: 20px;
            }
            
            .step-text {
                color: #e2e8f0;
                font-size: 0.9rem;
            }
            
            .loading-tip {
                background: rgba(251, 191, 36, 0.1);
                border: 1px solid rgba(251, 191, 36, 0.3);
                border-radius: 8px;
                padding: 1rem;
                color: #fef3c7;
                font-size: 0.9rem;
                line-height: 1.5;
                max-width: 400px;
            }
            
            .loading-tip strong {
                color: #fbbf24;
            }
        </style>
    `;
    
    showCustomModal({
        title: '🚀 Preparing Download Browser',
        content: content,
        type: 'info',
        customClass: 'browser-loading-modal',
        buttons: [] // No buttons during loading
    });
    
    // Animate the loading steps
    setTimeout(() => {
        const step1 = document.getElementById('step-1');
        const step2 = document.getElementById('step-2');
        if (step1 && step2) {
            step1.classList.add('completed');
            step1.classList.remove('active');
            step1.querySelector('.step-icon').textContent = '✅';
            step2.classList.add('active');
        }
    }, 1000);
    
    setTimeout(() => {
        const step2 = document.getElementById('step-2');
        const step3 = document.getElementById('step-3');
        if (step2 && step3) {
            step2.classList.add('completed');
            step2.classList.remove('active');
            step2.querySelector('.step-icon').textContent = '✅';
            step3.classList.add('active');
        }
    }, 2000);
}

/**
 * Start the browser download process
 */
async function startBrowserDownload() {
    console.log('🌐 Starting browser download process');
    
    try {
        // Use default site setting or fallback to Modrinth
        // The browser will automatically load the default site based on user settings
        let targetUrl = 'default'; // Special value that tells browser to use settings
        
        // Close the modal
        closeCustomModal();
        
        // Open the browser window (it will handle its own loading screen)
        if (window.electronAPI && window.electronAPI.openDownloadBrowser) {
            try {
                const result = await window.electronAPI.openDownloadBrowser(targetUrl);
                
                if (result.success) {
                    showNotification('✅ Download browser opened! Downloads will be detected automatically.', 'success');
                } else {
                    showNotification(`❌ Failed to open embedded browser: ${result.message}`, 'error');
                    
                    // Offer fallback to system browser
                    const fallbackModal = confirm('Would you like to open the website in your default browser instead?');
                    if (fallbackModal) {
                        if (window.electronAPI && window.electronAPI.openExternal) {
                            await window.electronAPI.openExternal(targetUrl);
                            showNotification('🌐 Opened in your default browser. Note: Downloads won\'t be auto-detected there.', 'info');
                        } else {
                            showNotification('❌ Unable to open external browser', 'error');
                        }
                    }
                }
            } catch (error) {
                console.error('Error opening browser:', error);
                showNotification('❌ Failed to open browser', 'error');
            }
        } else {
            // Fallback for demo mode
            showNotification('🔧 Demo mode: Browser functionality not available in development', 'info');
            console.log('Would open browser to:', targetUrl);
        }
        
    } catch (error) {
        console.error('❌ Error starting browser download:', error);
        showNotification('❌ Failed to start download browser', 'error');
    }
}

/**
 * Search for mods on CurseForge/Modrinth
 */
async function searchMods() {
    const searchInput = document.getElementById('mod-search-input');
    const resultsSection = document.getElementById('mod-search-results');
    const placeholder = document.getElementById('search-placeholder');
    const resultsList = document.getElementById('results-list');
    const resultsCount = document.getElementById('results-count');
    
    if (!searchInput || !searchInput.value.trim()) {
        showNotification('Please enter a search term', 'warning');
        return;
    }
    
    const searchTerm = searchInput.value.trim();
    const source = document.getElementById('mod-source')?.value || 'both';
    const category = document.getElementById('mod-category')?.value || '';
    
    console.log('🔍 Searching for mods:', { searchTerm, source, category });
    
    try {
        // Show loading state
        if (placeholder) placeholder.style.display = 'none';
        if (resultsSection) resultsSection.style.display = 'block';
        if (resultsList) resultsList.innerHTML = '<div class="loading-message">Searching for mods...</div>';
        
        // Call backend API
        if (window.electronAPI && window.electronAPI.searchMods) {
            const results = await window.electronAPI.searchMods({
                query: searchTerm,
                source: source,
                category: category,
                mcVersion: currentMinecraftProfile !== 'default' ? 
                    minecraftProfiles.find(p => p.id === currentMinecraftProfile)?.mcVersion : 'latest'
            });
            
            if (results.success && results.mods.length > 0) {
                displayModSearchResults(results.mods);
                if (resultsCount) resultsCount.textContent = `${results.mods.length} mods found`;
            } else {
                if (resultsList) resultsList.innerHTML = '<div class="no-results">No mods found for your search.</div>';
                if (resultsCount) resultsCount.textContent = '0 mods found';
            }
        } else {
            // Demo mode - show placeholder results
            const demoResults = [
                { name: 'OptiFine', description: 'Performance and graphics optimization', author: 'sp614x', downloads: '50M+' },
                { name: 'JEI', description: 'Just Enough Items recipe viewer', author: 'mezz', downloads: '30M+' },
                { name: 'Biomes O Plenty', description: 'Adds 60+ new biomes', author: 'Forstride', downloads: '25M+' }
            ];
            
            displayModSearchResults(demoResults);
            if (resultsCount) resultsCount.textContent = `${demoResults.length} mods found (Demo)`;
        }
    } catch (error) {
        console.error('❌ Error searching for mods:', error);
        if (resultsList) resultsList.innerHTML = '<div class="error-message">Failed to search for mods. Please try again.</div>';
        showNotification('❌ Failed to search for mods', 'error');
    }
}

/**
 * Display mod search results
 */
function displayModSearchResults(mods) {
    const resultsList = document.getElementById('results-list');
    if (!resultsList) return;
    
    const modsHTML = mods.map(mod => `
        <div class="mod-result-item">
            <div class="mod-result-info">
                <h5>${mod.name}</h5>
                <p>${mod.description}</p>
                <small>by ${mod.author} • ${mod.downloads} downloads</small>
                </div>
            <div class="mod-result-actions">
                <button class="btn-download" onclick="downloadMod('${mod.id || mod.name}')">
                    ⬇️ Download
                </button>
            </div>
        </div>
    `).join('');
    
    resultsList.innerHTML = modsHTML;
}

/**
 * Download a specific mod
 */
async function downloadMod(modId) {
    console.log('⬇️ Downloading mod:', modId);
    
    try {
        showNotification('Downloading mod...', 'info');
        
        if (window.electronAPI && window.electronAPI.downloadMod) {
            const result = await window.electronAPI.downloadMod({
                modId: modId,
                profile: currentMinecraftProfile
            });
            
            if (result.success) {
                showNotification(`✅ Mod downloaded successfully!`, 'success');
                
                // Refresh mods to show the new download
                if (selectedGameId === 'minecraft') {
                    await refreshMods();
                }
            } else {
                showNotification(`❌ Failed to download mod: ${result.message}`, 'error');
            }
        } else {
            // Demo mode
            showNotification('✅ Mod download simulated (Demo mode)', 'info');
        }
    } catch (error) {
        console.error('❌ Error downloading mod:', error);
        showNotification('❌ Failed to download mod', 'error');
    }
}

// === EXPOSE FUNCTIONS TO GLOBAL SCOPE ===
// Make sure all onclick handlers can access these functions
window.switchTab = switchTab;
window.startCleanup = startCleanup;
window.scanSystem = scanSystem;
window.openDesktopArsenal = openDesktopArsenal;
window.showAddGameDialog = showAddGameDialog;
window.showRemoveGameDialog = showRemoveGameDialog;
window.showModManagerHelp = showModManagerHelp;
window.filterGameLibrary = filterGameLibrary;
window.clearGameSearch = clearGameSearch;
window.changeSort = changeSort;
window.changeFilter = changeFilter;
window.togglePresetGames = togglePresetGames;
window.backToGameLibrary = backToGameLibrary;
window.showAddModDialog = showAddModDialog;
window.refreshMods = refreshMods;
window.openModFolder = openModFolder;
window.filterMods = filterMods;
window.clearSearch = clearSearch;
window.selectGame = selectGame;
window.showDeleteModDialog = showDeleteModDialog;
window.deleteMod = deleteMod;
window.closeCustomModal = closeCustomModal;
window.showNotification = showNotification;
window.closeNotification = closeNotification;
window.switchToCategory = switchToCategory;
window.addQuickLaunchApp = addQuickLaunchApp;
window.addQuickAccessItem = addQuickAccessItem;
window.openTaskManager = openTaskManager;
window.showCreateMinecraftProfile = showCreateMinecraftProfile;
window.showDownloadModsDialog = showDownloadModsDialog;
window.switchMinecraftProfile = switchMinecraftProfile;

// PC Health functions
window.openPCHealthTool = openPCHealthTool;
window.backToPCHealthMain = backToPCHealthMain;

// === PC HEALTH FUNCTIONS ===
// Declare variables at the top to avoid initialization errors
var benchmarkInterval = null;
var currentPCHealthTool = null;

function openPCHealthTool(toolName) {
    console.log('🔧 openPCHealthTool called with:', toolName);
    
    currentPCHealthTool = toolName;
    
    // For benchmark, show warning modal first
    if (toolName === 'benchmark') {
        showBenchmarkWarning();
        return;
    }
    
    // For desktop arsenal, check if premium and handle accordingly
    if (toolName === 'desktopArsenal') {
        // Always show the premium notice for now
        showPCHealthToolView(toolName);
        return;
    }
    
    // For other tools, show directly
    showPCHealthToolView(toolName);
}

function showPCHealthToolView(toolName) {
    const mainView = document.getElementById('pc-health-main');
    const toolViewId = `${toolName === 'junkCleaner' ? 'junk-cleaner' : toolName}-view`;
    const toolView = document.getElementById(toolViewId);
    
    if (!mainView || !toolView) {
        console.error('❌ Could not find views for tool:', toolName);
        return;
    }
    
    console.log('🔧 Transitioning to tool view:', toolName);
    
    // Use similar transition as mod manager
    animatePCHealthTransition(mainView, toolView);
}

function backToPCHealthMain() {
    console.log('🔧 Returning to PC Health main view');
    
    const mainView = document.getElementById('pc-health-main');
    const currentToolView = document.querySelector('.pc-health-tool-view.active');
    
    if (!mainView || !currentToolView) {
        console.error('❌ Could not find views for back navigation');
        return;
    }
    
    // Clear current tool
    currentPCHealthTool = null;
    
    // Use transition back to main view
    animatePCHealthTransition(currentToolView, mainView);
}

function animatePCHealthTransition(fromEl, toEl) {
    console.log('🎬 PC Health transition from', fromEl.id, 'to', toEl.id);

    // Immediately hide destination and show source
    toEl.style.display = 'none';
    toEl.style.opacity = '0';
    toEl.classList.remove('active');
    
    fromEl.style.display = 'flex';
    fromEl.style.opacity = '1';
    fromEl.classList.add('active');
    
    // Simple fade out source, then fade in destination
    fromEl.style.transition = 'opacity 0.3s ease';
    fromEl.style.opacity = '0';
    
    setTimeout(() => {
        // Hide source completely
        fromEl.style.display = 'none';
        fromEl.classList.remove('active');
        
        // Show destination
        toEl.style.display = 'flex';
        toEl.style.transition = 'opacity 0.3s ease';
        toEl.classList.add('active');
        
        // Use requestAnimationFrame for smooth opacity change
        requestAnimationFrame(() => {
            toEl.style.opacity = '1';
        });
        
        // Reset transition state
        setTimeout(() => {
            // Clean up inline styles
            fromEl.style.transition = '';
            toEl.style.transition = '';
            console.log('🎬 PC Health transition completed');
        }, 300);
        
    }, 300);
}

function openJunkCleaner() {
    const section = document.getElementById('junk-cleaner-section');
    const toolSection = document.querySelector('.pc-health-tool-section');
    
    if (section) {
        section.style.display = 'block';
        updateCleanupStats();
        
        // Show the tool section
        if (toolSection) toolSection.classList.add('active');
        
        // Mark card as active
        document.querySelector('[data-tool="junkCleaner"]')?.classList.add('active');
    }
}

function showBenchmarkWarning() {
    console.log('⚠️ Showing benchmark warning modal...');
    showCustomModal({
        title: '⚠️ Benchmark Warning',
        content: `
            <div style="text-align: center; padding: 2rem;">
                <div style="font-size: 5rem; margin-bottom: 2rem; filter: drop-shadow(0 4px 8px rgba(255, 165, 0, 0.3));">📊</div>
                <h3 style="margin-bottom: 1.5rem; color: var(--text-primary); font-size: 1.5rem; font-weight: 600;">Before Running the Benchmark</h3>
                
                <div style="background: rgba(255, 165, 0, 0.1); border: 1px solid rgba(255, 165, 0, 0.3); border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem;">
                    <p style="margin-bottom: 1rem; line-height: 1.6; color: var(--text-primary); font-size: 1rem;">To ensure accurate results, please:</p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 0.75rem; text-align: left;">
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                            <span style="color: #10b981; font-size: 1.2rem;">✅</span>
                            <span style="color: var(--text-primary); font-size: 0.95rem;">Close all other applications</span>
                    </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                            <span style="color: #10b981; font-size: 1.2rem;">✅</span>
                            <span style="color: var(--text-primary); font-size: 0.95rem;">Disable background programs</span>
                    </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                            <span style="color: #10b981; font-size: 1.2rem;">✅</span>
                            <span style="color: var(--text-primary); font-size: 0.95rem;">Ensure your PC is plugged in (not on battery)</span>
                    </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                            <span style="color: #10b981; font-size: 1.2rem;">✅</span>
                            <span style="color: var(--text-primary); font-size: 0.95rem;">Do not use your computer during the test</span>
                    </div>
                    </div>
                    </div>
                
                <div style="background: rgba(255, 165, 0, 0.15); border: 1px solid rgba(255, 165, 0, 0.4); border-radius: 12px; padding: 1.5rem; margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 0.75rem; margin-bottom: 0.75rem;">
                        <span style="color: #f59e0b; font-size: 1.5rem;">⚠️</span>
                        <span style="color: #f59e0b; font-weight: 600; font-size: 1.1rem;">Important Notice</span>
                </div>
                    <p style="color: var(--text-primary); margin: 0; line-height: 1.6; font-size: 1rem;">
                        The benchmark will test your CPU, GPU, RAM, and storage performance. 
                        This comprehensive test may take <strong style="color: #f59e0b;">2-5 minutes</strong> to complete.
                    </p>
            </div>
            
                <p style="color: var(--text-secondary); font-size: 0.9rem; margin: 0; font-style: italic;">
                    Click "I Understand" to proceed to the benchmark page where you can start the test.
                </p>
                </div>
        `,
        type: 'warning',
        preventOutsideClose: true,
        buttons: [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                action: () => {
                    closeCustomModal();
                    currentPCHealthTool = null;
                }
            },
            {
                text: 'I Understand',
                class: 'btn-primary',
                action: () => {
                    closeCustomModal();
                    openBenchmark();
                }
            }
        ]
    });
}

function openBenchmark() {
    console.log('🚀 Opening benchmark view...');
    
    // Show the benchmark tool view
    showPCHealthToolView('benchmark');
    
    // Reset to start section
    showBenchmarkSection('benchmark-start');
    
    console.log('✅ Benchmark view opened');
}

function openJunkCleaner() {
    console.log('🧹 Opening junk cleaner view...');
    
    // Show the junk cleaner tool view
    showPCHealthToolView('junkCleaner');
    
    console.log('✅ Junk cleaner view opened');
}

function showBenchmarkSection(sectionId) {
    const sections = document.querySelectorAll('.benchmark-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'flex';
    }
}

async function startBenchmark() {
    console.log('🚀 Starting benchmark...');
    
    if (!ipcRenderer) {
        console.error('❌ IPC Renderer not available');
        showNotification('IPC connection not available. Cannot run benchmark.', 'error');
        return;
    }
    
    // Test IPC connection first
    try {
        console.log('🔌 Testing IPC connection...');
        const testResult = await ipcRenderer.invoke('test-connection');
        console.log('🔌 IPC test result:', testResult);
    } catch (error) {
        console.error('❌ IPC test failed:', error);
        showNotification('IPC connection test failed: ' + error.message, 'error');
        return;
    }
    
    showBenchmarkSection('benchmark-progress');
    
    // Start progress monitoring
    benchmarkInterval = setInterval(updateBenchmarkProgress, 500);
    
    try {
        console.log('📊 Invoking run-benchmark IPC...');
        const result = await ipcRenderer.invoke('run-benchmark');
        console.log('📊 Benchmark result:', result);
        
        if (result && result.success) {
            console.log('✅ Benchmark completed successfully');
            displayBenchmarkResults(result.results);
        } else {
            console.error('❌ Benchmark failed:', result ? result.message : 'No response');
            showNotification('Benchmark failed: ' + (result ? result.message : 'No response from backend'), 'error');
            showBenchmarkSection('benchmark-start');
        }
    } catch (error) {
        console.error('❌ Benchmark error:', error);
        showNotification('Benchmark failed: ' + error.message, 'error');
        showBenchmarkSection('benchmark-start');
    } finally {
        if (benchmarkInterval) {
            clearInterval(benchmarkInterval);
            benchmarkInterval = null;
        }
    }
}

async function updateBenchmarkProgress() {
    try {
        if (!ipcRenderer) {
            console.warn('⚠️ IPC Renderer not available for progress update');
            return;
        }
        
        const progress = await ipcRenderer.invoke('get-benchmark-progress');
        console.log('📊 Benchmark progress:', progress);
        
        if (progress) {
            const progressPercent = (progress.current / progress.total) * 100;
            const progressFill = document.getElementById('benchmark-progress-fill');
            if (progressFill) {
                progressFill.style.width = `${progressPercent}%`;
            }
            
            const currentTest = document.getElementById('benchmark-current-test');
            if (currentTest) {
                currentTest.textContent = progress.currentTest || 'Running...';
            }
            
            const progressText = document.getElementById('benchmark-progress-text');
            if (progressText) {
                progressText.textContent = `${progress.current}/${progress.total}`;
            }
            
            // Update test indicators
            const tests = ['cpu', 'gpu', 'ram', 'storage'];
            tests.forEach((test, index) => {
                const indicator = document.getElementById(`${test}-indicator`);
                if (indicator) {
                    if (index < progress.current) {
                        indicator.classList.add('complete');
                        indicator.classList.remove('active');
                    } else if (index === progress.current) {
                        indicator.classList.add('active');
                        indicator.classList.remove('complete');
                    } else {
                        indicator.classList.remove('active', 'complete');
                    }
                }
            });
            
            // If benchmark is complete, stop monitoring
            if (progress.status === 'complete' || progress.status === 'error') {
                if (benchmarkInterval) {
                    clearInterval(benchmarkInterval);
                    benchmarkInterval = null;
                }
            }
        }
    } catch (error) {
        console.error('❌ Error updating benchmark progress:', error);
    }
}

function displayBenchmarkResults(results) {
    console.log('📊 Displaying benchmark results:', results);
    
    showBenchmarkSection('benchmark-results');
    
    // Overall Score
    if (results.overall) {
        console.log('📊 Setting overall score:', results.overall);
        const overallScoreEl = document.getElementById('overall-score');
        const overallRatingEl = document.getElementById('overall-rating');
        const overallDescEl = document.getElementById('overall-description');
        
        if (overallScoreEl) overallScoreEl.textContent = results.overall.percentile || '0';
        if (overallRatingEl) overallRatingEl.textContent = results.overall.rating || 'N/A';
        if (overallDescEl) overallDescEl.textContent = results.overall.description || '';
    }
    
    // CPU Results
    if (results.cpu) {
        console.log('📊 Setting CPU results:', results.cpu);
        const cpuDiv = document.getElementById('cpu-results');
        if (cpuDiv) {
            cpuDiv.innerHTML = `
                <div class="result-item">
                    <span>Single-Core:</span>
                    <strong>${results.cpu.singleCore.score.toLocaleString()} ${results.cpu.singleCore.unit} (${results.cpu.singleCore.percentile}%)</strong>
                    <small>Duration: ${(results.cpu.singleCore.duration / 1000).toFixed(1)}s | Clock: ${results.cpu.singleCore.clockSpeed?.avg || 'N/A'}GHz avg</small>
                </div>
                <div class="result-item">
                    <span>Multi-Core:</span>
                    <strong>${results.cpu.multiCore.score.toLocaleString()} ${results.cpu.multiCore.unit} (${results.cpu.multiCore.percentile}%)</strong>
                    <small>${results.cpu.details.cores} cores, Duration: ${(results.cpu.multiCore.duration / 1000).toFixed(1)}s | Clock: ${results.cpu.multiCore.clockSpeed?.avg || 'N/A'}GHz avg</small>
                </div>
                ${results.cpu.singleCore.temperature?.avg > 0 ? `
                <div class="result-item">
                    <span>Temperature:</span>
                    <strong>${results.cpu.singleCore.temperature.min}°C - ${results.cpu.singleCore.temperature.max}°C</strong>
                    <small>Average: ${results.cpu.singleCore.temperature.avg}°C during test</small>
                </div>
                ` : ''}
                ${results.cpu.details ? `
                <div class="result-item">
                    <span>CPU Info:</span>
                    <strong>${results.cpu.details.brand}</strong>
                    <small>${results.cpu.details.speed}GHz base, ${results.cpu.details.maxSpeed}GHz max, ${results.cpu.details.cores} cores/${results.cpu.details.threads} threads</small>
                </div>
                ` : ''}
            `;
        }
    }
    
    // GPU Results
    if (results.gpu) {
        console.log('📊 Setting GPU results:', results.gpu);
        const gpuDiv = document.getElementById('gpu-results');
        if (gpuDiv) {
            gpuDiv.innerHTML = `
                <div class="result-item">
                    <span>Compute:</span>
                    <strong>${results.gpu.compute.score.toLocaleString()} ${results.gpu.compute.unit} (${results.gpu.compute.percentile}%)</strong>
                    <small>${results.gpu.compute.operations.toLocaleString()} operations in ${(results.gpu.compute.duration / 1000).toFixed(1)}s</small>
                </div>
                <div class="result-item">
                    <span>Rendering:</span>
                    <strong>${results.gpu.rendering.score} ${results.gpu.rendering.unit} (${results.gpu.rendering.percentile}%)</strong>
                    <small>${results.gpu.rendering.frames} frames, avg ${results.gpu.rendering.avgFrameTime}ms/frame</small>
                </div>
                ${results.gpu.temperature?.avg > 0 ? `
                <div class="result-item">
                    <span>Temperature:</span>
                    <strong>${results.gpu.temperature.min}°C - ${results.gpu.temperature.max}°C</strong>
                    <small>Average: ${results.gpu.temperature.avg}°C during test</small>
                </div>
                ` : ''}
                ${results.gpu.details ? `
                <div class="result-item">
                    <span>GPU Info:</span>
                    <strong>${results.gpu.details.model}</strong>
                    <small>${results.gpu.details.vram}MB VRAM, ${results.gpu.details.vendor}${results.gpu.details.bus ? ` (${results.gpu.details.bus})` : ''}</small>
                </div>
                <div class="result-item">
                    <span>Test Note:</span>
                    <strong>CPU-based GPU simulation</strong>
                    <small>JavaScript cannot directly access GPU hardware - this simulates GPU workloads</small>
                </div>
                ` : ''}
            `;
        }
    }
    
    // RAM Results
    if (results.ram) {
        console.log('📊 Setting RAM results:', results.ram);
        const ramDiv = document.getElementById('ram-results');
        if (ramDiv) {
            ramDiv.innerHTML = `
                <div class="result-item">
                    <span>Bandwidth:</span>
                    <strong>${results.ram.bandwidth.score.toLocaleString()} ${results.ram.bandwidth.unit} (${results.ram.bandwidth.percentile}%)</strong>
                    <small>Read: ${results.ram.bandwidth.readSpeed}MB/s, Write: ${results.ram.bandwidth.writeSpeed}MB/s</small>
                </div>
                <div class="result-item">
                    <span>Latency:</span>
                    <strong>${results.ram.bandwidth.latency}μs</strong>
                    <small>Random access latency</small>
                </div>
                <div class="result-item">
                    <span>Capacity:</span>
                    <strong>${results.ram.size ? results.ram.size.totalFormatted : 'N/A'}</strong>
                    <small>${results.ram.size ? `${results.ram.size.usedPercent}% used (${results.ram.size.available ? (results.ram.size.available / (1024*1024*1024)).toFixed(1) + 'GB free' : 'free unknown'})` : ''}</small>
                </div>
            `;
        }
    }
    
    // Storage Results
    if (results.storage && results.storage.sequential) {
        console.log('📊 Setting storage results:', results.storage);
        const storageDiv = document.getElementById('storage-results');
        if (storageDiv) {
            storageDiv.innerHTML = `
                <div class="result-item">
                    <span>Read Speed:</span>
                    <strong>${results.storage.sequential.read} ${results.storage.sequential.unit} (${results.storage.sequential.percentile}%)</strong>
                    <small>Sequential read performance</small>
                </div>
                <div class="result-item">
                    <span>Write Speed:</span>
                    <strong>${results.storage.sequential.write} ${results.storage.sequential.unit}</strong>
                    <small>Sequential write performance</small>
                </div>
                ${results.storage.summary ? `
                <div class="result-item">
                    <span>Test Summary:</span>
                    <strong>${results.storage.summary.successfulTests}/${results.storage.summary.totalTests} tests passed</strong>
                    <small>Avg: ${results.storage.summary.avgReadSpeed}MB/s read, ${results.storage.summary.avgWriteSpeed}MB/s write</small>
                </div>
                ` : ''}
            `;
        }
    }
    
    // Update the benchmark stats on the main card
    if (results.overall) {
        updateBenchmarkStats(results.overall);
    }
    
    console.log('✅ Benchmark results display completed');
}

function updateBenchmarkStats(overallResults) {
    console.log('📊 Updating benchmark stats on main card:', overallResults);
    const benchmarkStats = document.getElementById('benchmark-stats');
    if (benchmarkStats && overallResults) {
        const statValue = benchmarkStats.querySelector('.stat-value');
        if (statValue) {
            statValue.textContent = `${overallResults.percentile}th percentile - ${overallResults.rating}`;
        }
    }
}

function runBenchmarkAgain() {
    console.log('🔄 Running benchmark again...');
    startBenchmark();
}

async function exportBenchmarkResults() {
    try {
        const results = await ipcRenderer.invoke('get-benchmark-results');
        if (!results || Object.keys(results).length === 0) {
            showNotification('No benchmark results to export', 'warning');
            return;
        }
        
        // Format results as text
        let exportText = 'Armory X - PC Benchmark Results\n';
        exportText += '================================\n\n';
        exportText += `Date: ${new Date().toLocaleString()}\n\n`;
        
        if (results.system) {
            exportText += 'System Information:\n';
            exportText += `CPU: ${results.system.cpu.brand} (${results.system.cpu.cores} cores)\n`;
            exportText += `Memory: ${results.system.memory.totalFormatted}\n`;
            if (results.system.gpu) {
                exportText += `GPU: ${results.system.gpu.model} (${results.system.gpu.vramFormatted})\n`;
            }
            exportText += `OS: ${results.system.os.distro} ${results.system.os.release}\n\n`;
        }
        
        if (results.overall) {
            exportText += `Overall Score: ${results.overall.percentile}th percentile\n`;
            exportText += `Rating: ${results.overall.rating}\n\n`;
        }
        
        // Add detailed results
        if (results.cpu) {
            exportText += 'CPU Performance:\n';
            exportText += `  Single-Core: ${results.cpu.singleCore.score.toLocaleString()} ops/sec (${results.cpu.singleCore.percentile}th percentile)\n`;
            exportText += `  Multi-Core: ${results.cpu.multiCore.score.toLocaleString()} ops/sec (${results.cpu.multiCore.percentile}th percentile)\n\n`;
        }
        
        if (results.gpu) {
            exportText += 'GPU Performance:\n';
            exportText += `  Compute: ${results.gpu.compute.score.toLocaleString()} pixels/sec (${results.gpu.compute.percentile}th percentile)\n`;
            exportText += `  Rendering: ${results.gpu.rendering.score} FPS (${results.gpu.rendering.percentile}th percentile)\n\n`;
        }
        
        if (results.ram) {
            exportText += 'Memory Performance:\n';
            exportText += `  Bandwidth: ${results.ram.bandwidth.score.toLocaleString()} MB/s (${results.ram.bandwidth.percentile}th percentile)\n`;
            exportText += `  Total: ${results.ram.size.totalFormatted}\n`;
            exportText += `  Available: ${results.ram.size.freeFormatted}\n\n`;
        }
        
        if (results.storage && results.storage.sequential) {
            exportText += 'Storage Performance:\n';
            exportText += `  Read Speed: ${results.storage.sequential.read} MB/s\n`;
            exportText += `  Write Speed: ${results.storage.sequential.write} MB/s\n`;
            exportText += `  Average: ${results.storage.sequential.average} MB/s (${results.storage.sequential.percentile}th percentile)\n`;
        }
        
        // Copy to clipboard
        await navigator.clipboard.writeText(exportText);
        showNotification('Benchmark results copied to clipboard!', 'success');
        
    } catch (error) {
        console.error('Export error:', error);
        showNotification('Failed to export results', 'error');
    }
}

function updateBenchmarkStats(overall) {
    const statsElement = document.getElementById('benchmark-stats');
    if (statsElement && overall) {
        statsElement.innerHTML = `<span class="stat-value">${overall.percentile}th percentile - ${overall.rating}</span>`;
    }
}

function updateCleanupStats() {
    // This can be enhanced to show real-time stats
    const cleanupStats = document.getElementById('cleanup-stats');
    if (cleanupStats) {
        // You can add logic here to check last cleanup stats
        cleanupStats.innerHTML = '<span class="stat-value">Ready to clean</span>';
    }
}

// Update the Desktop Arsenal stats display (already exists, but let's ensure the format matches)
function updateArsenalStats() {
    const stats = document.getElementById('arsenal-stats');
    if (stats) {
        ipcRenderer.invoke('desktop-arsenal-get-stats').then(arsenalStats => {
            if (arsenalStats) {
                stats.innerHTML = `<span class="stat-value">${arsenalStats.hiddenCount} files organized</span>`;
            }
        }).catch(console.error);
    }
}

// Call updateArsenalStats on page load
document.addEventListener('DOMContentLoaded', () => {
    updateArsenalStats();
});



// Make PC Health functions globally available
window.startBenchmark = startBenchmark;
window.runBenchmarkAgain = runBenchmarkAgain;
window.exportBenchmarkResults = exportBenchmarkResults;

