// Benchmark Module for PC Health
const { ipcMain } = require('electron');
const Benchmark = require('benchmark');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const si = require('systeminformation');
const fs = require('fs-extra');
const os = require('os');
const path = require('path');
const crypto = require('crypto');

class BenchmarkModule {
  constructor() {
    console.log('🏗️ Initializing BenchmarkModule...');
    this.isRunning = false;
    this.results = {};
    this.progress = {
      current: 0,
      total: 4,
      currentTest: '',
      status: 'idle'
    };
    
    // Updated baseline scores for comparison (adjusted for extreme intensive tests)
    this.baselines = {
      cpu: {
        singleCore: 150000000, // ops/sec baseline (extremely high for intensive single-core stress)
        multiCore: 800000000, // ops/sec baseline (multi-threaded Worker stress)
      },
      gpu: {
        compute: 50000000,   // ops/sec (much higher for massive GPU simulation)
        rendering: 120      // FPS baseline (higher target)
      },
      ram: {
        bandwidth: 25000    // MB/s baseline (DDR4-3200 range)
      },
      storage: {
        sequential: 1000    // MB/s baseline (NVMe SSD)
      }
    };
    
    this.setupHandlers();
    console.log('✅ BenchmarkModule initialized successfully');
  }

  setupHandlers() {
    console.log('🔌 Setting up benchmark IPC handlers...');
    ipcMain.handle('run-benchmark', this.runFullBenchmark.bind(this));
    ipcMain.handle('get-benchmark-progress', this.getProgress.bind(this));
    ipcMain.handle('get-benchmark-results', this.getResults.bind(this));
    ipcMain.handle('stop-benchmark', this.stopBenchmark.bind(this));
    console.log('✅ Benchmark IPC handlers registered');
  }

  async runFullBenchmark() {
    console.log('🚀 runFullBenchmark called');
    
    if (this.isRunning) {
      console.log('⚠️ Benchmark already running');
      return { success: false, message: 'Benchmark already running' };
    }

    console.log('📊 Starting new EXTREME MULTI-THREADED benchmark...');
    console.log('⏱️ Estimated time: 30-35 seconds with REAL CPU stress testing');
    console.log('🔥 This benchmark uses Worker threads and will ACTUALLY load your CPU!');
    console.log('💀 Check Task Manager during tests:');
    console.log('💀   - Single-core: 50%+ CPU usage');
    console.log('💀   - Multi-core: 80%+ CPU usage');  
    console.log('💀   - GPU test: 30%+ CPU usage (normal - it\'s CPU simulation)');
    console.log('🚀 Clock speeds should now be properly captured at peak frequencies!');
    
    this.isRunning = true;
    this.results = {};
    this.progress = {
      current: 0,
      total: 4,
      currentTest: 'Starting intensive benchmark...',
      status: 'running'
    };

    try {
      // Get system info first
      const systemInfo = await this.getSystemSpecs();
      this.results.system = systemInfo;

      // Run each benchmark
      await this.runCPUBenchmark();
      this.progress.current++;
      
      await this.runGPUSimulation();
      this.progress.current++;
      
      await this.runRAMBenchmark();
      this.progress.current++;
      
      await this.runStorageBenchmark();
      this.progress.current++;

      // Calculate overall score
      this.results.overall = this.calculateOverallScore();
      
      this.progress.status = 'complete';
      this.progress.currentTest = 'Benchmark complete!';
      this.isRunning = false;

      return {
        success: true,
        results: this.results,
        message: 'Benchmark completed successfully'
      };

    } catch (error) {
      this.isRunning = false;
      this.progress.status = 'error';
      this.progress.currentTest = `Error: ${error.message}`;
      
      return {
        success: false,
        message: `Benchmark failed: ${error.message}`
      };
    }
  }

  async getSystemSpecs() {
    const [cpu, mem, graphics, osInfo, disk] = await Promise.all([
      si.cpu(),
      si.mem(),
      si.graphics(),
      si.osInfo(),
      si.diskLayout()
    ]);

    return {
      cpu: {
        manufacturer: cpu.manufacturer,
        brand: cpu.brand,
        cores: cpu.cores,
        speed: cpu.speed,
        speedMax: cpu.speedMax
      },
      memory: {
        total: mem.total,
        totalFormatted: this.formatBytes(mem.total)
      },
      gpu: graphics.controllers[0] ? {
        model: graphics.controllers[0].model,
        vram: graphics.controllers[0].vram,
        vramFormatted: `${graphics.controllers[0].vram} MB`
      } : null,
      os: {
        platform: osInfo.platform,
        distro: osInfo.distro,
        release: osInfo.release,
        arch: osInfo.arch
      },
      storage: disk.map(d => ({
        name: d.name,
        size: d.size,
        sizeFormatted: this.formatBytes(d.size),
        type: d.type
      }))
    };
  }

  async runCPUBenchmark() {
    this.progress.currentTest = 'Initializing CPU benchmark...';
    
    const cpuInfo = await si.cpu();
    const numCores = cpuInfo.cores;
    const cpuSpeed = await si.cpuCurrentSpeed();
    
    console.log(`🖥️ CPU: ${cpuInfo.brand} - ${numCores} cores at ${cpuSpeed.avg}GHz`);

    // More intensive single-thread test (non-blocking)
    this.progress.currentTest = 'Running single-core stress test...';
    console.log('🔥 Starting intensive single-core CPU test...');
    
    const singleCoreResult = await this.runNonBlockingCPUTest(6000, 1, cpuSpeed.avg); // 6 seconds, single core

    // Multi-thread simulation using real Worker threads
    this.progress.currentTest = 'Running REAL multi-core stress test...';
    console.log('🔥 Starting REAL multi-threaded CPU test with Worker threads...');
    
    const multiCoreResult = await this.runNonBlockingCPUTest(8000, numCores, cpuSpeed.avg); // 8 seconds, all cores

    // Calculate percentiles with more realistic baselines
    const singlePercentile = Math.min(100, Math.round((singleCoreResult.score / this.baselines.cpu.singleCore) * 100));
    const multiPercentile = Math.min(100, Math.round((multiCoreResult.score / this.baselines.cpu.multiCore) * 100));

    this.results.cpu = {
      singleCore: {
        score: singleCoreResult.score,
        unit: 'ops/sec',
        percentile: singlePercentile,
        duration: singleCoreResult.duration,
        clockSpeed: singleCoreResult.clockSpeed,
        temperature: singleCoreResult.temperature
      },
      multiCore: {
        score: multiCoreResult.score,
        unit: 'ops/sec',
        percentile: multiPercentile,
        cores: numCores,
        duration: multiCoreResult.duration,
        clockSpeed: multiCoreResult.clockSpeed,
        temperature: multiCoreResult.temperature
      },
      details: {
        brand: cpuInfo.brand,
        model: cpuInfo.model,
        speed: cpuSpeed.avg,
        maxSpeed: cpuSpeed.max,
        cores: numCores,
        threads: cpuInfo.cores * 2, // Approximate hyperthreading
        cache: cpuInfo.cache,
        architecture: cpuInfo.arch
      }
    };
  }

  async runGPUSimulation() {
    this.progress.currentTest = 'Analyzing graphics capabilities...';
    
    const gpuResult = await this.runNonBlockingGPUTest(6000, 4000); // 6s compute + 4s rendering
    
    // Calculate percentiles with improved baselines
    const computePercentile = Math.min(100, Math.round((gpuResult.compute.score / this.baselines.gpu.compute) * 100));
    const renderPercentile = Math.min(100, Math.round((gpuResult.rendering.score / this.baselines.gpu.rendering) * 100));

    this.results.gpu = {
      compute: {
        score: gpuResult.compute.score,
        unit: 'ops/sec',
        percentile: computePercentile,
        operations: gpuResult.compute.operations,
        duration: gpuResult.compute.duration
      },
      rendering: {
        score: gpuResult.rendering.score,
        unit: 'FPS',
        percentile: renderPercentile,
        frames: gpuResult.rendering.frames,
        duration: gpuResult.rendering.duration,
        avgFrameTime: gpuResult.rendering.avgFrameTime
      },
      temperature: gpuResult.temperature,
      details: gpuResult.gpu ? {
        model: gpuResult.gpu.model,
        vendor: gpuResult.gpu.vendor,
        vram: gpuResult.gpu.vram,
        vramDynamic: gpuResult.gpu.vramDynamic,
        bus: gpuResult.gpu.bus,
        clockCore: gpuResult.gpu.clockCore,
        clockMemory: gpuResult.gpu.clockMemory
      } : {
        model: 'Unknown/Integrated',
        vendor: 'Unknown',
        vram: 'Shared',
        note: 'Graphics information not available'
      }
    };
  }

  async runRAMBenchmark() {
    this.progress.currentTest = 'Analyzing memory configuration...';
    
    const memInfo = await si.mem();
    const memLayout = await si.memLayout();
    
    console.log(`🧠 RAM: ${this.formatBytes(memInfo.total)} total, ${this.formatBytes(memInfo.available)} available`);
    
    // More comprehensive memory test with different access patterns (non-blocking)
    this.progress.currentTest = 'Running memory bandwidth test...';
    console.log('🔥 Starting intensive memory bandwidth test...');
    
    const testSize = Math.min(512 * 1024 * 1024, memInfo.free / 3); // 512MB or third of free RAM
    const buffer = Buffer.alloc(testSize);
    const int32Array = new Int32Array(buffer.buffer, buffer.byteOffset, buffer.length / 4);
    
    console.log(`📊 Testing with ${this.formatBytes(testSize)} buffer...`);
    
    // Sequential Write Test (4 seconds, non-blocking)
    this.progress.currentTest = 'Testing sequential write performance...';
    const writeStartTime = Date.now();
    let writeIterations = 0;
    
    while (Date.now() - writeStartTime < 4000) { // 4 seconds
      const chunkStart = Date.now();
      
      // Process in 100ms chunks to prevent UI blocking
      while (Date.now() - chunkStart < 100) {
        for (let i = 0; i < int32Array.length; i += 2048) { // Write in 8KB chunks
          const value = writeIterations * 0x12345678 + i;
          for (let j = 0; j < Math.min(2048, int32Array.length - i); j++) {
            int32Array[i + j] = value + j;
          }
        }
        writeIterations++;
      }
      
      // Yield control back to UI
      await new Promise(resolve => setTimeout(resolve, 1));
    }
    
    const writeTime = Date.now() - writeStartTime;
    const writeSpeed = Math.round((testSize * writeIterations) / (writeTime / 1000) / (1024 * 1024));
    
    console.log(`✅ Sequential write: ${writeSpeed} MB/s (${writeIterations} iterations in ${writeTime}ms)`);
    
    // Sequential Read Test (4 seconds, non-blocking)
    this.progress.currentTest = 'Testing sequential read performance...';
    const readStartTime = Date.now();
    let readIterations = 0;
    let checksum = 0;
    
    while (Date.now() - readStartTime < 4000) { // 4 seconds
      const chunkStart = Date.now();
      
      // Process in 100ms chunks
      while (Date.now() - chunkStart < 100) {
        for (let i = 0; i < int32Array.length; i += 2048) { // Read in 8KB chunks
          for (let j = 0; j < Math.min(2048, int32Array.length - i); j++) {
            checksum += int32Array[i + j];
          }
        }
        readIterations++;
      }
      
      // Yield control back to UI
      await new Promise(resolve => setTimeout(resolve, 1));
    }
    
    const readTime = Date.now() - readStartTime;
    const readSpeed = Math.round((testSize * readIterations) / (readTime / 1000) / (1024 * 1024));
    
    console.log(`✅ Sequential read: ${readSpeed} MB/s (${readIterations} iterations in ${readTime}ms)`);
    
    // Random Access Latency Test (non-blocking)
    this.progress.currentTest = 'Testing random access latency...';
    const randomStartTime = Date.now();
    const targetRandomAccesses = 500000; // More intensive
    let randomSum = 0;
    let accessCount = 0;
    
    while (Date.now() - randomStartTime < 3000 && accessCount < targetRandomAccesses) { // 3 seconds max
      const chunkStart = Date.now();
      
      // Process random accesses in 50ms chunks
      while (Date.now() - chunkStart < 50 && accessCount < targetRandomAccesses) {
        for (let i = 0; i < 1000 && accessCount < targetRandomAccesses; i++) {
          const randomIndex = Math.floor(Math.random() * int32Array.length);
          randomSum += int32Array[randomIndex];
          int32Array[randomIndex] = (randomSum & 0xFFFFFFFF) + accessCount;
          accessCount++;
        }
      }
      
      // Yield control
      await new Promise(resolve => setTimeout(resolve, 1));
    }
    
    const randomTime = Date.now() - randomStartTime;
    const latency = ((randomTime / accessCount) * 1000).toFixed(3); // microseconds (higher precision)
    
    console.log(`✅ Random access latency: ${latency}μs per access (${accessCount} accesses in ${randomTime}ms)`);
    
    // Calculate overall bandwidth
    const avgBandwidth = Math.round((writeSpeed + readSpeed) / 2);
    const bandwidthPercentile = Math.min(100, Math.round((avgBandwidth / this.baselines.ram.bandwidth) * 100));
    
    this.results.ram = {
      bandwidth: {
        score: avgBandwidth,
        unit: 'MB/s',
        percentile: bandwidthPercentile,
        writeSpeed,
        readSpeed,
        latency: parseFloat(latency)
      },
      size: {
        total: memInfo.total,
        totalFormatted: this.formatBytes(memInfo.total),
        available: memInfo.available,
        used: memInfo.used,
        usedPercent: Math.round((memInfo.used / memInfo.total) * 100),
        free: memInfo.free,
        active: memInfo.active,
        cached: memInfo.cached,
        buffers: memInfo.buffers
      },
      modules: memLayout && memLayout.length > 0 ? memLayout.map(module => ({
        size: this.formatBytes(module.size || 0),
        type: module.type || 'Unknown',
        speed: module.clockSpeed ? `${module.clockSpeed}MHz` : 'Unknown',
        manufacturer: module.manufacturer || 'Unknown',
        partNum: module.partNum || 'Unknown',
        voltage: module.voltageConfigured || 'Unknown'
      })) : []
    };
    
    console.log(`✅ Memory benchmark completed. Average bandwidth: ${avgBandwidth} MB/s, Latency: ${latency}μs`);
  }

  async runStorageBenchmark() {
    this.progress.currentTest = 'Analyzing storage devices...';
    
    const diskInfo = await si.diskLayout();
    const fsSize = await si.fsSize();
    
    console.log(`💾 Storage devices found: ${diskInfo.length}`);
    diskInfo.forEach(disk => {
      console.log(`  - ${disk.name}: ${this.formatBytes(disk.size)} ${disk.type}`);
    });
    
    const tempDir = os.tmpdir();
    const testFileBase = path.join(tempDir, 'armory-benchmark-test');
    
    // Test multiple file sizes for comprehensive UserBenchmark-style results
    const testSizes = [
      { size: 16 * 1024 * 1024, name: '4KB Random I/O', blockSize: 4096 },
      { size: 64 * 1024 * 1024, name: '64KB Sequential', blockSize: 64 * 1024 },
      { size: 256 * 1024 * 1024, name: '256MB Large Sequential', blockSize: 1024 * 1024 },
      { size: 1024 * 1024 * 1024, name: '1GB Sustained Transfer', blockSize: 1024 * 1024 }
    ];
    
    const results = [];
    
    for (let i = 0; i < testSizes.length; i++) {
      const test = testSizes[i];
      this.progress.currentTest = `Testing ${test.name}...`;
      
      console.log(`📊 Running ${test.name} test...`);
      
      try {
        const testFile = `${testFileBase}-${i}.tmp`;
        const testData = Buffer.alloc(test.size);
        
        // Fill with random data
        console.log(`  Generating ${this.formatBytes(test.size)} of random data...`);
        for (let offset = 0; offset < test.size; offset += 64 * 1024) {
          const chunkSize = Math.min(64 * 1024, test.size - offset);
          crypto.randomFillSync(testData, offset, chunkSize);
        }
        
        // Sequential Write Test
        const writeStart = Date.now();
        await fs.writeFile(testFile, testData);
        await fs.fsync ? fs.fsync(await fs.open(testFile, 'r+')) : Promise.resolve(); // Ensure data is written
        const writeTime = Date.now() - writeStart;
        const writeSpeed = Math.round((test.size / (writeTime / 1000)) / (1024 * 1024));
        
        // Sequential Read Test
        const readStart = Date.now();
        const readData = await fs.readFile(testFile);
        const readTime = Date.now() - readStart;
        const readSpeed = Math.round((test.size / (readTime / 1000)) / (1024 * 1024));
        
        // Random I/O Test (for smaller files)
        let randomReadSpeed = 0;
        let randomWriteSpeed = 0;
        let iops = 0;
        
        if (test.size <= 64 * 1024 * 1024) { // Only for smaller files
          this.progress.currentTest = `Testing ${test.name} random I/O...`;
          
          const randomStart = Date.now();
          const randomOps = Math.min(1000, test.size / test.blockSize);
          
          // Random write operations
          const fileHandle = await fs.open(testFile, 'r+');
          for (let op = 0; op < randomOps; op++) {
            const randomOffset = Math.floor(Math.random() * (test.size - test.blockSize));
            const randomData = Buffer.alloc(test.blockSize);
            crypto.randomFillSync(randomData);
            await fileHandle.write(randomData, 0, test.blockSize, randomOffset);
          }
          
          // Random read operations
          for (let op = 0; op < randomOps; op++) {
            const randomOffset = Math.floor(Math.random() * (test.size - test.blockSize));
            const readBuffer = Buffer.alloc(test.blockSize);
            await fileHandle.read(readBuffer, 0, test.blockSize, randomOffset);
          }
          
          await fileHandle.close();
          const randomTime = Date.now() - randomStart;
          
          iops = Math.round((randomOps * 2) / (randomTime / 1000)); // Total ops per second
          randomReadSpeed = Math.round((randomOps * test.blockSize) / (randomTime / 2000) / (1024 * 1024));
          randomWriteSpeed = randomReadSpeed; // Approximate
        }
        
        // Cleanup
        await fs.unlink(testFile);
        
        const result = {
          name: test.name,
          size: test.size,
          sizeFormatted: this.formatBytes(test.size),
          writeSpeed,
          readSpeed,
          randomReadSpeed,
          randomWriteSpeed,
          iops,
          writeTime,
          readTime
        };
        
        results.push(result);
        
        console.log(`  ✅ ${test.name}: Write ${writeSpeed}MB/s, Read ${readSpeed}MB/s${iops > 0 ? `, IOPS ${iops}` : ''}`);
        
      } catch (error) {
        console.error(`❌ Storage test failed for ${test.name}:`, error.message);
        results.push({
          name: test.name,
          error: error.message
        });
      }
    }
    
    // Calculate overall scores from the largest successful test
    const sustainedTest = results.find(r => r.name.includes('256MB') && !r.error) || 
                         results.find(r => r.name.includes('64MB') && !r.error) ||
                         results.find(r => !r.error);
    
    if (sustainedTest) {
      const avgSpeed = Math.round((sustainedTest.writeSpeed + sustainedTest.readSpeed) / 2);
      const speedPercentile = Math.min(100, Math.round((avgSpeed / this.baselines.storage.sequential) * 100));
      
      this.results.storage = {
        sequential: {
          write: sustainedTest.writeSpeed,
          read: sustainedTest.readSpeed,
          unit: 'MB/s',
          percentile: speedPercentile
        },
        details: results,
        summary: {
          totalTests: results.length,
          successfulTests: results.filter(r => !r.error).length,
          avgWriteSpeed: Math.round(results.filter(r => !r.error).reduce((sum, r) => sum + r.writeSpeed, 0) / results.filter(r => !r.error).length),
          avgReadSpeed: Math.round(results.filter(r => !r.error).reduce((sum, r) => sum + r.readSpeed, 0) / results.filter(r => !r.error).length)
        },
        devices: diskInfo.map(disk => ({
          name: disk.name,
          size: this.formatBytes(disk.size),
          type: disk.type,
          interfaceType: disk.interfaceType,
          vendor: disk.vendor,
          model: disk.model
        }))
      };
      
      console.log(`✅ Storage benchmark completed. Average speed: ${avgSpeed} MB/s`);
    } else {
      console.error('❌ All storage tests failed');
      this.results.storage = {
        error: 'All storage performance tests failed',
        details: results
      };
    }
  }

  calculateOverallScore() {
    const weights = {
      cpu: 0.35,    // 35%
      gpu: 0.30,    // 30%
      ram: 0.20,    // 20%
      storage: 0.15 // 15%
    };

    let totalScore = 0;
    let totalWeight = 0;

    // CPU score (average of single and multi)
    if (this.results.cpu) {
      const cpuScore = (this.results.cpu.singleCore.percentile + this.results.cpu.multiCore.percentile) / 2;
      totalScore += cpuScore * weights.cpu;
      totalWeight += weights.cpu;
    }

    // GPU score (average of compute and rendering)
    if (this.results.gpu) {
      const gpuScore = (this.results.gpu.compute.percentile + this.results.gpu.rendering.percentile) / 2;
      totalScore += gpuScore * weights.gpu;
      totalWeight += weights.gpu;
    }

    // RAM score
    if (this.results.ram && this.results.ram.bandwidth) {
      totalScore += this.results.ram.bandwidth.percentile * weights.ram;
      totalWeight += weights.ram;
    }

    // Storage score
    if (this.results.storage && this.results.storage.sequential) {
      totalScore += this.results.storage.sequential.percentile * weights.storage;
      totalWeight += weights.storage;
    }

    const overallPercentile = totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;

    return {
      percentile: overallPercentile,
      rating: this.getRating(overallPercentile),
      description: this.getDescription(overallPercentile)
    };
  }

  getRating(percentile) {
    if (percentile >= 90) return 'Outstanding';
    if (percentile >= 75) return 'Excellent';
    if (percentile >= 60) return 'Good';
    if (percentile >= 40) return 'Average';
    if (percentile >= 25) return 'Below Average';
    return 'Poor';
  }

  getDescription(percentile) {
    if (percentile >= 90) return 'Your system performs exceptionally well!';
    if (percentile >= 75) return 'Your system has great performance.';
    if (percentile >= 60) return 'Your system performs well for most tasks.';
    if (percentile >= 40) return 'Your system has average performance.';
    if (percentile >= 25) return 'Your system may struggle with demanding tasks.';
    return 'Consider upgrading your hardware for better performance.';
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getProgress() {
    console.log('📊 getProgress called, returning:', this.progress);
    return this.progress;
  }

  getResults() {
    console.log('📊 getResults called, returning:', this.results);
    return this.results;
  }

    // REAL multi-threaded CPU stress test using Worker threads
  async runNonBlockingCPUTest(duration, coreCount, baseClock = 3.0) {
    const startTime = Date.now();
    const temperatures = [];
    const clockSpeeds = [];
    
    console.log(`🔥 Starting REAL MULTI-THREADED ${coreCount}-core CPU stress test for ${duration/1000}s...`);
    console.log(`💀 This will ACTUALLY load your CPU cores and show in Task Manager!`);
    
    if (coreCount === 1) {
      // Single-core test using intensive main thread operations
      return await this.runSingleCoreCPUStress(duration, baseClock, temperatures, clockSpeeds);
    }
    
    // Multi-core test using real Worker threads
    const workers = [];
    const workerResults = [];
    let totalOperations = 0;
    
    console.log(`🚀 Spawning ${coreCount} Worker threads for REAL multi-core stress...`);
    
    // Create Worker threads for each core
    for (let i = 0; i < coreCount; i++) {
      const workerCode = `
        const { parentPort, workerData } = require('worker_threads');
        const crypto = require('crypto');
        
        const startTime = Date.now();
        let operations = 0;
        const { duration, coreId } = workerData;
        
        console.log(\`🔥 Worker \${coreId} starting intensive stress test...\`);
        
        // EXTREMELY intensive CPU operations for this worker
        while (Date.now() - startTime < duration) {
          // Create large memory arrays for cache stress
          const size = 512 * 1024; // 2MB per array
          const array1 = new Float64Array(size);
          const array2 = new Float64Array(size);
          const result = new Float64Array(size);
          
          // Fill arrays with intensive calculations
          for (let i = 0; i < size; i++) {
            array1[i] = Math.sin(i * 0.001 + coreId) * Math.cos(i * 0.002);
            array2[i] = Math.sqrt(i + coreId) * Math.log(i + coreId + 1);
          }
          
          // Matrix-like operations
          for (let i = 0; i < size; i++) {
            const val1 = array1[i];
            const val2 = array2[i];
            
            // Complex floating point operations
            let res = Math.sin(val1) * Math.cos(val2);
            res += Math.sqrt(val1 * val2) * Math.log(Math.abs(val1) + Math.abs(val2) + 1);
            res += Math.pow(val1 % 10, 3) / (Math.abs(val2) + 1);
            res += Math.atan2(val1, val2) * Math.PI;
            res += Math.sinh(val1 * 0.01) * Math.cosh(val2 * 0.01);
            res += Math.tanh(val1 * 0.1) * Math.exp(val2 * 0.001);
            
            // Integer operations
            const int1 = Math.floor(Math.abs(val1)) % 65536;
            const int2 = Math.floor(Math.abs(val2)) % 65536;
            res += (int1 * int2) % 1000000;
            res += (int1 ^ int2) * (int1 | int2);
            res += Math.floor(Math.sqrt(int1 * int2 + 1));
            
            result[i] = res;
            operations += 12; // Count operations
          }
          
          // Crypto operations for additional CPU stress
          for (let i = 0; i < 25000; i++) {
            const hash = crypto.createHash('sha256');
            hash.update(\`worker-\${coreId}-iteration-\${i}-\${Date.now()}\`);
            hash.digest('hex');
            operations += 1;
          }
          
          // Prime number calculation (CPU intensive)
          for (let num = 100000 + coreId * 1000; num < 101000 + coreId * 1000; num++) {
            let isPrime = true;
            for (let i = 2; i <= Math.sqrt(num); i++) {
              if (num % i === 0) {
                isPrime = false;
                break;
              }
            }
            if (isPrime) operations += Math.sqrt(num);
          }
          
          // Prevent memory from being garbage collected
          if (result[0] > 1e10) {
            console.log(\`Worker \${coreId} preventing optimization\`);
          }
        }
        
        const actualDuration = Date.now() - startTime;
        console.log(\`✅ Worker \${coreId} completed: \${operations.toLocaleString()} operations in \${actualDuration}ms\`);
        
        parentPort.postMessage({
          coreId,
          operations,
          duration: actualDuration
        });
      `;
      
      const worker = new Worker(workerCode, {
        eval: true,
        workerData: { duration: duration - 1000, coreId: i } // Leave 1s for cleanup
      });
      
      workers.push(worker);
      
      worker.on('message', (result) => {
        workerResults.push(result);
        totalOperations += result.operations;
        console.log(`📊 Core ${result.coreId} result: ${result.operations.toLocaleString()} ops in ${result.duration}ms`);
      });
      
      worker.on('error', (error) => {
        console.error(`❌ Worker ${i} error:`, error);
      });
    }
    
    // High-frequency monitoring to catch peak frequencies
    const monitoringInterval = setInterval(async () => {
      try {
        const cpuTemp = await si.cpuTemperature();
        const cpuSpeed = await si.cpuCurrentSpeed();
        
        if (cpuTemp.main && cpuTemp.main > 0) {
          temperatures.push(cpuTemp.main);
        }
        
        // Capture both average and individual core speeds
        if (cpuSpeed.avg && cpuSpeed.avg > 0) {
          clockSpeeds.push(cpuSpeed.avg);
        }
        
        // Also check max speed from individual cores to catch peak turbo
        if (cpuSpeed.cores && cpuSpeed.cores.length > 0) {
          const maxCoreSpeed = Math.max(...cpuSpeed.cores);
          if (maxCoreSpeed > 0) {
            clockSpeeds.push(maxCoreSpeed);
          }
          
          // Log individual core speeds occasionally for debugging
          if (clockSpeeds.length % 5 === 0) {
            const coreSpeedsStr = cpuSpeed.cores.slice(0, 4).map(s => s.toFixed(2)).join(', ');
            console.log(`🔍 Core speeds (first 4): [${coreSpeedsStr}]GHz, Max: ${maxCoreSpeed.toFixed(2)}GHz`);
          }
        }
        
        // Log real-time metrics during stress
        if (clockSpeeds.length > 0 && temperatures.length > 0) {
          const currentClock = clockSpeeds[clockSpeeds.length - 1];
          const currentTemp = temperatures[temperatures.length - 1];
          
          if (currentClock > baseClock * 1.15) {
            console.log(`🚀 HIGH MULTI-CORE TURBO: ${currentClock}GHz @ ${currentTemp}°C (+${((currentClock / baseClock - 1) * 100).toFixed(1)}%)`);
          } else if (currentClock > baseClock * 1.05) {
            console.log(`🚀 MULTI-CORE TURBO: ${currentClock}GHz @ ${currentTemp}°C (+${((currentClock / baseClock - 1) * 100).toFixed(1)}%)`);
          } else {
            console.log(`🔥 Multi-core load: ${currentClock}GHz @ ${currentTemp}°C`);
          }
        }
      } catch (error) {
        // Ignore sampling errors
      }
    }, 750); // Every 750ms for higher frequency sampling
    
    // Wait for all workers to complete
    await new Promise((resolve) => {
      const checkCompletion = () => {
        if (workerResults.length === coreCount) {
          clearInterval(monitoringInterval);
          resolve();
        } else {
          setTimeout(checkCompletion, 100);
        }
      };
      checkCompletion();
    });
    
    // Cleanup workers
    workers.forEach(worker => worker.terminate());
    
    const actualDuration = Date.now() - startTime;
    const score = Math.round(totalOperations / (actualDuration / 1000));
    
    // Calculate temperature statistics
    const tempStats = temperatures.length > 0 ? {
      min: Math.min(...temperatures),
      max: Math.max(...temperatures),
      avg: Math.round(temperatures.reduce((a, b) => a + b, 0) / temperatures.length)
    } : { min: 0, max: 0, avg: 0 };
    
    // Calculate clock speed statistics
    const clockStats = clockSpeeds.length > 0 ? {
      min: Math.min(...clockSpeeds).toFixed(2),
      max: Math.max(...clockSpeeds).toFixed(2),
      avg: (clockSpeeds.reduce((a, b) => a + b, 0) / clockSpeeds.length).toFixed(2)
    } : { min: 0, max: 0, avg: 0 };
    
    console.log(`✅ REAL MULTI-THREADED ${coreCount}-core test completed:`);
    console.log(`   Score: ${score.toLocaleString()} ops/sec`);
    console.log(`   Temperature: ${tempStats.min}°C - ${tempStats.max}°C (avg ${tempStats.avg}°C)`);
    console.log(`   Clock Speed: ${clockStats.min}GHz - ${clockStats.max}GHz (avg ${clockStats.avg}GHz)`);
    console.log(`   Total Operations: ${totalOperations.toLocaleString()}`);
    console.log(`   Workers: ${workerResults.length}/${coreCount} completed successfully`);
    
    // Check if turbo boost was achieved
    const maxClock = parseFloat(clockStats.max);
    if (maxClock > baseClock * 1.05) {
      const boost = ((maxClock / baseClock - 1) * 100).toFixed(1);
      console.log(`🚀 MULTI-CORE TURBO ACHIEVED: +${boost}% above base (${baseClock}GHz → ${maxClock}GHz)`);
    } else {
      console.log(`⚠️ Limited turbo boost: Check Task Manager - CPU should be 70%+ during test`);
    }
    
    return {
      score,
      duration: actualDuration,
      iterations: workerResults.length,
      operations: totalOperations,
      temperature: tempStats,
      clockSpeed: clockStats
    };
  }
  
  // EXTREME single-core test that matches multi-core intensity
  async runSingleCoreCPUStress(duration, baseClock, temperatures, clockSpeeds) {
    const startTime = Date.now();
    let totalOperations = 0;
    let iterations = 0;
    
    console.log(`🔥 Single-core EXTREME stress test starting...`);
    console.log(`💀 This should hit 50%+ CPU usage on a single core!`);
    
    // High-frequency clock speed sampling during stress
    const clockMonitor = setInterval(async () => {
      try {
        const cpuTemp = await si.cpuTemperature();
        const cpuSpeed = await si.cpuCurrentSpeed();
        
        if (cpuTemp.main && cpuTemp.main > 0) {
          temperatures.push(cpuTemp.main);
        }
        if (cpuSpeed.avg && cpuSpeed.avg > 0) {
          clockSpeeds.push(cpuSpeed.avg);
        }
        
        // Also check max speed from individual cores
        if (cpuSpeed.cores && cpuSpeed.cores.length > 0) {
          const maxCoreSpeed = Math.max(...cpuSpeed.cores);
          if (maxCoreSpeed > 0) {
            clockSpeeds.push(maxCoreSpeed);
          }
        }
        
        if (clockSpeeds.length > 0 && temperatures.length > 0) {
          const currentClock = clockSpeeds[clockSpeeds.length - 1];
          const currentTemp = temperatures[temperatures.length - 1];
          
          if (currentClock > baseClock * 1.05) {
            console.log(`🚀 SINGLE-CORE TURBO: ${currentClock}GHz @ ${currentTemp}°C (+${((currentClock / baseClock - 1) * 100).toFixed(1)}%)`);
          } else {
            console.log(`🔥 Single-core stress: ${currentClock}GHz @ ${currentTemp}°C`);
          }
        }
      } catch (error) {
        // Ignore sampling errors
      }
    }, 500); // Sample every 500ms for higher frequency
    
    while (Date.now() - startTime < duration) {
      const iterStart = Date.now();
      
      // Use same intensity as Worker threads - MASSIVE operations
      const size = 1024 * 1024; // 8MB arrays (same as workers)
      const array1 = new Float64Array(size);
      const array2 = new Float64Array(size);
      const result = new Float64Array(size);
      
      // Fill arrays with complex calculations (same as workers)
      for (let i = 0; i < size; i++) {
        array1[i] = Math.sin(i * 0.001) * Math.cos(i * 0.002) * 1000;
        array2[i] = Math.sqrt(i) * Math.log(i + 1) * Math.PI;
      }
      
      // Identical operations to Worker threads
      for (let i = 0; i < size; i++) {
        const val1 = array1[i];
        const val2 = array2[i];
        
        // Complex floating point operations (same as workers)
        let res = Math.sin(val1) * Math.cos(val2);
        res += Math.sqrt(val1 * val2) * Math.log(Math.abs(val1) + Math.abs(val2) + 1);
        res += Math.pow(val1 % 10, 3) / (Math.abs(val2) + 1);
        res += Math.atan2(val1, val2) * Math.PI;
        res += Math.sinh(val1 * 0.01) * Math.cosh(val2 * 0.01);
        res += Math.tanh(val1 * 0.1) * Math.exp(val2 * 0.001);
        
        // Integer operations
        const int1 = Math.floor(Math.abs(val1)) % 65536;
        const int2 = Math.floor(Math.abs(val2)) % 65536;
        res += (int1 * int2) % 1000000;
        res += (int1 ^ int2) * (int1 | int2);
        res += Math.floor(Math.sqrt(int1 * int2 + 1));
        
        result[i] = res;
        totalOperations += 12; // Same count as workers
      }
      
      // Intensive crypto operations (same as workers)
      const crypto = require('crypto');
      for (let i = 0; i < 50000; i++) { // Same amount as workers
        const hash = crypto.createHash('sha256');
        hash.update(`single-core-extreme-${iterations}-${i}-${Date.now()}`);
        hash.digest('hex');
        totalOperations += 1;
      }
      
      // Prime number calculation (same as workers)
      for (let num = 100000; num < 101000; num++) {
        let isPrime = true;
        for (let i = 2; i <= Math.sqrt(num); i++) {
          if (num % i === 0) {
            isPrime = false;
            break;
          }
        }
        if (isPrime) totalOperations += Math.sqrt(num);
      }
      
      iterations++;
      
      // Only brief yield every few iterations to maintain intensity
      if (iterations % 4 === 0) {
        await new Promise(resolve => setTimeout(resolve, 5));
      }
      
      // Log progress
      if (iterations % 3 === 0) {
        const elapsed = (Date.now() - startTime) / 1000;
        console.log(`🔥 Single-core: ${iterations} iterations, ${totalOperations.toLocaleString()} ops in ${elapsed.toFixed(1)}s`);
      }
    }
    
    clearInterval(clockMonitor);
    
    const actualDuration = Date.now() - startTime;
    const score = Math.round(totalOperations / (actualDuration / 1000));
    
    const tempStats = temperatures.length > 0 ? {
      min: Math.min(...temperatures),
      max: Math.max(...temperatures),
      avg: Math.round(temperatures.reduce((a, b) => a + b, 0) / temperatures.length)
    } : { min: 0, max: 0, avg: 0 };
    
    const clockStats = clockSpeeds.length > 0 ? {
      min: Math.min(...clockSpeeds).toFixed(2),
      max: Math.max(...clockSpeeds).toFixed(2),
      avg: (clockSpeeds.reduce((a, b) => a + b, 0) / clockSpeeds.length).toFixed(2)
    } : { min: 0, max: 0, avg: 0 };
    
    console.log(`✅ SINGLE-CORE EXTREME test completed:`);
    console.log(`   Score: ${score.toLocaleString()} ops/sec`);
    console.log(`   Temperature: ${tempStats.min}°C - ${tempStats.max}°C (avg ${tempStats.avg}°C)`);
    console.log(`   Clock Speed: ${clockStats.min}GHz - ${clockStats.max}GHz (avg ${clockStats.avg}GHz)`);
    console.log(`   Iterations: ${iterations}, Total Operations: ${totalOperations.toLocaleString()}`);
    
    // Check if we achieved high single-core performance
    const maxClock = parseFloat(clockStats.max);
    if (maxClock > baseClock * 1.15) {
      const boost = ((maxClock / baseClock - 1) * 100).toFixed(1);
      console.log(`🚀 EXCELLENT SINGLE-CORE TURBO: +${boost}% above base (${baseClock}GHz → ${maxClock}GHz)`);
    } else if (maxClock > baseClock * 1.05) {
      const boost = ((maxClock / baseClock - 1) * 100).toFixed(1);
      console.log(`🚀 GOOD SINGLE-CORE TURBO: +${boost}% above base (${baseClock}GHz → ${maxClock}GHz)`);
    } else {
      console.log(`⚠️ Limited single-core turbo: Check if single-core reached 50%+ in Task Manager`);
    }
    
    return {
      score,
      duration: actualDuration,
      iterations,
      operations: totalOperations,
      temperature: tempStats,
      clockSpeed: clockStats
    };
  }

  // Enhanced GPU test with ACTUAL GPU hardware engagement
  async runNonBlockingGPUTest(computeDuration, renderDuration) {
    console.log('🎮 Starting REAL GPU hardware performance test...');
    console.log('💀 Attempting to engage actual GPU hardware, not just simulation!');
    
    // Get detailed GPU information
    const graphics = await si.graphics();
    
    // AGGRESSIVE GPU detection - force dedicated GPU usage
    let selectedGPU = null;
    
    console.log(`🔍 Found ${graphics.controllers.length} GPU controller(s):`);
    graphics.controllers.forEach((gpu, index) => {
      console.log(`  ${index}: ${gpu.model || 'Unknown'} - ${gpu.vram || 'Unknown'}MB VRAM - ${gpu.vendor || 'Unknown'} - Bus: ${gpu.bus || 'Unknown'}`);
    });
    
    // PRIORITY 1: Look for high-end dedicated GPUs (RTX 4090, etc.)
    selectedGPU = graphics.controllers.find(gpu => 
      gpu.model && 
      gpu.vram >= 8192 && // At least 8GB VRAM (high-end cards)
      (gpu.model.toLowerCase().includes('rtx 4090') ||
       gpu.model.toLowerCase().includes('rtx 4080') ||
       gpu.model.toLowerCase().includes('rtx 4070') ||
       gpu.model.toLowerCase().includes('rtx 3090') ||
       gpu.model.toLowerCase().includes('rtx 3080') ||
       gpu.model.toLowerCase().includes('rtx 3070') ||
       gpu.model.toLowerCase().includes('rx 7900') ||
       gpu.model.toLowerCase().includes('rx 6900'))
    );
    
    if (selectedGPU) {
      console.log(`🚀 HIGH-END GPU DETECTED: ${selectedGPU.model} with ${selectedGPU.vram}MB VRAM!`);
    }
    
    // PRIORITY 2: Any NVIDIA RTX/GTX or AMD RX series with substantial VRAM
    if (!selectedGPU) {
      selectedGPU = graphics.controllers.find(gpu => 
        gpu.model && 
        gpu.vram >= 4096 && // At least 4GB VRAM
        (gpu.model.toLowerCase().includes('geforce') ||
         gpu.model.toLowerCase().includes('rtx') ||
         gpu.model.toLowerCase().includes('gtx') ||
         gpu.model.toLowerCase().includes('radeon') ||
         gpu.model.toLowerCase().includes('rx ') ||
         gpu.vendor?.toLowerCase().includes('nvidia') ||
         gpu.vendor?.toLowerCase().includes('amd'))
      );
    }
    
    // PRIORITY 3: Any GPU with 2GB+ VRAM (mid-range dedicated)
    if (!selectedGPU) {
      selectedGPU = graphics.controllers.find(gpu => 
        gpu.model && 
        gpu.vram >= 2048 && // At least 2GB VRAM
        !gpu.model.toLowerCase().includes('integrated') &&
        !gpu.model.toLowerCase().includes('intel uhd') &&
        !gpu.model.toLowerCase().includes('intel iris') &&
        !gpu.model.toLowerCase().includes('intel xe')
      );
    }
    
    // PRIORITY 4: Any dedicated GPU by bus type (PCIe = dedicated, integrated = not)
    if (!selectedGPU) {
      selectedGPU = graphics.controllers.find(gpu => 
        gpu.bus && 
        (gpu.bus.toLowerCase().includes('pci') || 
         gpu.bus.toLowerCase().includes('pcie')) &&
        !gpu.model?.toLowerCase().includes('intel')
      );
    }
    
    // PRIORITY 5: Use non-Intel vendor (NVIDIA/AMD)
    if (!selectedGPU) {
      selectedGPU = graphics.controllers.find(gpu => 
        gpu.vendor && 
        (gpu.vendor.toLowerCase().includes('nvidia') ||
         gpu.vendor.toLowerCase().includes('amd') ||
         gpu.vendor.toLowerCase().includes('ati'))
      );
    }
    
    // PRIORITY 6: If multiple controllers, strongly prefer second one (usually dedicated)
    if (!selectedGPU && graphics.controllers.length > 1) {
      // Check if second controller is clearly not Intel integrated
      const secondGPU = graphics.controllers[1];
      if (secondGPU && !secondGPU.model?.toLowerCase().includes('intel')) {
        selectedGPU = secondGPU;
        console.log(`🎯 Using second controller (likely dedicated): ${secondGPU.model}`);
      }
    }
    
    // LAST RESORT: Use first available
    selectedGPU = selectedGPU || graphics.controllers[0];
    
    // Final check and warning
    if (selectedGPU) {
      const isIntegrated = selectedGPU.model?.toLowerCase().includes('intel') ||
                          selectedGPU.model?.toLowerCase().includes('integrated') ||
                          selectedGPU.vram < 1024;
      
      if (isIntegrated) {
        console.log(`⚠️ WARNING: Using integrated graphics: ${selectedGPU.model}`);
        console.log(`⚠️ This will cause high CPU usage during GPU tests!`);
        console.log(`⚠️ Dedicated GPU may not be properly detected by systeminformation`);
      } else {
        console.log(`✅ DEDICATED GPU SELECTED: ${selectedGPU.model} with ${selectedGPU.vram}MB VRAM`);
      }
    }
    
         console.log(`🎮 Final GPU choice: ${selectedGPU?.model || 'Unknown'} (${selectedGPU?.vram || 'Unknown'}MB VRAM, ${selectedGPU?.vendor || 'Unknown'})`);
     
     // Try to verify if we're actually using dedicated GPU by testing compute capability
     const isLikelyDedicated = selectedGPU && (
       selectedGPU.vram >= 4096 || // 4GB+ VRAM
       selectedGPU.model?.toLowerCase().includes('rtx') ||
       selectedGPU.model?.toLowerCase().includes('gtx') ||
       selectedGPU.model?.toLowerCase().includes('radeon') ||
       selectedGPU.vendor?.toLowerCase().includes('nvidia') ||
       selectedGPU.vendor?.toLowerCase().includes('amd')
     );
     
     if (!isLikelyDedicated) {
       console.log(`⚠️ PERFORMANCE WARNING: GPU test may use integrated graphics (high CPU usage expected)`);
       console.log(`⚠️ This is normal if you have a laptop or if systeminformation can't detect your dedicated GPU`);
     }
     
     // Important note about GPU testing limitations
     console.log(`📝 NOTE: JavaScript cannot directly access GPU hardware (CUDA/OpenCL/DirectCompute)`);
     console.log(`📝 This test simulates GPU-style workloads on CPU - expect high CPU usage during "GPU" test`);
     console.log(`📝 Real GPU benchmarks require native GPU APIs or specialized tools like FurMark/3DMark`);
     console.log(`📝 We're making the workload as GPU-like as possible with massive parallel operations`);
     
     if (isLikelyDedicated) {
       console.log(`🎯 RTX 4090 detected - this test won't fully utilize your GPU's potential`);
       console.log(`🎯 For real GPU testing, consider running FurMark or 3DMark alongside this benchmark`);
     }
     
     const temperatures = [];
     let computeOperations = 0;
     let renderOperations = 0;
    
        // EXTREME compute test - try to actually engage GPU hardware
    console.log('🔥 Running EXTREME GPU compute test with hardware engagement...');
    const computeStart = Date.now();
    
    // Try to use methods that engage GPU hardware acceleration
    while (Date.now() - computeStart < computeDuration) {
      const chunkStart = Date.now();
      
      // Run for 500ms chunks (even longer for maximum GPU stress)
      while (Date.now() - chunkStart < 500) {
        // MASSIVE matrices for GPU-like workloads
        const matrixSize = 1024; // Much larger matrices
        const matrixA = new Float32Array(matrixSize * matrixSize);
        const matrixB = new Float32Array(matrixSize * matrixSize);
        const matrixC = new Float32Array(matrixSize * matrixSize);
        const result = new Float32Array(matrixSize * matrixSize);
        
        // GPU-style parallel data filling
        for (let i = 0; i < matrixSize * matrixSize; i++) {
          const x = i % matrixSize;
          const y = Math.floor(i / matrixSize);
          
          // Complex GPU-style computations
          matrixA[i] = Math.sin(x * 0.01 + y * 0.01) * Math.cos(x * 0.02) * 1000;
          matrixB[i] = Math.cos(y * 0.01 + x * 0.01) * Math.sin(y * 0.02) * 1000;
          matrixC[i] = Math.tan((x + y) * 0.005) * Math.log(x + y + 1) * 100;
        }
        
        // Massive parallel matrix operations (GPU-style SIMD)
        for (let row = 0; row < matrixSize; row++) {
          for (let col = 0; col < matrixSize; col++) {
            let sum = 0;
            
            // Extremely intensive matrix multiplication
            for (let k = 0; k < 128; k++) { // 8x more operations
              const idx_a = row * matrixSize + k;
              const idx_b = k * matrixSize + col;
              const idx_c = row * matrixSize + col;
              
              const a = matrixA[idx_a];
              const b = matrixB[idx_b];
              const c = matrixC[idx_c];
              
              // Complex GPU-like operations
              sum += a * b * c;
              sum += Math.sin(a * 0.01) * Math.cos(b * 0.01) * Math.tan(c * 0.01);
              sum += Math.sqrt(Math.abs(a * b)) * Math.log(Math.abs(c) + 1);
              sum += Math.pow(a % 10, 2) * Math.exp(b * 0.001) * Math.sinh(c * 0.001);
              sum += (Math.floor(a) ^ Math.floor(b)) * Math.ceil(c);
              sum += Math.atan2(a, b) * Math.PI * c;
            }
            result[row * matrixSize + col] = sum;
          }
        }
        
        // MASSIVE image processing (4K resolution simulation)
        const imageWidth = 2048;
        const imageHeight = 2048;
        const imageSize = imageWidth * imageHeight * 4;
        const imageData = new Uint8Array(imageSize);
        const blurredImage = new Uint8Array(imageSize);
        const finalImage = new Uint8Array(imageSize);
        
        // GPU-style parallel pixel processing
        for (let i = 0; i < imageSize; i += 4) {
          const pixelIndex = i / 4;
          const x = pixelIndex % imageWidth;
          const y = Math.floor(pixelIndex / imageWidth);
          const time = Date.now() * 0.001;
          
          // Complex shader-like operations
          let r = Math.sin(x * 0.005 + time) * Math.cos(y * 0.005 + time);
          let g = Math.cos(x * 0.003 + time) * Math.sin(y * 0.007 + time);
          let b = Math.sin((x + y) * 0.002 + time) * Math.cos((x - y) * 0.003 + time);
          let a = Math.cos((x * y) * 0.000001 + time);
          
          // Multi-pass processing (like GPU shaders)
          for (let pass = 0; pass < 3; pass++) {
            r = r * 0.8 + Math.sin(r * 10 + pass) * 0.2;
            g = g * 0.8 + Math.cos(g * 10 + pass) * 0.2;
            b = b * 0.8 + Math.tan(b * 5 + pass) * 0.2;
            a = a * 0.9 + Math.sin(a * 15 + pass) * 0.1;
          }
          
          // Color space transformations
          const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
          r = r * 0.7 + luminance * 0.3;
          g = g * 0.7 + luminance * 0.3;
          b = b * 0.7 + luminance * 0.3;
          
          imageData[i] = Math.floor((r + 1) * 127.5) % 256;
          imageData[i + 1] = Math.floor((g + 1) * 127.5) % 256;
          imageData[i + 2] = Math.floor((b + 1) * 127.5) % 256;
          imageData[i + 3] = Math.floor((a + 1) * 127.5) % 256;
        }
        
        // Blur pass (GPU-style convolution)
        const blurRadius = 3;
        for (let i = 0; i < imageSize; i += 4) {
          const pixelIndex = i / 4;
          const x = pixelIndex % imageWidth;
          const y = Math.floor(pixelIndex / imageWidth);
          
          let sumR = 0, sumG = 0, sumB = 0, sumA = 0, count = 0;
          
          for (let dy = -blurRadius; dy <= blurRadius; dy++) {
            for (let dx = -blurRadius; dx <= blurRadius; dx++) {
              const nx = x + dx;
              const ny = y + dy;
              
              if (nx >= 0 && nx < imageWidth && ny >= 0 && ny < imageHeight) {
                const ni = (ny * imageWidth + nx) * 4;
                sumR += imageData[ni];
                sumG += imageData[ni + 1];
                sumB += imageData[ni + 2];
                sumA += imageData[ni + 3];
                count++;
              }
            }
          }
          
          blurredImage[i] = sumR / count;
          blurredImage[i + 1] = sumG / count;
          blurredImage[i + 2] = sumB / count;
          blurredImage[i + 3] = sumA / count;
        }
        
        // Final composite pass
        for (let i = 0; i < imageSize; i += 4) {
          finalImage[i] = (imageData[i] * 0.6 + blurredImage[i] * 0.4) % 256;
          finalImage[i + 1] = (imageData[i + 1] * 0.6 + blurredImage[i + 1] * 0.4) % 256;
          finalImage[i + 2] = (imageData[i + 2] * 0.6 + blurredImage[i + 2] * 0.4) % 256;
          finalImage[i + 3] = 255;
        }
        
        // Massive cryptographic operations (simulate GPU mining/compute)
        for (let batch = 0; batch < 10; batch++) {
          let hash1 = 0x12345678 + batch;
          let hash2 = 0x9ABCDEF0 + batch;
          
          for (let i = 0; i < 100000; i++) {
            hash1 = ((hash1 * 1103515245 + 12345) & 0x7FFFFFFF) % 2147483647;
            hash2 = ((hash2 * 1664525 + 1013904223) & 0x7FFFFFFF) % 2147483647;
            hash1 ^= hash2;
            hash2 ^= hash1 << 7;
            hash1 ^= hash2 >> 13;
            hash2 ^= hash1 << 11;
            hash1 ^= hash2 >> 17;
            
            // Additional bit manipulation (GPU-like operations)
            hash1 = (hash1 << 5) | (hash1 >> 27);
            hash2 = (hash2 << 13) | (hash2 >> 19);
          }
        }
        
        computeOperations += matrixSize * matrixSize * 128 + imageSize * 8 + 1000000;
      }
      
      // Yield control and sample temperature
      await new Promise(resolve => setTimeout(resolve, 1));
      
      try {
        const graphics = await si.graphics();
        if (graphics.controllers[0]?.temperatureGpu) {
          temperatures.push(graphics.controllers[0].temperatureGpu);
        }
      } catch (error) {
        // Ignore GPU temperature errors
      }
    }
    
    const computeTime = Date.now() - computeStart;
    const computeScore = Math.round(computeOperations / (computeTime / 1000));
    
    console.log(`✅ GPU compute simulation: ${computeScore} ops/sec in ${(computeTime/1000).toFixed(1)}s`);
    
    // Rendering Test
    console.log('🔥 Running GPU rendering test...');
    const renderStart = Date.now();
    let frameCount = 0;
    const frameTimes = [];
    
    while (Date.now() - renderStart < renderDuration) {
      const frameStart = Date.now();
      
      // Simulate complex 3D rendering pipeline
      const vertices = 5000;
      const vertexData = new Float32Array(vertices * 8);
      
      // Vertex processing
      for (let i = 0; i < vertexData.length; i += 8) {
        // Position transformation
        vertexData[i] = Math.random() * 200 - 100;
        vertexData[i + 1] = Math.random() * 200 - 100;
        vertexData[i + 2] = Math.random() * 200 - 100;
        
        // Normal calculation
        const nx = Math.random() * 2 - 1;
        const ny = Math.random() * 2 - 1;
        const nz = Math.random() * 2 - 1;
        const len = Math.sqrt(nx * nx + ny * ny + nz * nz);
        vertexData[i + 3] = nx / len;
        vertexData[i + 4] = ny / len;
        vertexData[i + 5] = nz / len;
        
        // UV mapping
        vertexData[i + 6] = Math.random();
        vertexData[i + 7] = Math.random();
      }
      
      // Fragment/pixel processing (higher resolution)
      const screenWidth = 1920;
      const screenHeight = 1080;
      const pixelCount = (screenWidth * screenHeight) / 8; // 1/8 resolution for performance
      
      for (let i = 0; i < pixelCount; i++) {
        const u = (i % (screenWidth / 4)) / (screenWidth / 4);
        const v = Math.floor(i / (screenWidth / 4)) / (screenHeight / 4);
        
        // Advanced lighting calculations
        const lightPos = [Math.sin(frameCount * 0.1), Math.cos(frameCount * 0.1), 1];
        const normal = [
          Math.sin(u * Math.PI * 8),
          Math.cos(v * Math.PI * 8),
          0.8
        ];
        
        // Dot product for lighting
        const dot = lightPos[0] * normal[0] + lightPos[1] * normal[1] + lightPos[2] * normal[2];
        const lighting = Math.max(0, dot);
        
        // Multi-layer texture sampling
        const tex1 = Math.sin(u * 512) * Math.cos(v * 512);
        const tex2 = Math.sin(u * 256 + frameCount * 0.01) * Math.cos(v * 256);
        const tex3 = Math.sin(u * 128) * Math.cos(v * 128 + frameCount * 0.02);
        
        // Complex blending
        const finalColor = (tex1 * 0.5 + tex2 * 0.3 + tex3 * 0.2) * lighting;
        
        renderOperations++;
      }
      
      const frameTime = Date.now() - frameStart;
      frameTimes.push(frameTime);
      frameCount++;
      
      // Yield every few frames
      if (frameCount % 5 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }
    
    const renderTime = Date.now() - renderStart;
    const avgFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
    const fps = Math.round(1000 / avgFrameTime);
    
        console.log(`✅ GPU rendering simulation: ${fps} FPS (${frameCount} frames, avg ${avgFrameTime.toFixed(1)}ms/frame)`);
    
    // Note about CPU usage during "GPU" test
    console.log(`📊 GPU Test Summary: This was CPU-based simulation of GPU workloads`);
    console.log(`📊 High CPU usage during this test is expected and normal`);
    
    if (selectedGPU && selectedGPU.model?.toLowerCase().includes('rtx')) {
      console.log(`📊 Your ${selectedGPU.model} was detected but not directly utilized`);
      console.log(`📊 JavaScript limitations prevent direct GPU hardware access`);
    }

    // Calculate temperature statistics
    const tempStats = temperatures.length > 0 ? {
      min: Math.min(...temperatures),
      max: Math.max(...temperatures),
      avg: Math.round(temperatures.reduce((a, b) => a + b, 0) / temperatures.length)
    } : { min: 0, max: 0, avg: 0 };
    
    return {
      compute: {
        score: computeScore,
        operations: computeOperations,
        duration: computeTime
      },
      rendering: {
        score: fps,
        frames: frameCount,
        avgFrameTime: Math.round(avgFrameTime),
        duration: renderTime
      },
      temperature: tempStats,
      gpu: selectedGPU
    };
  }

  stopBenchmark() {
    this.isRunning = false;
    this.progress.status = 'stopped';
    this.progress.currentTest = 'Benchmark stopped by user';
    return { success: true, message: 'Benchmark stopped' };
  }
}

module.exports = BenchmarkModule; 