/* Account Page Styles */

/* Account Header */
.account-header {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    padding: 8rem 0 4rem;
    text-align: center;
    position: relative;
}

.account-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.account-header-content {
    position: relative;
    z-index: 1;
}

.account-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.account-title i {
    color: var(--primary-color);
    font-size: 2.5rem;
}

.account-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* <PERSON>gin Required Section */
.login-required {
    padding: 4rem 0;
    background: var(--bg-primary);
}

.login-message {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.login-message i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.login-message h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.login-message p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.auth-forms {
    max-width: 500px;
    margin: 0 auto 2rem;
}

.login-buttons {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

/* Account Content */
.account-content {
    padding: 4rem 0;
    background: var(--bg-primary);
}

/* Account Tabs */
.account-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.account-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.account-tab:hover {
    color: var(--text-primary);
}

.account-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.account-tab i {
    font-size: 1.1rem;
}

/* Tab Content */
.account-tab-content {
    display: none;
}

.account-tab-content.active {
    display: block;
}

/* Profile Section */
.profile-section {
    max-width: 800px;
    margin: 0 auto;
}

.profile-section h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

.profile-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: start;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    background: var(--bg-primary);
    border: 3px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.profile-avatar i {
    font-size: 3rem;
    color: var(--primary-color);
}

.profile-info {
    flex: 1;
}

.form-hint {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Email Verification Status */
.email-verification-status {
    margin-top: 0.75rem;
    padding: 1rem;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
}

.verification-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    color: #f59e0b;
    font-weight: 500;
}

.verification-badge.verified {
    color: #10b981;
}

.verification-badge.verified i:before {
    content: "\f058"; /* fa-check-circle */
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.email-verification-status.verified {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.forgot-password {
    display: block;
    text-align: center;
    margin-top: 1rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Settings Section */
.settings-section {
    max-width: 800px;
    margin: 0 auto;
}

.settings-section h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

.settings-grid {
    display: grid;
    gap: 1.5rem;
}

.setting-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setting-item h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-item h4 i {
    color: var(--primary-color);
}

.setting-item p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    transition: 0.4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 3px;
    background-color: var(--text-secondary);
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
    background-color: white;
}

/* Premium Section */
.premium-section {
    max-width: 1000px;
    margin: 0 auto;
}

.premium-section h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

.premium-status {
    margin-bottom: 3rem;
}

.status-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
}

.status-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.status-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.status-card p {
    color: var(--text-secondary);
}

.license-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
}

.license-section h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.license-section h3 i {
    color: var(--primary-color);
}

.license-input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.license-input-group input {
    flex: 1;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
}

.license-input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.license-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    display: none;
}

.license-status.success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.license-status.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.premium-features h3 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.feature-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.feature-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* License Key Management Dashboard */
.key-management-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    border-bottom: 2px solid #4a5568;
    padding-bottom: 10px;
}

.key-tab {
    background: #4a5568;
    color: #e2e8f0;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.key-tab:hover {
    background: #2d3748;
    transform: translateY(-1px);
}

.key-tab.active {
    background: #3182ce;
    color: white;
}

.key-management-controls {
    margin-bottom: 20px;
}

.search-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-bar input {
    flex: 1;
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #e2e8f0;
    padding: 10px;
    border-radius: 6px;
    font-size: 14px;
}

.search-bar input:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.key-management-list {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #4a5568;
    border-radius: 8px;
    background: #2d3748;
}

.key-item {
    padding: 15px;
    border-bottom: 1px solid #4a5568;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.2s ease;
}

.key-item:hover {
    background: #4a5568;
}

.key-item:last-child {
    border-bottom: none;
}

.key-info {
    flex: 1;
}

.key-code {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #3182ce;
    margin-bottom: 5px;
}

.key-details {
    color: #a0aec0;
    font-size: 13px;
    line-height: 1.4;
}

.key-user {
    color: #e2e8f0;
    font-weight: 500;
}

.key-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.key-status.unused {
    background: #065f46;
    color: #d1fae5;
}

.key-status.active {
    background: #1e40af;
    color: #dbeafe;
}

.key-status.revoked {
    background: #7f1d1d;
    color: #fecaca;
}

.key-actions {
    display: flex;
    gap: 8px;
    margin-left: 15px;
}

.key-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.no-keys-message {
    text-align: center;
    padding: 40px;
    color: #a0aec0;
}

    .no-keys-message i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    /* Broken/Corrupted Key Styling */
    .key-item[data-broken="true"] {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .key-code.broken-key {
        color: #f56565;
        font-style: italic;
    }

    .broken-key-warning {
        background: rgba(245, 101, 101, 0.2);
        color: #f56565;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        margin: 5px 0;
        font-weight: 500;
    }

/* Security Section */
.security-section {
    max-width: 600px;
    margin: 0 auto;
}

.security-section h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
}

.security-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.security-card h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.security-card h3 i {
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .account-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .account-tabs {
        justify-content: flex-start;
        padding-bottom: 0;
    }

    .profile-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .license-input-group {
        flex-direction: column;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
}

@media (max-width: 480px) {
    .account-header {
        padding: 6rem 0 3rem;
    }

    .account-title {
        font-size: 1.5rem;
    }

    .account-description {
        font-size: 1rem;
    }

    .account-tabs {
        gap: 0;
    }

    .account-tab {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Moderation Panel Styles */
.moderation-section {
    max-width: 1200px;
    margin: 0 auto;
}

.moderation-section h2 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.moderation-role-badge {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
}

.moderation-role-badge.admin {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.moderation-role-badge.moderator {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.moderation-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.moderation-card h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
}

.moderation-card h3 i {
    color: var(--primary-color);
}

/* User Search */
.user-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.user-search input {
    flex: 1;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
}

.user-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-search-results {
    max-height: 400px;
    overflow-y: auto;
}

.user-result-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.user-result-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.user-details h4 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
}

.user-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.85rem;
}

.user-role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.user-role-badge.admin {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.user-role-badge.moderator {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.user-role-badge.user {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-ban {
    background: #ef4444;
    color: white;
    border: 1px solid #ef4444;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-ban:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.btn-unban {
    background: #10b981;
    color: white;
    border: 1px solid #10b981;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-unban:hover {
    background: #059669;
    border-color: #059669;
}

/* Role Management */
.admin-note {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: var(--primary-color);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.role-actions {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
}

.role-actions select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
}

.role-actions select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.status-message {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    display: none;
}

.status-message.success {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-message.error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Banned Users List */
.banned-users-list {
    max-height: 400px;
    overflow-y: auto;
}

.banned-user-item {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ban-info h4 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.ban-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.85rem;
}

.ban-reason {
    background: rgba(239, 68, 68, 0.1);
    padding: 0.5rem;
    border-radius: 6px;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #ef4444;
}

/* Reported Content */
.reported-content-list {
    max-height: 500px;
    overflow-y: auto;
}

.report-item {
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.report-type {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.report-date {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.report-content {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 6px;
    font-size: 0.9rem;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.btn-resolve {
    background: #10b981;
    color: white;
    padding: 0.5rem 1rem;
    border: 1px solid #10b981;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-resolve:hover {
    background: #059669;
}

.btn-dismiss {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-dismiss:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-item i {
    font-size: 2rem;
    color: var(--primary-color);
    opacity: 0.8;
}

.stat-info {
    flex: 1;
    text-align: left;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.loading-message {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
    font-style: italic;
}

.loading-message i {
    margin-right: 0.5rem;
}

/* Responsive Design for Moderation */
@media (max-width: 768px) {
    .moderation-section {
        padding: 0 1rem;
    }

    .user-search {
        flex-direction: column;
    }

    .role-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .user-result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .user-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
    }

    .stat-info {
        text-align: center;
    }

    .quick-actions {
        flex-direction: column;
    }

    .quick-actions .btn {
        width: 100%;
    }

    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .report-actions {
        flex-direction: column;
        width: 100%;
    }

    .report-actions .btn {
        width: 100%;
    }
}

/* Inbox Styles */
.inbox-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.inbox-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.conversations-list {
    background: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.conversations-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversations-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.conversations-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.conversation-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
}

.conversation-item:hover {
    background: var(--bg-secondary);
}

.conversation-item.active {
    background: var(--primary-color);
    color: white;
}

.conversation-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.conversation-preview {
    color: var(--text-secondary);
    font-size: 0.8rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-time {
    color: var(--text-muted);
    font-size: 0.7rem;
    margin-top: 0.25rem;
}

.conversation-item.active .conversation-name,
.conversation-item.active .conversation-preview {
    color: white;
}

.conversation-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
    padding-left: 0.5rem;
}

.conversation-item:hover .conversation-actions {
    opacity: 1;
}

.conversation-delete-btn {
    background: rgba(239, 68, 68, 0.1) !important;
    border: 1px solid rgba(239, 68, 68, 0.3) !important;
    color: #ef4444 !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.8rem !important;
    border-radius: 4px !important;
}

.conversation-delete-btn:hover {
    background: #ef4444 !important;
    color: white !important;
    transform: scale(1.05);
}

.conversation-unread {
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.message-view {
    display: flex;
    flex-direction: column;
}

.message-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.recipient-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.recipient-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.recipient-details h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-primary);
}

.recipient-details p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.message-content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: var(--bg-primary);
}

.message-bubble {
    max-width: 70%;
    margin-bottom: 1rem;
    word-wrap: break-word;
}

.message-bubble.sent {
    margin-left: auto;
}

.message-bubble.sent .message-text {
    background: var(--primary-color);
    color: white;
    border-radius: 18px 18px 4px 18px;
}

.message-bubble.received .message-text {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 18px 18px 18px 4px;
}

.message-text {
    padding: 0.75rem 1rem;
    display: inline-block;
    max-width: 100%;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
    text-align: right;
}

.message-bubble.received .message-time {
    text-align: left;
}

.message-input {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.input-group input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.input-group button {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-conversation-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-secondary);
}

.no-conversation-selected i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.no-conversations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-secondary);
}

.no-conversations i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

/* Friends Styles */
.friends-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.friends-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.friends-search {
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.search-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-group input {
    width: 100%;
    padding: 0.75rem;
    padding-right: 2.5rem; /* Make room for clear button */
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.search-clear-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: all 0.2s ease;
}

.search-clear-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.search-clear-btn i {
    font-size: 0.8rem;
}

.search-status {
    padding: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-status i {
    color: var(--primary-color);
}

.friend-requests-section,
.friends-list-section,
.search-results-section {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.friend-requests-section h3,
.friends-list-section h3,
.search-results-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.friend-item,
.user-search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.friend-item:hover,
.user-search-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.friend-avatar,
.user-avatar {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

.friend-info,
.user-info {
    flex: 1;
    min-width: 0;
}

.friend-name,
.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.friend-status,
.user-email {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.friend-actions,
.user-actions {
    display: flex;
    gap: 0.5rem;
}

.online-indicator {
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
    border: 2px solid var(--bg-secondary);
    position: absolute;
    bottom: 2px;
    right: 2px;
}

.friend-avatar,
.user-avatar {
    position: relative;
}

.no-friends,
.no-requests {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.no-friends i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.text-secondary {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .inbox-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .conversations-list {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .friends-search {
        padding: 1rem;
    }
    
    .search-group input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .friend-item,
    .user-search-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem 1rem;
    }
    
    .friend-actions,
    .user-actions {
        justify-content: center;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    
    .friend-avatar,
    .user-avatar {
        width: 56px;
        height: 56px;
        font-size: 1.4rem;
        margin-bottom: 0.5rem;
    }
    
    .friends-section h2 {
        font-size: 1.5rem;
    }
    
    .search-results-section h3,
    .friend-requests-section h3,
    .friends-list-section h3 {
        font-size: 1.1rem;
    }
}

/* Start Conversation Section - Always Visible */
.start-conversation-section {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.start-conversation-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.start-conversation-btn:hover {
    background: transparent;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* New Conversation Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header i {
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    justify-content: flex-end;
}

/* Search Results in Modal */
.search-results-modal {
    margin-top: 1rem;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
}

.user-search-result {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-search-result:hover {
    background: var(--bg-secondary);
}

.user-search-result:last-child {
    border-bottom: none;
}

.user-search-result.selected {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

.user-search-result .user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    flex-shrink: 0;
}

.user-search-result .user-info {
    flex: 1;
}

.user-search-result .user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.user-search-result .user-email {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.user-search-result .user-status {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.user-search-result .user-action {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* Friends Search Results - Updated to match modal */
.user-search-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    transition: all 0.3s ease;
}

.user-search-item:hover {
    background: var(--bg-primary);
    transform: translateY(-1px);
}

.user-search-item:last-child {
    border-bottom: none;
}

.user-search-item .user-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    position: relative;
    flex-shrink: 0;
}

.user-search-item .user-info {
    flex: 1;
}

.user-search-item .user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.user-search-item .user-email {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-search-item .user-status {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.user-search-item .user-actions {
    display: flex;
    gap: 0.5rem;
}

/* Online Indicator */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border: 2px solid var(--bg-secondary);
    border-radius: 50%;
}

/* Confirmation Modal Specific Styles */
.confirmation-modal {
    max-width: 450px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.confirmation-modal .modal-header h3 {
    color: #f59e0b;
}

.confirmation-modal .modal-header i {
    color: #f59e0b;
}

.confirmation-modal .modal-body {
    text-align: center;
    padding: 2rem 1.5rem;
}

.confirmation-modal .modal-body p {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.5;
}

.confirmation-modal .modal-footer {
    justify-content: center;
    gap: 1.5rem;
}

.confirmation-modal .btn-danger {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transition: all 0.3s ease;
}

.confirmation-modal .btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Mobile Responsiveness for Modal */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 1rem;
    }
    
    .modal-content {
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
    
    .user-search-result,
    .user-search-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .user-search-result .user-avatar,
    .user-search-item .user-avatar {
        width: 40px;
        height: 40px;
    }
    
    .confirmation-modal .modal-body {
        padding: 1.5rem 1rem;
    }
    
    .confirmation-modal .modal-footer {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Loading and Error States for Messages */
.loading-messages,
.messages-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.loading-messages i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.messages-error i {
    font-size: 2rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.messages-error h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.messages-error p {
    margin-bottom: 1.5rem;
    max-width: 300px;
}

.messages-error .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
} 