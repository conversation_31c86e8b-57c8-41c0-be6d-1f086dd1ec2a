<!DOCTYPE html>
<html>
<head>
    <title>Armory X - Update Example</title>
    <style>
        /* Update notification banner */
        .update-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 15px;
            display: none;
            align-items: center;
            justify-content: space-between;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 9999;
        }

        .update-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .update-icon {
            font-size: 24px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .update-progress {
            background: rgba(255,255,255,0.2);
            height: 4px;
            border-radius: 2px;
            overflow: hidden;
            width: 200px;
            margin: 0 20px;
        }

        .update-progress-bar {
            background: white;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .update-buttons {
            display: flex;
            gap: 10px;
        }

        .update-button {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .update-button.primary {
            background: white;
            color: #1e40af;
        }

        .update-button.secondary {
            background: transparent;
            color: white;
            border: 1px solid white;
        }

        .update-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Update notification banner -->
    <div class="update-banner" id="updateBanner">
        <div class="update-content">
            <span class="update-icon">🚀</span>
            <span id="updateMessage">A new version of Armory X is available!</span>
            <div class="update-progress" id="updateProgress" style="display: none;">
                <div class="update-progress-bar" id="updateProgressBar"></div>
            </div>
            <span id="updateStatus"></span>
        </div>
        <div class="update-buttons">
            <button class="update-button primary" id="updateNowBtn" onclick="installUpdate()">
                Update Now
            </button>
            <button class="update-button secondary" id="updateLaterBtn" onclick="dismissUpdate()">
                Later
            </button>
        </div>
    </div>

    <!-- Your app content here -->
    <h1>Armory X</h1>
    <p>Your application content...</p>

    <script>
        // Listen for update events from main process
        const { ipcRenderer } = require('electron');

        // Update available
        ipcRenderer.on('update-available', (event, info) => {
            const banner = document.getElementById('updateBanner');
            const message = document.getElementById('updateMessage');
            
            banner.style.display = 'flex';
            message.textContent = `Version ${info.version} is available (current: ${info.currentVersion})`;
        });

        // Download progress
        ipcRenderer.on('download-progress', (event, progressObj) => {
            const progress = document.getElementById('updateProgress');
            const progressBar = document.getElementById('updateProgressBar');
            const status = document.getElementById('updateStatus');
            const updateBtn = document.getElementById('updateNowBtn');
            
            progress.style.display = 'block';
            progressBar.style.width = progressObj.percent + '%';
            status.textContent = `${Math.round(progressObj.percent)}%`;
            updateBtn.textContent = 'Downloading...';
            updateBtn.disabled = true;
        });

        // Update downloaded
        ipcRenderer.on('update-downloaded', () => {
            const message = document.getElementById('updateMessage');
            const updateBtn = document.getElementById('updateNowBtn');
            const progress = document.getElementById('updateProgress');
            
            progress.style.display = 'none';
            message.textContent = 'Update downloaded! Restart to apply.';
            updateBtn.textContent = 'Restart Now';
            updateBtn.disabled = false;
            updateBtn.onclick = () => {
                ipcRenderer.send('restart-app');
            };
        });

        function installUpdate() {
            ipcRenderer.send('install-update');
        }

        function dismissUpdate() {
            document.getElementById('updateBanner').style.display = 'none';
        }

        // Check for updates on startup (after 5 seconds)
        setTimeout(() => {
            ipcRenderer.send('check-for-updates');
        }, 5000);
    </script>
</body>
</html> 