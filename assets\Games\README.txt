Game Icons Folder

This folder contains icons for games that will be displayed in the Recently Launched and Quick Launch sections.

Required files:
- xbox_game.png: This is used as the default icon for all Xbox games
- steam_game.png: This is used as the default icon for all Steam games

The icons should be:
1. Sized at 64x64 pixels
2. In PNG format with transparency
3. Clearly recognizable as representing their respective gaming platforms

To use, simply place the appropriate icon files in this directory.

Note: Users can also set custom icons for any shortcut using the "Change Icon" button in the Quick Launch page.

To add a new game icon:
1. Save the icon as a .png file in this folder
2. Naming convention: use the game's name in lowercase with no spaces (e.g., minecraft.png, forza.png)
3. Recommended size: 64x64 pixels

Then update the TrySetGameIcon method in DashboardPage.xaml.cs to include the new game. 