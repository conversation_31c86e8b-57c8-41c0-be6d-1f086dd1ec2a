/* ===================================
   Badges and Premium Features
   =================================== */

/* License Badge */
.license-badge {
    margin-left: auto;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.license-badge.active {
    background: var(--primary-color);
    color: #fff;
    box-shadow: 0 0 15px rgba(0, 180, 255, 0.4);
}

.license-badge.expired {
    background: #ff4444;
    color: #fff;
    box-shadow: 0 0 15px rgba(255, 68, 68, 0.4);
}

.license-badge.premium {
    background: linear-gradient(135deg, #00d4ff, #00ff88);
    color: #000;
    text-shadow: none;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.license-badge.trial {
    background: linear-gradient(135deg, #ff9500, #ffb800);
    color: #000;
    text-shadow: none;
    box-shadow: 0 0 15px rgba(255, 184, 0, 0.3);
}

/* Premium Badge */
.premium-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    font-size: 0.65rem;
    font-weight: 700;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 10;
    animation: premiumGlow 2s ease-in-out infinite alternate;
}

@keyframes premiumGlow {
    0% {
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    }
    100% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6);
    }
}

.desktop-arsenal-card .premium-badge {
    position: relative;
    top: 0;
    right: 0;
    margin-left: auto;
    margin-bottom: 0.5rem;
}

/* Navigation Premium Badge */
.nav-item .premium-badge {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
    margin-left: auto;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #000;
    border-radius: 4px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
    position: static;
    top: auto;
    right: auto;
}

.nav-item:has(.premium-badge) {
    position: relative;
}

/* Game Type Badges */
.game-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-right: 0.5rem;
}

.preset-badge {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.5);
}

.custom-badge {
    background: rgba(139, 92, 246, 0.9);
    color: white;
    border: 1px solid rgba(139, 92, 246, 0.5);
}

.steam-badge {
    background: rgba(59, 130, 246, 0.9);
    color: white;
    border: 1px solid rgba(59, 130, 246, 0.5);
}

.xbox-badge {
    background: rgba(34, 197, 94, 0.9);
    color: white;
    border: 1px solid rgba(34, 197, 94, 0.5);
}

/* Suggestion Badge */
.suggestion-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-color);
    color: white;
    font-size: 0.6rem;
    font-weight: 700;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    z-index: 10;
    animation: pulse 2s infinite;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-badge.online {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge.offline {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-badge.pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-badge.processing {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Plan Cards Premium Styling */
.plan-card.premium {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.plan-card.premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
    animation: premiumShimmer 2s linear infinite;
}

@keyframes premiumShimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.plan-card.premium .plan-price {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Premium Buttons */
.btn-premium {
    background: linear-gradient(135deg, #ffd700, #ffb300);
    color: #000;
    font-weight: 600;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-premium:hover {
    background: linear-gradient(135deg, #ffb300, #ff9800);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 179, 0, 0.4);
}

.btn-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-premium:hover::before {
    left: 100%;
}

/* Premium Features Grid */
.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.premium-feature {
    background: rgba(255, 215, 0, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.premium-feature:hover {
    background: rgba(255, 215, 0, 0.1);
    border-color: rgba(255, 215, 0, 0.4);
    transform: translateY(-2px);
}

.premium-feature-icon {
    font-size: 2.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
    display: block;
}

.premium-feature-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.premium-feature-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Badge Animations */
.badge-pulse {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

.badge-bounce {
    animation: badgeBounce 1s infinite;
}

@keyframes badgeBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Notification Badges */
.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #ef4444;
    color: white;
    font-size: 0.6rem;
    font-weight: 700;
    padding: 0.15rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    animation: notificationPulse 2s infinite;
}

@keyframes notificationPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .license-badge {
        margin-left: 0;
        align-self: flex-start;
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
    }
    
    .premium-features {
        grid-template-columns: 1fr;
    }
    
    .premium-feature {
        padding: 1rem;
    }
    
    .premium-feature-icon {
        font-size: 2rem;
    }
    
    .game-type-badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }
    
    .status-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

@media (max-width: 480px) {
    .premium-badge {
        font-size: 0.6rem;
        padding: 0.15rem 0.4rem;
    }
    
    .nav-item .premium-badge {
        font-size: 0.55rem;
        padding: 0.08rem 0.25rem;
    }
    
    .suggestion-badge,
    .notification-badge {
        font-size: 0.55rem;
        padding: 0.1rem 0.3rem;
        min-width: 16px;
        height: 16px;
    }
}
