<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Armory X Installer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            user-select: none;
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Custom title bar */
        .titlebar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 32px;
            background: linear-gradient(to right, rgba(10, 10, 10, 0.95), rgba(20, 20, 20, 0.95));
            -webkit-app-region: drag;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            z-index: 1000;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }
        
        .titlebar-title {
            font-size: 13px;
            font-family: 'Orbitron', sans-serif;
            color: #3b82f6;
            margin-left: 5px;
        }

        .titlebar-controls {
            position: absolute;
            right: 0;
            height: 32px;
            -webkit-app-region: no-drag;
            display: flex;
            align-items: center;
        }

        .titlebar-button {
            width: 46px;
            height: 28px;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            padding: 0;
            margin: 0 1px;
        }

        .titlebar-button:hover {
            background: rgba(59, 130, 246, 0.2);
        }

        .titlebar-button.close:hover {
            background: #dc2626;
        }

        /* Main container */
        .container {
            height: 100vh;
            display: flex;
            padding-top: 32px;
        }

        /* Left side - branding */
        .branding-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f172a 100%);
            position: relative;
            overflow: hidden;
            border-right: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Animated grid background */
        .grid-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .brand-logo {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 70px;
            margin-bottom: 30px;
            box-shadow: 
                0 0 50px rgba(59, 130, 246, 0.5),
                0 0 100px rgba(59, 130, 246, 0.3);
            animation: pulse-glow 2s ease-in-out infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 50px rgba(59, 130, 246, 0.5), 0 0 100px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 70px rgba(59, 130, 246, 0.7), 0 0 120px rgba(59, 130, 246, 0.5); }
        }

        .brand-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 3px;
            background: linear-gradient(to right, #3b82f6, #60a5fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }

        .brand-subtitle {
            font-size: 18px;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #94a3b8;
            position: relative;
            z-index: 1;
        }

        /* Floating particles */
        .particle {
            position: absolute;
            background: rgba(59, 130, 246, 0.5);
            border-radius: 50%;
            pointer-events: none;
        }

        .particle:nth-child(1) {
            width: 4px;
            height: 4px;
            top: 20%;
            left: 10%;
            animation: float-up 15s linear infinite;
        }

        .particle:nth-child(2) {
            width: 6px;
            height: 6px;
            top: 80%;
            left: 70%;
            animation: float-up 20s linear infinite;
            animation-delay: 2s;
        }

        .particle:nth-child(3) {
            width: 3px;
            height: 3px;
            top: 60%;
            left: 30%;
            animation: float-up 18s linear infinite;
            animation-delay: 4s;
        }

        @keyframes float-up {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) rotate(720deg); opacity: 0; }
        }

        /* Right side - installation */
        .install-panel {
            flex: 1;
            padding: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: #0f0f0f;
        }

        .step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        h2 {
            font-family: 'Orbitron', sans-serif;
            font-size: 32px;
            margin-bottom: 15px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #3b82f6;
        }

        .subtitle {
            color: #94a3b8;
            margin-bottom: 40px;
            line-height: 1.6;
            font-size: 18px;
        }

        /* Form elements */
        .form-group {
            margin-bottom: 30px;
        }

        .form-label {
            display: block;
            margin-bottom: 10px;
            font-size: 14px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        input[type="text"] {
            flex: 1;
            padding: 14px 20px;
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            color: #fff;
            font-size: 16px;
            font-family: 'Rajdhani', sans-serif;
            transition: all 0.3s;
        }

        input[type="text"]:focus {
            outline: none;
            background: rgba(30, 41, 59, 0.7);
            border-color: #3b82f6;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        /* Custom checkbox */
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .checkbox-group:hover {
            transform: translateX(5px);
        }

        .checkbox {
            width: 24px;
            height: 24px;
            background: rgba(30, 41, 59, 0.5);
            border: 2px solid rgba(59, 130, 246, 0.5);
            border-radius: 4px;
            margin-right: 15px;
            position: relative;
            transition: all 0.3s;
        }

        .checkbox.checked {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            border-color: #3b82f6;
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        /* Buttons */
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 40px;
        }

        button {
            padding: 16px 40px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 700;
            font-family: 'Orbitron', sans-serif;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(59, 130, 246, 0.6);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn-primary:active::before {
            width: 300px;
            height: 300px;
        }

        .btn-secondary {
            background: transparent;
            color: #64748b;
            border: 2px solid rgba(59, 130, 246, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-color: #3b82f6;
            transform: translateY(-2px);
        }

        /* Progress */
        .progress-container {
            margin: 40px 0;
        }

        .progress-bar {
            height: 8px;
            background: rgba(30, 41, 59, 0.5);
            border-radius: 4px;
            overflow: hidden;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            width: 0%;
            transition: width 0.5s ease;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent
            );
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            margin-top: 15px;
            font-size: 16px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Success state */
        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            margin-bottom: 30px;
            box-shadow: 0 0 50px rgba(16, 185, 129, 0.5);
            animation: success-pulse 1s ease-out;
        }

        @keyframes success-pulse {
            0% { transform: scale(0); opacity: 0; }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Loading spinner */
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(59, 130, 246, 0.2);
            border-top-color: #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Custom title bar -->
    <div class="titlebar">
        <span class="titlebar-title">ARMORY X INSTALLER</span>
        <div class="titlebar-controls">
            <button class="titlebar-button" onclick="minimizeWindow()">－</button>
            <button class="titlebar-button close" onclick="closeWindow()">✕</button>
        </div>
    </div>

    <div class="container">
        <!-- Left branding panel -->
        <div class="branding-panel">
            <div class="grid-bg"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="brand-logo">⚡</div>
            <h1 class="brand-title">Armory X</h1>
            <p class="brand-subtitle">System Optimization Suite</p>
        </div>

        <!-- Right installation panel -->
        <div class="install-panel">
            <!-- Step 1: Welcome -->
            <div class="step active" id="step1">
                <h2>Welcome Commander</h2>
                <p class="subtitle">
                    Prepare to enhance your system with the most advanced optimization toolkit.
                    Armory X will transform your PC into a high-performance machine.
                </p>
                
                <div class="button-group">
                    <button class="btn-primary" onclick="nextStep()">Begin Installation</button>
                </div>
            </div>

            <!-- Step 2: Options -->
            <div class="step" id="step2">
                <h2>Configuration</h2>
                <p class="subtitle">Select your installation preferences and deployment options.</p>
                
                <div class="form-group">
                    <label class="form-label">Installation Directory</label>
                    <div class="input-group">
                        <input type="text" id="installPath" value="">
                        <button class="btn-secondary" onclick="browseFolder()">Browse</button>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group" onclick="toggleCheckbox('desktop')">
                        <div class="checkbox checked" id="desktopCheckbox"></div>
                        <span>Deploy desktop shortcut</span>
                    </div>
                    <div class="checkbox-group" onclick="toggleCheckbox('startmenu')">
                        <div class="checkbox checked" id="startmenuCheckbox"></div>
                        <span>Add to Start Menu</span>
                    </div>
                    <div class="checkbox-group" onclick="toggleCheckbox('startup')">
                        <div class="checkbox" id="startupCheckbox"></div>
                        <span>Launch on system startup</span>
                    </div>
                </div>

                <div class="button-group">
                    <button class="btn-secondary" onclick="previousStep()">Back</button>
                    <button class="btn-primary" onclick="startInstall()">Deploy Now</button>
                </div>
            </div>

            <!-- Step 3: Installing -->
            <div class="step" id="step3">
                <h2>Deploying Armory X</h2>
                <p class="subtitle">System optimization in progress. Please stand by...</p>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar"></div>
                    </div>
                    <p class="progress-text" id="progressText">Initializing deployment...</p>
                </div>
                
                <div class="spinner"></div>
            </div>

            <!-- Step 4: Complete -->
            <div class="step" id="step4">
                <div class="success-icon">✓</div>
                <h2>Mission Complete</h2>
                <p class="subtitle">
                    Armory X has been successfully deployed to your system.
                    Your optimization arsenal is now ready for action.
                </p>
                
                <div class="checkbox-group" onclick="toggleCheckbox('launch')">
                    <div class="checkbox checked" id="launchCheckbox"></div>
                    <span>Launch Armory X Command Center</span>
                </div>

                <div class="button-group">
                    <button class="btn-primary" onclick="finish()">Complete</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        let currentStep = 1;
        const totalSteps = 4;

        // Initialize
        window.onload = async () => {
            const defaultPath = await ipcRenderer.invoke('get-default-path');
            document.getElementById('installPath').value = defaultPath;
        };

        // Window controls
        function minimizeWindow() {
            ipcRenderer.send('minimize-window');
        }

        function closeWindow() {
            if (currentStep === 3) {
                alert('Please wait for installation to complete.');
                return;
            }
            ipcRenderer.send('close-window');
        }

        // Navigation
        function nextStep() {
            if (currentStep < totalSteps) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep--;
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
        }

        // Checkbox handling
        function toggleCheckbox(id) {
            const checkbox = document.getElementById(id + 'Checkbox');
            checkbox.classList.toggle('checked');
        }

        // Installation
        async function startInstall() {
            console.log('🚀 Starting Armory X installation...');
            nextStep(); // Go to progress screen
            
            const options = {
                installPath: document.getElementById('installPath').value,
                createDesktopShortcut: document.getElementById('desktopCheckbox').classList.contains('checked'),
                createStartMenuShortcut: document.getElementById('startmenuCheckbox').classList.contains('checked'),
                launchOnStartup: document.getElementById('startupCheckbox').classList.contains('checked')
            };

            console.log('📁 Install path:', options.installPath);
            console.log('⚙️ Options:', options);

            // Simulate progress
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            const steps = [
                { percent: 20, text: 'Creating directories...' },
                { percent: 40, text: 'Copying files...' },
                { percent: 60, text: 'Creating shortcuts...' },
                { percent: 80, text: 'Configuring registry...' },
                { percent: 100, text: 'Finalizing installation...' }
            ];

            for (const step of steps) {
                progressBar.style.width = step.percent + '%';
                progressText.textContent = step.text;
                console.log(`📊 Progress: ${step.percent}% - ${step.text}`);
                await new Promise(resolve => setTimeout(resolve, 800));
            }

            // Perform actual installation
            try {
                const result = await ipcRenderer.invoke('install', options);
                
                if (result.success) {
                    console.log('✅ Installation successful!');
                    nextStep(); // Go to success screen
                } else {
                    console.error('❌ Installation failed:', result.message);
                    alert('Installation failed: ' + result.message);
                    previousStep(); // Go back to options
                }
            } catch (error) {
                console.error('❌ Installation error:', error);
                alert('Installation error: ' + error.message);
                previousStep(); // Go back to options
            }
        }

        function finish() {
            const shouldLaunch = document.getElementById('launchCheckbox').classList.contains('checked');
            if (shouldLaunch) {
                // Launch the app
                console.log('✅ Launching Armory X Command Center...');
            }
            console.log('✅ Installation completed successfully!');
            console.log('✅ Closing installer...');
            
            // Send keep-alive to prevent premature closing
            ipcRenderer.send('keep-alive');
            
            // Small delay to ensure logs are visible
            setTimeout(() => {
                closeWindow();
            }, 500);
        }

        function browseFolder() {
            // In production, use dialog.showOpenDialog
            alert('Browse functionality would open folder picker here');
        }
    </script>
</body>
</html> 