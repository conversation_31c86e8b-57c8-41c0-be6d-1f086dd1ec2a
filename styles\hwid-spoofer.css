/* ===================================
   HWID Spoofer Interface
   =================================== */

/* HWID Spoofer Panel */
.hwid-spoofer-panel {
    border: 1px solid var(--primary-color);
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.hwid-spoofer-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(0, 180, 255, 0.02) 0%, 
        rgba(0, 180, 255, 0.01) 25%, 
        rgba(0, 180, 255, 0.02) 50%, 
        rgba(0, 180, 255, 0.01) 75%, 
        rgba(0, 180, 255, 0.02) 100%);
    background-size: 40px 40px;
    animation: hwidShimmer 8s linear infinite;
    z-index: 0;
}

.hwid-spoofer-panel > * {
    position: relative;
    z-index: 1;
}

.hwid-status-info {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--background-hover);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

.hwid-status-info h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
}

.hwid-status-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.hwid-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.hwid-status-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    text-align: center;
}

.hwid-status-item .status-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hwid-status-item .status-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.hwid-spoofing-options {
    margin: 1rem 0;
    padding: 0.75rem;
    background: rgba(0, 180, 255, 0.05);
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: var(--border-radius);
}

.hwid-spoofing-options .checkbox-label {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 500;
}

.hwid-spoofing-options .checkbox-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    line-height: 1.3;
}

/* HWID Info Modal Styles */
.hwid-info-modal.custom-modal-container {
    max-width: 95vw !important;
    width: 95vw !important;
    min-width: 1200px !important;
    max-height: 90vh !important;
    height: auto !important;
}

.hwid-info-modal .hwid-info-content {
    padding: 0;
    font-size: 1.1rem;
}

/* HWID Modal Body - Increase content area */
.hwid-info-modal .custom-modal-body {
    max-height: 72vh !important;
    padding: 1.5rem;
    overflow-y: auto;
    overflow-x: hidden;
}

/* HWID Modal Footer - Compact button area */
.hwid-info-modal .custom-modal-footer {
    padding: 1rem 1.5rem !important;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.hwid-info-modal .custom-modal-footer .btn {
    min-width: 100px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.hwid-info-modal .info-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.hwid-info-modal .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.hwid-info-modal .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.hwid-info-modal .info-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    flex: 1;
}

.hwid-info-modal .info-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    font-family: monospace;
    text-align: right;
    word-break: break-all;
    max-width: 60%;
}

/* HWID Comparison Grid */
.hwid-comparison-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 1rem;
}

.hwid-comparison-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.3s ease;
}

.hwid-comparison-item.modified {
    border-color: rgba(251, 191, 36, 0.3);
    background: rgba(251, 191, 36, 0.05);
}

.hwid-comparison-item.original {
    border-color: rgba(74, 222, 128, 0.3);
    background: rgba(74, 222, 128, 0.05);
}

.hwid-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.hwid-name {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 600;
}

.hwid-status {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hwid-status.status-modified {
    color: var(--warning-color);
    background: rgba(251, 191, 36, 0.2);
}

.hwid-status.status-original {
    color: var(--success-color);
    background: rgba(74, 222, 128, 0.2);
}

.hwid-values {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.hwid-value-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    gap: 1rem;
}

.hwid-value-row:last-child {
    border-bottom: none;
}

.value-label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    min-width: 80px;
    flex-shrink: 0;
}

.value-data {
    color: var(--text-primary);
    font-size: 13px;
    font-family: monospace;
    word-break: break-all;
    text-align: right;
    flex: 1;
}

.value-data.modified-value {
    color: var(--warning-color);
}

.value-data.original-value {
    color: var(--success-color);
}

/* MAC Spoof Modal Styles */
.mac-spoof-modal .modal-content {
    max-width: 650px;
}

.mac-input-group {
    margin: 1rem 0;
}

.mac-input-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.mac-input {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: var(--text-primary);
    font-family: monospace;
    font-size: 1rem;
}

.mac-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.mac-format-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Emergency Restore Modal */
.emergency-restore-modal .modal-content {
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
}

.restore-warning {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.restore-warning h4 {
    color: #ef4444;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.restore-warning p {
    color: #fca5a5;
    margin: 0;
    line-height: 1.4;
}

.restore-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.restore-option {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.restore-option:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.restore-option.selected {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.restore-option h5 {
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.restore-option p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

@keyframes hwidShimmer {
    0% { background-position: 0 0; }
    100% { background-position: 40px 40px; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hwid-status-info {
        padding: 0.5rem;
    }
    
    .hwid-status-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .hwid-info-modal.custom-modal-container {
        max-width: 98vw;
        width: 98vw;
        max-height: 85vh;
        min-width: auto;
    }
    
    .hwid-info-modal .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hwid-comparison-grid {
        gap: 12px;
    }
    
    .hwid-comparison-item {
        padding: 12px;
    }
    
    .hwid-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .hwid-value-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding-bottom: 6px;
    }
    
    .value-data {
        text-align: left;
    }
}

/* ===================================
   Kernel Protection System
   =================================== */

/* Kernel Protection Section */
.kernel-protection-section {
    margin: 1.5rem 0;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.05), rgba(0, 100, 200, 0.05));
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.kernel-protection-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent,
        rgba(0, 180, 255, 0.02),
        transparent,
        rgba(0, 180, 255, 0.02),
        transparent
    );
    animation: shimmer 4s infinite;
    z-index: 0;
}

.kernel-protection-section > * {
    position: relative;
    z-index: 1;
}

.kernel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.kernel-header h5 {
    margin: 0;
    color: #00b4ff;
    font-size: 1.1rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.kernel-status {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    border: 1px solid;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kernel-status.available {
    background: rgba(0, 255, 0, 0.2);
    border-color: rgba(0, 255, 0, 0.4);
    color: #00ff00;
}

.kernel-status.unavailable {
    background: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.4);
    color: #ff4444;
}

.kernel-status.active {
    background: rgba(0, 180, 255, 0.3);
    border-color: rgba(0, 180, 255, 0.5);
    color: #00b4ff;
    animation: kernelGlow 2s ease-in-out infinite alternate;
}

@keyframes kernelGlow {
    0% {
        box-shadow: 0 0 5px rgba(0, 180, 255, 0.3);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 0 15px rgba(0, 180, 255, 0.6);
        transform: scale(1.02);
    }
}

.kernel-description {
    margin-bottom: 1rem;
}

.kernel-description p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.kernel-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.btn-kernel {
    background: linear-gradient(135deg, #00b4ff, #0066cc);
    border: 1px solid #00b4ff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-kernel:hover:not(:disabled) {
    background: linear-gradient(135deg, #0099dd, #0055bb);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.5);
    transform: translateY(-1px);
}

.btn-kernel:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 180, 255, 0.3);
}

.btn-kernel:disabled {
    background: linear-gradient(135deg, #666, #555);
    border-color: #666;
    color: #999;
    cursor: not-allowed;
    opacity: 0.6;
}

.btn-kernel.active {
    background: linear-gradient(135deg, #ff6b35, #cc5529);
    border-color: #ff6b35;
    animation: kernelActive 2s ease-in-out infinite alternate;
}

@keyframes kernelActive {
    0% {
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }
    100% {
        box-shadow: 0 4px 16px rgba(255, 107, 53, 0.6);
    }
}

.kernel-warning {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 170, 0, 0.1);
    border: 1px solid rgba(255, 170, 0, 0.3);
    border-radius: 6px;
    margin-top: 1rem;
}

.kernel-warning .warning-icon {
    font-size: 1.1rem;
    color: #ffaa00;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.kernel-warning .warning-text {
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.kernel-warning .warning-text strong {
    color: #ffaa00;
    font-weight: 600;
}

/* Responsive adjustments for kernel protection */
@media (max-width: 768px) {
    .kernel-protection-section {
        margin: 1rem 0;
        padding: 1rem;
    }

    .kernel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .kernel-header h5 {
        font-size: 1rem;
    }

    .kernel-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-kernel {
        width: 100%;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .kernel-warning {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .kernel-warning .warning-text {
        font-size: 0.8rem;
    }
}
