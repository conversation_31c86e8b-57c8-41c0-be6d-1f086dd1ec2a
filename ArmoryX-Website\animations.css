/* ========================================
   ARMORY X - ADVANCED ANIMATIONS & EFFECTS
   ======================================== */

/* Starfield Background Animation */
.starfield {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
    overflow: hidden;
}

.starfield::before,
.starfield::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Layer 1 - Large twinkling stars */
.starfield::before {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, #fff, transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, #fff, transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: starMove 60s linear infinite;
}

/* Layer 2 - Small twinkling stars */
.starfield::after {
    background-image: 
        radial-gradient(1px 1px at 10px 10px, #fff, transparent),
        radial-gradient(1px 1px at 30px 60px, #ccc, transparent),
        radial-gradient(1px 1px at 80px 20px, #fff, transparent),
        radial-gradient(1px 1px at 110px 90px, #ddd, transparent),
        radial-gradient(1px 1px at 150px 50px, #fff, transparent);
    background-repeat: repeat;
    background-size: 180px 120px;
    animation: starMove 45s linear infinite reverse, starTwinkle 3s ease-in-out infinite alternate;
}

@keyframes starMove {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

@keyframes starTwinkle {
    0% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Shooting Stars */
.shooting-star {
    position: fixed;
    width: 2px;
    height: 2px;
    background: linear-gradient(45deg, #fff, transparent);
    border-radius: 50%;
    box-shadow: 0 0 6px 2px #fff;
    z-index: -1;
    animation: shootingStar 3s linear infinite;
}

.shooting-star::before {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 1px;
    background: linear-gradient(90deg, #fff, transparent);
    animation: shootingTail 3s linear infinite;
}

@keyframes shootingStar {
    0% {
        transform: rotate(45deg) translateX(-100px);
        opacity: 1;
    }
    70% {
        opacity: 1;
    }
    100% {
        transform: rotate(45deg) translateX(1200px);
        opacity: 0;
    }
}

@keyframes shootingTail {
    0% {
        width: 0;
    }
    30% {
        width: 50px;
    }
    100% {
        width: 0;
    }
}

/* Enhanced Button Hover Effects */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    box-shadow: 0 4px 15px rgba(45, 114, 255, 0.3);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 114, 255, 0.4);
    background: linear-gradient(135deg, #2d72ff, #4a90ff);
}

/* Glowing Effects */
.glow-effect {
    position: relative;
}

.glow-effect::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #2d72ff, #8b5cf6, #06b6d4, #10b981);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    animation: glowRotate 4s linear infinite;
    transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
    opacity: 1;
}

@keyframes glowRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Floating Particles */
.particle {
    position: fixed;
    pointer-events: none;
    z-index: -1;
}

.particle-1 {
    width: 4px;
    height: 4px;
    background: rgba(45, 114, 255, 0.6);
    border-radius: 50%;
    animation: float1 6s ease-in-out infinite;
}

.particle-2 {
    width: 6px;
    height: 6px;
    background: rgba(139, 92, 246, 0.4);
    border-radius: 50%;
    animation: float2 8s ease-in-out infinite;
}

.particle-3 {
    width: 3px;
    height: 3px;
    background: rgba(6, 182, 212, 0.5);
    border-radius: 50%;
    animation: float3 7s ease-in-out infinite;
}

@keyframes float1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(15px) rotate(240deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateX(0px) rotate(0deg); }
    50% { transform: translateX(25px) rotate(180deg); }
}

@keyframes float3 {
    0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
    25% { transform: translate(20px, -20px) rotate(90deg); }
    50% { transform: translate(-10px, 10px) rotate(180deg); }
    75% { transform: translate(-15px, -5px) rotate(270deg); }
}

/* Enhanced Card Hover Effects */
.forum-category,
.user-card,
.feature-card,
.post-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.forum-category:hover,
.user-card:hover,
.feature-card:hover,
.post-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.forum-category::before,
.user-card::before,
.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(45, 114, 255, 0.1), rgba(139, 92, 246, 0.1));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.forum-category:hover::before,
.user-card:hover::before,
.feature-card:hover::before {
    opacity: 1;
}

/* Pulse Animation for Important Elements */
.pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
    0% {
        box-shadow: 0 0 20px rgba(45, 114, 255, 0.4);
    }
    100% {
        box-shadow: 0 0 30px rgba(45, 114, 255, 0.8);
    }
}

/* Loading Animation Enhancement */
.loading-dots {
    display: inline-block;
    font-size: 0;
}

.loading-dots::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(45, 114, 255, 0.3);
    border-left-color: #2d72ff;
    border-radius: 50%;
    animation: loadingSpin 1s linear infinite;
}

@keyframes loadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Gradient Text Animation */
.gradient-text-animated {
    background: linear-gradient(45deg, #2d72ff, #8b5cf6, #06b6d4, #10b981);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Scroll Reveal Animation */
.reveal-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.reveal-on-scroll.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Navigation Hover */
.nav-link {
    position: relative;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #2d72ff, #8b5cf6);
    transform: translateX(-50%);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Forum-specific enhancements */
.like-button,
.dislike-button,
.reply-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.like-button:hover,
.dislike-button:hover,
.reply-button:hover {
    transform: scale(1.05);
}

.like-button.liked {
    animation: likeSuccess 0.6s ease;
}

.dislike-button.disliked {
    animation: dislikeSuccess 0.6s ease;
}

@keyframes likeSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.05); }
}

@keyframes dislikeSuccess {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(-10deg); }
    100% { transform: scale(1.05) rotate(0deg); }
}

/* Hero Section Enhancement */
.hero {
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(45, 114, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .starfield::before {
        background-size: 150px 80px;
        animation-duration: 40s;
    }
    
    .starfield::after {
        background-size: 120px 90px;
        animation-duration: 30s;
    }
    
    .shooting-star {
        display: none; /* Hide shooting stars on mobile for performance */
    }
    
    .particle {
        display: none; /* Hide particles on mobile for performance */
    }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
    .starfield::before,
    .starfield::after,
    .shooting-star,
    .particle,
    .gradient-text-animated {
        animation: none;
    }
    
    .reveal-on-scroll {
        opacity: 1;
        transform: none;
    }
} 