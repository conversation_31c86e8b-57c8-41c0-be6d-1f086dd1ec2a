// 🚨 EMERGENCY DATABASE CLEAR SCRIPT
// Run this in browser console (F12) if Nuclear Cleanup button fails

console.log('🚨 Emergency Database Clear Script Loaded');
console.log('⚠️  WARNING: This will delete ALL license data!');
console.log('📝 Usage: emergencyDatabaseClear()');

window.emergencyDatabaseClear = async function() {
    try {
        console.log('🚨 EMERGENCY DATABASE CLEAR INITIATED');
        
        // Check if Firebase is available
        if (!window.firebase || !window.firebase.db) {
            console.error('❌ Firebase not available');
            alert('Firebase not initialized. Please refresh the page and try again.');
            return;
        }

        // Confirmation
        const confirm1 = confirm(`🚨 EMERGENCY DATABASE CLEAR

This will PERMANENTLY DELETE:
❌ ALL license keys
❌ ALL user licenses  
❌ ALL license logs

This is IRREVERSIBLE!

Continue?`);

        if (!confirm1) {
            console.log('❌ Emergency cleanup cancelled');
            return;
        }

        const finalConfirm = prompt('Type "EMERGENCY DELETE" to confirm:');
        if (finalConfirm !== 'EMERGENCY DELETE') {
            console.log('❌ Wrong confirmation text');
            return;
        }

        let totalDeleted = 0;
        const collections = ['license_keys', 'user_licenses', 'key_generation_log', 'key_validation_log'];

        console.log('🗑️ Starting emergency database clear...');

        for (const collectionName of collections) {
            try {
                console.log(`🗑️ Clearing collection: ${collectionName}`);
                
                const snapshot = await window.firebase.getDocs(
                    window.firebase.collection(window.firebase.db, collectionName)
                );
                
                console.log(`📊 Found ${snapshot.size} documents in ${collectionName}`);
                
                const deletePromises = [];
                snapshot.forEach(doc => {
                    deletePromises.push(window.firebase.deleteDoc(doc.ref));
                });
                
                await Promise.all(deletePromises);
                totalDeleted += snapshot.size;
                
                console.log(`✅ Deleted ${snapshot.size} documents from ${collectionName}`);
                
            } catch (error) {
                console.error(`❌ Error clearing ${collectionName}:`, error);
            }
        }

        console.log(`🎯 EMERGENCY CLEANUP COMPLETED`);
        console.log(`💥 Total documents deleted: ${totalDeleted}`);
        
        alert(`🎯 Emergency cleanup completed!\n\nDeleted ${totalDeleted} documents.\n\nRefresh the page to see changes.`);
        
        // Refresh the page
        if (confirm('Refresh page to see changes?')) {
            window.location.reload();
        }

    } catch (error) {
        console.error('🚨 Emergency cleanup failed:', error);
        alert(`Emergency cleanup failed: ${error.message}`);
    }
};

// Auto-run instructions
console.log(`
🚨 EMERGENCY DATABASE CLEAR READY

To clear the entire license database, run:
emergencyDatabaseClear()

⚠️  This will delete ALL license data permanently!
`); 