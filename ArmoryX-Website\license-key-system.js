// ArmoryX License Key Management System
// Integrates with Firebase Firestore for key storage and validation

// Wait for Firebase to be initialized
function waitForFirebase() {
    return new Promise((resolve) => {
        if (window.firebaseInitialized && window.firebase) {
            resolve();
        } else {
            const checkFirebase = setInterval(() => {
                if (window.firebaseInitialized && window.firebase) {
                    clearInterval(checkFirebase);
                    resolve();
                }
            }, 100);
        }
    });
}

class LicenseKeyManager {
    constructor() {
        this.keyPrefix = 'ARMX';
        this.keyLength = 20; // Total length including dashes
        this.initialized = false;
        this.auth = null;
        this.db = null;
        
        // Initialize when Firebase is ready
        this.init();
    }

    async init() {
        try {
            await waitForFirebase();
            if (window.firebase) {
                this.auth = window.firebase.auth;
                this.db = window.firebase.db;
                this.initialized = true;
                console.log('✅ License Key Manager initialized with Firebase');
            } else {
                console.warn('⚠️ Firebase not available, License Key Manager in offline mode');
            }
        } catch (error) {
            console.error('❌ Failed to initialize License Key Manager:', error);
        }
    }

    // Generate a secure license key
    generateLicenseKey() {
        const segments = [];
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        
        // Generate 4 segments of 4 characters each
        for (let i = 0; i < 4; i++) {
            let segment = '';
            for (let j = 0; j < 4; j++) {
                segment += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            segments.push(segment);
        }
        
        return `${this.keyPrefix}-${segments.join('-')}`;
    }

    // Generate HWID from browser fingerprint
    async generateHWID() {
        try {
            // Create browser fingerprint for web-based HWID
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('ArmoryX HWID Generator', 2, 2);
            
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                canvas.toDataURL(),
                navigator.hardwareConcurrency || 'unknown',
                navigator.deviceMemory || 'unknown'
            ].join('|');
            
            // Create hash of fingerprint
            const encoder = new TextEncoder();
            const data = encoder.encode(fingerprint);
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 32);
        } catch (error) {
            console.error('Error generating HWID:', error);
            return 'HWID_ERROR_' + Date.now();
        }
    }

    // Create a new license key in the database (admin only)
    async createLicenseKey(keyType = 'lifetime', productType = 'standard', purchaseInfo = null) {
        try {
            if (!this.initialized) {
                throw new Error('License manager not initialized');
            }

            const user = this.auth.currentUser;
            if (!user) throw new Error('User not authenticated');

            // Check if user has admin role for key generation
            const userDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'users', user.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Insufficient permissions to generate keys');
            }

            const licenseKey = this.generateLicenseKey();
            const now = new Date();
            let expiresAt = null;

            // Set expiration based on key type
            if (keyType === 'monthly') {
                expiresAt = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
            } else if (keyType === 'trial') {
                expiresAt = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
            }

            // Create license key document
            const keyData = {
                keyValue: licenseKey,  // Changed from licenseKey to keyValue for consistency
                userId: null, // Will be set when activated
                hwid: null,   // Will be set when activated
                status: 'unused',
                createdAt: window.firebase.serverTimestamp(),
                activatedAt: null,
                expiresAt: expiresAt,
                keyType: keyType,
                productType: productType,
                lastValidated: null,
                activationCount: 0,
                maxActivations: 1,
                createdBy: user.uid, // Add the admin who created this key
                purchaseInfo: purchaseInfo || {
                    orderId: 'ADMIN_GENERATED',
                    amount: 0,
                    currency: 'USD',
                    paymentMethod: 'admin',
                    purchaseDate: window.firebase.serverTimestamp()
                }
            };

            // Add to license_keys collection
            const keyDocRef = await window.firebase.addDoc(window.firebase.collection(this.db, 'license_keys'), keyData);

            // Log the key generation
            await window.firebase.addDoc(window.firebase.collection(this.db, 'key_generation_log'), {
                generatedBy: user.uid,
                batchId: `single_${Date.now()}`,
                keyCount: 1,
                keyType: keyType,
                generatedAt: window.firebase.serverTimestamp(),
                keys: [licenseKey]
            });

            return {
                success: true,
                keyId: keyDocRef.id,
                licenseKey: licenseKey,
                keyType: keyType,
                expiresAt: expiresAt
            };

        } catch (error) {
            console.error('Error creating license key:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Activate/Redeem a license key with Firebase fallback
    async activateLicenseKey(licenseKey, hwid = null) {
        try {
            if (!this.initialized) {
                throw new Error('License manager not initialized');
            }

            const user = this.auth.currentUser;
            if (!user) throw new Error('User not authenticated');

            // Generate HWID if not provided
            if (!hwid) {
                hwid = await this.generateHWID();
            }

            console.log('🔑 Attempting license key activation:', licenseKey);

            // Validate license key format first
            if (!this.isValidLicenseFormat(licenseKey)) {
                return {
                    success: false,
                    error: 'Invalid license key format. Please check your key and try again.'
                };
            }

            // Try Firebase first (should work now with fixed rules)
            try {
                console.log('🔥 Attempting Firebase license activation...');
                return await this.activateLicenseKeyFirebase(licenseKey, hwid, user);
            } catch (firebaseError) {
                console.error('❌ Firebase activation failed:', firebaseError);
                
                // Only fall back to local storage for connection issues, not permission issues
                if (firebaseError.code === 'unavailable' || 
                    firebaseError.code === 'deadline-exceeded' ||
                    firebaseError.message.includes('network') ||
                    firebaseError.message.includes('offline')) {
                    
                    console.log('🌐 Firebase connection issue, using local fallback');
                    return await this.activateLicenseKeyLocal(licenseKey, hwid, user);
                } else {
                    // For permission or other errors, show the actual error to help debug
                    return {
                        success: false,
                        error: `Firebase error: ${firebaseError.message}. Please check your Firebase security rules.`
                    };
                }
            }

        } catch (error) {
            console.error('❌ Error activating license key:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Validate license key format
    isValidLicenseFormat(licenseKey) {
        // Check if it matches ARMX-XXXX-XXXX-XXXX-XXXX format
        const pattern = /^ARMX-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        return pattern.test(licenseKey.toUpperCase());
    }

    // Firebase license activation (original method)
    async activateLicenseKeyFirebase(licenseKey, hwid, user) {
        console.log('🔍 Step 1: Starting Firebase license activation for key:', licenseKey);
        console.log('🔍 User ID:', user.uid, 'HWID:', hwid);

        // Try to find the license key - check both field names in case of mismatch
        console.log('🔍 Step 2: Querying for license key in Firebase...');
        
        let keySnapshot;
        try {
            // First try with 'keyValue' field (new format)
            console.log('🔍 Step 2a: Trying keyValue field...');
            const keysQuery1 = window.firebase.query(
                window.firebase.collection(this.db, 'license_keys'),
                window.firebase.where('keyValue', '==', licenseKey)
            );
            keySnapshot = await window.firebase.getDocs(keysQuery1);
            
            // If empty, try with 'licenseKey' field (old format)
            if (keySnapshot.empty) {
                console.log('🔍 Step 2b: keyValue field empty, trying licenseKey field...');
                const keysQuery2 = window.firebase.query(
                    window.firebase.collection(this.db, 'license_keys'),
                    window.firebase.where('licenseKey', '==', licenseKey)
                );
                keySnapshot = await window.firebase.getDocs(keysQuery2);
            }
            
            console.log('✅ Step 2: Query successful. Found', keySnapshot.size, 'documents');
        } catch (queryError) {
            console.error('❌ Step 2: License key query failed:', queryError);
            throw new Error(`Query failed: ${queryError.message}`);
        }

        if (keySnapshot.empty) {
            console.log('❌ Step 3: License key not found in Firebase');
            await this.logValidationAttempt(null, user.uid, hwid, 'invalid_key');
            return {
                success: false,
                error: 'Invalid license key - not found in database'
            };
        }

        console.log('✅ Step 3: License key found in Firebase');
        const keyDoc = keySnapshot.docs[0];
        const keyData = keyDoc.data();
        
        console.log('📄 Step 4: License key data:', {
            id: keyDoc.id,
            status: keyData.status,
            userId: keyData.userId,
            keyType: keyData.keyType,
            fieldName: keyData.keyValue ? 'keyValue' : (keyData.licenseKey ? 'licenseKey' : 'unknown')
        });

        // Check if key is revoked
        if (keyData.status === 'revoked' || keyData.revokedAt) {
            await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'revoked');
            return {
                success: false,
                error: `License key has been revoked${keyData.revocationReason ? ': ' + keyData.revocationReason : ''}`
            };
        }

        // Check if key is already used
        if (keyData.status === 'active') {
            if (keyData.userId === user.uid) {
                // User is reactivating their own key
                await window.firebase.updateDoc(keyDoc.ref, {
                    lastValidated: window.firebase.serverTimestamp()
                });
                
                await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'revalidation');
                await this.updateUserLicense(user.uid, keyDoc.id, keyData.keyType, hwid, licenseKey);
                
                return {
                    success: true,
                    message: 'License key revalidated - your premium status is active',
                    keyType: keyData.keyType,
                    expiresAt: keyData.expiresAt,
                    isReactivation: true
                };
            } else {
                await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'already_used');
                return {
                    success: false,
                    error: 'License key is already in use by another user'
                };
            }
        } else if (keyData.status !== 'unused') {
            await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'invalid_status');
            return {
                success: false,
                error: `License key cannot be activated (status: ${keyData.status})`
            };
        }

        // Check if key is expired
        if (keyData.expiresAt && new Date() > keyData.expiresAt.toDate()) {
            await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'expired');
            return {
                success: false,
                error: 'License key has expired'
            };
        }

        // Activate the key
        console.log('🔍 Step 5: Attempting to update license key status to active...');
        try {
            await window.firebase.updateDoc(keyDoc.ref, {
                userId: user.uid,
                hwid: hwid,
                status: 'active',
                activatedAt: window.firebase.serverTimestamp(),
                lastValidated: window.firebase.serverTimestamp(),
                activationCount: window.firebase.increment(1)
            });
            console.log('✅ Step 5: License key updated successfully');
        } catch (updateError) {
            console.error('❌ Step 5: Failed to update license key:', updateError);
            throw new Error(`License key update failed: ${updateError.message}`);
        }

        // Update user's license info
        console.log('🔍 Step 6: Attempting to update user license document...');
        try {
            await this.updateUserLicense(user.uid, keyDoc.id, keyData.keyType, hwid, licenseKey);
            console.log('✅ Step 6: User license updated successfully');
        } catch (userLicenseError) {
            console.error('❌ Step 6: Failed to update user license:', userLicenseError);
            throw new Error(`User license update failed: ${userLicenseError.message}`);
        }

        console.log('🔍 Step 7: Logging validation attempt...');
        await this.logValidationAttempt(keyDoc.id, user.uid, hwid, 'success');

        return {
            success: true,
            message: 'License key activated successfully',
            keyType: keyData.keyType,
            expiresAt: keyData.expiresAt,
            userId: user.uid,
            keyId: keyDoc.id
        };
    }

    // Local license activation fallback (works without Firebase permissions)
    async activateLicenseKeyLocal(licenseKey, hwid, user) {
        console.log('🏠 Starting local license key activation...');
        
        // Check if key is in our known valid keys or generate valid test keys
        const validTestKeys = this.getValidTestKeys();
        const isValidKey = validTestKeys.includes(licenseKey.toUpperCase());
        
        if (!isValidKey) {
            // For demo purposes, generate a valid key if it matches the format
            if (this.isValidLicenseFormat(licenseKey)) {
                console.log('🎯 Valid format detected, activating as trial license');
            } else {
                return {
                    success: false,
                    error: 'Invalid license key format or key not found'
                };
            }
        }

        // Check if already activated locally
        const localLicenses = JSON.parse(localStorage.getItem('armoryX_local_licenses') || '{}');
        
        if (localLicenses[user.uid]) {
            const existingLicense = localLicenses[user.uid];
            if (existingLicense.keyValue === licenseKey.toUpperCase()) {
                console.log('🔄 Reactivating existing local license');
                return {
                    success: true,
                    message: 'License key reactivated locally - premium features enabled',
                    keyType: existingLicense.keyType,
                    expiresAt: existingLicense.expiresAt,
                    isReactivation: true,
                    isLocal: true
                };
            }
        }

        // Determine license type based on key pattern
        let keyType = 'lifetime';
        let expiresAt = null;
        
        if (licenseKey.includes('TRIAL')) {
            keyType = 'trial';
            expiresAt = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // 7 days
        } else if (licenseKey.includes('MONTH')) {
            keyType = 'monthly';
            expiresAt = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)); // 30 days
        } else if (licenseKey.includes('YEAR')) {
            keyType = 'yearly';
            expiresAt = new Date(Date.now() + (365 * 24 * 60 * 60 * 1000)); // 1 year
        }

        // Create local license data
        const localLicenseData = {
            keyValue: licenseKey.toUpperCase(),
            userId: user.uid,
            hwid: hwid,
            status: 'active',
            keyType: keyType,
            accountType: keyType === 'trial' ? 'trial' : 'premium',
            activatedAt: new Date().toISOString(),
            lastValidated: new Date().toISOString(),
            expiresAt: expiresAt ? expiresAt.toISOString() : null,
            subscriptionStatus: 'active',
            isLocal: true
        };

        // Store locally
        localLicenses[user.uid] = localLicenseData;
        localStorage.setItem('armoryX_local_licenses', JSON.stringify(localLicenses));

        // Try to store in user_licenses collection (might fail due to permissions)
        try {
            console.log('💾 Attempting to store license in Firebase...');
            const userLicenseRef = window.firebase.doc(this.db, 'user_licenses', user.uid);
            await window.firebase.setDoc(userLicenseRef, {
                userId: user.uid,
                activeKeys: [licenseKey],
                totalKeys: 1,
                subscriptionStatus: 'active',
                hwid: hwid,
                lastHwidUpdate: window.firebase.serverTimestamp(),
                accountType: keyType === 'trial' ? 'trial' : 'premium',
                keyValue: licenseKey,
                lastActivated: window.firebase.serverTimestamp(),
                licenseHistory: [{
                    keyType: keyType,
                    keyValue: licenseKey,
                    activatedAt: new Date().toISOString(),
                    status: 'active'
                }],
                isLocal: true
            });
            console.log('✅ Successfully stored license in Firebase');
        } catch (error) {
            console.log('⚠️ Could not store in Firebase, using local storage only:', error.message);
        }

        console.log('✅ Local license activation successful');
        return {
            success: true,
            message: `${keyType.charAt(0).toUpperCase() + keyType.slice(1)} license activated successfully! Premium features enabled.`,
            keyType: keyType,
            expiresAt: expiresAt,
            userId: user.uid,
            isLocal: true
        };
    }

    // Get valid test license keys for demo purposes
    getValidTestKeys() {
        return [
            'ARMX-DEMO-LIFE-TIME-001',
            'ARMX-DEMO-YEAR-LY01-001', 
            'ARMX-DEMO-MONT-HLY1-001',
            'ARMX-DEMO-TRIA-L001-001',
            'ARMX-TEST-PREM-IUM1-001',
            'ARMX-FREE-TRIA-L123-456',
            // Any key with valid format will be accepted in demo mode
        ];
    }

    // Debug function to test Firebase permissions
    async debugFirebasePermissions() {
        try {
            console.log('🔍 DEBUGGING: Testing Firebase permissions...');
            
            if (!this.initialized) {
                console.log('❌ License manager not initialized');
                return;
            }

            const user = this.auth.currentUser;
            if (!user) {
                console.log('❌ No user logged in');
                return;
            }

            console.log('✅ User logged in:', user.uid);

            // Test 1: Try to read license_keys collection
            console.log('🔍 Test 1: Reading license_keys collection...');
            try {
                const keysQuery = window.firebase.query(
                    window.firebase.collection(this.db, 'license_keys'),
                    window.firebase.limit(1)
                );
                const keysSnapshot = await window.firebase.getDocs(keysQuery);
                console.log('✅ Test 1: Can read license_keys collection. Found', keysSnapshot.size, 'documents');
            } catch (error) {
                console.error('❌ Test 1: Cannot read license_keys collection:', error.message);
            }

            // Test 2: Try to read user_licenses collection
            console.log('🔍 Test 2: Reading user_licenses collection...');
            try {
                const userLicenseDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'user_licenses', user.uid));
                console.log('✅ Test 2: Can read user_licenses collection. Document exists:', userLicenseDoc.exists());
            } catch (error) {
                console.error('❌ Test 2: Cannot read user_licenses collection:', error.message);
            }

            // Test 3: Try to create a test document in user_licenses
            console.log('🔍 Test 3: Testing write to user_licenses collection...');
            try {
                const testRef = window.firebase.doc(this.db, 'user_licenses', user.uid + '_test');
                await window.firebase.setDoc(testRef, {
                    test: true,
                    timestamp: window.firebase.serverTimestamp()
                });
                console.log('✅ Test 3: Can write to user_licenses collection');
                
                // Clean up test document
                await window.firebase.deleteDoc(testRef);
                console.log('✅ Test 3: Cleaned up test document');
            } catch (error) {
                console.error('❌ Test 3: Cannot write to user_licenses collection:', error.message);
            }

            console.log('🔍 DEBUGGING: Permission tests completed');

        } catch (error) {
            console.error('❌ DEBUGGING: Error during permission tests:', error);
        }
    }

    // Update user's license information
    async updateUserLicense(userId, keyId, keyType, hwid, licenseKey) {
        try {
            const userLicenseRef = window.firebase.doc(this.db, 'user_licenses', userId);
            const userLicenseDoc = await window.firebase.getDoc(userLicenseRef);

            // Create license data with regular Date instead of serverTimestamp for arrays
            const now = new Date();
            const licenseData = {
                keyId: keyId,
                keyType: keyType,
                keyValue: licenseKey,  // Changed from licenseKey to keyValue for consistency
                activatedAt: now.toISOString(), // Use ISO string instead of serverTimestamp
                status: 'active'
            };

            console.log('💾 Updating user license with data:', {
                userId,
                keyId,
                keyType,
                licenseKey,
                accountType: keyType === 'trial' ? 'trial' : 'premium'
            });

            if (userLicenseDoc.exists()) {
                const currentData = userLicenseDoc.data();
                await window.firebase.updateDoc(userLicenseRef, {
                    activeKeys: window.firebase.arrayUnion(keyId),
                    totalKeys: window.firebase.increment(1),
                    subscriptionStatus: 'active',
                    hwid: hwid,
                    lastHwidUpdate: window.firebase.serverTimestamp(),
                    accountType: keyType === 'trial' ? 'trial' : 'premium',
                    keyValue: licenseKey, // Store current active license key (changed from licenseKey for consistency)
                    lastActivated: window.firebase.serverTimestamp(),
                    licenseHistory: window.firebase.arrayUnion(licenseData)
                });
                console.log('✅ Updated existing user license document');
            } else {
                await window.firebase.setDoc(userLicenseRef, {
                    userId: userId,
                    activeKeys: [keyId],
                    totalKeys: 1,
                    subscriptionStatus: 'active',
                    hwid: hwid,
                    lastHwidUpdate: window.firebase.serverTimestamp(),
                    accountType: keyType === 'trial' ? 'trial' : 'premium',
                    keyValue: licenseKey, // Store current active license key (changed from licenseKey for consistency)
                    lastActivated: window.firebase.serverTimestamp(),
                    licenseHistory: [licenseData]
                });
                console.log('✅ Created new user license document');
            }
            
            // Verify the document was created/updated
            const verifyDoc = await window.firebase.getDoc(userLicenseRef);
            if (verifyDoc.exists()) {
                console.log('✅ License document verified:', verifyDoc.data());
            } else {
                console.error('❌ License document was not created!');
            }
            
        } catch (error) {
            console.error('❌ Error updating user license in Firebase:', error);
            
            // If it's a permission error, this indicates the rules need to be updated
            if (error.code === 'permission-denied' || error.message.includes('permissions')) {
                console.error('🚨 Firebase permission error! Please update your Firebase security rules.');
                console.log('📋 Copy the rules from firebase-security-rules.md to Firebase Console → Firestore → Rules');
            }
            
            throw error; // Re-throw to handle in calling function
        }
    }

    // Validate an active license key
    async validateLicenseKey(userId, hwid) {
        try {
            if (!this.initialized) {
                return {
                    valid: false,
                    error: 'License manager not initialized'
                };
            }

            // Get user's license info
            const userLicenseDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'user_licenses', userId));
            if (!userLicenseDoc.exists()) {
                return {
                    valid: false,
                    error: 'No license found for user'
                };
            }

            const userLicense = userLicenseDoc.data();

            // Check HWID match
            if (userLicense.hwid !== hwid) {
                return {
                    valid: false,
                    error: 'Hardware ID mismatch'
                };
            }

            // Check if user has active keys
            if (!userLicense.activeKeys || userLicense.activeKeys.length === 0) {
                return {
                    valid: false,
                    error: 'No active license keys'
                };
            }

            // Validate each active key
            for (const keyId of userLicense.activeKeys) {
                const keyDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'license_keys', keyId));
                if (keyDoc.exists()) {
                    const keyData = keyDoc.data();
                    
                    // Check if key is active and not expired
                    if (keyData.status === 'active' &&
                        (!keyData.expiresAt || new Date() <= keyData.expiresAt.toDate())) {
                        
                        // Update last validated timestamp
                        await window.firebase.updateDoc(keyDoc.ref, {
                            lastValidated: window.firebase.serverTimestamp()
                        });

                        await this.logValidationAttempt(keyId, userId, hwid, 'success');

                        return {
                            valid: true,
                            keyType: keyData.keyType,
                            expiresAt: keyData.expiresAt,
                            accountType: userLicense.accountType
                        };
                    }
                }
            }

            return {
                valid: false,
                error: 'All license keys are expired or invalid'
            };

        } catch (error) {
            console.error('Error validating license:', error);
            return {
                valid: false,
                error: error.message
            };
        }
    }

    // Log validation attempts for security monitoring
    async logValidationAttempt(keyId, userId, hwid, result) {
        try {
            if (!this.initialized) return;

            console.log('🔍 Logging validation attempt:', { keyId, userId, hwid, result });
            await window.firebase.addDoc(window.firebase.collection(this.db, 'key_validation_log'), {
                keyId: keyId,
                userId: userId,
                hwid: hwid,
                validationResult: result,
                timestamp: window.firebase.serverTimestamp(),
                ipAddress: 'N/A', // Would be filled by server-side function
                userAgent: navigator.userAgent
            });
            console.log('✅ Validation attempt logged successfully');
        } catch (error) {
            console.warn('⚠️ Error logging validation attempt (non-critical):', error.message);
            // Don't throw here since logging is not critical for activation
        }
    }

    // Get user's license status with local fallback
    async getUserLicenseStatus(userId, retryCount = 0) {
        try {
            console.log(`🔍 Getting license status for user: ${userId} (attempt ${retryCount + 1})`);
            
            if (!this.initialized) {
                console.log('❌ License manager not initialized');
                return {
                    hasLicense: false,
                    accountType: 'free',
                    subscriptionStatus: 'none'
                };
            }

            // Try Firebase first (should work with fixed rules)
            try {
                const userLicenseDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'user_licenses', userId));
                console.log('📄 User license document exists:', userLicenseDoc.exists());
                
                if (userLicenseDoc.exists()) {
                    const userLicense = userLicenseDoc.data();
                    console.log('📊 User license data from Firebase:', userLicense);
                    
                    // Get expiration date from the active license key
                    let expiresAt = null;
                    if (userLicense.activeKeys && userLicense.activeKeys.length > 0) {
                        try {
                            const keyId = userLicense.activeKeys[userLicense.activeKeys.length - 1];
                            const keyDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'license_keys', keyId));
                            
                            if (keyDoc.exists()) {
                                const keyData = keyDoc.data();
                                expiresAt = keyData.expiresAt;
                            }
                        } catch (error) {
                            console.error('❌ Error getting expiration date:', error);
                        }
                    }

                    console.log('✅ Returning Firebase license status');
                    return {
                        hasLicense: userLicense.subscriptionStatus === 'active',
                        accountType: userLicense.accountType || 'free',
                        subscriptionStatus: userLicense.subscriptionStatus || 'none',
                        totalKeys: userLicense.totalKeys || 0,
                        activeKeys: userLicense.activeKeys?.length || 0,
                        hwid: userLicense.hwid,
                        keyValue: userLicense.keyValue,
                        expiresAt: expiresAt,
                        source: 'firebase'
                    };
                }
            } catch (firebaseError) {
                console.warn('🔄 Firebase license check failed, checking local storage:', firebaseError.message);
                
                // Only use local storage for connection issues
                if (firebaseError.code !== 'permission-denied' && 
                    !firebaseError.message.includes('permissions')) {
                    console.log('🌐 Firebase connection issue, using local storage');
                } else {
                    console.error('❌ Firebase permission error - rules may need updating');
                }
            }

            // Check local storage for license data
            console.log('🏠 Checking local license storage...');
            const localLicenses = JSON.parse(localStorage.getItem('armoryX_local_licenses') || '{}');
            
            if (localLicenses[userId]) {
                const localLicense = localLicenses[userId];
                console.log('📊 Found local license data:', localLicense);
                
                // Check if local license is expired
                let isExpired = false;
                if (localLicense.expiresAt) {
                    isExpired = new Date() > new Date(localLicense.expiresAt);
                }
                
                console.log('✅ Returning local license status');
                return {
                    hasLicense: !isExpired && localLicense.subscriptionStatus === 'active',
                    accountType: isExpired ? 'free' : (localLicense.accountType || 'free'),
                    subscriptionStatus: isExpired ? 'expired' : (localLicense.subscriptionStatus || 'none'),
                    totalKeys: 1,
                    activeKeys: isExpired ? 0 : 1,
                    hwid: localLicense.hwid,
                    keyValue: localLicense.keyValue,
                    expiresAt: localLicense.expiresAt,
                    source: 'local',
                    isExpired: isExpired
                };
            }

            return {
                hasLicense: false,
                accountType: 'free',
                subscriptionStatus: 'none'
            };
            
        } catch (error) {
            console.error('❌ Error getting user license status:', error);
            return {
                hasLicense: false,
                accountType: 'free',
                subscriptionStatus: 'none',
                error: error.message
            };
        }
    }

    // Revoke a license key (admin only)
    async revokeLicenseKey(licenseKey, reason = 'Admin revoked') {
        try {
            if (!this.initialized) {
                throw new Error('License manager not initialized');
            }

            const user = this.auth.currentUser;
            if (!user) throw new Error('User not authenticated');

            const userDoc = await window.firebase.getDoc(window.firebase.doc(this.db, 'users', user.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Insufficient permissions');
            }

            const keysQuery = window.firebase.query(
                window.firebase.collection(this.db, 'license_keys'),
                window.firebase.where('keyValue', '==', licenseKey)  // Changed from licenseKey to keyValue for consistency
            );
            const keySnapshot = await window.firebase.getDocs(keysQuery);

            if (keySnapshot.empty) {
                return {
                    success: false,
                    error: 'License key not found'
                };
            }

            const keyDoc = keySnapshot.docs[0];
            const keyData = keyDoc.data();
            
            // Update the license key status
            await window.firebase.updateDoc(keyDoc.ref, {
                status: 'revoked',
                revokedAt: window.firebase.serverTimestamp(),
                revokedBy: user.uid,
                revocationReason: reason
            });

            // If the key was active, update the user's license status
            if (keyData.userId && keyData.status === 'active') {
                const userLicenseRef = window.firebase.doc(this.db, 'user_licenses', keyData.userId);
                const userLicenseDoc = await window.firebase.getDoc(userLicenseRef);
                
                if (userLicenseDoc.exists()) {
                    const userLicense = userLicenseDoc.data();
                    const updatedActiveKeys = (userLicense.activeKeys || []).filter(id => id !== keyDoc.id);
                    
                    // Update user license
                    await window.firebase.updateDoc(userLicenseRef, {
                        activeKeys: updatedActiveKeys,
                        subscriptionStatus: updatedActiveKeys.length > 0 ? 'active' : 'none',
                        accountType: updatedActiveKeys.length > 0 ? userLicense.accountType : 'free',
                        revokedKeys: window.firebase.arrayUnion(keyDoc.id),
                        lastUpdate: window.firebase.serverTimestamp()
                    });
                }
            }

            return {
                success: true,
                message: 'License key revoked successfully'
            };

        } catch (error) {
            console.error('Error revoking license key:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Export the license key manager
window.LicenseKeyManager = LicenseKeyManager;

// Global debug function for testing Firebase permissions
window.debugLicensePermissions = async function() {
    try {
        if (window.licenseManager) {
            await window.licenseManager.debugFirebasePermissions();
        } else {
            console.log('❌ License manager not available. Initializing...');
            const manager = new LicenseKeyManager();
            await manager.init();
            await manager.debugFirebasePermissions();
        }
    } catch (error) {
        console.error('❌ Debug function error:', error);
    }
}; 