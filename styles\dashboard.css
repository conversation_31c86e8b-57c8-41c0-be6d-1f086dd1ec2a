/* ===================================
   Dashboard and Widgets
   =================================== */

/* Modern Desktop Dashboard Styles */
.modern-dashboard {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-section.full-width {
    grid-column: 1 / -1;
}

.dashboard-section {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

.dashboard-section:nth-child(2) {
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.dashboard-section:nth-child(3) {
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

.dashboard-section:nth-child(4) {
    animation-delay: 0.6s;
    animation-fill-mode: both;
}

.dashboard-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-smooth);
}

.dashboard-section:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 180, 255, 0.15);
    border-color: rgba(0, 180, 255, 0.3);
}

.dashboard-section:hover::before {
    opacity: 1;
}

/* Remove hover effects from welcome-clock section */
.welcome-clock-section:hover {
    transform: none;
    box-shadow: none;
    border-color: var(--border-color);
}

.welcome-clock-section:hover::before {
    opacity: 0;
}

.launch-section {
    grid-column: 1 / -1;
}

/* Compact Welcome & Clock Header */
.welcome-clock-section {
    margin-bottom: 2rem;
    animation: slideInFromTop 0.8s ease-out;
}

.welcome-clock-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--gradient-primary), rgba(0, 180, 255, 0.8));
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 180, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.compact-welcome {
    color: white;
    flex: 1;
}

.welcome-message {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.welcome-subtitle {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.clock-calendar-compact {
    display: flex;
    align-items: center;
    gap: 2rem;
    color: white;
}

.clock-display {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    animation: slideInFromRight 1s ease-out 0.4s both;
}

.digital-clock-compact {
    display: flex;
    align-items: baseline;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.time-compact {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    animation: gentleGlow 3s infinite ease-in-out;
}

.period-compact {
    font-size: 1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    margin-left: 0.25rem;
}

.date-compact {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.day-name-compact {
    font-size: 1rem;
    font-weight: 600;
    color: white;
}

.date-detail-compact {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Mini Calendar */
.mini-calendar-compact {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInFromRight 1s ease-out 0.8s both;
}

.calendar-header-compact {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding: 0 0.25rem;
}

.calendar-month-compact {
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
}

.calendar-nav-compact {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    font-size: 1rem;
    font-weight: bold;
}

.calendar-nav-compact:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.calendar-grid-compact {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    font-size: 0.75rem;
}

.calendar-weekday-compact {
    text-align: center;
    font-size: 0.7rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.25rem;
}

.calendar-day-compact {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    position: relative;
    min-height: 24px;
}

.calendar-day-compact:hover {
    background: rgba(255, 255, 255, 0.2);
}

.calendar-day-compact.today {
    background: rgba(255, 255, 255, 0.3);
    font-weight: 700;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.calendar-day-compact.other-month {
    opacity: 0.4;
}

.calendar-day-compact.other-month:hover {
    opacity: 0.7;
}

/* Quick Launch Grid */
.launch-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.launch-app {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.launch-app:nth-child(1) { animation-delay: 0.1s; }
.launch-app:nth-child(2) { animation-delay: 0.2s; }
.launch-app:nth-child(3) { animation-delay: 0.3s; }
.launch-app:nth-child(4) { animation-delay: 0.4s; }
.launch-app:nth-child(5) { animation-delay: 0.5s; }
.launch-app:nth-child(6) { animation-delay: 0.6s; }
.launch-app:nth-child(n+7) { animation-delay: 0.7s; }

.launch-app::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.launch-app:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 180, 255, 0.3);
}

.launch-app:hover::before {
    left: 100%;
}

.app-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(0, 180, 255, 0.1);
    transition: var(--transition-fast);
}

.launch-app:hover .app-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.app-name {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.add-app-card {
    border-style: dashed;
    opacity: 0.7;
    justify-content: center;
}

.add-app-card:hover {
    opacity: 1;
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
}

.add-app-icon {
    font-size: 2rem;
    color: var(--text-secondary);
}

/* Quick Access Grid */
.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.favorite-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.favorite-card:nth-child(1) { animation-delay: 0.1s; }
.favorite-card:nth-child(2) { animation-delay: 0.2s; }
.favorite-card:nth-child(3) { animation-delay: 0.3s; }
.favorite-card:nth-child(4) { animation-delay: 0.4s; }
.favorite-card:nth-child(5) { animation-delay: 0.5s; }
.favorite-card:nth-child(6) { animation-delay: 0.6s; }
.favorite-card:nth-child(n+7) { animation-delay: 0.7s; }

.favorite-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.favorite-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 180, 255, 0.3);
}

.favorite-card:hover::before {
    left: 100%;
}

.favorite-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(0, 180, 255, 0.1);
    transition: var(--transition-fast);
}

.favorite-card:hover .favorite-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.favorite-name {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.add-favorite-card {
    border-style: dashed;
    opacity: 0.7;
    justify-content: center;
}

.add-favorite-card:hover {
    opacity: 1;
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
}

.add-favorite-icon {
    font-size: 2rem;
    color: var(--text-secondary);
}

/* Recent Activity */
.recent-activity {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.recent-activity h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: 8px;
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--border-color);
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.activity-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
}

.activity-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Game Grid */
.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.game-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.game-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.game-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.game-card .game-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
    display: none;
}

.game-card img[style*="display: none"] + .game-icon,
.game-card:not(:has(img)) .game-icon {
    display: block;
}

.game-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.game-card p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}
