/* ===================================
   Global Reset and Base Styles
   =================================== */

/* Global Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* HTML and Body Base Styles */
html, body {
    height: 100%;
    overflow: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--background-dark);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
    user-select: none;
    -webkit-font-smoothing: antialiased;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--background-hover);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Focus Styles */
*:focus {
    outline: none;
}

/* Selection Styles */
::selection {
    background: rgba(0, 180, 255, 0.3);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(0, 180, 255, 0.3);
    color: var(--text-primary);
}
