const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const { exec } = require('child_process');

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1000,  // Increased from 800
    height: 650,  // Increased from 500
    frame: false, // No title bar - completely custom
    resizable: false,
    backgroundColor: '#0a0a0a',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    // Remove icon path that might be causing issues
    // icon: path.join(__dirname, '../../ArmoryX-Website/assets/Armory_X.ico')
  });

  mainWindow.loadFile('installer.html');
  
  // Uncomment for dev tools
  // mainWindow.webContents.openDevTools();
}

app.whenReady().then(() => {
  console.log('🚀 Armory X Installer starting...');
  console.log(`📍 Platform: ${process.platform}`);
  console.log(`📍 Electron: ${process.versions.electron}`);
  console.log(`📍 Node: ${process.versions.node}`);
  
  createWindow();
  
  console.log('✅ Installer window created successfully');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle window controls
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.on('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
  app.quit();
});

// Installation logic
ipcMain.handle('install', async (event, options) => {
  const installPath = options.installPath || path.join(process.env.LOCALAPPDATA, 'ArmoryX');
  
  try {
    // Create directories
    await fs.ensureDir(installPath);
    await fs.ensureDir(path.join(installPath, 'Resources'));
    await fs.ensureDir(path.join(installPath, 'Data'));
    await fs.ensureDir(path.join(installPath, 'Logs'));
    
    // Copy files (in production, these would be bundled)
    const sourceFiles = path.join(__dirname, '../payload');
    if (fs.existsSync(sourceFiles)) {
      await fs.copy(sourceFiles, installPath);
    }
    
    // Create uninstaller
    const uninstallerContent = `
@echo off
echo Uninstalling Armory X...
rmdir /s /q "${installPath}"
reg delete "HKCU\\Software\\ArmoryX" /f
del "%USERPROFILE%\\Desktop\\Armory X.lnk" /f
rmdir /s /q "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Armory X"
echo Uninstall complete.
pause
del "%~f0"
`;
    await fs.writeFile(path.join(installPath, 'Uninstall.bat'), uninstallerContent);
    
    // Create shortcuts if requested
    if (options.createDesktopShortcut) {
      // In production, use windows-shortcuts package
      console.log('Creating desktop shortcut...');
    }
    
    if (options.createStartMenuShortcut) {
      console.log('Creating start menu shortcut...');
    }
    
    // Registry entries
    if (process.platform === 'win32') {
      exec(`reg add "HKCU\\Software\\ArmoryX" /v "InstallPath" /d "${installPath}" /f`);
    }
    
    return { success: true, message: 'Installation complete!' };
  } catch (error) {
    return { success: false, message: error.message };
  }
});

// Get default install path
ipcMain.handle('get-default-path', () => {
  return path.join(process.env.LOCALAPPDATA, 'ArmoryX');
});

// Keep app running until user closes
ipcMain.on('keep-alive', () => {
  // This prevents the app from closing prematurely
}); 