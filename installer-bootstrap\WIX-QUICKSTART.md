# Armory X WiX Installer - Quick Start Guide

## ✅ You Now Have a Working MSI Installer!

Your `ArmoryX-Setup.msi` installer is ready to use. This is a professional Windows installer that:

- Installs to `%LOCALAPPDATA%\ArmoryX` (no admin required)
- Creates desktop shortcut
- Creates Start Menu entries
- Shows in Add/Remove Programs
- Supports clean uninstall
- No window closing issues! 🎉

## Test Your Installer

### Install:
```powershell
msiexec /i ArmoryX-Setup.msi
```

### Silent Install (no UI):
```powershell
msiexec /i ArmoryX-Setup.msi /quiet
```

### Uninstall:
```powershell
msiexec /x ArmoryX-Setup.msi
```

## What We Created

1. **armory-x-simple.wxs** - The installer configuration
2. **ArmoryX-Setup.msi** - Your installer (315KB)
3. **build-simple.ps1** - Build script

## Next Steps

### 1. Replace the Placeholder EXE
Currently using a placeholder. To use your real application:
```powershell
# Copy your actual exe to payload folder
Copy-Item "..\armory-x\target\release\armory-x.exe" "payload\ArmoryX.exe"

# Rebuild
.\build-simple.ps1
```

### 2. Add Custom Graphics
Create these images for a professional look:
- `banner.bmp` (493x58 pixels) - Top banner
- `dialog.bmp` (493x312 pixels) - Side image

### 3. Test Installation Flow
1. Double-click `ArmoryX-Setup.msi`
2. Choose install location
3. Click Install
4. Check desktop shortcut works
5. Check Start Menu entries
6. Test uninstall from Add/Remove Programs

## Why WiX is Better Than Tauri for Installers

- **Rock-solid window management** - No issues with closing
- **Native Windows experience** - Users trust MSI files
- **Fast** - Builds in seconds, not minutes
- **IT-friendly** - Can be deployed via Group Policy
- **No runtime needed** - Works on any Windows PC

## Common Commands

```powershell
# Build installer
.\build-simple.ps1

# Install with logging (for debugging)
msiexec /i ArmoryX-Setup.msi /l*v install.log

# Repair installation
msiexec /f ArmoryX-Setup.msi

# Change installation (add/remove features)
msiexec /i ArmoryX-Setup.msi ADDLOCAL=ALL
```

## Troubleshooting

**"File not found" errors:**
- Make sure all files in `payload\` folder exist
- Check paths in .wxs file

**Installation fails:**
- Run with logging: `msiexec /i ArmoryX-Setup.msi /l*v install.log`
- Check the log file for errors

**Can't run PowerShell scripts:**
- Already fixed with: `Set-ExecutionPolicy RemoteSigned`

---

Congratulations! You now have a professional Windows installer without the Tauri window management headaches! 🚀 