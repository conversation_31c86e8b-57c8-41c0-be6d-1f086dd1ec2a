// Debug Script for License System Issues
// Run this in browser console to diagnose problems

// Ensure DOM is loaded before defining the function
document.addEventListener('DOMContentLoaded', function() {
    window.debugLicenseSystem = async function() {
    console.log('🔍 === LICENSE SYSTEM DEBUG ===');
    
    // 1. Check Firebase initialization
    console.log('1️⃣ Checking Firebase initialization...');
    if (!window.firebase) {
        console.error('❌ Firebase not initialized');
        return;
    }
    console.log('✅ Firebase object available');
    
    // 2. Check authentication
    console.log('2️⃣ Checking authentication...');
    const user = window.firebase.auth.currentUser;
    if (!user) {
        console.error('❌ User not authenticated');
        return;
    }
    console.log('✅ User authenticated:', user.email);
    console.log('👤 User ID:', user.uid);
    
    // 3. Check user document and role
    console.log('3️⃣ Checking user role...');
    try {
        const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', user.uid));
        if (!userDoc.exists()) {
            console.error('❌ User document does not exist in users collection');
            console.log('🛠️ Creating user document with admin role...');
            
            // Create user document with admin role
            await window.firebase.setDoc(window.firebase.doc(window.firebase.db, 'users', user.uid), {
                role: 'admin',
                email: user.email,
                displayName: user.displayName || 'Admin User',
                createdAt: window.firebase.serverTimestamp()
            }, { merge: true });
            
            console.log('✅ User document created with admin role');
        } else {
            const userData = userDoc.data();
            console.log('📄 User document exists:', userData);
            console.log('🔑 User role:', userData.role);
            
            if (userData.role !== 'admin') {
                console.warn('⚠️ User role is not admin, updating...');
                await window.firebase.updateDoc(window.firebase.doc(window.firebase.db, 'users', user.uid), {
                    role: 'admin'
                });
                console.log('✅ User role updated to admin');
            } else {
                console.log('✅ User has admin role');
            }
        }
    } catch (error) {
        console.error('❌ Error checking user document:', error);
        console.log('🔍 Error details:', error.code, error.message);
    }
    
    // 4. Test license_keys collection access
    console.log('4️⃣ Testing license_keys collection access...');
    try {
        // Try to read license_keys collection
        const keysQuery = window.firebase.query(
            window.firebase.collection(window.firebase.db, 'license_keys'),
            window.firebase.limit(1)
        );
        const snapshot = await window.firebase.getDocs(keysQuery);
        console.log('✅ Can read license_keys collection, found', snapshot.size, 'documents');
    } catch (error) {
        console.error('❌ Cannot read license_keys collection:', error);
        console.log('🔍 This suggests security rules are not applied correctly');
    }
    
    // 5. Test creating a document in license_keys collection
    console.log('5️⃣ Testing license key creation...');
    try {
        const testKey = {
            keyValue: 'TEST-1234-**************',
            userId: null,
            hwid: null,
            status: 'unused',
            createdAt: window.firebase.serverTimestamp(),
            activatedAt: null,
            expiresAt: null,
            keyType: 'trial',
            productType: 'standard',
            lastValidated: null,
            activationCount: 0,
            maxActivations: 1,
            createdBy: user.uid,
            purchaseInfo: {
                orderId: 'DEBUG_TEST',
                amount: 0,
                currency: 'USD',
                paymentMethod: 'debug',
                purchaseDate: window.firebase.serverTimestamp()
            }
        };
        
        const testDocRef = await window.firebase.addDoc(window.firebase.collection(window.firebase.db, 'license_keys'), testKey);
        console.log('✅ Successfully created test license key:', testDocRef.id);
        
        // Clean up test key
        await window.firebase.deleteDoc(testDocRef);
        console.log('🧹 Test key cleaned up');
        
    } catch (error) {
        console.error('❌ Cannot create license key:', error);
        console.log('🔍 Error code:', error.code);
        console.log('🔍 Error message:', error.message);
        
        if (error.code === 'permission-denied') {
            console.log('🛠️ SOLUTION: Update your Firebase security rules!');
            console.log('📋 Copy the rules from firebase-security-rules.md to Firebase Console');
        }
    }
    
    // 6. Check network connectivity
    console.log('6️⃣ Checking network connectivity...');
    try {
        const response = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', { method: 'HEAD' });
        if (response.ok) {
            console.log('✅ Network connectivity to Firebase CDN is working');
        } else {
            console.warn('⚠️ Network connectivity issues detected');
        }
    } catch (error) {
        console.error('❌ Network connectivity test failed:', error);
        console.log('🔍 This might be caused by ad blockers or firewall');
    }
    
    console.log('🏁 === DEBUG COMPLETE ===');
    console.log('');
    console.log('💡 NEXT STEPS:');
    console.log('1. Make sure Firebase security rules are applied (check firebase-security-rules.md)');
    console.log('2. Disable ad blockers temporarily');
    console.log('3. Try refreshing the page and testing again');
    console.log('4. If still failing, check Firebase Console for security rule errors');
    };

    console.log('🔧 Debug script loaded! Run window.debugLicenseSystem() to diagnose issues');
});

// Also make it available immediately for direct console use
window.debugLicenseSystem = async function() {
    console.log('🔍 === LICENSE SYSTEM DEBUG ===');
    
    // 1. Check Firebase initialization
    console.log('1️⃣ Checking Firebase initialization...');
    if (!window.firebase) {
        console.error('❌ Firebase not initialized');
        return;
    }
    console.log('✅ Firebase object available');
    
    // 2. Check authentication
    console.log('2️⃣ Checking authentication...');
    const user = window.firebase.auth.currentUser;
    if (!user) {
        console.error('❌ User not authenticated');
        return;
    }
    console.log('✅ User authenticated:', user.email);
    
    // 3. Check user document and role
    console.log('3️⃣ Checking user role...');
    try {
        const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', user.uid));
        if (userDoc.exists()) {
            const userData = userDoc.data();
            console.log('📄 User role:', userData.role);
        } else {
            console.log('❌ User document does not exist');
        }
    } catch (error) {
        console.error('❌ Error checking user:', error);
    }
    
    // 4. Test license key creation
    console.log('4️⃣ Testing license key creation...');
    if (window.LicenseKeyManager) {
        try {
            const manager = new window.LicenseKeyManager();
            await manager.init();
            const result = await manager.createLicenseKey('trial');
            if (result.success) {
                console.log('✅ Successfully generated license key:', result.licenseKey);
            } else {
                console.error('❌ Failed to generate license key:', result.error);
            }
        } catch (error) {
            console.error('❌ Error with LicenseKeyManager:', error);
        }
    } else {
        console.error('❌ LicenseKeyManager not available');
    }
}; 