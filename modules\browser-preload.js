/**
 * Simplified <PERSON>rowser Preload Script for Armory X
 * Injects navigation bar UI into browser window (no context isolation)
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');

console.log('🎯 Armory X browser preload script loaded');

// Wait for DOM to be ready and inject navigation bar
function injectNavigationBar() {
  console.log('🚀 Injecting Armory X navigation bar...');
  
  const navBarHtml = `
    <div id="armory-nav-bar" style="
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      height: 50px !important;
      background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
      border-bottom: 2px solid rgba(59, 130, 246, 0.3) !important;
      display: flex !important;
      align-items: center !important;
      padding: 0 12px !important;
      gap: 8px !important;
      z-index: 999998 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
      pointer-events: auto !important;
    ">
      <!-- Navigation Controls -->
      <div style="display: flex; gap: 8px;">
                 <button id="armory-back-btn" style="
          background: rgba(59, 130, 246, 0.2);
          border: 1px solid rgba(59, 130, 246, 0.3);
          color: white;
          padding: 5px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s ease;
          min-width: 45px;
        " title="Go Back (Alt+Left)">
          ← Back
        </button>
        
        <button id="armory-forward-btn" style="
          background: rgba(59, 130, 246, 0.2);
          border: 1px solid rgba(59, 130, 246, 0.3);
          color: white;
          padding: 5px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s ease;
          min-width: 55px;
        " title="Go Forward (Alt+Right)">
          Forward →
        </button>
        
        <button id="armory-refresh-btn" style="
          background: rgba(34, 197, 94, 0.2);
          border: 1px solid rgba(34, 197, 94, 0.3);
          color: white;
          padding: 5px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s ease;
          min-width: 50px;
        " title="Refresh (F5)">
          🔄
        </button>
      </div>
      
             <!-- Address Bar -->
       <div style="flex: 1; margin: 0 10px;">
         <input id="armory-address-bar" type="text" value="${window.location.href}" style="
           width: 100%;
           background: rgba(255, 255, 255, 0.1);
           border: 1px solid rgba(255, 255, 255, 0.2);
           color: white;
           padding: 5px 8px;
           border-radius: 4px;
           font-size: 12px;
           transition: all 0.2s ease;
           height: 30px;
         " placeholder="Enter URL...">
       </div>
      
             <!-- Quick Navigation -->
       <div style="display: flex; gap: 4px;">
         <button id="armory-modrinth-btn" style="
           background: rgba(168, 85, 247, 0.2);
           border: 1px solid rgba(168, 85, 247, 0.3);
           color: white;
           padding: 4px 6px;
           border-radius: 4px;
           cursor: pointer;
           font-size: 10px;
           min-width: 65px;
         " title="Go to Modrinth">
           📦 Modrinth
         </button>
         
         <button id="armory-curseforge-btn" style="
           background: rgba(168, 85, 247, 0.2);
           border: 1px solid rgba(168, 85, 247, 0.3);
           color: white;
           padding: 4px 6px;
           border-radius: 4px;
           cursor: pointer;
           font-size: 10px;
           min-width: 70px;
         " title="Go to CurseForge">
           🔥 CurseForge
         </button>
         

       </div>
    </div>
    
    <style>
      #armory-nav-bar button:hover {
        background: rgba(59, 130, 246, 0.4) !important;
        transform: translateY(-1px);
      }
      
      #armory-nav-bar button:active {
        transform: translateY(0);
      }
      
      #armory-nav-bar button:disabled {
        opacity: 0.4 !important;
        cursor: not-allowed !important;
        transform: none !important;
      }
      
      #armory-address-bar:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.5) !important;
        background: rgba(255, 255, 255, 0.15) !important;
      }
      
             /* Reliable approach - use body padding with high z-index */
       html {
         padding-top: 0 !important;
         margin-top: 0 !important;
       }
       
       body {
         padding-top: 50px !important;
         margin-top: 0 !important;
         box-sizing: border-box !important;
       }
       
       /* Keep our navigation bar on top but allow website dropdowns to work */
       #armory-nav-bar {
         position: fixed !important;
         top: 0 !important;
         left: 0 !important;
         right: 0 !important;
         z-index: 10000 !important; /* High but not maximum */
         pointer-events: auto !important;
       }
       
              /* Handle common problematic elements */
       body > header:not(#armory-nav-bar),
       body > nav:not(#armory-nav-bar),
       body > .header,
       body > .navbar,
       body > .top-bar {
         margin-top: 4px !important;
       }
       
       /* Target website navigation elements with minimal interference */
       body > header:not(#armory-nav-bar),
       body > nav:not(#armory-nav-bar),
       header:not(#armory-nav-bar),
       nav:not(#armory-nav-bar),
       [role="banner"]:not(#armory-nav-bar),
       [data-testid*="header"]:not(#armory-nav-bar) {
         margin-top: 50px !important;
         /* Don't override position, z-index, or other layout properties */
       }
       
       /* Additional spacing for main content areas */
       body > main:not(#armory-nav-bar),
       body > .main:not(#armory-nav-bar),
       body > #main:not(#armory-nav-bar) {
         padding-top: 2px !important;
       }
       
       /* Ensure website dropdowns can appear above our nav bar */
       [role="dialog"], 
       .modal, 
       .dropdown-menu,
       .popover,
       .tooltip,
       [class*="dropdown"],
       [class*="menu"],
       [data-testid*="dropdown"],
       [data-testid*="menu"] {
         z-index: 10001 !important;
       }
    </style>
  `;
  
  // Inject navigation bar
  if (document.body) {
    document.body.insertAdjacentHTML('afterbegin', navBarHtml);
    console.log('✅ Navigation bar injected successfully');
    
    // Set up event handlers
    setupEventHandlers();
    
    // Update URL periodically  
    setInterval(updateAddressBar, 1000);
    
    // Set up keyboard shortcuts
    setupKeyboardShortcuts();
    
  } else {
    console.log('⏳ Body not ready, retrying...');
    setTimeout(injectNavigationBar, 100);
  }
}

function setupEventHandlers() {
  console.log('🔧 Setting up navigation event handlers...');
  
  // Back button
  const backBtn = document.getElementById('armory-back-btn');
  if (backBtn) {
    backBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('browser-go-back');
        if (!result.success) {
          history.back();
        }
      } catch (error) {
        console.log('Using fallback back navigation');
        history.back();
      }
    });
  }
  
  // Forward button
  const forwardBtn = document.getElementById('armory-forward-btn');
  if (forwardBtn) {
    forwardBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('browser-go-forward');
        if (!result.success) {
          history.forward();
        }
      } catch (error) {
        console.log('Using fallback forward navigation');
        history.forward();
      }
    });
  }
  
  // Refresh button
  const refreshBtn = document.getElementById('armory-refresh-btn');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', async () => {
      try {
        const result = await ipcRenderer.invoke('browser-refresh');
        if (!result.success) {
          location.reload();
        }
      } catch (error) {
        console.log('Using fallback refresh');
        location.reload();
      }
    });
  }
  
  // Address bar
  const addressBar = document.getElementById('armory-address-bar');
  if (addressBar) {
    addressBar.addEventListener('keypress', async (e) => {
      if (e.key === 'Enter') {
        let url = e.target.value.trim();
        if (url) {
          // Add protocol if missing
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          
          try {
            const result = await ipcRenderer.invoke('browser-navigate', url);
            if (!result.success) {
              window.location.href = url;
            }
          } catch (error) {
            console.log('Using fallback navigation');
            window.location.href = url;
          }
        }
      }
    });
  }
  
  // Quick navigation buttons
  const modrinthBtn = document.getElementById('armory-modrinth-btn');
  if (modrinthBtn) {
    modrinthBtn.addEventListener('click', () => navigateToSite('https://modrinth.com/mods'));
  }
  
  const curseforgeBtn = document.getElementById('armory-curseforge-btn');
  if (curseforgeBtn) {
    curseforgeBtn.addEventListener('click', () => navigateToSite('https://www.curseforge.com/minecraft/mc-mods'));
  }
  

  
  console.log('✅ Event handlers set up successfully');
}

async function navigateToSite(url) {
  try {
    const result = await ipcRenderer.invoke('browser-navigate', url);
    if (!result.success) {
      window.location.href = url;
    }
  } catch (error) {
    console.log('Using fallback navigation to:', url);
    window.location.href = url;
  }
}

function updateAddressBar() {
  const addressBar = document.getElementById('armory-address-bar');
  if (addressBar && addressBar !== document.activeElement) {
    addressBar.value = window.location.href;
  }
  
  // Update button states
  updateButtonStates();
}

async function updateButtonStates() {
  try {
    const canGoBack = await ipcRenderer.invoke('browser-can-go-back');
    const canGoForward = await ipcRenderer.invoke('browser-can-go-forward');
    
    const backBtn = document.getElementById('armory-back-btn');
    const forwardBtn = document.getElementById('armory-forward-btn');
    
    if (backBtn) {
      backBtn.disabled = !canGoBack;
    }
    
    if (forwardBtn) {
      forwardBtn.disabled = !canGoForward;
    }
  } catch (error) {
    // Fallback button state management
    const backBtn = document.getElementById('armory-back-btn');
    const forwardBtn = document.getElementById('armory-forward-btn');
    
    if (backBtn) {
      backBtn.disabled = window.history.length <= 1;
    }
    
    if (forwardBtn) {
      forwardBtn.disabled = false; // Can't easily detect forward state
    }
  }
}

function setupKeyboardShortcuts() {
  document.addEventListener('keydown', (e) => {
    // Alt+Left - Back
    if (e.altKey && e.key === 'ArrowLeft') {
      e.preventDefault();
      const backBtn = document.getElementById('armory-back-btn');
      if (backBtn && !backBtn.disabled) {
        backBtn.click();
      }
    }
    
    // Alt+Right - Forward  
    else if (e.altKey && e.key === 'ArrowRight') {
      e.preventDefault();
      const forwardBtn = document.getElementById('armory-forward-btn');
      if (forwardBtn && !forwardBtn.disabled) {
        forwardBtn.click();
      }
    }
    
    // F5 - Refresh
    else if (e.key === 'F5') {
      e.preventDefault();
      const refreshBtn = document.getElementById('armory-refresh-btn');
      if (refreshBtn) {
        refreshBtn.click();
      }
    }
    
    // Ctrl+L - Focus address bar
    else if (e.ctrlKey && e.key.toLowerCase() === 'l') {
      e.preventDefault();
      const addressBar = document.getElementById('armory-address-bar');
      if (addressBar) {
        addressBar.focus();
        addressBar.select();
      }
    }
  });
}

// Aggressive injection to minimize disappearing during navigation
function tryInject() {
  if (!document.getElementById('armory-nav-bar')) {
    if (document.body) {
      injectNavigationBar();
    } else if (document.documentElement) {
      // Inject into html element if body isn't ready yet
      console.log('📍 Body not ready, injecting into document element...');
      // Remove conflicting fallback padding - main injection handles this
      setTimeout(tryInject, 50);
    } else {
      setTimeout(tryInject, 50);
    }
  }
}

// Immediate injection attempt
tryInject();

// Try injection on document state changes
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', tryInject);
} else {
  tryInject();
}

// Aggressive monitoring for navigation changes
let lastUrl = window.location.href;
const urlCheckInterval = setInterval(() => {
  if (window.location.href !== lastUrl) {
    lastUrl = window.location.href;
    console.log('🔄 URL changed, ensuring navigation bar exists...');
    setTimeout(tryInject, 10); // Almost immediate re-injection
  }
  
  // Also check if navigation bar still exists
  if (!document.getElementById('armory-nav-bar')) {
    console.log('🚨 Navigation bar missing, re-injecting...');
    tryInject();
  }
}, 100); // Check every 100ms

// Listen for various navigation events
window.addEventListener('beforeunload', () => {
  console.log('🔄 Page unloading, will re-inject on next page...');
});

window.addEventListener('load', () => {
  setTimeout(tryInject, 10);
});

// Monitor for DOM changes that might remove our navigation bar
const observer = new MutationObserver((mutations) => {
  let needsReinjection = false;
  
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList') {
      mutation.removedNodes.forEach((node) => {
        if (node.id === 'armory-nav-bar') {
          needsReinjection = true;
        }
      });
    }
  });
  
  if (needsReinjection) {
    console.log('🚨 Navigation bar was removed by website, re-injecting...');
    setTimeout(tryInject, 10);
  }
});

// Start observing once we have a document
if (document.body) {
  observer.observe(document.body, { childList: true, subtree: true });
} else {
  document.addEventListener('DOMContentLoaded', () => {
    observer.observe(document.body, { childList: true, subtree: true });
  });
}

console.log('🎯 Armory X preload script initialization complete'); 