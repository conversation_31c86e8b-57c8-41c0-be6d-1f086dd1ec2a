const { app, BrowserWindow, ipcMain, shell, dialog, Menu, screen } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { exec, spawn } = require('child_process');
const si = require('systeminformation');
const { machineIdSync } = require('node-machine-id');
const chokidar = require('chokidar');
const glob = require('glob');

// Import utility functions
const { formatBytes } = require('./modules/utils');
const { SystemInfo } = require('./modules/system-info');
const { FileOperations } = require('./modules/file-operations');
const { SettingsManager } = require('./modules/settings-manager');
const { WindowManager } = require('./modules/window-manager');
const { ModManager } = require('./modules/mod-manager');

// Initialize module instances
const systemInfo = new SystemInfo();
const fileOps = new FileOperations();
const settingsManager = new SettingsManager();
const windowManager = new WindowManager({ app, isDev, APP_NAME });
const modManager = new ModManager({ systemInfo });

let mainWindow;

// App configuration
const isDev = process.argv.includes('--dev');
const APP_NAME = 'Armory X';

function createWindow() {
  mainWindow = windowManager.createWindow();
  
  // Update file operations with main window reference
  fileOps.setMainWindow(mainWindow);
  
  return mainWindow;
}

// App event handlers
app.whenReady().then(() => {
  console.log(`🚀 ${APP_NAME} starting...`);
  console.log(`📍 Platform: ${process.platform}`);
  console.log(`📍 Electron: ${process.versions.electron}`);
  console.log(`📍 Node: ${process.versions.node}`);
  console.log(`📍 Development mode: ${isDev}`);

  createWindow();
  windowManager.setupMenu();
  windowManager.registerWindowControlHandlers();
  
  console.log('✅ App initialization complete');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Window management functions moved to modules/window-manager.js

// Test connection handler for debugging
ipcMain.handle('test-connection', async () => {
  return { success: true, message: 'IPC connection working' };
});

// System information handlers
ipcMain.handle('get-system-info', systemInfo.getSystemInfo.bind(systemInfo));

ipcMain.handle('get-system-stats', systemInfo.getSystemStats.bind(systemInfo));

// Cleanup functionality
ipcMain.handle('clean-junk-files', fileOps.cleanJunkFiles.bind(fileOps));

ipcMain.handle('get-cleanup-progress', fileOps.getCleanupProgress.bind(fileOps));

// All mod management functions moved to modules/mod-manager.js

// getModCategory function moved to modules/mod-manager.js

// Enhanced mod folder opening with multiple path support
ipcMain.handle('open-mod-folder', async (event, gameId) => {
  console.log(`📁 Opening mod folder for: ${gameId}`);
  
  // Check if this is a custom game
  if (gameId && gameId.startsWith('custom_')) {
    console.log(`🎮 Opening mod folder for custom game: ${gameId}`);
    
    // Get custom game data from renderer
    const customGameData = await event.sender.executeJavaScript(`
      customGames.find(game => game.id === '${gameId}')
    `).catch(() => null);
    
    if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
      console.log(`❌ No custom game data or mod folders found for ${gameId}`);
      return { success: false, message: 'No mod folders configured for this custom game' };
    }
    
    // Try to open the first available mod folder
    for (const modFolder of customGameData.modFolders) {
      const modPath = modFolder.path;
      console.log(`   Trying to open: ${modPath}`);
      
      try {
        if (await fs.pathExists(modPath)) {
          shell.openPath(modPath);
          console.log(`✅ Opened custom game mod folder: ${modPath}`);
          return { success: true, path: modPath };
        }
      } catch (error) {
        console.log(`❌ Error opening path ${modPath}:`, error.message);
      }
    }
    
    // If no folder exists, try to create the first one
    const primaryModFolder = customGameData.modFolders[0];
    if (primaryModFolder) {
      try {
        await fs.ensureDir(primaryModFolder.path);
        shell.openPath(primaryModFolder.path);
        console.log(`✅ Created and opened custom game mod folder: ${primaryModFolder.path}`);
        return { success: true, path: primaryModFolder.path, created: true };
      } catch (error) {
        console.log(`❌ Error creating folder ${primaryModFolder.path}:`, error.message);
        return { success: false, message: `Failed to create mod folder: ${error.message}` };
      }
    }
    
    return { success: false, message: 'No valid mod folder paths found for this custom game' };
  }
  
  // Handle preset games with proper path detection
  let possiblePaths = [];
  
  if (gameId === 'schedule1') {
    // For Schedule I, use the same logic as scanning and installation
    const availableDrives = await getAllDrives();
    
    // Generate Schedule 1 paths for all drives (Steam locations)
    for (const drive of availableDrives) {
      possiblePaths.push(
        path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
      );
    }
    
    // Add fallback to Documents (but it should be last priority)
    possiblePaths.push(
      path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
      path.join(os.homedir(), 'Documents', 'Schedule 1')
    );
  } else {
    // Handle other preset games
    const gameModPaths = {
      minecraft: [
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft')
      ],
      fs22: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022')
      ]
    };
    
    possiblePaths = gameModPaths[gameId] || [];
  }
  
  // Try to find an existing path
  for (const modPath of possiblePaths) {
    try {
      if (await fs.pathExists(modPath)) {
        shell.openPath(modPath);
        console.log(`✅ Opened folder: ${modPath}`);
        return { success: true, path: modPath };
      }
    } catch (error) {
      console.log(`❌ Error opening path ${modPath}:`, error.message);
    }
  }
  
  // If no path exists, create the primary mod directory
  const primaryPath = possiblePaths[0];
  if (primaryPath) {
    try {
      await fs.ensureDir(primaryPath);
      shell.openPath(primaryPath);
      console.log(`✅ Created and opened folder: ${primaryPath}`);
      return { success: true, path: primaryPath, created: true };
    } catch (error) {
      console.log(`❌ Error creating folder ${primaryPath}:`, error.message);
      return { success: false, message: `Failed to create mod folder: ${error.message}` };
    }
  }
  
  return { success: false, message: 'No valid mod paths configured for this game' };
});

// Add a new handler to get mod directory info
ipcMain.handle('get-mod-directories', async (event, gameId) => {
  const gameModPaths = {
    minecraft: [
      path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      path.join(os.homedir(), 'curseforge', 'minecraft', 'Instances'),
      path.join(os.homedir(), 'AppData', 'Roaming', 'PrismLauncher', 'instances')
    ],
    fs22: [
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods')
    ],
    schedule1: [
      path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
      path.join(os.homedir(), 'AppData', 'LocalLow', 'Schedule 1', 'mods')
    ]
  };
  
  const result = {};
  
  if (gameId) {
    // Get info for specific game
    const paths = gameModPaths[gameId] || [];
    result[gameId] = {
      paths: paths,
      existing: []
    };
    
    for (const modPath of paths) {
      try {
        if (await fs.pathExists(modPath)) {
          result[gameId].existing.push(modPath);
        }
      } catch (error) {
        console.log(`Error checking path ${modPath}:`, error.message);
      }
    }
  } else {
    // Get info for all games
    for (const [game, paths] of Object.entries(gameModPaths)) {
      result[game] = {
        paths: paths,
        existing: []
      };
      
      for (const modPath of paths) {
        try {
          if (await fs.pathExists(modPath)) {
            result[game].existing.push(modPath);
          }
        } catch (error) {
          console.log(`Error checking path ${modPath}:`, error.message);
        }
      }
    }
  }
  
  return result;
});

// Add handler to create test mods for testing
ipcMain.handle('create-test-mods', async (event, gameId) => {
  console.log(`🧪 Creating test mods for: ${gameId}`);
  
  const gameModPaths = {
    minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
    fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
    schedule1: path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods')
  };
  
  const modPath = gameModPaths[gameId];
  if (!modPath) {
    return { success: false, message: 'Invalid game ID' };
  }
  
  try {
    // Ensure the directory exists
    await fs.ensureDir(modPath);
    
    const testMods = {
      minecraft: [
        { name: 'TestMod-OptiFine-HD.jar', size: 2500000 },
        { name: 'JustEnoughItems-1.20.1.jar', size: 1800000 },
        { name: 'BiomesOPlenty-1.20.1.jar', size: 3200000 },
        { name: 'Waila-1.8.26.jar', size: 450000 },
        { name: 'IronChests-1.20.1.jar', size: 890000 }
      ],
      fs22: [
        { name: 'TestTractor-JohnDeere8R.zip', size: 15000000 },
        { name: 'FS22_RealisticVehicles.zip', size: 8500000 },
        { name: 'FS22_SeasonsMod.zip', size: 45000000 }
      ],
      schedule1: [
        { name: 'EnhancedChemistry.zip', size: 25600000 },
        { name: 'BusinessExpansion.zip', size: 18900000 },
        { name: 'SecurityUpgrade.zip', size: 31200000 }
      ]
    };
    
    const modsToCreate = testMods[gameId] || [];
    let createdCount = 0;
    
    for (const mod of modsToCreate) {
      const filePath = path.join(modPath, mod.name);
      
      // Check if file already exists
      if (await fs.pathExists(filePath)) {
        console.log(`   Skipping existing file: ${mod.name}`);
        continue;
      }
      
      // Create a dummy file with some content
      const content = `# Test Mod File: ${mod.name}\n` +
                     `# Created by Armory X for testing purposes\n` +
                     `# Game: ${gameId}\n` +
                     `# Size: ${mod.size} bytes\n` +
                     `# Created: ${new Date().toISOString()}\n\n` +
                     `This is a test mod file created for testing the mod manager functionality.\n` +
                     `${'='.repeat(Math.floor(mod.size / 100))}`;
      
      await fs.writeFile(filePath, content);
      console.log(`   ✅ Created test mod: ${mod.name}`);
      createdCount++;
    }
    
    console.log(`✅ Created ${createdCount} test mods in: ${modPath}`);
    
    return {
      success: true,
      message: `Created ${createdCount} test mods`,
      createdCount,
      skipped: modsToCreate.length - createdCount,
      path: modPath
    };
    
  } catch (error) {
    console.error(`❌ Error creating test mods:`, error);
    return {
      success: false,
      message: `Failed to create test mods: ${error.message}`
    };
  }
});

// File operations
ipcMain.handle('show-in-folder', (event, filePath) => fileOps.showInFolder(filePath));
ipcMain.handle('select-folder', fileOps.selectFolder.bind(fileOps));
ipcMain.handle('select-file', (event, options) => fileOps.selectFile(options));
ipcMain.handle('extract-icon', (event, filePath) => fileOps.extractIcon(filePath));
ipcMain.handle('select-folder-dialog', (event, options) => fileOps.selectFolderDialog(options));

// Settings management
ipcMain.handle('load-settings', settingsManager.loadSettings.bind(settingsManager));
ipcMain.handle('save-settings', (event, settings) => settingsManager.saveSettings(settings));

// License management
ipcMain.handle('get-machine-id', systemInfo.getMachineId.bind(systemInfo));

// Mod management handlers
ipcMain.handle('get-mods-for-game', modManager.getModsForGame.bind(modManager));
ipcMain.handle('open-mod-folder', modManager.openModFolder.bind(modManager));
ipcMain.handle('get-mod-directories', modManager.getModDirectories.bind(modManager));
ipcMain.handle('create-test-mods', modManager.createTestMods.bind(modManager));
ipcMain.handle('delete-mod', modManager.deleteMod.bind(modManager));
ipcMain.handle('install-mods-with-categories', modManager.installModsWithCategories.bind(modManager));
ipcMain.handle('install-mods', modManager.installMods.bind(modManager));
ipcMain.handle('add-mods', modManager.addMods.bind(modManager));

// All mod installation functions moved to modules/mod-manager.js

// Utility functions
// formatBytes function moved to modules/utils.js

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('✅ Main process setup complete'); 