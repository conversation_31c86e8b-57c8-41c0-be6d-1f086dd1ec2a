/* ===================================
   Steam-like Game Manager
   =================================== */

/* Steam-like Game Cards */
.steam-game-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    backdrop-filter: blur(10px);
}

.steam-game-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);
}

.steam-game-card.preset-game:hover {
    border-color: rgba(16, 185, 129, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(16, 185, 129, 0.2);
}

.steam-game-card.custom-game:hover {
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(139, 92, 246, 0.2);
}

/* Game Image Container */
.game-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #1e293b, #0f172a);
}

.game-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.steam-game-card:hover .game-cover {
    transform: scale(1.05);
}

/* Small icon handling with gradient background */
.game-image-container.small-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(29, 78, 216, 0.1) 100%);
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.game-image-container.small-icon .game-cover {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.steam-game-card:hover .game-image-container.small-icon .game-cover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59,130,246,0.4);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.steam-game-card:hover .game-overlay {
    opacity: 1;
}

.game-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 2;
}

.game-type-badge.preset {
    background: rgba(16, 185, 129, 0.9);
    color: white;
}

.game-type-badge.custom {
    background: rgba(139, 92, 246, 0.9);
    color: white;
}

/* Game Info Section */
.game-info {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95), rgba(15, 23, 42, 0.95));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.game-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.game-description {
    font-size: 0.85rem;
    color: #94a3b8;
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.game-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #64748b;
}

.last-played {
    font-weight: 500;
}

.mod-count {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
}

/* Edit Button for Custom Games */
.edit-button-pro {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: rgba(139, 92, 246, 0.9);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.4rem 0.6rem;
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 3;
}

/* Show edit button on hover for custom games */
.steam-game-card:not(.preset-game):not(.delete-mode):hover .edit-button-pro {
    opacity: 1;
    transform: translateY(0);
}

/* Remove Mode - Full Card Red Highlight with Center Trash Icon */
.steam-game-card.delete-mode {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.5);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.15), rgba(185, 28, 28, 0.1));
    position: relative;
    overflow: hidden;
    cursor: pointer;
    animation: deleteModePulse 2s ease-in-out infinite;
}

.steam-game-card.delete-mode::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(220, 38, 38, 0.1) 0%, 
        rgba(220, 38, 38, 0.05) 25%, 
        rgba(220, 38, 38, 0.1) 50%, 
        rgba(220, 38, 38, 0.05) 75%, 
        rgba(220, 38, 38, 0.1) 100%);
    background-size: 40px 40px;
    animation: deleteStripes 3s linear infinite;
    z-index: 1;
}

.steam-game-card.delete-mode:hover {
    border-color: #b91c1c;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.7);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.25), rgba(185, 28, 28, 0.15));
    transform: translateY(-4px);
}

/* Center trash icon overlay for remove mode */
.delete-mode-center-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.steam-game-card.delete-mode:hover .delete-mode-center-overlay {
    opacity: 1;
}

.delete-icon-large {
    font-size: 3rem;
    color: #f87171;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    animation: deleteIconBounce 1s ease-in-out infinite;
}

@keyframes deleteModePulse {
    0%, 100% { 
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.5); 
    }
    50% { 
        box-shadow: 0 0 0 6px rgba(220, 38, 38, 0.3); 
    }
}

@keyframes deleteStripes {
    0% { background-position: 0 0; }
    100% { background-position: 40px 40px; }
}

@keyframes deleteIconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Red styling for game info in delete mode */
.steam-game-card.delete-mode .game-title {
    color: #f87171 !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .game-description {
    color: #fca5a5 !important;
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .last-played {
    color: #fecaca !important;
    position: relative;
    z-index: 2;
}

/* Global delete mode styling */
body.delete-mode-active .steam-game-card.preset-game {
    opacity: 0.4;
    pointer-events: none;
    filter: grayscale(0.7);
    transition: all 0.3s ease;
}

body.delete-mode-active .steam-game-card.preset-game .game-title {
    color: #6b7280 !important;
}

body.delete-mode-active .steam-game-card.preset-game .game-description {
    color: #9ca3af !important;
}

/* Steam and Xbox Game Confirmation Dialogs */
.steam-confirmation, .xbox-confirmation {
    text-align: center;
    padding: 1rem;
}

.confirmation-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.steam-confirmation h3, .xbox-confirmation h3 {
    color: #3b82f6;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.steam-options, .xbox-info {
    margin: 1.5rem 0;
}

.option-explanation {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: left;
}

.option-explanation h4 {
    color: #3b82f6;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.option-explanation p {
    color: #94a3b8;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* Game Grid Layout */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .steam-game-card {
        transform: none;
    }
    
    .steam-game-card:hover {
        transform: translateY(-4px);
    }
    
    .game-image-container {
        height: 150px;
    }
    
    .game-info {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1rem;
    }
    
    .game-description {
        font-size: 0.8rem;
    }
}
