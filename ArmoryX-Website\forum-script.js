// Enhanced Forum System with Firebase Integration
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Firebase initialization before setting up forum
    waitForFirebaseInit().then(() => {
        initializeForum();
        // Set up retry mechanism for authentication detection
        setupAuthRetryMechanism();
    }).catch(error => {
        console.warn('Firebase initialization failed, continuing with fallback mode:', error);
        initializeForum();
        // Set up retry mechanism for authentication detection
        setupAuthRetryMechanism();
    });
});

// Wait for Firebase to be initialized (or fail) - same function as main script
function waitForFirebaseInit() {
    return new Promise((resolve) => {
        // Check if Firebase is already available
        if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
            console.log('🏎️ Firebase already available for forums, proceeding immediately');
            resolve();
            return;
        }
        
        let attempts = 0;
        const maxAttempts = 150; // 15 seconds at 100ms intervals
        
        const checkInterval = setInterval(() => {
            attempts++;
            
            // Check for either firebaseInitialized flag OR firebase object
            if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
                clearInterval(checkInterval);
                console.log(`✅ Firebase ready for forums after ${attempts * 100}ms`);
                resolve();
                return;
            }
            
            // Timeout after max attempts
            if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.log('🕒 Firebase initialization timeout in forums - proceeding without Firebase');
                console.log('⚠️ Forums will use fallback authentication and mock data');
                window.firebase = null;
                resolve();
            }
        }, 100);
    });
}

function initializeForum() {
    console.log('🚀 Initializing forum with authentication check...');
    
    // Check authentication status with improved detection
    if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
        console.log('🔥 Forums using Firebase authentication');
        const { auth, onAuthStateChanged } = window.firebase;
        
        // First check if there's already a current user
        const currentUser = auth.currentUser;
        if (currentUser) {
            console.log('✅ User already authenticated in forums:', currentUser.email);
            showForumContent(currentUser);
            startForumStatsTracking();
        }
        
        // Then set up the listener for auth state changes
        onAuthStateChanged(auth, (user) => {
            console.log('🔄 Auth state changed in forums:', user ? user.email : 'No user');
            if (user) {
                console.log('✅ User authenticated in forums:', user.email);
                showForumContent(user);
                startForumStatsTracking();
            } else {
                console.log('❌ User not authenticated, checking fallback...');
                // Check fallback auth before showing login required
                const isLoggedIn = checkLegacyAuth();
                if (isLoggedIn) {
                    console.log('✅ Fallback authentication successful');
                    showForumContent();
                    startForumStatsTracking();
                } else {
                    console.log('❌ No authentication found, showing login required');
                    showLoginRequired();
                }
            }
        });
    } else {
        console.log('🔧 Forums using fallback authentication (Firebase not available)');
        // Fallback authentication check
        const isLoggedIn = checkLegacyAuth();
        if (isLoggedIn) {
            console.log('✅ Fallback authentication successful');
            showForumContent();
            startForumStatsTracking();
        } else {
            console.log('❌ Fallback authentication failed, showing login required');
            showLoginRequired();
        }
    }
    
    setupForumInteractions();
    
    // Initialize enhanced features
    initializeEnhancedFeatures();
}

function checkLegacyAuth() {
    console.log('🔍 Checking legacy authentication...');
    
    // Check multiple possible authentication tokens/sessions
    const checks = [
        localStorage.getItem('armoryX_user_token'),
        localStorage.getItem('armoryX_auth_token'),
        localStorage.getItem('firebase_auth_token'),
        sessionStorage.getItem('armoryX_user_token'),
        sessionStorage.getItem('armoryX_auth_token'),
        sessionStorage.getItem('user_logged_in'),
        localStorage.getItem('user_logged_in'),
        document.cookie.includes('armoryX_session'),
        document.cookie.includes('user_session')
    ];
    
    console.log('🔍 Auth checks:', {
        localStorage_armoryX_user_token: !!localStorage.getItem('armoryX_user_token'),
        localStorage_armoryX_auth_token: !!localStorage.getItem('armoryX_auth_token'),
        localStorage_firebase_auth_token: !!localStorage.getItem('firebase_auth_token'),
        sessionStorage_armoryX_user_token: !!sessionStorage.getItem('armoryX_user_token'),
        sessionStorage_user_logged_in: !!sessionStorage.getItem('user_logged_in'),
        localStorage_user_logged_in: !!localStorage.getItem('user_logged_in'),
        cookies_contain_session: document.cookie.includes('armoryX_session') || document.cookie.includes('user_session')
    });
    
    const isLoggedIn = checks.some(check => check !== null && check !== false);
    console.log('🔍 Legacy auth result:', isLoggedIn);
    
    return isLoggedIn;
}

function showForumContent(user = null) {
    const loginRequired = document.getElementById('login-required');
    const forumContent = document.getElementById('forum-content');
    
    if (loginRequired) loginRequired.style.display = 'none';
    if (forumContent) forumContent.style.display = 'block';
    
    // Load forum data
    loadForumData(user);
    
    // Update user welcome message if user is provided
    if (user) {
        updateUserWelcome(user.email);
        
        // Initialize sample posts if this is a new forum
        setTimeout(() => {
            initializeSamplePosts();
        }, 2000); // Wait 2 seconds for Firebase to be ready
    }
}

function showLoginRequired() {
    const loginRequired = document.getElementById('login-required');
    const forumContent = document.getElementById('forum-content');
    
    if (loginRequired) {
        loginRequired.style.display = 'block';
        
        // Add debug button for troubleshooting
        if (!document.getElementById('debug-access-btn')) {
            const debugBtn = document.createElement('button');
            debugBtn.id = 'debug-access-btn';
            debugBtn.className = 'btn btn-secondary';
            debugBtn.style.marginTop = '1rem';
            debugBtn.innerHTML = '<i class="fas fa-bug"></i> Debug: Force Forum Access';
            debugBtn.onclick = function() {
                console.log('🐛 Debug: Forcing forum access...');
                // Set a demo mode flag to bypass Firebase issues
                localStorage.setItem('armoryX_demo_mode', 'true');
                localStorage.setItem('armoryX_user_token', 'demo_user_' + Date.now());
                showForumContent();
                startForumStatsTracking();
            };
            
            const loginButtons = loginRequired.querySelector('.login-buttons');
            if (loginButtons) {
                loginButtons.appendChild(debugBtn);
            }
        }
        
        // Add another button for Firebase permission bypass
        if (!document.getElementById('firebase-bypass-btn')) {
            const bypassBtn = document.createElement('button');
            bypassBtn.id = 'firebase-bypass-btn';
            bypassBtn.className = 'btn btn-warning';
            bypassBtn.style.marginTop = '0.5rem';
            bypassBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Bypass Firebase Issues';
            bypassBtn.onclick = function() {
                console.log('🔧 Bypassing Firebase permission issues...');
                // Force demo mode due to Firebase permissions
                window.firebase = null;
                localStorage.setItem('armoryX_demo_mode', 'true');
                localStorage.setItem('armoryX_user_token', 'demo_user_' + Date.now());
                showForumContent();
                startForumStatsTracking();
            };
            
            const loginButtons = loginRequired.querySelector('.login-buttons');
            if (loginButtons) {
                loginButtons.appendChild(bypassBtn);
            }
        }
    }
    if (forumContent) forumContent.style.display = 'none';
}

async function updateUserWelcome(email) {
    const welcomeElement = document.querySelector('.forum-welcome');
    if (welcomeElement) {
        let displayName = email.split('@')[0]; // fallback to username
        
        // Try to get display name from Firebase
        if (window.firebase && window.firebase.auth) {
            const { auth } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                try {
                    const userDisplayName = await getUserDisplayName(user.uid);
                    if (userDisplayName) {
                        displayName = userDisplayName;
                    }
                } catch (error) {
                    console.log('Could not fetch display name:', error);
                }
            }
        }
        
        welcomeElement.innerHTML = `
            <h2>Welcome to the Forums, ${displayName}!</h2>
            <p>Join the discussion with fellow Armory X users</p>
        `;
    }
}

// Function to get user's display name (for use in forums)
async function getUserDisplayName(userId) {
    if (!userId) return { name: 'Anonymous', role: 'user', emailVerified: false };
    
    if (window.firebase && window.firebase.db) {
        try {
            const { db, doc, getDoc } = window.firebase;
            const userRef = doc(db, 'users', userId);
            const userSnap = await getDoc(userRef);
            
            if (userSnap.exists()) {
                const userData = userSnap.data();
                const displayName = userData.displayName || userData.username || 
                                   (userData.email ? userData.email.split('@')[0] : 'Anonymous');
                const userRole = userData.role || 'user';
                const emailVerified = userData.emailVerified !== false; // Default to true for existing users
                
                return { name: displayName, role: userRole, emailVerified: emailVerified };
            }
        } catch (error) {
            console.error('Error getting user display name:', error);
        }
    }
    
    return { name: 'Anonymous', role: 'user', emailVerified: false };
}

// Function to enhance posts with display names
async function enhancePostsWithDisplayNames(posts) {
    if (!window.firebase || !window.firebase.db) {
        return posts;
    }
    
    const enhancedPosts = [];
    
    for (const post of posts) {
        const enhancedPost = { ...post };
        
        if (post.authorId) {
            const userInfo = await getUserDisplayName(post.authorId);
            if (userInfo) {
                enhancedPost.authorDisplayName = userInfo.name;
                enhancedPost.authorRole = userInfo.role;
                enhancedPost.authorEmailVerified = userInfo.emailVerified;
            }
        }
        
        enhancedPosts.push(enhancedPost);
    }
    
    return enhancedPosts;
}

// Function to enhance replies with display names
async function enhanceRepliesWithDisplayNames(replies) {
    if (!window.firebase || !window.firebase.db) {
        return replies;
    }
    
    const enhancedReplies = [];
    
    for (const reply of replies) {
        const enhancedReply = { ...reply };
        
        if (reply.authorId) {
            const userInfo = await getUserDisplayName(reply.authorId);
            if (userInfo) {
                enhancedReply.authorDisplayName = userInfo.name;
                enhancedReply.authorRole = userInfo.role;
                enhancedReply.authorEmailVerified = userInfo.emailVerified;
            }
        }
        
        enhancedReplies.push(enhancedReply);
    }
    
    return enhancedReplies;
}

function loadForumData(user) {
    // Load real-time forum statistics
    updateForumStats();
    
    // Load recent posts
    loadRecentPosts();
    
    // Update category statistics with real post counts
    updateCategoryStats();
    
    // Set up real-time updates every 30 seconds
    setInterval(() => {
        updateForumStats();
        loadRecentPosts();
        updateCategoryStats();
    }, 30000);
    
    // If Firebase is available, set up real-time listeners
    if (window.firebase && window.firebase.db) {
        setupForumRealtimeListeners(user);
    }
}

function startForumStatsTracking() {
    // Track user forum visit
    if (window.firebase && window.firebase.auth && window.firebase.db) {
        const { auth, db, doc, updateDoc, serverTimestamp } = window.firebase;
        const user = auth.currentUser;
        
        if (user) {
            updateDoc(doc(db, 'users', user.uid), {
                lastForumVisit: serverTimestamp(),
                lastSeen: serverTimestamp()
            }).catch(error => {
                console.log('Error tracking forum visit:', error);
            });
        }
    }
}

function setupForumRealtimeListeners(user) {
    if (!window.firebase || !window.firebase.db) {
        console.warn('Firebase database not available for real-time listeners');
        return;
    }
    
    const { db, collection, onSnapshot, query, orderBy, limit } = window.firebase;
    
    try {
        // Listen for real-time forum posts with error handling
        const postsRef = query(
            collection(db, 'forum_posts'),
            orderBy('createdAt', 'desc'),
            limit(10)
        );
        
        onSnapshot(postsRef, async (snapshot) => {
            const posts = [];
            snapshot.forEach((doc) => {
                posts.push({ id: doc.id, ...doc.data() });
            });
            
            // Enhance posts with display names
            const enhancedPosts = await enhancePostsWithDisplayNames(posts);
            displayRecentPosts(enhancedPosts);
        }, (error) => {
            console.log('🔕 Forum posts listener error (using fallback):', error.code);
            // Fallback to loading static posts
            loadRecentPosts();
        });
        
        // Listen for user count updates
        const usersRef = collection(db, 'users');
        onSnapshot(usersRef, (snapshot) => {
            updateForumStatElement('total-users', snapshot.size);
        }, (error) => {
            console.log('🔕 Users count listener error:', error.code);
        });
        
        // Listen for posts count
        const allPostsRef = collection(db, 'forum_posts');
        onSnapshot(allPostsRef, (snapshot) => {
            updateForumStatElement('total-posts', snapshot.size);
        }, (error) => {
            console.log('🔕 Posts count listener error:', error.code);
        });
        
        // Count online users (active in last 5 minutes)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        onSnapshot(usersRef, (snapshot) => {
            let onlineCount = 0;
            snapshot.forEach((doc) => {
                const userData = doc.data();
                if (userData.lastSeen && userData.lastSeen.toDate && userData.lastSeen.toDate() > fiveMinutesAgo) {
                    onlineCount++;
                }
            });
            updateForumStatElement('online-users', onlineCount);
        }, (error) => {
            console.log('🔕 Online users listener error:', error.code);
        });
        
    } catch (error) {
        console.log('🔕 Error setting up forum listeners (using fallback):', error.code);
        // Fallback to static loading
        loadRecentPosts();
        updateForumStats();
    }
}

function updateForumStats() {
    if (!window.firebase || !window.firebase.db) {
        // Fallback to simulated stats
        const simulatedStats = {
            totalPosts: Math.floor(Math.random() * 50) + 280,    // 280-330
            totalUsers: Math.floor(Math.random() * 30) + 95,     // 95-125  
            onlineUsers: Math.floor(Math.random() * 15) + 8      // 8-23
        };
        
        updateForumStatElement('total-posts', simulatedStats.totalPosts);
        updateForumStatElement('total-users', simulatedStats.totalUsers);
        updateForumStatElement('online-users', simulatedStats.onlineUsers);
    }
}

function updateForumStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

function loadRecentPosts() {
    if (window.firebase && window.firebase.db) {
        // Real posts will be loaded via real-time listeners
        return;
    }
    
    // Fallback to mock recent posts
    const sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
    
    const defaultMockPosts = [
        {
            id: '1',
            title: 'Speed test accuracy improvements in v1.2.0',
            author: 'ArmoryDev',
            category: 'General Discussion',
            replies: 7,
            lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            isSticky: true
        },
        {
            id: '2', 
            title: 'Widget loading slowly on high-res displays',
            author: 'GamerUser123',
            category: 'Bug Reports',
            replies: 3,
            lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago
        },
        {
            id: '3',
            title: 'Request: Dark mode for desktop widget',
            author: 'DarkModeUser',
            category: 'Feature Requests', 
            replies: 12,
            lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago
        },
        {
            id: '4',
            title: 'How to properly disable Windows Defender?',
            author: 'NewUser456',
            category: 'Technical Support',
            replies: 5,
            lastActivity: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
        }
    ];
    
    // Combine session posts with default posts, sorted by lastActivity
    const allPosts = [...sessionPosts, ...defaultMockPosts].sort((a, b) => {
        const aTime = a.lastActivity || a.createdAt || new Date(0);
        const bTime = b.lastActivity || b.createdAt || new Date(0);
        return new Date(bTime) - new Date(aTime);
    });
    
    displayRecentPosts(allPosts);
}

function displayRecentPosts(posts) {
    const recentPostsContainer = document.querySelector('.recent-posts');
    if (!recentPostsContainer) return;
    
    const postsHTML = posts.map(post => {
        // Handle both Firebase data and mock data
        const title = post.title || 'Untitled Post';
        // Use proper display name logic - prefer displayName, then username, then email prefix
        const author = post.authorDisplayName || post.authorUsername || 
                      (post.authorEmail ? post.authorEmail.split('@')[0] : (post.author || 'Anonymous'));
        const authorRole = post.authorRole || 'user';
        const emailVerified = post.authorEmailVerified !== false; // Default to true for existing users
        const verificationClass = emailVerified ? '' : 'user-email-unverified';
        const category = post.category || 'General';
        const replies = post.replies || 0;
        const isSticky = post.isSticky || false;
        
        // Handle different timestamp formats - prioritize creation time for posts
        let timeAgo = 'Unknown';
        let lastActivityAgo = null;
        
        // Show creation time for the main post timestamp
        if (post.createdAt) {
            if (post.createdAt.toDate) {
                timeAgo = formatTimeAgo(post.createdAt.toDate());
            } else if (post.createdAt instanceof Date) {
                timeAgo = formatTimeAgo(post.createdAt);
            }
        } else if (post.lastActivity) {
            if (post.lastActivity.toDate) {
                timeAgo = formatTimeAgo(post.lastActivity.toDate());
            } else if (post.lastActivity instanceof Date) {
                timeAgo = formatTimeAgo(post.lastActivity);
            }
        }
        
        // Calculate last activity if different from creation time
        if (post.lastActivity && post.createdAt) {
            const createdTime = post.createdAt.toDate ? post.createdAt.toDate() : new Date(post.createdAt);
            const lastActivityTime = post.lastActivity.toDate ? post.lastActivity.toDate() : new Date(post.lastActivity);
            
            // Only show last activity if it's significantly different (more than 1 minute)
            if (Math.abs(lastActivityTime - createdTime) > 60000) {
                lastActivityAgo = formatTimeAgo(lastActivityTime);
            }
        }
        
        return `
            <div class="post-item ${isSticky ? 'sticky' : ''}" data-post-id="${post.id}">
                <div class="post-header">
                    <h4 class="post-title">
                        ${isSticky ? '<i class="fas fa-thumbtack sticky-icon"></i>' : ''}
                        ${title}
                    </h4>
                    <span class="post-category">${category}</span>
                </div>
                <div class="post-meta">
                    <span class="post-author">
                        <i class="fas fa-user"></i> <span class="author-name user-role-${authorRole} ${verificationClass}">${author}</span>
                    </span>
                    <span class="post-replies">
                        <i class="fas fa-comments"></i> ${replies} replies
                    </span>
                    <span class="post-likes">
                        <i class="fas fa-heart"></i> ${post.likes || 0} likes
                    </span>
                    <span class="post-time">
                        <i class="fas fa-clock"></i> Posted ${timeAgo}${lastActivityAgo && lastActivityAgo !== timeAgo ? ` • Last reply ${lastActivityAgo}` : ''}
                    </span>
                </div>
            </div>
        `;
    }).join('');
    
    recentPostsContainer.innerHTML = postsHTML;
}

function setupForumInteractions() {
    // Tab click handlers
    document.addEventListener('click', function(e) {
        if (e.target.closest('.forum-tab')) {
            const tab = e.target.closest('.forum-tab');
            const tabId = tab.getAttribute('data-tab');
            console.log('Tab clicked:', tabId, tab);
            switchForumTab(tabId);
        }
        
        if (e.target.closest('.forum-category')) {
            const category = e.target.closest('.forum-category');
            const categoryTitle = category.querySelector('.category-title').textContent;
            handleCategoryClick(categoryTitle);
        }
        
        if (e.target.closest('.post-item')) {
            const post = e.target.closest('.post-item');
            const postTitle = post.querySelector('.post-title').textContent.replace('📌', '').trim();
            handlePostClick(postTitle, post.dataset.postId);
        }
        
        if (e.target.closest('.new-post-btn')) {
            handleNewPost();
        }
    });
}

// Forum Tab Management
function switchForumTab(tabId) {
    console.log('Switching to tab:', tabId);
    
    // If we're in a category view and switching to a different tab, go back to main view first
    const categoryPostsView = document.getElementById('category-posts-view');
    if (categoryPostsView && categoryPostsView.style.display !== 'none' && tabId !== 'categories') {
        console.log('Coming back from category view, resetting to main forum view');
        // Hide category view but don't call backToCategories() as it sets categories as active
        categoryPostsView.style.display = 'none';
        const categoriesTab = document.getElementById('categories-tab');
        const forumTabs = document.querySelector('.forum-tabs');
        if (categoriesTab) categoriesTab.style.display = 'block';
        if (forumTabs) forumTabs.style.display = 'flex';
        hideNewPostForm();
    }
    
    // Remove active class from all tabs and content
    document.querySelectorAll('.forum-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none'; // Explicitly hide all content
    });
    
    // Add active class to clicked tab and corresponding content
    const activeTab = document.querySelector(`[data-tab="${tabId}"]`);
    const activeContent = document.getElementById(`${tabId}-tab`);
    
    if (activeTab) {
        activeTab.classList.add('active');
        console.log('Tab activated:', activeTab);
    }
    if (activeContent) {
        activeContent.classList.add('active');
        activeContent.style.display = 'block'; // Explicitly show active content
        console.log('Content shown:', activeContent.id);
    }
    
    // Load content based on tab
    if (tabId === 'users') {
        console.log('Loading users tab content');
        loadUsersTab();
    } else if (tabId === 'recent') {
        console.log('Loading recent posts content');
        loadRecentPosts();
    } else if (tabId === 'categories') {
        console.log('Categories tab selected');
    }
}

// Load Users Tab
async function loadUsersTab() {
    const usersList = document.getElementById('users-list');
    const usersCount = document.getElementById('users-count');
    const usersOnline = document.getElementById('users-online');
    
    if (!usersList) return;
    
    // Show loading state
    usersList.innerHTML = '<p class="loading-message">Loading users...</p>';
    if (usersCount) usersCount.textContent = 'Loading...';
    if (usersOnline) usersOnline.textContent = 'Loading...';
    
    try {
        if (window.firebase && window.firebase.db) {
            // Load users from Firebase
            const { db, collection, getDocs, query, orderBy } = window.firebase;
            
            const usersRef = query(collection(db, 'users'), orderBy('createdAt', 'desc'));
            const snapshot = await getDocs(usersRef);
            
            const users = [];
            snapshot.forEach((doc) => {
                const userData = doc.data();
                users.push({
                    id: doc.id,
                    ...userData
                });
            });
            
            displayUsers(users);
            
            // Update stats
            if (usersCount) usersCount.textContent = users.length;
            
            // Count online users (active in last 5 minutes)
            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
            const onlineCount = users.filter(user => {
                if (user.lastSeen && user.lastSeen.toDate) {
                    return user.lastSeen.toDate() > fiveMinutesAgo;
                }
                return false;
            }).length;
            
            if (usersOnline) usersOnline.textContent = onlineCount;
            
        } else {
            // Fallback to mock users
            const mockUsers = generateMockUsers();
            displayUsers(mockUsers);
            
            if (usersCount) usersCount.textContent = mockUsers.length;
            if (usersOnline) usersOnline.textContent = Math.floor(mockUsers.length * 0.2); // 20% online
        }
        
    } catch (error) {
        console.error('Error loading users:', error);
        usersList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Failed to load users. Please try again later.</p>
                <p class="error-details">${error.message}</p>
            </div>
        `;
    }
}

function displayUsers(users) {
    const usersList = document.getElementById('users-list');
    if (!usersList) return;
    
    if (users.length === 0) {
        usersList.innerHTML = '<p class="loading-message">No users found.</p>';
        return;
    }
    
    const usersHTML = users.map(user => {
        const email = user.email || 'Unknown';
        const username = email.split('@')[0];
        // Use display name if available, fallback to username from email, but NEVER show full email
        const displayName = user.displayName || user.username || username;
        const userRole = user.role || 'user';
        const joinDate = user.createdAt ? formatUserDate(user.createdAt) : 'Recently';
        const forumPosts = user.forumPosts || 0;
        const reputation = user.reputation || 0;
        
        // Determine online status
        let statusClass = '';
        let statusText = 'Offline';
        
        if (user.lastSeen) {
            const lastSeen = user.lastSeen.toDate ? user.lastSeen.toDate() : new Date(user.lastSeen);
            const now = new Date();
            const diffMinutes = (now - lastSeen) / (1000 * 60);
            
            if (diffMinutes < 5) {
                statusClass = 'online';
                statusText = 'Online';
            } else if (diffMinutes < 60) {
                statusClass = 'recent';
                statusText = 'Recently';
            }
        }
        
        return `
            <div class="user-card">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-name user-role-${userRole} ${user.emailVerified !== false ? '' : 'user-email-unverified'}">${displayName}</div>
                <div class="user-stats">
                    <span>Posts: ${forumPosts}</span>
                    <span>Rep: ${reputation}</span>
                </div>
                <div class="user-status">
                    <div class="status-indicator ${statusClass}"></div>
                    <span>${statusText}</span>
                </div>
                <div class="user-join-date">Joined: ${joinDate}</div>
            </div>
        `;
    }).join('');
    
    usersList.innerHTML = usersHTML;
}

function generateMockUsers() {
    const mockUsers = [
        {
            id: '1',
            email: '<EMAIL>',
            displayName: 'ArmoryX Team',
            createdAt: new Date('2024-01-15'),
            lastSeen: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
            forumPosts: 25,
            reputation: 100
        },
        {
            id: '2', 
            email: '<EMAIL>',
            displayName: 'TechExpert92',
            createdAt: new Date('2024-02-20'),
            lastSeen: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
            forumPosts: 12,
            reputation: 45
        },
        {
            id: '3',
            email: '<EMAIL>',
            displayName: 'GamingGuru',
            createdAt: new Date('2024-03-10'),
            lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
            forumPosts: 8,
            reputation: 32
        },
        {
            id: '4',
            email: '<EMAIL>',
            displayName: 'ProGamer2024',
            createdAt: new Date('2024-03-25'),
            lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
            forumPosts: 15,
            reputation: 67
        }
    ];
    
    return mockUsers;
}

function formatUserDate(date) {
    if (!date) return 'Unknown';
    
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

async function handleCategoryClick(categoryTitle) {
    console.log('Category clicked:', categoryTitle);
    
    // Track category interaction
    if (window.firebase && window.firebase.auth && window.firebase.db) {
        const { auth, db, collection, addDoc, serverTimestamp } = window.firebase;
        const user = auth.currentUser;
        
        if (user) {
            try {
                await addDoc(collection(db, 'forum_interactions'), {
                    userId: user.uid,          // Required by security rules
                    userEmail: user.email,
                    action: 'category_view',
                    category: categoryTitle,
                    timestamp: serverTimestamp()
                });
            } catch (error) {
                console.log('Error tracking category interaction:', error);
            }
        }
    }
    
    // Show category posts
    showCategoryPosts(categoryTitle);
}

function showCategoryPosts(categoryTitle) {
    // Hide main categories view
    const categoriesTab = document.getElementById('categories-tab');
    const forumTabs = document.querySelector('.forum-tabs');
    
    if (categoriesTab) {
        categoriesTab.style.display = 'none';
    }
    if (forumTabs) {
        forumTabs.style.display = 'none';
    }
    
    // Create or show category posts view
    let categoryPostsDiv = document.getElementById('category-posts-view');
    if (!categoryPostsDiv) {
        categoryPostsDiv = document.createElement('div');
        categoryPostsDiv.id = 'category-posts-view';
        categoryPostsDiv.className = 'category-posts active';
        categoriesTab.parentNode.appendChild(categoryPostsDiv);
    }
    
    categoryPostsDiv.innerHTML = `
        <div class="category-posts-header">
            <div class="header-left">
                <button class="back-to-categories" onclick="backToCategories()">
                    <i class="fas fa-arrow-left"></i> Back to Categories
                </button>
                <h2>${categoryTitle}</h2>
            </div>
            <div class="header-center">
                <!-- Sort controls removed - posts now default to newest first -->
            </div>
            <div class="header-right">
                <button class="btn btn-primary new-post-btn" onclick="handleNewPost('${categoryTitle}')">
                    <i class="fas fa-plus"></i> New Post
                </button>
            </div>
        </div>
        <div class="posts-list" id="category-posts-list">
            <p class="loading-message">Loading posts for ${categoryTitle}...</p>
        </div>
    `;
    
    categoryPostsDiv.style.display = 'block';
    
    // Load posts for this category
    loadCategoryPosts(categoryTitle);
}

async function loadCategoryPosts(categoryTitle) {
    const postsList = document.getElementById('category-posts-list');
    if (!postsList) return;
    
    try {
        if (window.firebase && window.firebase.db) {
            const { db, collection, query, where, orderBy, getDocs } = window.firebase;
            
            const postsQuery = query(
                collection(db, 'forum_posts'),
                where('category', '==', categoryTitle),
                orderBy('lastActivity', 'desc')
            );
            
            const snapshot = await getDocs(postsQuery);
            const posts = [];
            
            snapshot.forEach((doc) => {
                posts.push({ id: doc.id, ...doc.data() });
            });
            
            if (posts.length === 0) {
                postsList.innerHTML = `
                    <div class="no-posts-message">
                        <i class="fas fa-comments"></i>
                        <h3>No posts yet in ${categoryTitle}</h3>
                        <p>Be the first to start a discussion!</p>
                        <button class="btn btn-primary" onclick="handleNewPost('${categoryTitle}')">
                            <i class="fas fa-plus"></i> Create First Post
                        </button>
                    </div>
                `;
            } else {
                            // Sort posts by newest first
            const sortedPosts = sortPostsNewestFirst(posts);
            
            // Enhance posts with display names
            const enhancedPosts = await enhancePostsWithDisplayNames(sortedPosts);
            displayCategoryPosts(enhancedPosts);
            }
            
        } else {
            // Fallback to mock posts for this category
            const mockPosts = getMockPostsForCategory(categoryTitle);
            
            // Sort mock posts by newest first
            const sortedMockPosts = sortPostsNewestFirst(mockPosts);
            displayCategoryPosts(sortedMockPosts);
        }
        
    } catch (error) {
        console.error('Error loading category posts:', error);
        postsList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Failed to load posts for ${categoryTitle}</p>
                <p class="error-details">${error.message}</p>
                <button class="btn btn-secondary" onclick="loadCategoryPosts('${categoryTitle}')">
                    <i class="fas fa-refresh"></i> Try Again
                </button>
            </div>
        `;
    }
}

function displayCategoryPosts(posts) {
    const postsList = document.getElementById('category-posts-list');
    if (!postsList) return;
    
    const postsHTML = posts.map(post => {
        const title = post.title || 'Untitled Post';
        // Use proper display name logic - prefer displayName, then username, then email prefix
        const author = post.authorDisplayName || post.authorUsername || 
                      (post.authorEmail ? post.authorEmail.split('@')[0] : (post.author || 'Anonymous'));
        const authorRole = post.authorRole || 'user';
        const emailVerified = post.authorEmailVerified !== false; // Default to true for existing users
        const verificationClass = emailVerified ? '' : 'user-email-unverified';
        const replies = post.replies || 0;
        const views = post.views || 0;
        const isSticky = post.isSticky || false;
        
        // Handle different timestamp formats - prioritize creation time for posts
        let timeAgo = 'Unknown';
        let lastActivityAgo = null;
        
        // Show creation time for the main post timestamp
        if (post.createdAt) {
            if (post.createdAt.toDate) {
                timeAgo = formatTimeAgo(post.createdAt.toDate());
            } else if (post.createdAt instanceof Date) {
                timeAgo = formatTimeAgo(post.createdAt);
            }
        } else if (post.lastActivity) {
            if (post.lastActivity.toDate) {
                timeAgo = formatTimeAgo(post.lastActivity.toDate());
            } else if (post.lastActivity instanceof Date) {
                timeAgo = formatTimeAgo(post.lastActivity);
            }
        }
        
        // Calculate last activity if different from creation time
        if (post.lastActivity && post.createdAt) {
            const createdTime = post.createdAt.toDate ? post.createdAt.toDate() : new Date(post.createdAt);
            const lastActivityTime = post.lastActivity.toDate ? post.lastActivity.toDate() : new Date(post.lastActivity);
            
            // Only show last activity if it's significantly different (more than 1 minute)
            if (Math.abs(lastActivityTime - createdTime) > 60000) {
                lastActivityAgo = formatTimeAgo(lastActivityTime);
            }
        }
        
        return `
            <div class="post-item ${isSticky ? 'sticky' : ''}" data-post-id="${post.id}" onclick="handlePostClick('${title}', '${post.id}')">
                <div class="post-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="post-content">
                    <h4 class="post-title">
                        ${isSticky ? '<i class="fas fa-thumbtack sticky-icon"></i>' : ''}
                        ${title}
                    </h4>
                    <div class="post-meta">
                        <span class="post-author">
                            <i class="fas fa-user"></i> <span class="author-name user-role-${authorRole} ${verificationClass}">${author}</span>
                        </span>
                        <span class="post-time">
                            <i class="fas fa-clock"></i> Posted ${timeAgo}${lastActivityAgo && lastActivityAgo !== timeAgo ? ` • Last reply ${lastActivityAgo}` : ''}
                        </span>
                    </div>
                    ${post.content ? `<p class="post-preview">${post.content.substring(0, 150)}${post.content.length > 150 ? '...' : ''}</p>` : ''}
                </div>
                <div class="post-stats">
                    <span class="replies">
                        <i class="fas fa-comments"></i> ${replies} replies
                    </span>
                    <span class="views">
                        <i class="fas fa-eye"></i> ${views} views
                    </span>
                    <span class="likes">
                        <i class="fas fa-heart"></i> ${post.likes || 0} likes
                    </span>
                </div>
            </div>
        `;
    }).join('');
    
    postsList.innerHTML = postsHTML;
}

function getMockPostsForCategory(categoryTitle) {
    // Get session posts first (user-created posts)
    const sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
    const categorySessionPosts = sessionPosts.filter(post => post.category === categoryTitle);
    
    const mockPosts = {
        'General Discussion': [
            {
                id: '1',
                title: 'Speed test accuracy improvements in v1.2.0',
                author: 'ArmoryDev',
                content: 'The latest update includes significant improvements to speed test accuracy and reliability. We\'ve enhanced the testing algorithms and added better error handling.',
                replies: 7,
                views: 45,
                lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
                isSticky: true
            },
            {
                id: '2',
                title: 'Welcome to Armory X Forums!',
                author: 'ArmoryTeam',
                content: 'Welcome to the official Armory X community forums. Feel free to discuss features, share tips, and connect with other users.',
                replies: 12,
                views: 89,
                lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
                isSticky: true
            }
        ],
        'Bug Reports': [
            {
                id: '3',
                title: 'Widget loading slowly on high-res displays',
                author: 'GamerUser123',
                content: 'Has anyone else experienced slow widget loading on 4K displays? The desktop widget takes about 10-15 seconds to fully load.',
                replies: 3,
                views: 28,
                lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000)
            }
        ],
        'Feature Requests': [
            {
                id: '4',
                title: 'Request: Dark mode for desktop widget',
                author: 'DarkModeUser',
                content: 'Would love to see a dark mode option for the desktop widget. The current light theme is too bright for nighttime use.',
                replies: 15,
                views: 67,
                lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000)
            }
        ],
        'Technical Support': [
            {
                id: '5',
                title: 'How to properly configure game optimization?',
                author: 'NewUser456',
                content: 'I\'m new to Armory X and need help setting up game optimization. What are the recommended settings for competitive gaming?',
                replies: 8,
                views: 34,
                lastActivity: new Date(Date.now() - 5 * 60 * 60 * 1000)
            }
        ],
        'Gaming & Performance': [
            {
                id: '6',
                title: 'Best settings for FPS optimization?',
                author: 'ProGamer',
                content: 'What are the optimal Armory X settings for maximizing FPS in competitive games like CS2 and Valorant?',
                replies: 11,
                views: 156,
                lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000)
            }
        ]
    };
    
    const defaultPosts = mockPosts[categoryTitle] || [];
    // Combine session posts with default posts, session posts first
    return [...categorySessionPosts, ...defaultPosts];
}

function backToCategories() {
    console.log('Back to categories called');
    
    // Show main categories view
    const categoriesTab = document.getElementById('categories-tab');
    const forumTabs = document.querySelector('.forum-tabs');
    const categoryPostsView = document.getElementById('category-posts-view');
    
    if (categoriesTab) {
        categoriesTab.style.display = 'block';
    }
    if (forumTabs) {
        forumTabs.style.display = 'flex';
    }
    if (categoryPostsView) {
        categoryPostsView.style.display = 'none';
    }
    
    // Reset all tab content visibility and make categories active
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
    });
    
    // Make categories tab and content active
    document.querySelectorAll('.forum-tab').forEach(tab => tab.classList.remove('active'));
    const categoriesTabBtn = document.querySelector('[data-tab="categories"]');
    const categoriesContent = document.getElementById('categories-tab');
    
    if (categoriesTabBtn) {
        categoriesTabBtn.classList.add('active');
    }
    if (categoriesContent) {
        categoriesContent.classList.add('active');
        categoriesContent.style.display = 'block';
    }
    
    // Hide new post form if visible
    hideNewPostForm();
}

async function handlePostClick(postTitle, postId) {
    console.log('Post clicked:', postTitle, postId);
    
    // Track post interaction  
    if (window.firebase && window.firebase.auth && window.firebase.db) {
        const { auth, db, collection, addDoc, serverTimestamp, doc, updateDoc, increment } = window.firebase;
        const user = auth.currentUser;
        
        if (user) {
            try {
                await addDoc(collection(db, 'forum_interactions'), {
                    userId: user.uid,          // Required by security rules
                    userEmail: user.email,
                    action: 'post_view',
                    postId: postId,
                    postTitle: postTitle,
                    timestamp: serverTimestamp()
                });
                
                // Increment view count
                const postRef = doc(db, 'forum_posts', postId);
                await updateDoc(postRef, {
                    views: increment(1)
                });
            } catch (error) {
                console.log('Error tracking post interaction:', error);
            }
        }
    }
    
    // Show post detail modal
    showPostModal(postId);
}

async function showPostModal(postId) {
    try {
        // Get post data
        const postData = await getPostData(postId);
        if (!postData) {
            showMessage('Post not found!', 'error');
            return;
        }
        
        // Check if current user is the author
        const isAuthor = await checkIfUserIsAuthor(postData);
        
        // Create modal if it doesn't exist
        let modal = document.getElementById('post-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'post-modal';
            modal.className = 'post-modal';
            document.body.appendChild(modal);
        }
        
        // Build modal content with enhanced user details and OP controls
        modal.innerHTML = `
            <div class="post-modal-content">
                <div class="post-modal-header">
                    <button class="post-modal-close" onclick="hidePostModal()">
                        <i class="fas fa-times"></i>
                    </button>
                    <h2 class="post-modal-title">${postData.title}</h2>
                    <div class="post-author-details">
                        <div class="author-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="author-info">
                            <div class="author-name">
                                <i class="fas fa-user"></i> <span class="author-display-name user-role-${postData.authorRole || 'user'} ${postData.authorEmailVerified !== false ? 'user-email-verified' : 'user-email-unverified'}">${postData.author}</span>
                                ${isAuthor ? '<span class="op-badge"><i class="fas fa-crown"></i> OP</span>' : ''}
                                <span class="reputation-badge ${postData.authorReputation < 0 ? 'negative' : ''}">
                                    <i class="fas fa-star"></i> ${postData.authorReputation || 0}
                                </span>
                            </div>
                            <div class="author-meta">
                                <span><i class="fas fa-folder"></i> ${postData.category}</span>
                                <span><i class="fas fa-clock"></i> ${postData.timeAgo}</span>
                                <span><i class="fas fa-eye"></i> ${postData.views || 0} views</span>
                            </div>
                        </div>
                        ${isAuthor || await checkUserIsModerator() ? `
                            <div class="op-controls">
                                <button class="btn btn-small btn-secondary" onclick="editPost('${postId}')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button class="btn btn-small btn-danger" onclick="deletePost('${postId}')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                ${!isAuthor && await checkUserIsModerator() ? `
                                    <span class="mod-badge">
                                        <i class="fas fa-shield-alt"></i> Moderator Action
                                    </span>
                                ` : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="post-modal-body">
                    <div class="post-content-full" id="post-content-display">${postData.content}</div>
                    <div class="post-edit-form" id="post-edit-form" style="display: none;">
                        <h4><i class="fas fa-edit"></i> Edit Post</h4>
                        <div class="form-group">
                            <label for="edit-post-title">Title</label>
                            <input type="text" id="edit-post-title" value="${postData.title}" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-post-content">Content</label>
                            <textarea id="edit-post-content" required>${postData.content}</textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-post-category">Category</label>
                            <select id="edit-post-category" required>
                                <option value="General Discussion" ${postData.category === 'General Discussion' ? 'selected' : ''}>General Discussion</option>
                                <option value="Bug Reports" ${postData.category === 'Bug Reports' ? 'selected' : ''}>Bug Reports</option>
                                <option value="Feature Requests" ${postData.category === 'Feature Requests' ? 'selected' : ''}>Feature Requests</option>
                                <option value="Technical Support" ${postData.category === 'Technical Support' ? 'selected' : ''}>Technical Support</option>
                                <option value="Gaming & Performance" ${postData.category === 'Gaming & Performance' ? 'selected' : ''}>Gaming & Performance</option>
                            </select>
                        </div>
                        <div class="edit-form-actions">
                            <button class="btn btn-secondary" onclick="cancelEditPost()">Cancel</button>
                            <button class="btn btn-primary" onclick="savePostEdit('${postId}')">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="post-actions">
                    <button class="like-button" data-post-id="${postId}" onclick="handleLikePost('${postId}')">
                        <i class="fas fa-heart"></i>
                        <span class="like-count">${postData.likes || 0}</span>
                    </button>
                    <button class="dislike-button" data-post-id="${postId}" onclick="handleDislikePost('${postId}')">
                        <i class="fas fa-heart-broken"></i>
                        <span class="dislike-count">${postData.dislikes || 0}</span>
                    </button>
                    <button class="reply-button" onclick="showReplyForm()">
                        <i class="fas fa-reply"></i>
                        Reply
                    </button>
                </div>
                
                <div class="replies-section">
                    <div class="replies-header">
                        <h3><i class="fas fa-comments"></i> Replies (${postData.replies || 0})</h3>
                    </div>
                    <div class="replies-list" id="replies-list">
                        <p class="loading-message">Loading replies...</p>
                    </div>
                    <div class="reply-form" id="reply-form" style="display: none;">
                        <h4><i class="fas fa-reply"></i> Write a Reply</h4>
                        <textarea id="reply-content" placeholder="Write your reply here..." required></textarea>
                        <div class="reply-form-actions">
                            <button class="cancel-reply" onclick="hideReplyForm()">Cancel</button>
                            <button class="btn btn-primary" onclick="submitReply('${postId}')">
                                <i class="fas fa-paper-plane"></i> Post Reply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Show modal and set post ID for tracking
        modal.classList.add('active');
        modal.dataset.postId = postId;
        document.body.style.overflow = 'hidden';
        
        // Add keyboard support
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                hidePostModal();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        
        // Add click outside to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hidePostModal();
            }
        });
        
        // Load replies
        loadPostReplies(postId);
        
        // Check if user has liked/disliked this post
        checkUserLikeDislikeStatus(postId);
        
        // Update reply count in header
        updateReplyCount(postId);
        
    } catch (error) {
        console.error('Error showing post modal:', error);
        showMessage('Failed to load post details', 'error');
    }
}

function hidePostModal() {
    const modal = document.getElementById('post-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 300);
    }
}

async function getPostData(postId) {
    if (window.firebase && window.firebase.db) {
        const { db, doc, getDoc } = window.firebase;
        
        try {
            const postRef = doc(db, 'forum_posts', postId);
            const postSnap = await getDoc(postRef);
            
            if (postSnap.exists()) {
                const data = postSnap.data();
                
                // Get display name if available
                let authorName = 'Anonymous';
                let authorRole = 'user';
                let emailVerified = false;
                if (data.authorId) {
                    const userInfo = await getUserDisplayName(data.authorId);
                    authorName = userInfo.name || (data.authorEmail ? data.authorEmail.split('@')[0] : 'Anonymous');
                    authorRole = userInfo.role || 'user';
                    emailVerified = userInfo.emailVerified !== false;
                } else if (data.authorEmail) {
                    authorName = data.authorEmail.split('@')[0];
                }
                
                return {
                    id: postId,
                    title: data.title,
                    content: data.content,
                    author: authorName,
                    authorId: data.authorId,
                    authorEmail: data.authorEmail,
                    authorRole: authorRole,
                    authorEmailVerified: emailVerified,
                    category: data.category,
                    views: data.views || 0,
                    likes: data.likes || 0,
                    dislikes: data.dislikes || 0,
                    replies: data.replies || 0,
                    timeAgo: formatTimeAgo(data.createdAt?.toDate() || new Date()),
                    authorReputation: await getUserReputation(data.authorId)
                };
            }
        } catch (error) {
            console.error('Error getting post data from Firebase:', error);
        }
    }
    
    // Fallback to session storage or mock data
    const sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
    const post = sessionPosts.find(p => p.id === postId);
    
    if (post) {
        return {
            id: postId,
            title: post.title,
            content: post.content,
            author: post.author || 'You',
            authorId: post.authorId || null,
            authorEmail: post.authorEmail || null,
            category: post.category,
            views: post.views || 0,
            likes: post.likes || 0,
            dislikes: post.dislikes || 0,
            replies: post.replies || 0,
            timeAgo: formatTimeAgo(post.createdAt || new Date()),
            authorReputation: 10 // Default for mock
        };
    }
    
    // Default mock data if post not found
    return {
        id: postId,
        title: 'Sample Post',
        content: 'This is a sample post to demonstrate the forum functionality.',
        author: 'DemoUser',
        authorId: null,
        authorEmail: null,
        category: 'General Discussion',
        views: 42,
        likes: 5,
        dislikes: 1,
        replies: 3,
        timeAgo: '2 hours ago',
        authorReputation: 25
    };
}

async function getUserReputation(userId) {
    if (!userId || !window.firebase?.db) return 0;
    
    const { db, doc, getDoc } = window.firebase;
    
    try {
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);
        
        if (userSnap.exists()) {
            return userSnap.data().reputation || 0;
        }
    } catch (error) {
        console.log('Error getting user reputation:', error);
    }
    
    return 0;
}

// Replies System
async function loadPostReplies(postId) {
    const repliesList = document.getElementById('replies-list');
    if (!repliesList) return;
    
    // Show loading state briefly
    repliesList.innerHTML = '<p class="loading-message">Loading replies...</p>';
    
    try {
        if (window.firebase && window.firebase.db) {
            const { db, collection, query, where, orderBy, getDocs } = window.firebase;
            
            const repliesQuery = query(
                collection(db, 'forum_replies'),
                where('postId', '==', postId),
                orderBy('createdAt', 'asc')
            );
            
            const snapshot = await getDocs(repliesQuery);
            const replies = [];
            
            for (const doc of snapshot.docs) {
                const data = doc.data();
                const authorReputation = await getUserReputation(data.authorId);
                replies.push({
                    id: doc.id,
                    ...data,
                    authorReputation
                });
            }
            
            // Sort replies by newest first
            const sortedReplies = sortRepliesNewestFirst(replies);
            
            // Enhance replies with display names while preserving all data
            const enhancedReplies = await enhanceRepliesWithDisplayNames(sortedReplies);
            displayReplies(enhancedReplies);
            
            console.log(`Loaded ${enhancedReplies.length} replies for post ${postId}`);
        } else {
            // Fallback to mock replies
            const mockReplies = getMockReplies(postId);
            
            // Sort mock replies by newest first
            const sortedMockReplies = sortRepliesNewestFirst(mockReplies);
            displayReplies(sortedMockReplies);
            
            console.log(`Loaded ${sortedMockReplies.length} mock replies for post ${postId}`);
        }
    } catch (error) {
        console.error('Error loading replies:', error);
        repliesList.innerHTML = '<p class="error-message">Failed to load replies. Please try again.</p>';
    }
}

async function displayReplies(replies) {
    const repliesList = document.getElementById('replies-list');
    if (!repliesList) {
        console.log('Replies list element not found!');
        return;
    }
    
    console.log('Displaying replies:', replies);
    
    if (replies.length === 0) {
        repliesList.innerHTML = '<p class="no-replies">No replies yet. Be the first to reply!</p>';
        return;
    }
    
    // Check user permissions
    const isUserModerator = await checkUserIsModerator();
    const currentUserId = window.firebase?.auth?.currentUser?.uid;
    
    // Get post data to check if current user is OP
    const postModal = document.getElementById('post-modal');
    const postId = postModal?.querySelector('[onclick*="submitReply"]')?.getAttribute('onclick')?.match(/'([^']+)'/)?.[1];
    let isOP = false;
    if (postId) {
        const postData = await getPostData(postId);
        isOP = await checkIfUserIsAuthor(postData);
    }
    
    const repliesHTML = replies.map(reply => {
        // Use proper display name logic - prefer displayName, then username, then email prefix
        const author = reply.authorDisplayName || reply.authorUsername || 
                      (reply.authorEmail ? reply.authorEmail.split('@')[0] : (reply.author || 'Anonymous'));
        const authorRole = reply.authorRole || 'user';
        const emailVerified = reply.authorEmailVerified !== false; // Default to true for existing users
        const verificationClass = emailVerified ? 'user-email-verified' : 'user-email-unverified';
        const timeAgo = formatTimeAgo(reply.createdAt?.toDate ? reply.createdAt.toDate() : new Date(reply.createdAt));
        const reputation = reply.authorReputation || 0;
        
        // Check if current user can delete this reply
        const isReplyAuthor = currentUserId && reply.authorId === currentUserId;
        const canDelete = isUserModerator || isOP || isReplyAuthor;
        
        return `
            <div class="reply-item" data-reply-id="${reply.id}">
                <div class="reply-header">
                    <div class="reply-author">
                        <i class="fas fa-user"></i> <span class="author-name user-role-${authorRole} ${verificationClass}">${author}</span>
                        ${reputation > 0 ? `<span class="reputation-badge"><i class="fas fa-star"></i> ${reputation}</span>` : ''}
                    </div>
                    <div class="reply-controls">
                        <div class="reply-time">${timeAgo}</div>
                        ${canDelete ? `
                            <div class="reply-actions-dropdown">
                                <button class="reply-options-btn" onclick="toggleReplyOptions('${reply.id}')">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="reply-options-menu" id="reply-options-${reply.id}" style="display: none;">
                                    ${isReplyAuthor ? `
                                        <button class="reply-option" onclick="editReply('${reply.id}')">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    ` : ''}
                                    <button class="reply-option delete-option" onclick="deleteReply('${reply.id}')">
                                        <i class="fas fa-trash"></i> Delete
                                        ${!isReplyAuthor ? `<span class="mod-indicator">${isOP ? '(OP)' : '(Mod)'}</span>` : ''}
                                    </button>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="reply-content" id="reply-content-${reply.id}">${reply.content}</div>
                <div class="reply-edit-form" id="reply-edit-${reply.id}" style="display: none;">
                    <textarea id="reply-edit-textarea-${reply.id}">${reply.content}</textarea>
                    <div class="reply-edit-actions">
                        <button class="btn btn-secondary" onclick="cancelEditReply('${reply.id}')">Cancel</button>
                        <button class="btn btn-primary" onclick="saveEditReply('${reply.id}')">Save</button>
                    </div>
                </div>
                <div class="reply-actions">
                    <button class="reply-like-button" data-reply-id="${reply.id}" onclick="handleLikeReply('${reply.id}')">
                        <i class="fas fa-heart"></i> 
                        <span class="like-count">${reply.likes || 0}</span>
                    </button>
                    <button class="reply-dislike-button" data-reply-id="${reply.id}" onclick="handleDislikeReply('${reply.id}')">
                        <i class="fas fa-heart-broken"></i> 
                        <span class="dislike-count">${reply.dislikes || 0}</span>
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    repliesList.innerHTML = repliesHTML;
    
    console.log('Replies HTML set, checking like status for', replies.length, 'replies');
    
    // Update the reply count header to match actual displayed replies
    const repliesHeader = document.querySelector('.replies-header h3');
    if (repliesHeader) {
        repliesHeader.innerHTML = `<i class="fas fa-comments"></i> Replies (${replies.length})`;
    }
    
    // Also ensure the stored count is accurate for the current post
    const currentPostId = getCurrentPostId();
    if (currentPostId) {
        updatePostReplyCountInLists(currentPostId, replies.length);
        
        // Update the stored count in Firebase if there's a discrepancy
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { db, doc, updateDoc } = window.firebase;
            try {
                const postRef = doc(db, 'forum_posts', currentPostId);
                updateDoc(postRef, {
                    replies: replies.length
                });
            } catch (error) {
                console.log('Error syncing reply count in displayReplies:', error);
            }
        }
    }
    
    // Check user like/dislike status for all replies
    replies.forEach(reply => {
        checkUserLikeDislikeStatus(reply.id, true);
    });
}

function getMockReplies(postId) {
    // Get session replies first (user-created replies)
    const sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
    const postReplies = sessionReplies.filter(reply => reply.postId === postId);
    
    console.log('Looking for replies for postId:', postId);
    console.log('Session replies found:', postReplies.length);
    
    // Add some default replies for any post that doesn't have any
    let defaultReplies = [];
    if (postReplies.length === 0) {
        defaultReplies = [
            {
                id: `reply_${postId}_1`,
                content: 'Thanks for sharing this! Really helpful post.',
                author: 'TestUser1',
                authorDisplayName: 'TestUser1',
                authorRole: 'user',
                authorEmailVerified: true,
                createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
                likes: 3,
                dislikes: 0,
                authorReputation: 15
            },
            {
                id: `reply_${postId}_2`,
                content: 'I had a similar experience. Great to see others discussing this.',
                author: 'TechHelper',
                authorDisplayName: 'TechHelper',
                authorRole: 'user',
                authorEmailVerified: true,
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                likes: 5,
                dislikes: 1,
                authorReputation: 42
            },
            {
                id: `reply_${postId}_3`,
                content: 'This is very useful information, thank you for sharing!',
                author: 'ForumUser',
                authorDisplayName: 'ForumUser',
                authorRole: 'user',
                authorEmailVerified: true,
                createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
                likes: 2,
                dislikes: 0,
                authorReputation: 8
            }
        ];
    }
    
    // Ensure all replies have the required fields
    const allReplies = [...postReplies, ...defaultReplies].map(reply => ({
        ...reply,
        authorReputation: reply.authorReputation || 0,
        authorDisplayName: reply.authorDisplayName || reply.author || 'Anonymous',
        authorRole: reply.authorRole || 'user',
        authorEmailVerified: reply.authorEmailVerified !== false,
        likes: reply.likes || 0,
        dislikes: reply.dislikes || 0
    }));
    
    console.log('Total replies to display:', allReplies.length);
    
    return allReplies;
}

function showReplyForm() {
    const replyForm = document.getElementById('reply-form');
    if (replyForm) {
        replyForm.style.display = 'block';
        const textarea = document.getElementById('reply-content');
        if (textarea) {
            textarea.focus();
        }
    }
}

function hideReplyForm() {
    const replyForm = document.getElementById('reply-form');
    if (replyForm) {
        replyForm.style.display = 'none';
        const textarea = document.getElementById('reply-content');
        if (textarea) {
            textarea.value = '';
        }
    }
}

async function submitReply(postId) {
    const textarea = document.getElementById('reply-content');
    const content = textarea.value.trim();
    
    if (!content) {
        showMessage('Please enter a reply.', 'error');
        return;
    }
    
    if (content.length < 5) {
        showMessage('Reply must be at least 5 characters long.', 'error');
        return;
    }
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, collection, addDoc, serverTimestamp, doc, updateDoc, increment, getDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) {
                showMessage('You must be logged in to reply.', 'error');
                return;
            }
            
            // Check email verification status (warn but don't block)
            if (!user.emailVerified) {
                console.warn('User email not verified, but allowing reply creation');
                // Just log a warning but don't block replying
            }
            
            // Check if user has display name (use email as fallback)
            let displayName = user.email.split('@')[0]; // Default fallback
            
            const userDocRef = doc(db, 'users', user.uid);
            try {
                const userDoc = await getDoc(userDocRef);
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    if (userData.displayName && userData.displayName.trim() !== '') {
                        displayName = userData.displayName;
                    }
                }
            } catch (error) {
                console.log('Could not fetch user display name for reply, using email fallback');
            }
            
            // Add reply to database
            const replyData = {
                postId: postId,
                authorId: user.uid,
                authorEmail: user.email,
                content: content,
                likes: 0,
                createdAt: serverTimestamp()
            };
            
            await addDoc(collection(db, 'forum_replies'), replyData);
            
            // Increment reply count on post
            const postRef = doc(db, 'forum_posts', postId);
            await updateDoc(postRef, {
                replies: increment(1),
                lastActivity: serverTimestamp()
            });
            
            showMessage('Reply posted successfully!', 'success');
            
            // Create notification for post author
            try {
                const { db, doc, getDoc } = window.firebase;
                const postRef = doc(db, 'forum_posts', postId);
                const postSnap = await getDoc(postRef);
                
                if (postSnap.exists()) {
                    const postData = postSnap.data();
                    if (postData.authorId && postData.authorId !== user.uid && window.addNotification) {
                        // Get current user's display name
                        const userInfo = await getUserDisplayName(user.uid);
                        const userName = userInfo?.name || user.email?.split('@')[0] || 'Someone';
                        
                        // Create notification for post author (not the replier)
                        window.addNotificationForUser(
                            postData.authorId, // Target user (post author)
                            'reply',
                            'New Reply',
                            `${userName} replied to your post "${postData.title || 'Untitled'}"`,
                            { postId: postId, replyId: 'new', replierId: user.uid }
                        );
                    }
                }
            } catch (error) {
                console.log('Error creating reply notification:', error);
            }
            
        } else {
            // Fallback for demo mode
            const mockReply = {
                id: Date.now().toString(),
                postId: postId,
                content: content,
                author: 'You',
                createdAt: new Date(),
                likes: 0,
                dislikes: 0,
                authorReputation: 10
            };
            
            // Store in session
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            sessionReplies.push(mockReply);
            sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
            
            // Also update mock posts to increment reply count
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            const postIndex = sessionPosts.findIndex(p => p.id === postId);
            if (postIndex >= 0) {
                sessionPosts[postIndex].replies = (sessionPosts[postIndex].replies || 0) + 1;
                sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
            }
            
            showMessage('Reply posted successfully! (Demo mode)', 'success');
        }
        
        // Clear form and reload replies immediately
        hideReplyForm();
        
        // Update reply count in modal header
        updateReplyCount(postId);
        
        // Reload replies immediately
        loadPostReplies(postId);
        
        // Also refresh any category views that might be showing outdated counts
        setTimeout(() => {
            const categoryView = document.getElementById('category-posts-view');
            if (categoryView && categoryView.style.display !== 'none') {
                const categoryTitle = categoryView.querySelector('h2')?.textContent?.split(' Posts')[0];
                if (categoryTitle) {
                    console.log('Refreshing category view after new reply:', categoryTitle);
                    loadCategoryPosts(categoryTitle);
                }
            }
            
            // Also refresh recent posts if that's the active view
            const activeTab = document.querySelector('.forum-tab.active');
            if (activeTab && activeTab.getAttribute('data-tab') === 'recent') {
                console.log('Refreshing recent posts after new reply');
                loadRecentPosts();
            }
        }, 1000);
        
    } catch (error) {
        console.error('Error submitting reply:', error);
        
        // Check if it's a Firebase permission error
        if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
            showMessage(`Firebase permission error: Your database security rules need to be updated to allow replies. Using demo mode instead.`, 'error');
            
            // Fall back to demo mode
            const mockReply = {
                id: Date.now().toString(),
                postId: postId,
                content: content,
                author: 'You',
                createdAt: new Date(),
                likes: 0,
                dislikes: 0,
                authorReputation: 10
            };
            
            // Store in session
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            sessionReplies.push(mockReply);
            sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
            
            // Also update mock posts to increment reply count
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            const postIndex = sessionPosts.findIndex(p => p.id === postId);
            if (postIndex >= 0) {
                sessionPosts[postIndex].replies = (sessionPosts[postIndex].replies || 0) + 1;
                sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
            }
            
            showMessage('Reply posted successfully! (Demo mode - permission error with Firebase)', 'success');
            
            // Clear form and reload replies immediately
            hideReplyForm();
            
            // Update reply count in modal header
            updateReplyCount(postId);
            
            // Reload replies immediately
            loadPostReplies(postId);
            
            // Also refresh any category views that might be showing outdated counts
            setTimeout(() => {
                const categoryView = document.getElementById('category-posts-view');
                if (categoryView && categoryView.style.display !== 'none') {
                    const categoryTitle = categoryView.querySelector('h2')?.textContent?.split(' Posts')[0];
                    if (categoryTitle) {
                        console.log('Refreshing category view after new reply (demo mode):', categoryTitle);
                        loadCategoryPosts(categoryTitle);
                    }
                }
                
                // Also refresh recent posts if that's the active view
                const activeTab = document.querySelector('.forum-tab.active');
                if (activeTab && activeTab.getAttribute('data-tab') === 'recent') {
                    console.log('Refreshing recent posts after new reply (demo mode)');
                    loadRecentPosts();
                }
            }, 1000);
        } else {
            showMessage(`Failed to post reply: ${error.message}`, 'error');
        }
    }
}

// Like/Dislike System
async function handleLikePost(postId) {
    await handleLike(postId, false);
}

async function handleLikeReply(replyId) {
    await handleLike(replyId, true);
}

async function handleDislikePost(postId) {
    await handleDislike(postId, false);
}

async function handleDislikeReply(replyId) {
    await handleDislike(replyId, true);
}

async function handleLike(itemId, isReply = false) {
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, collection, addDoc, deleteDoc, doc, getDoc, getDocs, query, where, updateDoc, increment, serverTimestamp } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) {
                showMessage('You must be logged in to like posts.', 'error');
                return;
            }
            
            const collectionName = isReply ? 'forum_replies' : 'forum_posts';
            const likesCollection = isReply ? 'reply_likes' : 'post_likes';
            const dislikesCollection = isReply ? 'reply_dislikes' : 'post_dislikes';
            
            // Check if user already disliked this item (remove dislike if exists)
            const existingDislikeQuery = query(
                collection(db, dislikesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const existingDislikes = await getDocs(existingDislikeQuery);
            if (!existingDislikes.empty) {
                // Remove existing dislike
                const dislikeDoc = existingDislikes.docs[0];
                await deleteDoc(dislikeDoc.ref);
                
                // Decrease dislike count and increase author reputation
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    dislikes: increment(-1)
                });
                
                const itemSnap = await getDoc(itemRef);
                if (itemSnap.exists()) {
                    const authorId = itemSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(1) // Remove negative impact
                        });
                    }
                }
            }
            
            // Check if user already liked this item
            const existingLikeQuery = query(
                collection(db, likesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const existingLikes = await getDocs(existingLikeQuery);
            
            if (!existingLikes.empty) {
                // Unlike - remove like
                const likeDoc = existingLikes.docs[0];
                await deleteDoc(likeDoc.ref);
                
                // Decrease like count
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    likes: increment(-1)
                });
                
                // Decrease author reputation
                const itemSnap = await getDoc(itemRef);
                if (itemSnap.exists()) {
                    const authorId = itemSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(-1)
                        });
                    }
                }
                
                showMessage('Like removed!', 'info');
            } else {
                // Like - add like
                await addDoc(collection(db, likesCollection), {
                    itemId: itemId,
                    userId: user.uid,
                    userEmail: user.email,
                    createdAt: serverTimestamp()
                });
                
                // Increase like count
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    likes: increment(1)
                });
                
                // Increase author reputation
                const itemSnap = await getDoc(itemRef);
                if (itemSnap.exists()) {
                    const authorId = itemSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(1)
                        });
                    }
                }
                
                showMessage('Post liked! +1 reputation to author', 'success');
                
                // Create notification for post author
                const postSnap = await getDoc(itemRef);
                if (postSnap.exists()) {
                    const postData = postSnap.data();
                    if (postData.authorId && postData.authorId !== user.uid && window.addNotificationForUser) {
                        // Get current user's display name for the notification
                        const userInfo = await getUserDisplayName(user.uid);
                        const userName = userInfo?.name || user.email?.split('@')[0] || 'Someone';
                        
                        window.addNotificationForUser(
                            postData.authorId, // Target user (post author)
                            'like',
                            'Post Liked',
                            `${userName} liked your post "${postData.title || 'Untitled'}"`,
                            { postId: itemId, likerId: user.uid }
                        );
                    }
                }
            }
            
        } else {
            // Fallback for demo mode
            toggleMockLike(itemId, isReply);
        }
        
        // Update UI immediately
        updateLikeDislikeButtons(itemId, isReply);
        
    } catch (error) {
        console.error('Error handling like:', error);
        
        // Check if it's a Firebase permission error
        if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
            showMessage(`Firebase permission error: Your database security rules need to be updated to allow likes. Using demo mode instead.`, 'error');
            // Fall back to demo mode
            toggleMockLike(itemId, isReply);
            // Update UI immediately
            updateLikeDislikeButtons(itemId, isReply);
        } else {
            showMessage(`Failed to like ${isReply ? 'reply' : 'post'}: ${error.message}`, 'error');
        }
    }
}

async function handleDislike(itemId, isReply = false) {
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, collection, addDoc, deleteDoc, doc, getDoc, getDocs, query, where, updateDoc, increment, serverTimestamp } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) {
                showMessage('You must be logged in to dislike posts.', 'error');
                return;
            }
            
            const collectionName = isReply ? 'forum_replies' : 'forum_posts';
            const likesCollection = isReply ? 'reply_likes' : 'post_likes';
            const dislikesCollection = isReply ? 'reply_dislikes' : 'post_dislikes';
            
            // Check if user already liked this item (remove like if exists)
            const existingLikeQuery = query(
                collection(db, likesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const existingLikes = await getDocs(existingLikeQuery);
            if (!existingLikes.empty) {
                // Remove existing like
                const likeDoc = existingLikes.docs[0];
                await deleteDoc(likeDoc.ref);
                
                // Decrease like count and author reputation
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    likes: increment(-1)
                });
                
                const decrementSnap = await getDoc(itemRef);
                if (decrementSnap.exists()) {
                    const authorId = decrementSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(-1) // Remove positive impact
                        });
                    }
                }
            }
            
            // Check if user already disliked this item
            const existingDislikeQuery = query(
                collection(db, dislikesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const existingDislikes = await getDocs(existingDislikeQuery);
            
            if (!existingDislikes.empty) {
                // Remove dislike
                const dislikeDoc = existingDislikes.docs[0];
                await deleteDoc(dislikeDoc.ref);
                
                // Decrease dislike count
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    dislikes: increment(-1)
                });
                
                // Increase author reputation (remove negative impact)
                const removeDislikeSnap = await getDoc(itemRef);
                if (removeDislikeSnap.exists()) {
                    const authorId = removeDislikeSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(1)
                        });
                    }
                }
                
                showMessage('Dislike removed!', 'info');
            } else {
                // Add dislike
                await addDoc(collection(db, dislikesCollection), {
                    itemId: itemId,
                    userId: user.uid,
                    userEmail: user.email,
                    createdAt: serverTimestamp()
                });
                
                // Increase dislike count
                const itemRef = doc(db, collectionName, itemId);
                await updateDoc(itemRef, {
                    dislikes: increment(1)
                });
                
                // Decrease author reputation
                const dislikeAuthorSnap = await getDoc(itemRef);
                if (dislikeAuthorSnap.exists()) {
                    const authorId = dislikeAuthorSnap.data().authorId;
                    if (authorId && authorId !== user.uid) {
                        const authorRef = doc(db, 'users', authorId);
                        await updateDoc(authorRef, {
                            reputation: increment(-1)
                        });
                    }
                }
                
                showMessage('Post disliked! -1 reputation to author', 'warning');
            }
            
        } else {
            // Fallback for demo mode
            toggleMockDislike(itemId, isReply);
        }
        
        // Update UI immediately
        updateLikeDislikeButtons(itemId, isReply);
        
    } catch (error) {
        console.error('Error handling dislike:', error);
        
        // Check if it's a Firebase permission error
        if (error.code === 'permission-denied' || error.message.includes('Missing or insufficient permissions')) {
            showMessage(`Firebase permission error: Your database security rules need to be updated to allow dislikes. Using demo mode instead.`, 'error');
            // Fall back to demo mode
            toggleMockDislike(itemId, isReply);
            // Update UI immediately
            updateLikeDislikeButtons(itemId, isReply);
        } else {
            showMessage(`Failed to dislike ${isReply ? 'reply' : 'post'}: ${error.message}`, 'error');
        }
    }
}

function toggleMockLike(itemId, isReply) {
    const likeStorageKey = isReply ? 'mockReplyLikes' : 'mockPostLikes';
    const dislikeStorageKey = isReply ? 'mockReplyDislikes' : 'mockPostDislikes';
    let likes = JSON.parse(sessionStorage.getItem(likeStorageKey) || '[]');
    let dislikes = JSON.parse(sessionStorage.getItem(dislikeStorageKey) || '[]');
    
    const userLikeIndex = likes.findIndex(like => like.itemId === itemId && like.userId === 'demo-user');
    const userDislikeIndex = dislikes.findIndex(dislike => dislike.itemId === itemId && dislike.userId === 'demo-user');
    
    // Remove existing dislike if present
    if (userDislikeIndex >= 0) {
        dislikes.splice(userDislikeIndex, 1);
        sessionStorage.setItem(dislikeStorageKey, JSON.stringify(dislikes));
    }
    
    if (userLikeIndex >= 0) {
        // Remove like
        likes.splice(userLikeIndex, 1);
        showMessage('Like removed!', 'info');
    } else {
        // Add like
        likes.push({
            itemId: itemId,
            userId: 'demo-user',
            createdAt: new Date()
        });
        showMessage(`${isReply ? 'Reply' : 'Post'} liked!`, 'success');
    }
    
    sessionStorage.setItem(likeStorageKey, JSON.stringify(likes));
    
    // Update counts immediately
    updateMockCounts(itemId, isReply);
}

function toggleMockDislike(itemId, isReply) {
    const likeStorageKey = isReply ? 'mockReplyLikes' : 'mockPostLikes';
    const dislikeStorageKey = isReply ? 'mockReplyDislikes' : 'mockPostDislikes';
    let likes = JSON.parse(sessionStorage.getItem(likeStorageKey) || '[]');
    let dislikes = JSON.parse(sessionStorage.getItem(dislikeStorageKey) || '[]');
    
    const userLikeIndex = likes.findIndex(like => like.itemId === itemId && like.userId === 'demo-user');
    const userDislikeIndex = dislikes.findIndex(dislike => dislike.itemId === itemId && dislike.userId === 'demo-user');
    
    // Remove existing like if present
    if (userLikeIndex >= 0) {
        likes.splice(userLikeIndex, 1);
        sessionStorage.setItem(likeStorageKey, JSON.stringify(likes));
    }
    
    if (userDislikeIndex >= 0) {
        // Remove dislike
        dislikes.splice(userDislikeIndex, 1);
        showMessage('Dislike removed!', 'info');
    } else {
        // Add dislike
        dislikes.push({
            itemId: itemId,
            userId: 'demo-user',
            createdAt: new Date()
        });
        showMessage(`${isReply ? 'Reply' : 'Post'} disliked!`, 'warning');
    }
    
    sessionStorage.setItem(dislikeStorageKey, JSON.stringify(dislikes));
    
    // Update counts immediately
    updateMockCounts(itemId, isReply);
}

function updateMockCounts(itemId, isReply) {
    const likeStorageKey = isReply ? 'mockReplyLikes' : 'mockPostLikes';
    const dislikeStorageKey = isReply ? 'mockReplyDislikes' : 'mockPostDislikes';
    const likes = JSON.parse(sessionStorage.getItem(likeStorageKey) || '[]');
    const dislikes = JSON.parse(sessionStorage.getItem(dislikeStorageKey) || '[]');
    
    const currentLikes = likes.filter(like => like.itemId === itemId).length;
    const currentDislikes = dislikes.filter(dislike => dislike.itemId === itemId).length;
    
    // Update the counts in the UI immediately
    const likeSelector = isReply ? 
        `[data-reply-id="${itemId}"] .like-count` : 
        `[data-post-id="${itemId}"] .like-count`;
    const dislikeSelector = isReply ? 
        `[data-reply-id="${itemId}"] .dislike-count` : 
        `[data-post-id="${itemId}"] .dislike-count`;
    
    const likeCountElement = document.querySelector(likeSelector);
    const dislikeCountElement = document.querySelector(dislikeSelector);
    
    if (likeCountElement) {
        likeCountElement.textContent = currentLikes;
    }
    if (dislikeCountElement) {
        dislikeCountElement.textContent = currentDislikes;
    }
    
    // Also update in the modal if it's open
    if (!isReply) {
        const modalLikeCount = document.querySelector('.post-modal .like-count');
        const modalDislikeCount = document.querySelector('.post-modal .dislike-count');
        if (modalLikeCount) {
            modalLikeCount.textContent = currentLikes;
        }
        if (modalDislikeCount) {
            modalDislikeCount.textContent = currentDislikes;
        }
    }
}

async function checkUserLikeDislikeStatus(itemId, isReply = false) {
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, collection, query, where, getDocs } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) return;
            
            const likesCollection = isReply ? 'reply_likes' : 'post_likes';
            const dislikesCollection = isReply ? 'reply_dislikes' : 'post_dislikes';
            
            const likeQuery = query(
                collection(db, likesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const dislikeQuery = query(
                collection(db, dislikesCollection),
                where('itemId', '==', itemId),
                where('userId', '==', user.uid)
            );
            
            const [likeSnapshot, dislikeSnapshot] = await Promise.all([
                getDocs(likeQuery),
                getDocs(dislikeQuery)
            ]);
            
            const hasLiked = !likeSnapshot.empty;
            const hasDisliked = !dislikeSnapshot.empty;
            
            updateButtonStates(itemId, hasLiked, hasDisliked, isReply);
            
        } else {
            // Fallback for demo mode
            const likeStorageKey = isReply ? 'mockReplyLikes' : 'mockPostLikes';
            const dislikeStorageKey = isReply ? 'mockReplyDislikes' : 'mockPostDislikes';
            const likes = JSON.parse(sessionStorage.getItem(likeStorageKey) || '[]');
            const dislikes = JSON.parse(sessionStorage.getItem(dislikeStorageKey) || '[]');
            
            const hasLiked = likes.some(like => like.itemId === itemId && like.userId === 'demo-user');
            const hasDisliked = dislikes.some(dislike => dislike.itemId === itemId && dislike.userId === 'demo-user');
            
            updateButtonStates(itemId, hasLiked, hasDisliked, isReply);
        }
    } catch (error) {
        console.log('Error checking like/dislike status:', error);
    }
}

// Legacy function for compatibility
async function checkUserLikeStatus(itemId, isReply = false) {
    return checkUserLikeDislikeStatus(itemId, isReply);
}

function updateButtonStates(itemId, hasLiked, hasDisliked, isReply = false) {
    const likeSelector = isReply ? 
        `.reply-like-button[data-reply-id="${itemId}"]` : 
        `.like-button[data-post-id="${itemId}"]`;
    const dislikeSelector = isReply ? 
        `.reply-dislike-button[data-reply-id="${itemId}"]` : 
        `.dislike-button[data-post-id="${itemId}"]`;
    
    const likeButton = document.querySelector(likeSelector);
    const dislikeButton = document.querySelector(dislikeSelector);
    
    if (likeButton) {
        if (hasLiked) {
            likeButton.classList.add('liked');
        } else {
            likeButton.classList.remove('liked');
        }
    }
    
    if (dislikeButton) {
        if (hasDisliked) {
            dislikeButton.classList.add('disliked');
        } else {
            dislikeButton.classList.remove('disliked');
        }
    }
}

// Legacy function for compatibility
function updateLikeButtonState(itemId, hasLiked, isReply = false) {
    updateButtonStates(itemId, hasLiked, false, isReply);
}

async function updateLikeDislikeButtons(itemId, isReply = false) {
    // This function refreshes both like/dislike status and counts after changes
    checkUserLikeDislikeStatus(itemId, isReply);
    
    // Also update the actual counts displayed
    await updateLikeDislikeCounts(itemId, isReply);
}

// Legacy function for compatibility
async function updateLikeButton(itemId, isReply = false) {
    return updateLikeDislikeButtons(itemId, isReply);
}

async function updateLikeDislikeCounts(itemId, isReply = false) {
    try {
        let currentLikes = 0;
        let currentDislikes = 0;
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Get current counts from Firebase
            const { db, doc, getDoc } = window.firebase;
            const collectionName = isReply ? 'forum_replies' : 'forum_posts';
            
            const itemRef = doc(db, collectionName, itemId);
            const countsSnap = await getDoc(itemRef);
            
            if (countsSnap.exists()) {
                currentLikes = countsSnap.data().likes || 0;
                currentDislikes = countsSnap.data().dislikes || 0;
            }
        } else {
            // Get current counts from demo mode
            const likeStorageKey = isReply ? 'mockReplyLikes' : 'mockPostLikes';
            const dislikeStorageKey = isReply ? 'mockReplyDislikes' : 'mockPostDislikes';
            const likes = JSON.parse(sessionStorage.getItem(likeStorageKey) || '[]');
            const dislikes = JSON.parse(sessionStorage.getItem(dislikeStorageKey) || '[]');
            
            currentLikes = likes.filter(like => like.itemId === itemId).length;
            currentDislikes = dislikes.filter(dislike => dislike.itemId === itemId).length;
        }
        
        // Update the counts in the UI
        const likeSelector = isReply ? 
            `[data-reply-id="${itemId}"] .like-count` : 
            `[data-post-id="${itemId}"] .like-count`;
        const dislikeSelector = isReply ? 
            `[data-reply-id="${itemId}"] .dislike-count` : 
            `[data-post-id="${itemId}"] .dislike-count`;
        
        const likeCountElement = document.querySelector(likeSelector);
        const dislikeCountElement = document.querySelector(dislikeSelector);
        
        if (likeCountElement) {
            likeCountElement.textContent = currentLikes;
        }
        if (dislikeCountElement) {
            dislikeCountElement.textContent = currentDislikes;
        }
        
        // Also update in the modal if it's open
        if (!isReply) {
            const modalLikeCount = document.querySelector('.post-modal .like-count');
            const modalDislikeCount = document.querySelector('.post-modal .dislike-count');
            if (modalLikeCount) {
                modalLikeCount.textContent = currentLikes;
            }
            if (modalDislikeCount) {
                modalDislikeCount.textContent = currentDislikes;
            }
        }
        
    } catch (error) {
        console.log('Error updating like/dislike counts:', error);
    }
}

// Legacy function for compatibility
async function updateLikeCount(itemId, isReply = false) {
    return updateLikeDislikeCounts(itemId, isReply);
}

async function updateReplyCount(postId) {
    try {
        let actualReplies = 0;
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Actually count the replies in the database instead of relying on stored count
            const { db, collection, query, where, getDocs, doc, updateDoc } = window.firebase;
            
            // Query all replies for this post
            const repliesQuery = query(
                collection(db, 'forum_replies'),
                where('postId', '==', postId)
            );
            
            const repliesSnapshot = await getDocs(repliesQuery);
            actualReplies = repliesSnapshot.size; // This is the actual count
            
            // Update the stored count in the post document to keep it in sync
            try {
                const postRef = doc(db, 'forum_posts', postId);
                await updateDoc(postRef, {
                    replies: actualReplies
                });
                console.log(`Updated post ${postId} reply count to ${actualReplies}`);
            } catch (error) {
                console.log('Error syncing stored reply count:', error);
            }
            
        } else {
            // Get current reply count from demo mode
            const sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            actualReplies = sessionReplies.filter(reply => reply.postId === postId).length;
        }
        
        // Update the reply count in the modal header
        const repliesHeader = document.querySelector('.replies-header h3');
        if (repliesHeader) {
            repliesHeader.innerHTML = `<i class="fas fa-comments"></i> Replies (${actualReplies})`;
        }
        
        // Also update any category views or post listings that might be open
        updatePostReplyCountInLists(postId, actualReplies);
        
    } catch (error) {
        console.log('Error updating reply count:', error);
    }
}

// Helper function to get the current post ID from the modal
function getCurrentPostId() {
    const modal = document.getElementById('post-modal');
    if (!modal || !modal.classList.contains('active')) return null;
    
    // Try to get from submit reply button onclick
    const submitBtn = modal.querySelector('[onclick*="submitReply"]');
    if (submitBtn) {
        const onclick = submitBtn.getAttribute('onclick');
        const match = onclick.match(/submitReply\('([^']+)'\)/);
        if (match) return match[1];
    }
    
    // Try to get from the modal dataset if available
    if (modal.dataset && modal.dataset.postId) {
        return modal.dataset.postId;
    }
    
    return null;
}

// Helper function to update reply counts in post lists/category views
function updatePostReplyCountInLists(postId, actualCount) {
    // Update in category view
    const postElements = document.querySelectorAll(`[data-post-id="${postId}"]`);
    postElements.forEach(element => {
        const replyCountElement = element.querySelector('.post-replies');
        if (replyCountElement) {
            replyCountElement.innerHTML = `<i class="fas fa-comments"></i> ${actualCount} replies`;
        }
    });
    
    // Update in recent posts view
    const recentPostElements = document.querySelectorAll('.post-item');
    recentPostElements.forEach(element => {
        const postTitle = element.querySelector('.post-title');
        if (postTitle && postTitle.getAttribute('onclick') && postTitle.getAttribute('onclick').includes(postId)) {
            const replyCountElement = element.querySelector('.post-replies');
            if (replyCountElement) {
                replyCountElement.innerHTML = `<i class="fas fa-comments"></i> ${actualCount} replies`;
            }
        }
         });
}

// Comprehensive function to recalculate and fix all reply counts
async function recalculateAllReplyCounts() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        console.log('Firebase not available for reply count recalculation');
        return;
    }
    
    try {
        const { db, collection, getDocs, doc, updateDoc, query, where } = window.firebase;
        
        console.log('🔄 Recalculating all reply counts...');
        
        // Get all posts
        const postsSnapshot = await getDocs(collection(db, 'forum_posts'));
        let updatedCount = 0;
        
        for (const postDoc of postsSnapshot.docs) {
            const postId = postDoc.id;
            const postData = postDoc.data();
            
            // Count actual replies for this post
            const repliesQuery = query(
                collection(db, 'forum_replies'),
                where('postId', '==', postId)
            );
            const repliesSnapshot = await getDocs(repliesQuery);
            const actualReplyCount = repliesSnapshot.size;
            const storedReplyCount = postData.replies || 0;
            
            // Update if there's a discrepancy
            if (actualReplyCount !== storedReplyCount) {
                await updateDoc(doc(db, 'forum_posts', postId), {
                    replies: actualReplyCount
                });
                
                console.log(`📝 Fixed post ${postId}: ${storedReplyCount} → ${actualReplyCount} replies`);
                updatedCount++;
                
                // Update UI if this post is currently visible
                updatePostReplyCountInLists(postId, actualReplyCount);
                
                // If this post is currently open in modal, update that too
                const currentPostId = getCurrentPostId();
                if (currentPostId === postId) {
                    const repliesHeader = document.querySelector('.replies-header h3');
                    if (repliesHeader) {
                        repliesHeader.innerHTML = `<i class="fas fa-comments"></i> Replies (${actualReplyCount})`;
                    }
                }
            }
        }
        
        if (updatedCount > 0) {
            console.log(`✅ Fixed ${updatedCount} reply count discrepancies`);
            showMessage(`Fixed ${updatedCount} reply count issues`, 'success');
        } else {
            console.log('✅ All reply counts are accurate');
        }
        
    } catch (error) {
        console.error('Error recalculating reply counts:', error);
    }
}

// Auto-fix reply counts when forum loads
document.addEventListener('DOMContentLoaded', function() {
    // Run after a delay to ensure Firebase is initialized
    setTimeout(() => {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            recalculateAllReplyCounts();
        }
    }, 3000);
});

async function handleNewPost(defaultCategory = '') {
    console.log('New post clicked', defaultCategory);
    
    // Track new post attempt
    if (window.firebase && window.firebase.auth && window.firebase.db) {
        const { auth, db, collection, addDoc, serverTimestamp } = window.firebase;
        const user = auth.currentUser;
        
        if (user) {
            try {
                await addDoc(collection(db, 'forum_interactions'), {
                    userId: user.uid,          // Required by security rules
                    userEmail: user.email,
                    action: 'new_post_attempt',
                    timestamp: serverTimestamp()
                });
            } catch (error) {
                console.log('Error tracking new post attempt:', error);
            }
        }
    }
    
    // Show new post form
    showNewPostForm(defaultCategory);
}

function showNewPostForm(defaultCategory = '') {
    // Hide any existing messages
    const existingMessage = document.querySelector('.forum-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create or show new post form
    let newPostForm = document.getElementById('new-post-form');
    if (!newPostForm) {
        newPostForm = document.createElement('div');
        newPostForm.id = 'new-post-form';
        newPostForm.className = 'new-post-form';
        
        // Insert at the top of forum content
        const forumContent = document.getElementById('forum-content');
        if (forumContent) {
            const container = forumContent.querySelector('.container');
            if (container) {
                container.insertBefore(newPostForm, container.firstChild);
            }
        }
    }
    
    newPostForm.innerHTML = `
        <h3><i class="fas fa-plus"></i> Create New Post</h3>
        <form id="create-post-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="post-title">Post Title *</label>
                    <input type="text" id="post-title" name="title" required maxlength="100" 
                           placeholder="Enter a descriptive title for your post">
                </div>
                <div class="form-group">
                    <label for="post-category">Category *</label>
                    <select id="post-category" name="category" required>
                        <option value="">Select Category</option>
                        <option value="General Discussion" ${defaultCategory === 'General Discussion' ? 'selected' : ''}>General Discussion</option>
                        <option value="Bug Reports" ${defaultCategory === 'Bug Reports' ? 'selected' : ''}>Bug Reports</option>
                        <option value="Feature Requests" ${defaultCategory === 'Feature Requests' ? 'selected' : ''}>Feature Requests</option>
                        <option value="Technical Support" ${defaultCategory === 'Technical Support' ? 'selected' : ''}>Technical Support</option>
                        <option value="Gaming & Performance" ${defaultCategory === 'Gaming & Performance' ? 'selected' : ''}>Gaming & Performance</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="post-content">Content *</label>
                <textarea id="post-content" name="content" required 
                          placeholder="Write your post content here. Be descriptive and helpful to get better responses."></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="hideNewPostForm()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Create Post
                </button>
            </div>
        </form>
    `;
    
    newPostForm.classList.add('active');
    
    // Add form submit handler
    const form = document.getElementById('create-post-form');
    if (form) {
        form.addEventListener('submit', handleCreatePost);
    }
    
    // Focus on title field
    const titleField = document.getElementById('post-title');
    if (titleField) {
        titleField.focus();
    }
}

function hideNewPostForm() {
    const newPostForm = document.getElementById('new-post-form');
    if (newPostForm) {
        newPostForm.classList.remove('active');
        setTimeout(() => {
            if (newPostForm.parentNode) {
                newPostForm.remove();
            }
        }, 300);
    }
}

async function handleCreatePost(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const title = formData.get('title').trim();
    const category = formData.get('category');
    const content = formData.get('content').trim();
    
    // Validate form
    if (!title || !category || !content) {
        showMessage('Please fill in all required fields.', 'error');
        return;
    }
    
    if (title.length < 10) {
        showMessage('Post title must be at least 10 characters long.', 'error');
        return;
    }
    
    if (content.length < 20) {
        showMessage('Post content must be at least 20 characters long.', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Post...';
    submitBtn.disabled = true;
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Create post in Firebase
            const { auth, db, collection, addDoc, serverTimestamp, doc, getDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) {
                throw new Error('You must be logged in to create posts.');
            }
            
            // Check email verification status (warn but don't block)
            if (!user.emailVerified) {
                console.warn('User email not verified, but allowing post creation');
                // Just log a warning but don't block posting
            }
            
            // Check if user has display name (use email as fallback)
            let displayName = user.email.split('@')[0]; // Default fallback
            
            const userDocRef = doc(db, 'users', user.uid);
            try {
                const userDoc = await getDoc(userDocRef);
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    if (userData.displayName && userData.displayName.trim() !== '') {
                        displayName = userData.displayName;
                    }
                }
            } catch (error) {
                console.log('Could not fetch user display name, using email fallback');
            }
            
            const postData = {
                authorId: user.uid,
                authorEmail: user.email,
                title: title,
                content: content,
                category: category,
                replies: 0,
                views: 0,
                createdAt: serverTimestamp(),
                lastActivity: serverTimestamp(),
                isSticky: false
            };
            
            const docRef = await addDoc(collection(db, 'forum_posts'), postData);
            console.log('Post created successfully:', docRef.id);
            
            showMessage('Post created successfully!', 'success');
            hideNewPostForm();
            
            // Refresh the current view with a small delay to ensure Firebase has processed the post
            setTimeout(() => {
                const categoryPostsView = document.getElementById('category-posts-view');
                if (categoryPostsView && categoryPostsView.style.display !== 'none') {
                    // We're in a category view, refresh it and stay there
                    console.log('Refreshing category view for:', category);
                    loadCategoryPosts(category);
                } else {
                    // We're in the main forum view, check which tab is currently active
                    const activeTab = document.querySelector('.forum-tab.active');
                    const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : 'recent';
                    
                    console.log('Refreshing view for active tab:', activeTabId);
                    
                    if (activeTabId === 'recent') {
                        loadRecentPosts();
                    }
                    // Don't force switch to recent tab - stay on current tab
                }
            }, 1000); // 1 second delay to allow Firebase to process
            
        } else {
            // Fallback for development - simulate post creation
            console.log('Simulating post creation:', { title, category, content });
            
            showMessage('Post created successfully! (Demo mode - not saved to database)', 'success');
            hideNewPostForm();
            
            // Add to mock recent posts for immediate feedback
            addMockPost(title, content, category);
            
            // Refresh the view to show the new mock post
            setTimeout(() => {
                const categoryPostsView = document.getElementById('category-posts-view');
                if (categoryPostsView && categoryPostsView.style.display !== 'none') {
                    console.log('Refreshing category view for mock post:', category);
                    loadCategoryPosts(category);
                } else {
                    // Check which tab is currently active and only refresh if it's recent
                    const activeTab = document.querySelector('.forum-tab.active');
                    const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : 'recent';
                    
                    console.log('Refreshing mock post view for active tab:', activeTabId);
                    
                    if (activeTabId === 'recent') {
                        loadRecentPosts();
                    }
                    // Don't force switch to recent tab - stay on current tab
                }
            }, 500);
        }
        
    } catch (error) {
        console.error('Error creating post:', error);
        showMessage(`Failed to create post: ${error.message}`, 'error');
        
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

function addMockPost(title, content, category) {
    // This is for development/demo purposes
    const mockPost = {
        id: Date.now().toString(),
        title: title,
        content: content,
        category: category,
        author: 'You',
        replies: 0,
        views: 0,
        lastActivity: new Date(),
        createdAt: new Date(),
        isSticky: false
    };
    
    // Store in session storage for persistence during the session
    let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
    sessionPosts.unshift(mockPost); // Add to beginning
    sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
    
    console.log('Mock post added:', mockPost);
}

// Utility functions
function formatTimeAgo(date) {
    if (!date) return 'Unknown';
    
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
}

function showMessage(message, type = 'info') {
    // Remove existing messages
    const existingMessage = document.querySelector('.forum-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `forum-message ${type}`;
    messageElement.innerHTML = `
        <i class="fas ${type === 'info' ? 'fa-info-circle' : 'fa-exclamation-circle'}"></i>
        <span>${message}</span>
        <button class="message-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to forum content
    const forumContent = document.getElementById('forum-content');
    if (forumContent) {
        forumContent.insertBefore(messageElement, forumContent.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.remove();
            }
        }, 5000);
    }
}

// ============================================
// MODERATION SYSTEM FUNCTIONS FOR FORUM
// ============================================

// Check if current user is a moderator or admin
async function checkUserIsModerator() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        return false;
    }
    
    try {
        const { auth, db, doc, getDoc } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) return false;
        
        const userDocRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            const userRole = userData.role || 'user';
            return userRole === 'moderator' || userRole === 'admin';
        }
    } catch (error) {
        console.error('Error checking moderator status:', error);
    }
    
    return false;
}

// Add moderation controls to posts and replies
function addModerationControls(elementId, contentType, contentId, authorId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    // Check if user is moderator
    checkUserIsModerator().then(isModerator => {
        if (!isModerator) return;
        
        // Add moderator action buttons
        const moderationControls = document.createElement('div');
        moderationControls.className = 'moderation-controls';
        moderationControls.innerHTML = `
            <div class="mod-actions">
                <button class="btn-mod-edit" onclick="moderatorEdit('${contentId}', '${contentType}')">
                    <i class="fas fa-edit"></i> Mod Edit
                </button>
                <button class="btn-mod-delete" onclick="moderatorDelete('${contentId}', '${contentType}')">
                    <i class="fas fa-trash"></i> Mod Delete
                </button>
                ${authorId ? `
                    <button class="btn-mod-ban" onclick="moderatorBanUser('${authorId}')">
                        <i class="fas fa-ban"></i> Ban User
                    </button>
                ` : ''}
            </div>
        `;
        
        element.appendChild(moderationControls);
    });
}

// Moderator edit function
async function moderatorEdit(contentId, contentType) {
    try {
        if (!await checkUserIsModerator()) {
            showMessage('Insufficient permissions', 'error');
            return;
        }
        
        const reason = prompt('Enter moderation reason for editing:');
        if (!reason) return;
        
        // Log moderation action
        await logModerationAction('edit', contentType, contentId, reason);
        
        // Proceed with edit (use existing edit functions)
        if (contentType === 'post') {
            editPost(contentId);
        } else if (contentType === 'reply') {
            editReply(contentId);
        }
        
        showMessage('Moderator edit initiated', 'success');
        
    } catch (error) {
        console.error('Error in moderator edit:', error);
        showMessage('Failed to initiate moderator edit', 'error');
    }
}

// Moderator delete function
async function moderatorDelete(contentId, contentType) {
    try {
        if (!await checkUserIsModerator()) {
            showMessage('Insufficient permissions', 'error');
            return;
        }
        
        const reason = prompt('Enter moderation reason for deletion:');
        if (!reason) return;
        
        const confirmed = confirm(`Are you sure you want to delete this ${contentType}? This action cannot be undone.`);
        if (!confirmed) return;
        
        // Log moderation action
        await logModerationAction('delete', contentType, contentId, reason);
        
        // Proceed with deletion
        if (contentType === 'post') {
            await deletePost(contentId);
        } else if (contentType === 'reply') {
            await deleteReply(contentId);
        }
        
        showMessage(`${contentType.charAt(0).toUpperCase() + contentType.slice(1)} deleted by moderator`, 'success');
        
    } catch (error) {
        console.error('Error in moderator delete:', error);
        showMessage('Failed to delete content', 'error');
    }
}

// Moderator ban user function
async function moderatorBanUser(userId) {
    try {
        if (!await checkUserIsModerator()) {
            showMessage('Insufficient permissions', 'error');
            return;
        }
        
        const reason = prompt('Enter ban reason:');
        if (!reason) return;
        
        const confirmed = confirm('Are you sure you want to ban this user? They will be unable to post or reply.');
        if (!confirmed) return;
        
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, doc, updateDoc, serverTimestamp } = window.firebase;
        
        // Ban the user
        await updateDoc(doc(db, 'users', userId), {
            banned: true,
            bannedAt: serverTimestamp(),
            bannedReason: reason,
            bannedBy: window.firebase.auth.currentUser.uid
        });
        
        // Log moderation action
        await logModerationAction('ban', 'user', userId, reason);
        
        showMessage('User has been banned', 'success');
        
    } catch (error) {
        console.error('Error banning user:', error);
        showMessage('Failed to ban user', 'error');
    }
}

// Report content function
async function reportContent(contentId, contentType, authorId) {
    try {
        const reason = prompt('Why are you reporting this content?');
        if (!reason) return;
        
        if (!window.firebase || !window.firebase.db) {
            showMessage('Reporting requires Firebase connection', 'error');
            return;
        }
        
        const { db, collection, addDoc, serverTimestamp, auth } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) {
            showMessage('You must be logged in to report content', 'error');
            return;
        }
        
        // Create report
        await addDoc(collection(db, 'reports'), {
            contentId: contentId,
            contentType: contentType,
            authorId: authorId,
            reporterId: user.uid,
            reporterEmail: user.email,
            reason: reason,
            status: 'pending',
            createdAt: serverTimestamp()
        });
        
        showMessage('Content reported successfully. Moderators will review it.', 'success');
        
    } catch (error) {
        console.error('Error reporting content:', error);
        showMessage('Failed to report content', 'error');
    }
}

// Log moderation actions
async function logModerationAction(action, targetType, targetId, reason) {
    try {
        if (!window.firebase || !window.firebase.db) return;
        
        const { db, collection, addDoc, serverTimestamp, auth } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) return;
        
        await addDoc(collection(db, 'moderation_logs'), {
            action: action,
            targetType: targetType,
            targetId: targetId,
            moderatorId: user.uid,
            moderatorEmail: user.email,
            reason: reason,
            timestamp: serverTimestamp()
        });
        
    } catch (error) {
        console.error('Error logging moderation action:', error);
    }
}

// Custom styled confirmation dialog
function showConfirmDialog(title, message, confirmText = 'Delete', cancelText = 'Cancel', type = 'danger') {
    return new Promise((resolve) => {
        // Remove any existing confirmation dialog
        const existingDialog = document.getElementById('confirm-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }
        
        // Create dialog HTML
        const dialogHTML = `
            <div id="confirm-dialog" class="confirm-dialog-overlay">
                <div class="confirm-dialog">
                    <div class="confirm-dialog-header ${type}">
                        <div class="confirm-icon">
                            <i class="fas fa-${type === 'danger' ? 'exclamation-triangle' : 'question-circle'}"></i>
                        </div>
                        <h3>${title}</h3>
                    </div>
                    
                    <div class="confirm-dialog-content">
                        <p>${message}</p>
                    </div>
                    
                    <div class="confirm-dialog-actions">
                        <button class="btn btn-secondary confirm-cancel">${cancelText}</button>
                        <button class="btn btn-${type === 'danger' ? 'danger' : 'primary'} confirm-ok">${confirmText}</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add dialog to page
        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        
        // Get elements
        const overlay = document.getElementById('confirm-dialog');
        const cancelBtn = overlay.querySelector('.confirm-cancel');
        const okBtn = overlay.querySelector('.confirm-ok');
        
        // Add event listeners
        const cleanup = () => {
            overlay.classList.add('dialog-closing');
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.remove();
                }
            }, 300);
        };
        
        // Cancel actions
        cancelBtn.addEventListener('click', () => {
            cleanup();
            resolve(false);
        });
        
        // Click outside to cancel
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                cleanup();
                resolve(false);
            }
        });
        
        // Escape key to cancel
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                cleanup();
                resolve(false);
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        
        // Confirm action
        okBtn.addEventListener('click', () => {
            cleanup();
            resolve(true);
        });
        
        // Focus the confirm button
        setTimeout(() => okBtn.focus(), 100);
    });
}

// Make confirmation dialog and reply count fixer globally available
window.showConfirmDialog = showConfirmDialog;
window.recalculateAllReplyCounts = recalculateAllReplyCounts;

// Delete reply function (for reply authors, OPs, and moderators)
async function deleteReply(replyId) {
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { auth, db, doc, getDoc, deleteDoc, updateDoc, increment, collection, query, where, getDocs } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) {
            showMessage('You must be logged in to delete replies.', 'error');
            return;
        }
        
        // Get reply data first to check permissions and update post count
        const replyRef = doc(db, 'forum_replies', replyId);
        const replySnap = await getDoc(replyRef);
        
        if (replySnap.exists()) {
            const replyData = replySnap.data();
            const postId = replyData.postId;
            
            // Check if user has permission to delete this reply
            const isReplyAuthor = replyData.authorId === user.uid;
            
            // Check if user is the original poster (OP) of the thread
            let isOP = false;
            try {
                const postRef = doc(db, 'forum_posts', postId);
                const postSnap = await getDoc(postRef);
                if (postSnap.exists()) {
                    const postData = postSnap.data();
                    isOP = postData.authorId === user.uid;
                }
            } catch (error) {
                console.log('Error checking OP status:', error);
            }
            
            // Check if user is a moderator
            const isModerator = await checkUserIsModerator();
            
            // Validate permissions
            if (!isReplyAuthor && !isOP && !isModerator) {
                showMessage('You can only delete your own replies or replies on your posts.', 'error');
                return;
            }
            
            // Show custom confirmation dialog
            const confirmTitle = isReplyAuthor 
                ? 'Delete Your Reply?' 
                : isOP 
                    ? 'Delete Reply from Your Post?' 
                    : 'Delete Reply? (Moderator Action)';
                    
            const confirmMessage = isReplyAuthor 
                ? 'Are you sure you want to delete your reply? This action cannot be undone.' 
                : isOP 
                    ? 'Are you sure you want to delete this reply from your post? This action cannot be undone.' 
                    : 'Are you sure you want to delete this reply? This moderator action cannot be undone.';
                    
            const confirmed = await showConfirmDialog(confirmTitle, confirmMessage, 'Delete Reply', 'Cancel', 'danger');
            
            if (!confirmed) {
                return;
            }
            
            // Delete the reply
            await deleteDoc(replyRef);
            
            // Decrease reply count on post
            const postRef = doc(db, 'forum_posts', postId);
            await updateDoc(postRef, {
                replies: increment(-1)
            });
            
            // Delete associated likes/dislikes
            const likesQuery = query(collection(db, 'reply_likes'), where('itemId', '==', replyId));
            const likesSnapshot = await getDocs(likesQuery);
            
            const dislikesQuery = query(collection(db, 'reply_dislikes'), where('itemId', '==', replyId));
            const dislikesSnapshot = await getDocs(dislikesQuery);
            
            const deletePromises = [];
            likesSnapshot.forEach(doc => deletePromises.push(deleteDoc(doc.ref)));
            dislikesSnapshot.forEach(doc => deletePromises.push(deleteDoc(doc.ref)));
            
            await Promise.all(deletePromises);
            
            // Show success message
            showMessage('Reply deleted successfully!', 'success');
            
            // Force immediate reload of replies with loading indicator
            const repliesList = document.getElementById('replies-list');
            if (repliesList) {
                repliesList.innerHTML = '<p class="loading-message"><i class="fas fa-spinner fa-spin"></i> Refreshing replies...</p>';
            }
            
            // Update reply count in modal header immediately
            updateReplyCount(postId);
            
            // Reload replies with a small delay to ensure Firebase sync
            setTimeout(() => {
                loadPostReplies(postId);
                
                // Also refresh any category views that might be showing outdated counts
                const categoryView = document.getElementById('category-posts-view');
                if (categoryView && categoryView.style.display !== 'none') {
                    const categoryTitle = categoryView.querySelector('h2')?.textContent?.split(' Posts')[0];
                    if (categoryTitle) {
                        console.log('Refreshing category view after reply deletion:', categoryTitle);
                        loadCategoryPosts(categoryTitle);
                    }
                }
                
                // Also refresh recent posts if that's the active view
                const activeTab = document.querySelector('.forum-tab.active');
                if (activeTab && activeTab.getAttribute('data-tab') === 'recent') {
                    console.log('Refreshing recent posts after reply deletion');
                    setTimeout(() => loadRecentPosts(), 1000);
                }
            }, 500);
        } else {
            showMessage('Reply not found.', 'error');
        }
        
    } catch (error) {
        console.error('Error deleting reply:', error);
        
        if (error.code === 'permission-denied') {
            showMessage('Permission denied: You cannot delete this reply.', 'error');
        } else {
            showMessage(`Failed to delete reply: ${error.message}`, 'error');
        }
    }
}

// Edit reply function (placeholder for moderators)
function editReply(replyId) {
    showMessage('Reply editing functionality will be implemented soon', 'info');
}

// Add report buttons to posts and replies
function addReportButton(elementId, contentType, contentId, authorId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const reportButton = document.createElement('button');
    reportButton.className = 'btn-report';
    reportButton.innerHTML = '<i class="fas fa-flag"></i> Report';
    reportButton.onclick = () => reportContent(contentId, contentType, authorId);
    
    // Find actions container or create one
    let actionsContainer = element.querySelector('.post-actions, .reply-actions');
    if (!actionsContainer) {
        actionsContainer = document.createElement('div');
        actionsContainer.className = `${contentType}-actions`;
        element.appendChild(actionsContainer);
    }
    
    actionsContainer.appendChild(reportButton);
}

// Check if user is banned before allowing actions
async function checkUserBanStatus() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        return false; // Not banned if no Firebase
    }
    
    try {
        const { auth, db, doc, getDoc } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) return false;
        
        const userDocRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            return userData.banned === true;
        }
    } catch (error) {
        console.error('Error checking ban status:', error);
    }
    
    return false;
}

// Override post creation to check ban status
const originalHandleCreatePost = handleCreatePost;
handleCreatePost = async function(event) {
    const isBanned = await checkUserBanStatus();
    if (isBanned) {
        showMessage('You are banned from posting. Contact moderators if you believe this is an error.', 'error');
        return;
    }
    
    return originalHandleCreatePost.call(this, event);
};

// Override reply submission to check ban status
const originalSubmitReply = submitReply;
submitReply = async function(postId) {
    const isBanned = await checkUserBanStatus();
    if (isBanned) {
        showMessage('You are banned from replying. Contact moderators if you believe this is an error.', 'error');
        return;
    }
    
    return originalSubmitReply.call(this, postId);
};

// Legacy API compatibility (for main site integration)
window.ForumAPI = {
    loginUser: async (email, password) => {
        // Redirect to Firebase auth
        if (window.firebase && window.firebase.auth) {
            const { auth, signInWithEmailAndPassword } = window.firebase;
            return await signInWithEmailAndPassword(auth, email, password);
        } else {
            throw new Error('Firebase not available');
        }
    },
    
    registerUser: async (email, password, confirmPassword) => {
        // Redirect to Firebase auth
        if (window.firebase && window.firebase.auth) {
            const { auth, createUserWithEmailAndPassword } = window.firebase;
            return await createUserWithEmailAndPassword(auth, email, password);
        } else {
            throw new Error('Firebase not available');
        }
    },
    
    logoutUser: async () => {
        if (window.firebase && window.firebase.auth) {
            const { auth, signOut } = window.firebase;
            return await signOut(auth);
        }
    },
    
    checkUserAuthentication: () => {
        if (window.firebase && window.firebase.auth) {
            const { auth } = window.firebase;
            return auth.currentUser !== null;
        }
        return checkLegacyAuth();
    },
    
    updateForumStats,
    checkUserIsModerator,
    reportContent,
    moderatorBanUser
};

// Function to create sample forum posts (for testing)
async function createSampleForumPost(title, content, category) {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) return;
    
    const { auth, db, collection, addDoc, serverTimestamp } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) {
        console.log('User must be logged in to create posts');
        return;
    }
    
    try {
        const postData = {
            authorId: user.uid,           // Required by security rules
            authorEmail: user.email,
            title: title,
            content: content,
            category: category,
            replies: 0,
            views: 0,
            createdAt: serverTimestamp(),
            lastActivity: serverTimestamp(),
            isSticky: false
        };
        
        const docRef = await addDoc(collection(db, 'forum_posts'), postData);
        console.log('Forum post created:', docRef.id);
        return docRef.id;
    } catch (error) {
        console.error('Error creating forum post:', error);
        throw error;
    }
}

// Initialize sample posts for testing (call this after user logs in)
async function initializeSamplePosts() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) return;
    
    const { auth, db, collection, getDocs } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) return;
    
    try {
        // Check if there are already posts
        const postsSnapshot = await getDocs(collection(db, 'forum_posts'));
        
        if (postsSnapshot.empty) {
            console.log('Creating sample forum posts...');
            
            // Create some sample posts
            await createSampleForumPost(
                'Welcome to Armory X Forums!',
                'Welcome to the official Armory X community forums. Feel free to discuss features, report bugs, and connect with other users.',
                'General Discussion'
            );
            
            await createSampleForumPost(
                'Speed test accuracy improvements in v1.2.0',
                'The latest update includes significant improvements to speed test accuracy and reliability.',
                'General Discussion'
            );
            
            await createSampleForumPost(
                'Widget loading slowly on high-res displays',
                'Has anyone else experienced slow widget loading on 4K displays? Looking for solutions.',
                'Bug Reports'
            );
            
            console.log('Sample posts created successfully!');
        }
    } catch (error) {
        console.log('Error initializing sample posts:', error);
    }
}

// Development helper function to clear mock posts
window.clearMockPosts = function() {
    sessionStorage.removeItem('mockForumPosts');
    sessionStorage.removeItem('mockForumReplies');
    sessionStorage.removeItem('mockPostLikes');
    sessionStorage.removeItem('mockReplyLikes');
    sessionStorage.removeItem('mockPostDislikes');
    sessionStorage.removeItem('mockReplyDislikes');
    console.log('Mock posts, replies, likes and dislikes cleared. Refresh the page to see changes.');
};

// Development helper function to test real-time updates
window.testRealtimeUpdates = function() {
    console.log('Testing real-time updates...');
    
    // Test like updates
    const likeButtons = document.querySelectorAll('[data-post-id]');
    if (likeButtons.length > 0) {
        const firstPostId = likeButtons[0].getAttribute('data-post-id');
        console.log('Testing like update for post:', firstPostId);
        updateLikeCount(firstPostId, false);
    }
    
    // Test reply updates
    const modal = document.getElementById('post-modal');
    if (modal && modal.classList.contains('active')) {
        const postId = modal.querySelector('[onclick*="submitReply"]')?.getAttribute('onclick')?.match(/'([^']+)'/)?.[1];
        if (postId) {
            console.log('Testing reply update for post:', postId);
            updateReplyCount(postId);
            loadPostReplies(postId);
        }
    }
    
    console.log('Real-time update test completed. Check console for details.');
};

// OP (Original Poster) Management Functions
async function checkIfUserIsAuthor(postData) {
    try {
        if (window.firebase && window.firebase.auth) {
            const user = window.firebase.auth.currentUser;
            if (!user) return false;
            
            // Check if user is the author by comparing user ID
            if (postData.authorId && postData.authorId === user.uid) {
                return true;
            }
            
            // Fallback: check by email
            if (postData.authorEmail && postData.authorEmail === user.email) {
                return true;
            }
        } else {
            // For demo mode, check if the author is "You" (user-created posts)
            if (postData.author === 'You') {
                return true;
            }
            
            // Check session storage for user-created posts
            const sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            const post = sessionPosts.find(p => p.id === postData.id);
            if (post && post.author === 'You') {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        console.error('Error checking if user is author:', error);
        return false;
    }
}

async function editPost(postId) {
    console.log('Editing post:', postId);
    
    // Hide the content display and show the edit form
    const contentDisplay = document.getElementById('post-content-display');
    const editForm = document.getElementById('post-edit-form');
    
    if (contentDisplay) contentDisplay.style.display = 'none';
    if (editForm) editForm.style.display = 'block';
    
    // Focus on the title field
    const titleField = document.getElementById('edit-post-title');
    if (titleField) {
        titleField.focus();
        titleField.select();
    }
}

function cancelEditPost() {
    console.log('Cancelling post edit');
    
    // Show the content display and hide the edit form
    const contentDisplay = document.getElementById('post-content-display');
    const editForm = document.getElementById('post-edit-form');
    
    if (contentDisplay) contentDisplay.style.display = 'block';
    if (editForm) editForm.style.display = 'none';
}

async function savePostEdit(postId) {
    console.log('Saving post edit for:', postId);
    
    const titleField = document.getElementById('edit-post-title');
    const contentField = document.getElementById('edit-post-content');
    const categoryField = document.getElementById('edit-post-category');
    
    if (!titleField || !contentField || !categoryField) {
        showMessage('Error: Edit form fields not found', 'error');
        return;
    }
    
    const newTitle = titleField.value.trim();
    const newContent = contentField.value.trim();
    const newCategory = categoryField.value;
    
    // Validation
    if (!newTitle) {
        showMessage('Title is required', 'error');
        titleField.focus();
        return;
    }
    
    if (newTitle.length < 5) {
        showMessage('Title must be at least 5 characters long', 'error');
        titleField.focus();
        return;
    }
    
    if (!newContent) {
        showMessage('Content is required', 'error');
        contentField.focus();
        return;
    }
    
    if (newContent.length < 10) {
        showMessage('Content must be at least 10 characters long', 'error');
        contentField.focus();
        return;
    }
    
    try {
        if (window.firebase && window.firebase.db) {
            const { db, doc, updateDoc, serverTimestamp } = window.firebase;
            
            // Update the post in Firebase
            const postRef = doc(db, 'forum_posts', postId);
            await updateDoc(postRef, {
                title: newTitle,
                content: newContent,
                category: newCategory,
                lastModified: serverTimestamp(),
                edited: true
            });
            
            showMessage('Post updated successfully!', 'success');
            
        } else {
            // Update in session storage for demo mode
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            const postIndex = sessionPosts.findIndex(p => p.id === postId);
            
            if (postIndex >= 0) {
                sessionPosts[postIndex].title = newTitle;
                sessionPosts[postIndex].content = newContent;
                sessionPosts[postIndex].category = newCategory;
                sessionPosts[postIndex].lastModified = new Date();
                sessionPosts[postIndex].edited = true;
                
                sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
            }
            
            showMessage('Post updated successfully! (Demo mode)', 'success');
        }
        
        // Update the display
        const modalTitle = document.querySelector('.post-modal-title');
        const contentDisplay = document.getElementById('post-content-display');
        
        if (modalTitle) modalTitle.textContent = newTitle;
        if (contentDisplay) contentDisplay.innerHTML = newContent;
        
        // Hide edit form and show content
        cancelEditPost();
        
        // Refresh the forum displays
        loadRecentPosts();
        
    } catch (error) {
        console.error('Error updating post:', error);
        
        if (error.code === 'permission-denied') {
            showMessage('Firebase permission error: Unable to update post. Using demo mode instead.', 'error');
            
            // Fallback to demo mode
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            const postIndex = sessionPosts.findIndex(p => p.id === postId);
            
            if (postIndex >= 0) {
                sessionPosts[postIndex].title = newTitle;
                sessionPosts[postIndex].content = newContent;
                sessionPosts[postIndex].category = newCategory;
                sessionPosts[postIndex].lastModified = new Date();
                sessionPosts[postIndex].edited = true;
                
                sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
                
                // Update the display
                const modalTitle = document.querySelector('.post-modal-title');
                const contentDisplay = document.getElementById('post-content-display');
                
                if (modalTitle) modalTitle.textContent = newTitle;
                if (contentDisplay) contentDisplay.innerHTML = newContent;
                
                cancelEditPost();
                loadRecentPosts();
                
                showMessage('Post updated successfully! (Demo mode)', 'success');
            }
        } else {
            showMessage('Failed to update post. Please try again.', 'error');
        }
    }
}

async function deletePost(postId) {
    console.log('Deleting post:', postId);
    
    // Show confirmation dialog
    const confirmed = confirm('Are you sure you want to delete this post? This action cannot be undone.');
    
    if (!confirmed) {
        return;
    }
    
    try {
        if (window.firebase && window.firebase.db) {
            const { db, doc, deleteDoc, collection, query, where, getDocs } = window.firebase;
            
            // Delete all replies to this post first
            const repliesQuery = query(collection(db, 'forum_replies'), where('postId', '==', postId));
            const repliesSnapshot = await getDocs(repliesQuery);
            
            const deletePromises = [];
            repliesSnapshot.forEach((replyDoc) => {
                deletePromises.push(deleteDoc(replyDoc.ref));
            });
            
            // Delete all likes for this post
            const likesQuery = query(collection(db, 'post_likes'), where('postId', '==', postId));
            const likesSnapshot = await getDocs(likesQuery);
            
            likesSnapshot.forEach((likeDoc) => {
                deletePromises.push(deleteDoc(likeDoc.ref));
            });
            
            // Delete all dislikes for this post
            const dislikesQuery = query(collection(db, 'post_dislikes'), where('postId', '==', postId));
            const dislikesSnapshot = await getDocs(dislikesQuery);
            
            dislikesSnapshot.forEach((dislikeDoc) => {
                deletePromises.push(deleteDoc(dislikeDoc.ref));
            });
            
            // Wait for all deletions to complete
            await Promise.all(deletePromises);
            
            // Finally, delete the post itself
            const postRef = doc(db, 'forum_posts', postId);
            await deleteDoc(postRef);
            
            showMessage('Post deleted successfully!', 'success');
            
        } else {
            // Delete from session storage for demo mode
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            sessionPosts = sessionPosts.filter(p => p.id !== postId);
            sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
            
            // Also delete replies
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            sessionReplies = sessionReplies.filter(r => r.postId !== postId);
            sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
            
            showMessage('Post deleted successfully! (Demo mode)', 'success');
        }
        
        // Close the modal
        hidePostModal();
        
        // Refresh the forum displays
        loadRecentPosts();
        
        // If we're in a category view, refresh that too
        const categoryPostsView = document.getElementById('category-posts-view');
        if (categoryPostsView && categoryPostsView.style.display !== 'none') {
            const categoryTitle = document.querySelector('.category-posts-header h2')?.textContent || 'General Discussion';
            loadCategoryPosts(categoryTitle);
        }
        
    } catch (error) {
        console.error('Error deleting post:', error);
        
        if (error.code === 'permission-denied') {
            showMessage('Firebase permission error: Unable to delete post. Using demo mode instead.', 'error');
            
            // Fallback to demo mode
            let sessionPosts = JSON.parse(sessionStorage.getItem('mockForumPosts') || '[]');
            sessionPosts = sessionPosts.filter(p => p.id !== postId);
            sessionStorage.setItem('mockForumPosts', JSON.stringify(sessionPosts));
            
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            sessionReplies = sessionReplies.filter(r => r.postId !== postId);
            sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
            
            hidePostModal();
            loadRecentPosts();
            
            showMessage('Post deleted successfully! (Demo mode)', 'success');
        } else {
            showMessage('Failed to delete post. Please try again.', 'error');
        }
    }
}

// Function to get category post statistics
async function getCategoryStats() {
    if (!window.firebase || !window.firebase.db) {
        // Return fallback stats for demo mode
        return {
            'General Discussion': { count: 2, lastPost: new Date(Date.now() - 2 * 60 * 60 * 1000) },
            'Bug Reports': { count: 0, lastPost: null },
            'Feature Requests': { count: 0, lastPost: null },
            'Technical Support': { count: 0, lastPost: null },
            'Gaming & Performance': { count: 0, lastPost: null }
        };
    }
    
    try {
        const { db, collection, query, where, getDocs, orderBy, limit } = window.firebase;
        const categories = ['General Discussion', 'Bug Reports', 'Feature Requests', 'Technical Support', 'Gaming & Performance'];
        const stats = {};
        
        for (const category of categories) {
            // Count posts in category
            const countQuery = query(
                collection(db, 'forum_posts'),
                where('category', '==', category)
            );
            const countSnapshot = await getDocs(countQuery);
            const count = countSnapshot.size;
            
            // Get last post timestamp
            let lastPost = null;
            if (count > 0) {
                const lastPostQuery = query(
                    collection(db, 'forum_posts'),
                    where('category', '==', category),
                    orderBy('lastActivity', 'desc'),
                    limit(1)
                );
                const lastPostSnapshot = await getDocs(lastPostQuery);
                if (!lastPostSnapshot.empty) {
                    const lastPostData = lastPostSnapshot.docs[0].data();
                    lastPost = lastPostData.lastActivity?.toDate() || lastPostData.createdAt?.toDate();
                }
            }
            
            stats[category] = { count, lastPost };
        }
        
        return stats;
    } catch (error) {
        console.error('Error getting category stats:', error);
        // Return fallback stats
        return {
            'General Discussion': { count: 0, lastPost: null },
            'Bug Reports': { count: 0, lastPost: null },
            'Feature Requests': { count: 0, lastPost: null },
            'Technical Support': { count: 0, lastPost: null },
            'Gaming & Performance': { count: 0, lastPost: null }
        };
    }
}

// Function to update category display with real statistics
async function updateCategoryStats() {
    const stats = await getCategoryStats();
    
    // Update each category's stats
    const categories = document.querySelectorAll('.forum-category');
    categories.forEach(categoryElement => {
        const titleElement = categoryElement.querySelector('.category-title');
        if (!titleElement) return;
        
        const categoryTitle = titleElement.textContent;
        const categoryStats = stats[categoryTitle];
        
        if (categoryStats) {
            const postsCountElement = categoryElement.querySelector('.posts-count');
            const lastPostElement = categoryElement.querySelector('.last-post');
            
            if (postsCountElement) {
                const postText = categoryStats.count === 1 ? 'post' : 'posts';
                postsCountElement.textContent = `${categoryStats.count} ${postText}`;
            }
            
            if (lastPostElement) {
                if (categoryStats.lastPost) {
                    lastPostElement.textContent = `Last post ${formatTimeAgo(categoryStats.lastPost)}`;
                } else {
                    lastPostElement.textContent = 'No posts yet';
                }
            }
        }
    });
}

// Simple function to sort posts by newest first
function sortPostsNewestFirst(posts) {
    const sortedPosts = [...posts];
    
    // Sort by creation time, newest first
    sortedPosts.sort((a, b) => {
        const aTime = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt || 0);
        const bTime = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt || 0);
        return bTime - aTime;
    });
    
    return sortedPosts;
}

// Simple function to sort replies by newest first
function sortRepliesNewestFirst(replies) {
    const sortedReplies = [...replies];
    
    // Sort by newest first (most recent at top)
    sortedReplies.sort((a, b) => {
        const aTime = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt || 0);
        const bTime = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt || 0);
        return bTime - aTime; // newest first
    });
    
    return sortedReplies;
}

// Reply management functions
function toggleReplyOptions(replyId) {
    const menu = document.getElementById(`reply-options-${replyId}`);
    if (!menu) return;
    
    // Close all other menus
    document.querySelectorAll('.reply-options-menu').forEach(m => {
        if (m.id !== `reply-options-${replyId}`) {
            m.style.display = 'none';
        }
    });
    
    // Toggle current menu
    menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
    
    // Close menu when clicking outside
    if (menu.style.display === 'block') {
        setTimeout(() => {
            const closeOnClick = (e) => {
                if (!e.target.closest('.reply-actions-dropdown')) {
                    menu.style.display = 'none';
                    document.removeEventListener('click', closeOnClick);
                }
            };
            document.addEventListener('click', closeOnClick);
        }, 100);
    }
}

function editReply(replyId) {
    console.log('Editing reply:', replyId);
    
    // Hide the content and show the edit form
    const content = document.getElementById(`reply-content-${replyId}`);
    const editForm = document.getElementById(`reply-edit-${replyId}`);
    const optionsMenu = document.getElementById(`reply-options-${replyId}`);
    
    if (content) content.style.display = 'none';
    if (editForm) editForm.style.display = 'block';
    if (optionsMenu) optionsMenu.style.display = 'none';
    
    // Focus on textarea
    const textarea = document.getElementById(`reply-edit-textarea-${replyId}`);
    if (textarea) {
        textarea.focus();
        textarea.select();
    }
}

function cancelEditReply(replyId) {
    console.log('Cancelling edit for reply:', replyId);
    
    // Show the content and hide the edit form
    const content = document.getElementById(`reply-content-${replyId}`);
    const editForm = document.getElementById(`reply-edit-${replyId}`);
    
    if (content) content.style.display = 'block';
    if (editForm) editForm.style.display = 'none';
}

async function saveEditReply(replyId) {
    console.log('Saving edit for reply:', replyId);
    
    const textarea = document.getElementById(`reply-edit-textarea-${replyId}`);
    if (!textarea) return;
    
    const newContent = textarea.value.trim();
    
    if (!newContent) {
        showMessage('Reply content cannot be empty', 'error');
        return;
    }
    
    if (newContent.length < 5) {
        showMessage('Reply must be at least 5 characters long', 'error');
        return;
    }
    
    try {
        if (window.firebase && window.firebase.db) {
            const { db, doc, updateDoc, serverTimestamp } = window.firebase;
            
            // Update the reply in Firebase
            const replyRef = doc(db, 'forum_replies', replyId);
            await updateDoc(replyRef, {
                content: newContent,
                lastModified: serverTimestamp(),
                edited: true
            });
            
            showMessage('Reply updated successfully!', 'success');
            
        } else {
            // Update in session storage for demo mode
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            const replyIndex = sessionReplies.findIndex(r => r.id === replyId);
            
            if (replyIndex >= 0) {
                sessionReplies[replyIndex].content = newContent;
                sessionReplies[replyIndex].lastModified = new Date();
                sessionReplies[replyIndex].edited = true;
                
                sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
            }
            
            showMessage('Reply updated successfully! (Demo mode)', 'success');
        }
        
        // Update the display
        const contentDiv = document.getElementById(`reply-content-${replyId}`);
        if (contentDiv) {
            contentDiv.innerHTML = newContent + (newContent !== textarea.defaultValue ? ' <span class="edited-indicator">(edited)</span>' : '');
        }
        
        // Hide edit form and show content
        cancelEditReply(replyId);
        
    } catch (error) {
        console.error('Error updating reply:', error);
        
        if (error.code === 'permission-denied') {
            showMessage('Firebase permission error: Using demo mode instead.', 'error');
            
            // Fallback to demo mode
            let sessionReplies = JSON.parse(sessionStorage.getItem('mockForumReplies') || '[]');
            const replyIndex = sessionReplies.findIndex(r => r.id === replyId);
            
            if (replyIndex >= 0) {
                sessionReplies[replyIndex].content = newContent;
                sessionReplies[replyIndex].lastModified = new Date();
                sessionReplies[replyIndex].edited = true;
                
                sessionStorage.setItem('mockForumReplies', JSON.stringify(sessionReplies));
                
                const contentDiv = document.getElementById(`reply-content-${replyId}`);
                if (contentDiv) {
                    contentDiv.innerHTML = newContent + ' <span class="edited-indicator">(edited)</span>';
                }
                
                cancelEditReply(replyId);
                showMessage('Reply updated successfully! (Demo mode)', 'success');
            }
        } else {
            showMessage('Failed to update reply. Please try again.', 'error');
        }
    }
}

// Real-time update system
function setupRealtimeUpdates() {
    console.log('Setting up real-time updates...');
    
    // Update timestamps every 30 seconds
    setInterval(() => {
        updateAllTimestamps();
        updateCounts();
    }, 30000);
    
    // Update counts every 10 seconds for more responsiveness
    setInterval(() => {
        updateLiveCounts();
    }, 10000);
}

function updateAllTimestamps() {
    // Update all timestamps in posts and replies
    document.querySelectorAll('.post-time, .reply-time').forEach(element => {
        const timeElement = element.querySelector('i') ? element.textContent.split(' ').slice(1).join(' ') : element.textContent;
        
        // Try to extract and update timestamp
        if (timeElement.includes('ago') || timeElement.includes('Posted') || timeElement.includes('Last reply')) {
            // This would need the original timestamp to recalculate - for now just mark as updated
            element.classList.add('timestamp-updated');
        }
    });
}

function updateCounts() {
    // Update forum stats
    updateForumStats();
    
    // Update category stats  
    updateCategoryStats();
}

function updateLiveCounts() {
    // Update like/dislike counts for visible items
    document.querySelectorAll('[data-post-id]').forEach(element => {
        const postId = element.getAttribute('data-post-id');
        if (postId) {
            updateLikeCount(postId, false);
        }
    });
    
    document.querySelectorAll('[data-reply-id]').forEach(element => {
        const replyId = element.getAttribute('data-reply-id');
        if (replyId) {
            updateLikeCount(replyId, true);
        }
    });
}

// Enhanced online status system
async function updateUserOnlineStatus() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) return;
    
    const { auth, db, doc, updateDoc, serverTimestamp } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) return;
    
    try {
        // Update user's last seen timestamp
        await updateDoc(doc(db, 'users', user.uid), {
            lastSeen: serverTimestamp(),
            isOnline: true
        });
        
        // Only log occasionally to prevent spam
        if (Math.random() < 0.1) { // 10% chance to log
            console.log('🟢 Online status updated');
        }
    } catch (error) {
        console.log('Error updating online status:', error);
    }
}

// Set up online status tracking with proper throttling
function setupOnlineStatusTracking() {
    if (!window.firebase || !window.firebase.auth) return;
    
    let isUserActive = true;
    let lastActivityTime = Date.now();
    let statusUpdateTimeout;
    let awayTimeout;
    
    // Throttled activity detection to prevent lag
    let activityThrottle = false;
    
    const markUserActive = () => {
        if (activityThrottle) return; // Prevent excessive calls
        
        activityThrottle = true;
        setTimeout(() => { activityThrottle = false; }, 5000); // Throttle for 5 seconds
        
        lastActivityTime = Date.now();
        
        if (!isUserActive) {
            isUserActive = true;
            console.log('🟢 User became active');
            
            // Clear any pending away timeout
            clearTimeout(awayTimeout);
            
            // Update status immediately when becoming active
            clearTimeout(statusUpdateTimeout);
            statusUpdateTimeout = setTimeout(() => {
                updateUserOnlineStatus();
            }, 1000); // Debounce by 1 second
        }
        
        // Reset the away timer
        clearTimeout(awayTimeout);
        awayTimeout = setTimeout(() => {
            if (Date.now() - lastActivityTime >= 5 * 60 * 1000) { // 5 minutes
                isUserActive = false;
                console.log('🔴 User went away due to inactivity');
                markUserAway();
            }
        }, 5 * 60 * 1000); // 5 minutes
    };
    
    const markUserAway = async () => {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) return;
        
        const { auth, db, doc, updateDoc } = window.firebase;
        const user = auth.currentUser;
        
        if (user) {
            try {
                await updateDoc(doc(db, 'users', user.uid), {
                    isOnline: false,
                    lastSeen: new Date()
                });
                console.log('🔴 Updated status to away');
            } catch (error) {
                console.log('Error updating away status:', error);
            }
        }
    };
    
    // Update status every 2 minutes only if user is active
    const statusInterval = setInterval(() => {
        if (isUserActive && !activityThrottle) {
            console.log('⏰ Periodic status update');
            updateUserOnlineStatus();
        }
    }, 2 * 60 * 1000);
    
    // Update status on page visibility change
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            console.log('👁️ Page became visible');
            markUserActive();
        } else {
            console.log('👁️ Page became hidden');
            // Don't immediately mark as away, just stop periodic updates
        }
    });
    
    // Track user activity with throttling
    const activityEvents = ['mousedown', 'keypress', 'scroll', 'click'];
    activityEvents.forEach(event => {
        document.addEventListener(event, markUserActive, { passive: true });
    });
    
    // Initial status update
    markUserActive();
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        clearInterval(statusInterval);
        clearTimeout(statusUpdateTimeout);
        clearTimeout(awayTimeout);
        
        // Mark as offline when leaving
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, doc, updateDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                updateDoc(doc(db, 'users', user.uid), {
                    isOnline: false,
                    lastSeen: new Date()
                }).catch(error => console.log('Error updating offline status:', error));
            }
        }
    });
}

// Initialize enhanced features
function initializeEnhancedFeatures() {
    console.log('Initializing enhanced forum features...');
    
    // Set up real-time updates
    setupRealtimeUpdates();
    
    // Set up online status tracking
    setupOnlineStatusTracking();
    
    console.log('Enhanced features initialized');
}

// Authentication retry mechanism
function setupAuthRetryMechanism() {
    let retryCount = 0;
    const maxRetries = 5;
    const retryInterval = 2000; // 2 seconds
    
    const retryAuth = () => {
        retryCount++;
        console.log(`🔄 Auth retry attempt ${retryCount}/${maxRetries}`);
        
        // If forum content is already showing, no need to retry
        const forumContent = document.getElementById('forum-content');
        if (forumContent && forumContent.style.display !== 'none') {
            console.log('✅ Forum already accessible, stopping retry');
            return;
        }
        
        // Check Firebase auth
        if (window.firebase && window.firebase.auth) {
            const currentUser = window.firebase.auth.currentUser;
            if (currentUser) {
                console.log(`✅ Retry ${retryCount}: Found Firebase user:`, currentUser.email);
                showForumContent(currentUser);
                startForumStatsTracking();
                return;
            }
        }
        
        // Check fallback auth
        if (checkLegacyAuth()) {
            console.log(`✅ Retry ${retryCount}: Found legacy auth`);
            showForumContent();
            startForumStatsTracking();
            return;
        }
        
        // Continue retrying if under max attempts
        if (retryCount < maxRetries) {
            console.log(`⏳ Retry ${retryCount}: No auth found, trying again in ${retryInterval}ms...`);
            setTimeout(retryAuth, retryInterval);
        } else {
            console.log('❌ Max auth retry attempts reached, user may need to login again');
        }
    };
    
    // Start retry mechanism after a short delay
    setTimeout(retryAuth, 1000);
}