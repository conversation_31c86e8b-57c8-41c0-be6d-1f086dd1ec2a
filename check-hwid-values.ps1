Write-Host "=== HWID Values Verification ===" -ForegroundColor Cyan
Write-Host ""

Write-Host "Machine GUID:" -ForegroundColor Yellow
try {
    $machineGuid = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Cryptography" -Name MachineGuid -ErrorAction Stop
    Write-Host $machineGuid.MachineGuid -ForegroundColor Green
} catch {
    Write-Host "Error reading Machine GUID" -ForegroundColor Red
}

Write-Host ""
Write-Host "Hardware Profile GUID:" -ForegroundColor Yellow
try {
    $hwProfile = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001" -Name HwProfileGuid -ErrorAction Stop
    Write-Host $hwProfile.HwProfileGuid -ForegroundColor Green
} catch {
    Write-Host "Error reading Hardware Profile GUID" -ForegroundColor Red
}

Write-Host ""
Write-Host "Computer Hardware ID:" -ForegroundColor Yellow
try {
    $hwId = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name ComputerHardwareId -ErrorAction Stop
    Write-Host $hwId.ComputerHardwareId -ForegroundColor Green
} catch {
    Write-Host "Error reading Computer Hardware ID" -ForegroundColor Red
}

Write-Host ""
Write-Host "Build GUID:" -ForegroundColor Yellow
try {
    $buildGuid = Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion" -Name BuildGUID -ErrorAction Stop
    Write-Host $buildGuid.BuildGUID -ForegroundColor Green
} catch {
    Write-Host "Error reading Build GUID" -ForegroundColor Red
}

Write-Host ""
Write-Host "Computer Hardware IDs:" -ForegroundColor Yellow
try {
    $hwIds = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation" -Name ComputerHardwareIds -ErrorAction Stop
    Write-Host $hwIds.ComputerHardwareIds -ForegroundColor Green
} catch {
    Write-Host "Error reading Computer Hardware IDs" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Cyan
Write-Host "If values are different from your original system, spoofing is working!" -ForegroundColor Green
Write-Host "Run this script before and after spoofing to compare values." -ForegroundColor Yellow 