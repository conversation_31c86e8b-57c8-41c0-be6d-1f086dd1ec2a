# Desktop Arsenal - Windows Startup Feature

## Overview
The Desktop Arsenal includes a "Start with Windows" feature that allows the application to automatically launch when Windows starts. This feature is specifically designed for the Desktop Arsenal overlay functionality.

## Implementation Details

### Registry Integration
The feature uses Windows Registry to add/remove the application from startup:
- **Registry Key**: `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run`
- **Entry Name**: `ArmoryX`
- **Entry Value**: `"<app_path>" --startup`

### Code Implementation

The feature is implemented in `modules/desktop-arsenal.js`:

```javascript
async setAutoStart(enabled) {
    // Uses the winreg npm package to modify Windows Registry
    // Adds/removes the application from Windows startup
}

async checkAutoStart() {
    // Checks if the application is set to start with Windows
    // Returns true/false
}
```

### Startup Flag Handling

When the application is launched with the `--startup` flag, it should:
1. Start minimized to system tray
2. Initialize Desktop Arsenal in the background
3. Enable file monitoring if configured
4. Register hotkeys for quick access

## Testing Requirements

### Pre-Packaging Tests
1. **Registry Write Test**: Verify the application can write to the Windows Registry
2. **Permission Test**: Ensure no admin rights are required for HKCU registry access
3. **Path Resolution**: Confirm `process.execPath` returns the correct path

### Post-Packaging Tests
1. **Installation Path**: Verify the packaged app path is correctly stored in registry
2. **Startup Launch**: Test that Windows launches the app on startup
3. **Flag Handling**: Confirm the `--startup` flag is properly handled
4. **Silent Start**: Ensure the app starts minimized when launched via startup

## User Experience

### Enabling Start with Windows
1. Open Desktop Arsenal overlay (Ctrl+Space)
2. Click Settings button
3. Find "Start with Windows" option
4. Select "Enabled" from dropdown
5. Click "Save Settings"

### Disabling Start with Windows
1. Follow steps 1-3 above
2. Select "Disabled" from dropdown
3. Click "Save Settings"

### Visual Feedback
- The setting shows current state when opening settings
- Changes are applied immediately upon saving
- No system restart required

## Packaging Considerations

### Electron Builder Configuration
Add to `package.json`:
```json
{
  "build": {
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

### Testing Checklist
- [ ] Package the application using Electron Builder
- [ ] Install the packaged application
- [ ] Enable "Start with Windows" in Desktop Arsenal settings
- [ ] Restart Windows
- [ ] Verify application starts automatically
- [ ] Check that overlay hotkey (Ctrl+Space) works immediately
- [ ] Verify app appears in system tray (if implemented)
- [ ] Test disabling the feature and restarting

## Troubleshooting

### Common Issues

1. **Registry Access Denied**
   - Solution: The app uses HKCU which doesn't require admin rights
   - If issues persist, check antivirus software

2. **App Doesn't Start**
   - Check Event Viewer for errors
   - Verify registry entry exists: `regedit` → Navigate to startup key
   - Ensure app path in registry is correct

3. **App Starts but Desktop Arsenal Doesn't Work**
   - Check if `--startup` flag is being passed
   - Verify Desktop Arsenal initialization in startup mode
   - Check console for initialization errors

### Debug Commands

Check current registry value:
```powershell
Get-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run" -Name "ArmoryX"
```

Manually add to startup (for testing):
```powershell
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run" -Name "ArmoryX" -Value '"C:\Path\To\ArmoryX.exe" --startup'
```

Remove from startup:
```powershell
Remove-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run" -Name "ArmoryX"
```

## Future Enhancements

1. **System Tray Integration**
   - Add system tray icon when started via Windows startup
   - Quick access menu for Desktop Arsenal features
   - Status indicators for file monitoring

2. **Startup Options**
   - Option to start minimized vs. show overlay
   - Delay startup to reduce system load
   - Different startup profiles

3. **Cross-Platform Support**
   - macOS: Login Items API
   - Linux: Desktop entry in autostart directory 