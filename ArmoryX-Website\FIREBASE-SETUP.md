# Firebase Setup Guide for Armory X Website

This guide will help you set up Firebase Authentication and Firestore Database for your Armory X website to enable real user accounts and live data tracking.

## Prerequisites

- A Google account
- Access to [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `armoryx-website` (or your preferred name)
4. Disable Google Analytics (optional, but recommended for this setup)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project, click "Authentication" in the left sidebar
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" authentication:
   - Click on "Email/Password"
   - Toggle "Enable" to ON
   - Click "Save"

## Step 3: Create Firestore Database

1. Click "Firestore Database" in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" (we'll secure it later)
4. Select your preferred location (closest to your users)
5. Click "Done"

## Step 4: Configure Web App

1. In Project Settings (gear icon), scroll to "Your apps"
2. Click the web icon (`</>`) to add a web app
3. Enter app nickname: "ArmoryX Website"
4. Don't check "Set up Firebase Hosting" (since you're using Cloudflare Pages)
5. Click "Register app"
6. Copy the Firebase configuration object

## Step 5: Update Your Website Code

1. Open `index.html` in your project
2. Find the Firebase configuration section (around line 40)
3. Replace the placeholder values with your actual Firebase config:

```javascript
// Replace this section in index.html
const firebaseConfig = {
    apiKey: "YOUR_ACTUAL_API_KEY",
    authDomain: "your-project-id.firebaseapp.com", 
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789012"
};
```

## Step 6: Set Up Firestore Security Rules

1. In Firestore Database, go to "Rules" tab
2. Replace the rules with the following secure configuration:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Forum posts are readable by authenticated users
    match /forum_posts/{postId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.auth.uid == resource.data.authorId;
      allow update: if request.auth != null && request.auth.uid == resource.data.authorId;
    }
    
    // Forum interactions for analytics
    match /forum_interactions/{interactionId} {
      allow create: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Public stats (read-only)
    match /stats/{statDoc} {
      allow read: if true;
    }
  }
}
```

3. Click "Publish"

## Step 7: Initialize Database Collections

You can create these collections manually in Firestore Console or they'll be created automatically when users start using the site:

### Collections to create:
- `users` - User profiles and data
- `forum_posts` - Forum posts and discussions  
- `forum_interactions` - User interaction tracking
- `stats` - Site-wide statistics

## Step 8: Test the Setup

1. Deploy your updated website to Cloudflare Pages
2. Try creating a new account
3. Check Firebase Authentication to see if the user was created
4. Check Firestore to see if user data was stored

## Features Enabled

✅ **Secure Authentication**
- Email/password registration
- Duplicate email prevention
- Real-time auth state tracking

✅ **Real-time Data**
- Live user count updates
- Real-time forum statistics
- Online user tracking
- User activity monitoring

✅ **Forum Integration**
- User authentication for forum access
- Activity tracking
- Real-time post updates

✅ **License Key System**
- User-specific license management
- Persistent license storage

## Troubleshooting

### Firebase not loading
- Check browser console for errors
- Verify Firebase config values are correct
- Ensure internet connection is stable

### Authentication errors
- Check if Email/Password is enabled in Firebase Console
- Verify Firestore rules allow user document creation
- Check for CORS issues (usually not a problem with Cloudflare Pages)

### Database permission errors
- Review Firestore security rules
- Ensure user is authenticated before making database calls
- Check that collection names match between code and rules

## Optional Enhancements

### Email Verification
To enable email verification:
1. In Firebase Authentication settings
2. Go to Templates tab
3. Customize email verification template
4. Update code to require email verification

### Advanced Analytics
Set up additional tracking:
- User engagement metrics
- Download tracking
- Forum activity analytics
- License key usage statistics

## Security Best Practices

1. **Never expose Firebase API keys in client-side code** - The current setup is safe as Firebase API keys are meant to be public
2. **Use Firestore security rules** - Always validate permissions server-side
3. **Monitor usage** - Set up Firebase usage alerts
4. **Regular backups** - Export Firestore data regularly

## Support

If you encounter issues:
1. Check Firebase Console for error logs
2. Review browser developer console
3. Test with a fresh browser session
4. Verify all configuration steps were completed

---

## Next Steps

After setup is complete, you can:
- Customize user profiles
- Add forum categories and posts
- Implement premium features with license keys
- Set up email notifications
- Add social login providers (Google, Discord, etc.)

Your Armory X website now has a professional authentication system with real-time data tracking! 