/**
 * System Information module for Armory X
 * Extracted from main.js for better maintainability
 */

const os = require('os');
const fs = require('fs-extra');
const { machineIdSync } = require('node-machine-id');

class SystemInfo {
  constructor() {
    // Constructor can be used for future initialization if needed
  }

  /**
   * Get basic system information
   * @returns {Object} System info object
   */
  async getSystemInfo() {
    return {
      os: {
        platform: os.platform(),
        release: os.release(),
        type: os.type()
      },
      cpu: {
        brand: os.cpus()[0].model,
        cores: os.cpus().length
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem()
      }
    };
  }

  /**
   * Get system stats (memory and CPU usage)
   * @returns {Object} System stats object
   */
  async getSystemStats() {
    const stats = process.getSystemMemoryInfo();
    const cpuUsage = process.getCPUUsage();
    
    return {
      memory: Math.round((stats.total - stats.free) / stats.total * 100),
      cpu: Math.round(cpuUsage.percentCPUUsage)
    };
  }

  /**
   * Get machine ID for licensing
   * @returns {string|null} Machine ID or null if error
   */
  getMachineId() {
    try {
      return machineIdSync();
    } catch (error) {
      console.error('Error getting machine ID:', error);
      return null;
    }
  }

  /**
   * Get all available drives (Windows-specific)
   * @returns {Array<string>} Array of drive letters
   */
  async getAllDrives() {
    const drives = [];
    
    try {
      // On Windows, check drives A through Z
      for (let i = 65; i <= 90; i++) {
        const driveLetter = String.fromCharCode(i);
        const drivePath = `${driveLetter}:`;
        
        try {
          await fs.access(drivePath);
          drives.push(driveLetter);
        } catch (error) {
          // Drive doesn't exist or isn't accessible
        }
      }
    } catch (error) {
      console.log('Error checking drives:', error.message);
      // Fallback to just C drive
      drives.push('C');
    }
    
    console.log(`🗂️ Found ${drives.length} available drives: ${drives.join(', ')}`);
    return drives;
  }
}

module.exports = { SystemInfo };