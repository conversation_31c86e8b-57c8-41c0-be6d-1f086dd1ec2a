/* ===================================
   Beta Warning System
   =================================== */

/* Beta Warning Banner */
.beta-warning-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 0.75rem 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    animation: betaWarningSlideDown 0.5s ease-out;
}

.beta-warning-banner.hidden {
    transform: translateY(-100%);
    opacity: 0;
    pointer-events: none;
}

.beta-warning-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.beta-warning-icon {
    font-size: 1.2rem;
    animation: betaWarningPulse 2s ease-in-out infinite;
}

.beta-warning-text {
    flex: 1;
    text-align: center;
}

.beta-warning-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    opacity: 0.8;
}

.beta-warning-close:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

/* Beta Warning Modal */
.beta-warning-modal {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #f59e0b;
    border-radius: 16px;
    max-width: 600px;
    width: 90vw;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 0 30px rgba(245, 158, 11, 0.3);
}

.beta-warning-modal .custom-modal-header {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 14px 14px 0 0;
}

.beta-warning-modal .warning-icon-large {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: betaWarningBounce 1s ease-in-out infinite;
}

.beta-warning-modal .custom-modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.beta-warning-modal .custom-modal-body {
    padding: 2rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
}

.beta-warning-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.beta-warning-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #e2e8f0;
    font-size: 0.95rem;
}

.beta-warning-features li::before {
    content: '⚠️';
    font-size: 1.1rem;
    flex-shrink: 0;
}

.beta-disclaimer {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.beta-disclaimer h4 {
    color: #ef4444;
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.beta-disclaimer p {
    color: #fca5a5;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.beta-acknowledgment {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.beta-acknowledgment label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #bfdbfe;
    font-size: 0.95rem;
    cursor: pointer;
}

.beta-acknowledgment input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.beta-warning-modal .custom-modal-footer {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.beta-warning-modal .btn-understand {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.5;
    pointer-events: none;
}

.beta-warning-modal .btn-understand.enabled {
    opacity: 1;
    pointer-events: auto;
}

.beta-warning-modal .btn-understand.enabled:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.beta-warning-modal .btn-exit {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.beta-warning-modal .btn-exit:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Body adjustment when beta warning is shown */
body.beta-warning-active {
    padding-top: 60px;
}

body.beta-warning-active .app-container {
    margin-top: 0;
}

/* Animations */
@keyframes betaWarningSlideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes betaWarningPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes betaWarningBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .beta-warning-banner {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .beta-warning-content {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .beta-warning-text {
        text-align: center;
    }
    
    .beta-warning-close {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
    }
    
    .beta-warning-modal {
        width: 95vw;
        margin: 1rem;
    }
    
    .beta-warning-modal .custom-modal-header,
    .beta-warning-modal .custom-modal-body,
    .beta-warning-modal .custom-modal-footer {
        padding: 1.5rem;
    }
    
    .beta-warning-modal .custom-modal-footer {
        flex-direction: column;
    }
    
    .beta-warning-modal .btn-understand,
    .beta-warning-modal .btn-exit {
        width: 100%;
        margin: 0;
    }
    
    body.beta-warning-active {
        padding-top: 80px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .beta-warning-banner {
        background: #d97706;
        border-bottom: 3px solid #000;
    }
    
    .beta-warning-modal {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .beta-warning-banner {
        animation: none;
    }
    
    .beta-warning-icon,
    .warning-icon-large {
        animation: none;
    }
    
    .beta-warning-modal .btn-understand:hover,
    .beta-warning-modal .btn-exit:hover {
        transform: none;
    }
}
