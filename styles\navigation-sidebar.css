/* ===================================
   Navigation and Sidebar System
   =================================== */

/* Menu Toggle */
.menu-toggle {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.menu-toggle:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
}

.hamburger {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    transition: all 0.3s ease;
    border-radius: 1px;
}

.menu-toggle.open .hamburger:nth-child(1) {
    transform: translateY(6px) rotate(45deg);
}

.menu-toggle.open .hamburger:nth-child(2) {
    opacity: 0;
}

.menu-toggle.open .hamburger:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 60px; /* Position below header */
    left: 0;
    width: 280px;
    height: calc(100vh - 60px);
    background: var(--background-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem 1rem;
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.sidebar:not(.collapsed) {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
}

.sidebar.collapsed {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
}

/* Navigation Items */
.nav-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 2rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.nav-item:hover {
    background: var(--background-hover);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    transform: translateX(4px);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
    border: 1px solid var(--primary-color);
}

.nav-item .icon {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    transition: var(--transition-smooth);
    overflow: hidden;
    white-space: nowrap;
    flex: 1;
}

/* Sidebar Footer */
.sidebar-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-top: auto;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-bottom: 8px;
}

.info-item .nav-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
    line-height: 1.3;
}

.info-item .nav-text small {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Premium Badge */
.nav-item .premium-badge {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
    margin-left: auto;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #000;
    border-radius: 4px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
}

.nav-item:has(.premium-badge) {
    position: relative;
}

/* Calendar Navigation */
.calendar-nav-compact {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.calendar-nav-compact:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.calendar-nav-compact:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Mod Categories Sidebar */
.mod-sidebar-layout {
    display: flex;
    gap: 1rem;
    height: 70vh;
    min-height: 500px;
}

.mod-categories-sidebar {
    flex: 0 0 300px;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    overflow-y: auto;
    height: 100%;
}

.categories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.categories-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
}

.category-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(59, 130, 246, 0.3);
    color: var(--text-primary);
    transform: translateX(4px);
}

.category-item.active {
    background: rgba(59, 130, 246, 0.15);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.category-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.category-count {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-muted);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.category-item.active .category-count {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

/* View Options */
.view-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.view-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
}

.view-toggle.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .mod-categories-sidebar {
        flex: 0 0 250px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 280px;
        position: fixed;
        left: 0;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 1001;
    }
    
    .sidebar:not(.collapsed) {
        transform: translateX(0);
    }
    
    .sidebar.collapsed {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .mod-sidebar-layout {
        flex-direction: column;
        height: auto;
        min-height: auto;
        gap: 1rem;
    }
    
    .mod-categories-sidebar {
        flex: none;
        height: auto;
        padding: 1rem;
        max-height: 300px;
    }
    
    .nav-item {
        padding: 10px 14px;
        font-size: 0.9rem;
    }
    
    .nav-item .icon {
        font-size: 1.2rem;
        width: 20px;
    }
    
    .category-item {
        padding: 0.6rem 0.8rem;
        margin-bottom: 0.4rem;
    }
    
    .category-name {
        font-size: 0.85rem;
    }
    
    .category-count {
        font-size: 0.7rem;
        padding: 0.15rem 0.4rem;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100vw;
    }
    
    .nav-item {
        padding: 12px 16px;
    }
    
    .categories-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .view-options {
        width: 100%;
        justify-content: flex-end;
    }
}
