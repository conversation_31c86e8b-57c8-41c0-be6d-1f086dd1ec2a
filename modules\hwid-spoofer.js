const { exec, spawn } = require('child_process');
const crypto = require('crypto');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');

class HWIDSpoofer {
    constructor() {
        this.isActive = false;
        this.mainWindow = null;
        this.backupPath = path.join(os.homedir(), 'AppData', 'Roaming', 'ArmoryX', 'hwid-backup.json');
        this.statePath = path.join(os.homedir(), 'AppData', 'Roaming', 'ArmoryX', 'hwid-state.json');
        this.originalValues = null;
        this.spoofedValues = null;
        
        // Registry keys for various hardware identifiers
        this.registryKeys = {
            // System GUID
            systemGuid: {
                key: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography',
                value: 'MachineGuid',
                type: 'REG_SZ'
            },
            // Hardware Profile GUID
            hardwareProfileGuid: {
                key: 'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\IDConfigDB\\Hardware Profiles\\0001',
                value: 'HwProfileGuid',
                type: 'REG_SZ'
            },
            // System Boot ID
            systemBootId: {
                key: 'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation',
                value: 'ComputerHardwareId',
                type: 'REG_SZ'
            },
            // Network adapter MAC addresses will be handled separately
            // Volume serial numbers will be handled separately
            // System UUID
            systemUuid: {
                key: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion',
                value: 'BuildGUID',
                type: 'REG_SZ'
            },
            // Hardware Hash
            hardwareHash: {
                key: 'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation',
                value: 'ComputerHardwareIds',
                type: 'REG_MULTI_SZ'
            }
        };

        this.ensureBackupDirectory();
        this.initializeState();
    }

    async ensureBackupDirectory() {
        try {
            const backupDir = path.dirname(this.backupPath);
            await fs.ensureDir(backupDir);
        } catch (error) {
            console.error('Error creating backup directory:', error);
        }
    }

    async initializeState() {
        try {
            // Load existing backup if available
            if (await fs.pathExists(this.backupPath)) {
                this.originalValues = await fs.readJson(this.backupPath);
                console.log('✅ Loaded existing HWID backup');
            }

            // Check current state against backup to detect active spoofing
            await this.detectCurrentSpoofState();
        } catch (error) {
            console.error('❌ Error initializing HWID spoofer state:', error);
        }
    }

    async detectCurrentSpoofState() {
        try {
            // Load previous state first
            const savedState = await this.loadCurrentState();
            if (savedState && savedState.isActive) {
                console.log('🔍 Found saved active state from previous session');
                this.isActive = savedState.isActive;
                this.spoofedValues = savedState.spoofedValues;
            }
            
            if (!this.originalValues) {
                this.isActive = false;
                return;
            }

            // Compare current values with backup to detect if spoofing is active
            const comparison = await this.getHWIDComparison();
            if (comparison.success && comparison.comparison.isCurrentlyModified) {
                this.isActive = true;
                console.log('🔍 Detected active HWID spoofing from hardware comparison');
                
                // If we don't have spoofed values but we're modified, try to reconstruct them
                if (!this.spoofedValues) {
                    this.spoofedValues = {};
                    for (const [identifier, data] of Object.entries(comparison.comparison.values)) {
                        if (data.isModified) {
                            this.spoofedValues[identifier] = {
                                key: this.registryKeys[identifier]?.key,
                                value: this.registryKeys[identifier]?.value,
                                type: this.registryKeys[identifier]?.type,
                                spoofedValue: data.current
                            };
                        }
                    }
                }
                
                // Save current state
                await this.saveCurrentState();
            } else if (!savedState || !savedState.isActive) {
                this.isActive = false;
                this.spoofedValues = null;
            }
        } catch (error) {
            console.error('❌ Error detecting spoof state:', error);
            this.isActive = false;
        }
    }

    async saveCurrentState() {
        try {
            const state = {
                isActive: this.isActive,
                timestamp: new Date().toISOString(),
                hasBackup: this.originalValues !== null,
                spoofedValues: this.spoofedValues
            };

            await fs.writeJson(this.statePath, state, { spaces: 2 });
            console.log('✅ Saved HWID spoofer state');
        } catch (error) {
            console.error('❌ Error saving state:', error);
        }
    }

    async loadCurrentState() {
        try {
            if (await fs.pathExists(this.statePath)) {
                const state = await fs.readJson(this.statePath);
                return state;
            }
            return null;
        } catch (error) {
            console.error('❌ Error loading state:', error);
            return null;
        }
    }

    setMainWindow(window) {
        this.mainWindow = window;
    }

    // Generate random GUID
    generateGuid() {
        return crypto.randomUUID();
    }

    // Generate random hardware ID
    generateHardwareId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 16; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // Generate random MAC address
    generateMacAddress() {
        const chars = '0123456789ABCDEF';
        let mac = '';
        for (let i = 0; i < 12; i++) {
            mac += chars.charAt(Math.floor(Math.random() * chars.length));
            if (i % 2 === 1 && i < 11) mac += '-';
        }
        return mac;
    }

    // Generate random serial number
    generateSerialNumber() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let serial = '';
        for (let i = 0; i < 10; i++) {
            serial += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return serial;
    }

    // Read registry value
    async readRegistryValue(keyPath, valueName) {
        return new Promise((resolve, reject) => {
            const command = `reg query "${keyPath}" /v "${valueName}"`;
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                
                try {
                    const lines = stdout.split('\n');
                    for (const line of lines) {
                        if (line.includes(valueName)) {
                            const parts = line.trim().split(/\s+/);
                            if (parts.length >= 3) {
                                // Join all parts after the type as the value
                                const value = parts.slice(2).join(' ');
                                resolve(value);
                                return;
                            }
                        }
                    }
                    reject(new Error('Value not found'));
                } catch (parseError) {
                    reject(parseError);
                }
            });
        });
    }

    // Write registry value
    async writeRegistryValue(keyPath, valueName, value, type = 'REG_SZ') {
        return new Promise((resolve, reject) => {
            const command = `reg add "${keyPath}" /v "${valueName}" /t ${type} /d "${value}" /f`;
            exec(command, { shell: true }, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                    return;
                }
                resolve(stdout);
            });
        });
    }

    // Backup current HWID values
    async backupCurrentValues() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                values: {}
            };

            // Read current values for each registry key
            for (const [identifier, config] of Object.entries(this.registryKeys)) {
                try {
                    const value = await this.readRegistryValue(config.key, config.value);
                    backupData.values[identifier] = {
                        key: config.key,
                        value: config.value,
                        type: config.type,
                        originalValue: value
                    };
                } catch (error) {
                    console.warn(`Could not read ${identifier}:`, error.message);
                    backupData.values[identifier] = {
                        key: config.key,
                        value: config.value,
                        type: config.type,
                        originalValue: null,
                        error: error.message
                    };
                }
            }

            // Get additional system information
            backupData.systemInfo = await this.getSystemInfo();

            // Save backup
            await fs.writeJson(this.backupPath, backupData, { spaces: 2 });
            this.originalValues = backupData;

            console.log('✅ HWID backup created successfully');
            return { success: true, message: 'Backup created successfully' };
        } catch (error) {
            console.error('❌ Error creating HWID backup:', error);
            return { success: false, message: 'Failed to create backup: ' + error.message };
        }
    }

    // Get additional system information
    async getSystemInfo() {
        return new Promise((resolve) => {
            const systemInfo = {
                platform: os.platform(),
                hostname: os.hostname(),
                username: os.userInfo().username,
                timestamp: new Date().toISOString()
            };

            // Get additional Windows-specific info
            if (os.platform() === 'win32') {
                exec('wmic csproduct get UUID', (error, stdout) => {
                    if (!error) {
                        const lines = stdout.split('\n');
                        for (const line of lines) {
                            if (line.includes('-')) {
                                systemInfo.systemUUID = line.trim();
                                break;
                            }
                        }
                    }
                    resolve(systemInfo);
                });
            } else {
                resolve(systemInfo);
            }
        });
    }

    // Generate spoofed values
    generateSpoofedValues() {
        const spoofedValues = {};
        
        for (const [identifier, config] of Object.entries(this.registryKeys)) {
            let spoofedValue;
            
            switch (identifier) {
                case 'systemGuid':
                case 'hardwareProfileGuid':
                case 'biosGuid':
                case 'systemUuid':
                    spoofedValue = this.generateGuid();
                    break;
                case 'systemBootId':
                case 'hardwareHash':
                    spoofedValue = this.generateHardwareId();
                    break;
                default:
                    spoofedValue = this.generateGuid();
            }
            
            spoofedValues[identifier] = {
                key: config.key,
                value: config.value,
                type: config.type,
                spoofedValue: spoofedValue
            };
        }
        
        return spoofedValues;
    }

    // Apply comprehensive spoofed values (includes SMBIOS, disk serials, network adapters, GPU spoofing)
    async applySpoofedValues() {
        try {
            // First, create backup if it doesn't exist
            if (!this.originalValues) {
                const backupResult = await this.backupCurrentValues();
                if (!backupResult.success) {
                    return backupResult;
                }
            }

            console.log('🎯 Applying comprehensive HWID spoofing...');

            // Generate spoofed values
            this.spoofedValues = this.generateSpoofedValues();

            let totalSuccessCount = 0;
            let totalErrorCount = 0;
            const allErrors = [];
            const operations = [];

            // 1. Apply SMBIOS spoofing (system identifiers)
            console.log('🔧 Spoofing SMBIOS information...');
            const smbiosResult = await this.spoofSMBIOS();
            operations.push(smbiosResult);
            if (smbiosResult.success) {
                totalSuccessCount += smbiosResult.applied || 0;
            } else {
                totalErrorCount++;
                allErrors.push(`SMBIOS: ${smbiosResult.message}`);
            }

            // 2. Apply disk serial spoofing
            console.log('💿 Spoofing disk serials...');
            const diskResult = await this.spoofDiskSerials();
            operations.push(diskResult);
            if (diskResult.success) {
                totalSuccessCount += diskResult.applied || 0;
            } else {
                totalErrorCount++;
                allErrors.push(`Disk: ${diskResult.message}`);
            }

            // 3. Apply network adapter spoofing
            console.log('🌐 Spoofing network adapters...');
            const networkResult = await this.spoofNetworkAdapters();
            operations.push(networkResult);
            if (networkResult.success) {
                totalSuccessCount += networkResult.applied || 0;
            } else {
                totalErrorCount++;
                allErrors.push(`Network: ${networkResult.message}`);
            }

            // 4. Apply GPU spoofing
            console.log('🎮 Spoofing GPU devices...');
            const gpuResult = await this.spoofGPUSerials();
            operations.push(gpuResult);
            if (gpuResult.success) {
                totalSuccessCount += gpuResult.applied || 0;
            } else {
                totalErrorCount++;
                allErrors.push(`GPU: ${gpuResult.message}`);
            }

            this.isActive = true;
            await this.saveCurrentState();
            this.notifyRenderer('hwid-spoofer-status-changed', { 
                active: true, 
                successCount: totalSuccessCount, 
                errorCount: totalErrorCount,
                errors: allErrors,
                operations: operations
            });

            const successfulOperations = operations.filter(op => op.success).length;
            const totalOperations = operations.length;

            if (totalErrorCount === 0) {
                return { 
                    success: true, 
                    message: `Comprehensive HWID spoofing activated successfully! ${totalSuccessCount} total changes applied across ${successfulOperations} categories.`,
                    operations: operations
                };
            } else {
                return { 
                    success: successfulOperations > 0, 
                    message: `HWID spoofing partially activated. ${successfulOperations}/${totalOperations} categories successful, ${totalSuccessCount} total changes applied.`,
                    errors: allErrors,
                    operations: operations
                };
            }
        } catch (error) {
            console.error('❌ Error applying spoofed values:', error);
            return { success: false, message: 'Failed to apply spoofed values: ' + error.message };
        }
    }

    // Restore original values
    async restoreOriginalValues() {
        try {
            // Load backup if not in memory
            if (!this.originalValues) {
                if (await fs.pathExists(this.backupPath)) {
                    this.originalValues = await fs.readJson(this.backupPath);
                } else {
                    return { success: false, message: 'No backup found. Cannot restore original values.' };
                }
            }

            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            // Restore each original value
            for (const [identifier, config] of Object.entries(this.originalValues.values)) {
                if (config.originalValue && !config.error) {
                    try {
                        await this.writeRegistryValue(
                            config.key,
                            config.value,
                            config.originalValue,
                            config.type
                        );
                        successCount++;
                        console.log(`✅ Restored original value for ${identifier}`);
                    } catch (error) {
                        errorCount++;
                        errors.push(`${identifier}: ${error.message}`);
                        console.error(`❌ Failed to restore original value for ${identifier}:`, error);
                    }
                }
            }

            this.isActive = false;
            this.spoofedValues = null;
            await this.saveCurrentState();
            this.notifyRenderer('hwid-spoofer-status-changed', { 
                active: false, 
                successCount, 
                errorCount,
                errors 
            });

            if (errorCount === 0) {
                return { 
                    success: true, 
                    message: `Original HWID values restored successfully! ${successCount} values restored.` 
                };
            } else {
                return { 
                    success: true, 
                    message: `HWID restoration partially successful. ${successCount} succeeded, ${errorCount} failed.`,
                    errors 
                };
            }
        } catch (error) {
            console.error('❌ Error restoring original values:', error);
            return { success: false, message: 'Failed to restore original values: ' + error.message };
        }
    }

    // Get current status
    getStatus() {
        return {
            active: this.isActive,
            hasBackup: this.originalValues !== null,
            backupPath: this.backupPath,
            spoofedValues: this.spoofedValues,
            originalValues: this.originalValues
        };
    }

    // Get current live HWID values (for display and comparison)
    async getCurrentHWIDValues() {
        try {
            const currentValues = {};
            
            // Read current values for each registry key
            for (const [identifier, config] of Object.entries(this.registryKeys)) {
                try {
                    const value = await this.readRegistryValue(config.key, config.value);
                    currentValues[identifier] = {
                        key: config.key,
                        value: config.value,
                        type: config.type,
                        currentValue: value,
                        displayName: this.getDisplayName(identifier)
                    };
                } catch (error) {
                    console.warn(`Could not read current ${identifier}:`, error.message);
                    currentValues[identifier] = {
                        key: config.key,
                        value: config.value,
                        type: config.type,
                        currentValue: null,
                        error: error.message,
                        displayName: this.getDisplayName(identifier)
                    };
                }
            }

            // Get additional system information
            const systemInfo = await this.getSystemInfo();
            
            return {
                success: true,
                currentValues: currentValues,
                systemInfo: systemInfo,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Error getting current HWID values:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Helper function to get display names for identifiers
    getDisplayName(identifier) {
        const displayNames = {
            systemGuid: 'System Machine GUID',
            hardwareProfileGuid: 'Hardware Profile GUID',
            systemBootId: 'Computer Hardware ID',
            systemUuid: 'Build GUID',
            hardwareHash: 'Computer Hardware IDs'
        };
        return displayNames[identifier] || identifier;
    }

    // Compare current values with original backup
    async getHWIDComparison() {
        try {
            const currentData = await this.getCurrentHWIDValues();
            if (!currentData.success) {
                return currentData;
            }

            const comparison = {
                hasBackup: this.originalValues !== null,
                isCurrentlyModified: false,
                values: {},
                systemInfo: currentData.systemInfo,
                timestamp: new Date().toISOString()
            };

            // Compare each value (or just show current if no backup)
            for (const [identifier, currentDataItem] of Object.entries(currentData.currentValues)) {
                const originalData = this.originalValues?.values[identifier];
                
                comparison.values[identifier] = {
                    displayName: currentDataItem.displayName,
                    current: currentDataItem.currentValue,
                    original: originalData?.originalValue || null,
                    isModified: originalData && currentDataItem.currentValue !== originalData.originalValue,
                    hasError: !!currentDataItem.error,
                    error: currentDataItem.error
                };

                if (comparison.values[identifier].isModified) {
                    comparison.isCurrentlyModified = true;
                }
            }

            return {
                success: true,
                comparison: comparison
            };
        } catch (error) {
            console.error('❌ Error comparing HWID values:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Toggle HWID spoofing
    async toggleSpoofing() {
        if (this.isActive) {
            return await this.restoreOriginalValues();
        } else {
            return await this.applySpoofedValues();
        }
    }

    // Advanced: Spoof network adapter MAC addresses
    async spoofMacAddresses() {
        return new Promise((resolve) => {
            // Get network adapters
            const command = 'wmic path win32_networkadapter get name,macaddress /format:csv';
            exec(command, async (error, stdout) => {
                if (error) {
                    resolve({ success: false, message: 'Failed to get network adapters' });
                    return;
                }

                try {
                    const lines = stdout.split('\n');
                    const adapters = [];
                    
                    for (const line of lines) {
                        if (line.includes(',') && !line.includes('Node,Name')) {
                            const parts = line.split(',');
                            if (parts.length >= 3 && parts[2].trim()) {
                                adapters.push({
                                    name: parts[1].trim(),
                                    mac: parts[2].trim()
                                });
                            }
                        }
                    }

                    // For each adapter, we would need to modify registry entries
                    // This is more complex and requires specific adapter registry manipulation
                    resolve({ 
                        success: true, 
                        message: 'MAC address spoofing requires elevated privileges and system restart',
                        adapters 
                    });
                } catch (parseError) {
                    resolve({ success: false, message: 'Failed to parse network adapters' });
                }
            });
        });
    }

    // Get system hardware information (for display purposes)
    async getSystemHardwareInfo() {
        return new Promise((resolve) => {
            const info = {
                platform: os.platform(),
                hostname: os.hostname(),
                username: os.userInfo().username
            };

            // Get additional Windows info
            if (os.platform() === 'win32') {
                exec('wmic computersystem get model,manufacturer', (error, stdout) => {
                    if (!error) {
                        const lines = stdout.split('\n');
                        for (const line of lines) {
                            if (line.includes('Manufacturer') || line.includes('Model')) continue;
                            if (line.trim()) {
                                const parts = line.trim().split(/\s+/);
                                if (parts.length >= 2) {
                                    info.manufacturer = parts[0];
                                    info.model = parts.slice(1).join(' ');
                                }
                                break;
                            }
                        }
                    }
                    resolve(info);
                });
            } else {
                resolve(info);
            }
        });
    }

    // Notify renderer process
    notifyRenderer(event, data) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send(event, data);
        }
    }

    // Comprehensive SMBIOS spoofing method
    async spoofSMBIOS(options = {}) {
        try {
            console.log('🔧 Spoofing SMBIOS information using real registry modifications...');
            
            const smbiosChanges = {
                systemGuid: options.systemGuid || crypto.randomUUID(),
                hardwareProfileGuid: options.hardwareProfileGuid || crypto.randomUUID(),
                computerHardwareId: options.computerHardwareId || this.generateHardwareId(),
                buildGuid: options.buildGuid || crypto.randomUUID(),
                computerHardwareIds: options.computerHardwareIds || this.generateHardwareId()
            };

            // Apply actual registry changes
            const results = await Promise.all([
                this.modifyRegistryValue('HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography', 'MachineGuid', smbiosChanges.systemGuid),
                this.modifyRegistryValue('HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\IDConfigDB\\Hardware Profiles\\0001', 'HwProfileGuid', smbiosChanges.hardwareProfileGuid),
                this.modifyRegistryValue('HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation', 'ComputerHardwareId', smbiosChanges.computerHardwareId),
                this.modifyRegistryValue('HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion', 'BuildGUID', smbiosChanges.buildGuid),
                this.modifyRegistryValue('HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation', 'ComputerHardwareIds', smbiosChanges.computerHardwareIds)
            ]);

            const failedChanges = results.filter(r => !r.success);
            if (failedChanges.length > 0) {
                console.log('⚠️ Some SMBIOS changes failed:', failedChanges.map(f => f.error));
            }

            const successfulChanges = results.filter(r => r.success).length;
            console.log(`✅ Applied ${successfulChanges}/${results.length} SMBIOS changes`);

            return {
                success: successfulChanges > 0,
                operation: 'smbios',
                message: `SMBIOS spoofing completed: ${successfulChanges}/${results.length} changes applied`,
                changes: smbiosChanges,
                applied: successfulChanges,
                total: results.length
            };
        } catch (error) {
            return {
                success: false,
                message: `SMBIOS spoofing failed: ${error.message}`,
                error: error.message
            };
        }
    }

    // Comprehensive disk serial spoofing method
    async spoofDiskSerials(options = {}) {
        try {
            console.log('💿 Spoofing disk serials using real registry modifications...');
            
            const diskChanges = [];
            
            // Get all disk drives including advanced types
            const disks = await this.getPhysicalDisks();
            
            for (const disk of disks) {
                const newSerial = options.preserveLength ? 
                    this.generateSerial(disk.serial?.length || 20) :
                    this.generateSerial(20);
                
                diskChanges.push({
                    device: disk.deviceId,
                    originalSerial: disk.serial,
                    newSerial: newSerial,
                    diskType: disk.type || 'UNKNOWN',
                    storageGUID: crypto.randomUUID(),
                    scsiSerial: this.generateSerial(16),
                    ataSerial: this.generateSerial(20),
                    usbSerial: this.generateSerial(12),
                    nvmeSerial: this.generateSerial(16)
                });
            }

            // Apply storage device registry changes (skip volatile Services\disk keys)
            const registryResults = [];
            for (const change of diskChanges) {
                const diskKeys = [
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Enum\\SCSI\\Disk&Ven_Generic&Prod_Storage\\${change.device}`,
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Enum\\IDE\\Disk_${change.device}`
                ];
                
                for (const key of diskKeys) {
                    try {
                        const result = await this.modifyRegistryValue(key, 'SerialNumber', change.newSerial);
                        registryResults.push(result);
                    } catch (error) {
                        // Log the error but don't fail completely
                        console.log(`⚠️ Skipping volatile registry key: ${key}`);
                        registryResults.push({ success: false, error: error.message, skipped: true });
                    }
                }
            }

            const successfulChanges = registryResults.filter(r => r.success).length;
            const totalAttempts = registryResults.length;
            
            console.log(`✅ Applied ${successfulChanges}/${totalAttempts} disk serial changes`);

            return {
                success: successfulChanges > 0,
                operation: 'disk',
                message: `Disk serial spoofing completed: ${successfulChanges}/${totalAttempts} changes applied`,
                changes: diskChanges,
                applied: successfulChanges,
                total: totalAttempts
            };
        } catch (error) {
            return {
                success: false,
                message: `Disk serial spoofing failed: ${error.message}`,
                error: error.message
            };
        }
    }

    // Comprehensive network adapter spoofing method
    async spoofNetworkAdapters(options = {}) {
        try {
            console.log('🌐 Spoofing network adapters using real registry modifications...');
            
            const networkChanges = [];
            const adapters = await this.getNetworkAdapters();
            
            for (const adapter of adapters) {
                const newMac = this.generateMacAddress();
                const newGuid = crypto.randomUUID();
                const newInstanceId = this.generateInstanceId();
                
                networkChanges.push({
                    name: adapter.name,
                    originalMac: adapter.mac,
                    newMac: newMac,
                    originalGuid: adapter.guid,
                    newGuid: newGuid,
                    originalInstanceId: adapter.instanceId,
                    newInstanceId: newInstanceId,
                    permanentMacAddress: this.generateMacAddress(),
                    networkAdapterInstanceId: this.generateInstanceId()
                });
            }

            // Apply network adapter registry changes
            const registryResults = [];
            for (const change of networkChanges) {
                // Registry keys for network adapter information
                const networkKeys = [
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4D36E972-E325-11CE-BFC1-08002BE10318}\\0001`,
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4D36E972-E325-11CE-BFC1-08002BE10318}\\0002`,
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters\\Interfaces\\${change.originalGuid}`
                ];
                
                for (const key of networkKeys) {
                    try {
                        const macResult = await this.modifyRegistryValue(key, 'NetworkAddress', change.newMac.replace(/-/g, ''));
                        const guidResult = await this.modifyRegistryValue(key, 'NetCfgInstanceId', change.newGuid);
                        registryResults.push(macResult, guidResult);
                    } catch (error) {
                        registryResults.push({ success: false, error: error.message });
                    }
                }
            }

            const successfulChanges = registryResults.filter(r => r.success).length;
            const totalAttempts = registryResults.length;
            
            console.log(`✅ Applied ${successfulChanges}/${totalAttempts} network adapter changes`);

            return {
                success: successfulChanges > 0,
                operation: 'network',
                message: `Network adapter spoofing completed: ${successfulChanges}/${totalAttempts} changes applied`,
                changes: networkChanges,
                applied: successfulChanges,
                total: totalAttempts
            };
        } catch (error) {
            return {
                success: false,
                message: `Network adapter spoofing failed: ${error.message}`,
                error: error.message
            };
        }
    }

    // Comprehensive GPU spoofing method
    async spoofGPUSerials(options = {}) {
        try {
            console.log('🎮 Spoofing GPU serials using real registry modifications...');
            
            const gpuChanges = [];
            const gpus = await this.getGPUDevices();
            
            for (const gpu of gpus) {
                const newSerial = this.generateSerial(16);
                const newDeviceId = this.generateDeviceId();
                const newVendorId = this.generateVendorId();
                const newBiosVersion = this.generateBiosVersion();
                
                gpuChanges.push({
                    name: gpu.name,
                    originalSerial: gpu.serial,
                    newSerial: newSerial,
                    originalDeviceId: gpu.deviceId,
                    newDeviceId: newDeviceId,
                    originalVendorId: gpu.vendorId,
                    newVendorId: newVendorId,
                    originalBiosVersion: gpu.biosVersion,
                    newBiosVersion: newBiosVersion,
                    gpuDeviceDescription: this.generateDeviceDescription(),
                    displayAdapterGuid: crypto.randomUUID(),
                    monitorSerial: this.generateSerial(12)
                });
            }

            // Apply GPU registry changes
            const registryResults = [];
            for (const change of gpuChanges) {
                // Registry keys for GPU information
                const gpuKeys = [
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Enum\\PCI\\${change.originalDeviceId}`,
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4D36E968-E325-11CE-BFC1-08002BE10318}\\0000`,
                    `HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Video\\{${change.displayAdapterGuid}}`
                ];
                
                for (const key of gpuKeys) {
                    try {
                        const deviceResult = await this.modifyRegistryValue(key, 'Device', change.newDeviceId);
                        const vendorResult = await this.modifyRegistryValue(key, 'Vendor', change.newVendorId);
                        const biosResult = await this.modifyRegistryValue(key, 'BiosVersion', change.newBiosVersion);
                        registryResults.push(deviceResult, vendorResult, biosResult);
                    } catch (error) {
                        registryResults.push({ success: false, error: error.message });
                    }
                }
            }

            const successfulChanges = registryResults.filter(r => r.success).length;
            const totalAttempts = registryResults.length;
            
            console.log(`✅ Applied ${successfulChanges}/${totalAttempts} GPU changes`);

            return {
                success: successfulChanges > 0,
                operation: 'gpu',
                message: `GPU spoofing completed: ${successfulChanges}/${totalAttempts} changes applied`,
                changes: gpuChanges,
                applied: successfulChanges,
                total: totalAttempts
            };
        } catch (error) {
            return {
                success: false,
                message: `GPU spoofing failed: ${error.message}`,
                error: error.message
            };
        }
    }

    // Registry modification method
    async modifyRegistryValue(keyPath, valueName, newValue) {
        try {
            console.log(`📝 Modifying registry: ${keyPath}\\${valueName} = ${newValue}`);
            
            // Modify the registry value
            const command = `reg add "${keyPath}" /v "${valueName}" /t REG_SZ /d "${newValue}" /f`;
            
            return new Promise((resolve) => {
                exec(command, { timeout: 10000 }, (error, stdout, stderr) => {
                    if (error) {
                        console.log(`❌ Failed to modify ${keyPath}\\${valueName}: ${error.message}`);
                        resolve({
                            success: false,
                            error: error.message,
                            keyPath: keyPath,
                            valueName: valueName
                        });
                        return;
                    }
                    
                    console.log(`✅ Successfully modified ${keyPath}\\${valueName}`);
                    resolve({
                        success: true,
                        keyPath: keyPath,
                        valueName: valueName,
                        newValue: newValue
                    });
                });
            });
        } catch (error) {
            return {
                success: false,
                error: error.message,
                keyPath: keyPath,
                valueName: valueName
            };
        }
    }

    // Hardware information gathering methods
    async getPhysicalDisks() {
        return new Promise((resolve, reject) => {
            const command = 'Get-PhysicalDisk | Select-Object DeviceId, SerialNumber, Model | ConvertTo-Json';
            exec(`powershell -Command "${command}"`, (error, stdout, stderr) => {
                if (error) {
                    resolve([]); // Return empty array instead of rejecting
                    return;
                }

                try {
                    const disks = JSON.parse(stdout);
                    const diskArray = Array.isArray(disks) ? disks : [disks];
                    resolve(diskArray.map(disk => ({
                        deviceId: disk.DeviceId || 'UNKNOWN',
                        serial: disk.SerialNumber,
                        model: disk.Model,
                        type: 'SSD' // Default type
                    })));
                } catch (parseError) {
                    resolve([]);
                }
            });
        });
    }

    async getNetworkAdapters() {
        return new Promise((resolve, reject) => {
            const command = `Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | Select-Object Name, MacAddress, InterfaceGuid | ConvertTo-Json`;
            exec(`powershell -Command "${command}"`, (error, stdout, stderr) => {
                if (error) {
                    resolve([]); // Return empty array instead of rejecting
                    return;
                }

                try {
                    const adapters = JSON.parse(stdout);
                    const adapterArray = Array.isArray(adapters) ? adapters : [adapters];
                    resolve(adapterArray.map(adapter => ({
                        name: adapter.Name,
                        mac: adapter.MacAddress,
                        guid: adapter.InterfaceGuid,
                        instanceId: adapter.InterfaceGuid
                    })));
                } catch (parseError) {
                    resolve([]);
                }
            });
        });
    }

    async getGPUDevices() {
        return new Promise((resolve, reject) => {
            const command = 'Get-WmiObject -Class Win32_VideoController | Select-Object Name, PNPDeviceID | ConvertTo-Json';
            exec(`powershell -Command "${command}"`, (error, stdout, stderr) => {
                if (error) {
                    resolve([]); // Return empty array instead of rejecting
                    return;
                }

                try {
                    const gpus = JSON.parse(stdout);
                    const gpuArray = Array.isArray(gpus) ? gpus : [gpus];
                    resolve(gpuArray.map(gpu => ({
                        name: gpu.Name,
                        deviceId: gpu.PNPDeviceID,
                        serial: this.extractSerialFromPNP(gpu.PNPDeviceID),
                        vendorId: this.extractVendorFromPNP(gpu.PNPDeviceID),
                        biosVersion: '1.0.0'
                    })));
                } catch (parseError) {
                    resolve([]);
                }
            });
        });
    }

    // Helper methods for advanced spoofing
    extractSerialFromPNP(pnpId) {
        if (!pnpId) return null;
        const parts = pnpId.split('\\');
        return parts[parts.length - 1] || null;
    }

    extractVendorFromPNP(pnpId) {
        if (!pnpId) return null;
        const match = pnpId.match(/VEN_([A-F0-9]{4})/i);
        return match ? `0x${match[1]}` : null;
    }

    generateSerial(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    generateInstanceId() {
        return `PCI\\VEN_${this.generateHex(4)}&DEV_${this.generateHex(4)}&SUBSYS_${this.generateHex(8)}&REV_${this.generateHex(2)}\\${this.generateHex(16)}`;
    }

    generateHex(length) {
        const chars = '0123456789ABCDEF';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    generateVendorId() {
        const vendors = ['0x10DE', '0x1002', '0x8086', '0x1414']; // NVIDIA, AMD, Intel, Microsoft
        return vendors[Math.floor(Math.random() * vendors.length)];
    }

    generateBiosVersion() {
        const major = Math.floor(Math.random() * 99) + 1;
        const minor = Math.floor(Math.random() * 99) + 1;
        const patch = Math.floor(Math.random() * 99) + 1;
        return `${major}.${minor}.${patch}`;
    }

    generateDeviceDescription() {
        const manufacturers = ['NVIDIA', 'AMD', 'Intel', 'Microsoft'];
        const types = ['Graphics', 'Display', 'Render', 'Compute'];
        const series = ['GTX', 'RTX', 'RX', 'Arc', 'Radeon', 'GeForce'];
        
        const manufacturer = manufacturers[Math.floor(Math.random() * manufacturers.length)];
        const type = types[Math.floor(Math.random() * types.length)];
        const seriesName = series[Math.floor(Math.random() * series.length)];
        const model = Math.floor(Math.random() * 9999) + 1000;
        
        return `${manufacturer} ${seriesName} ${model} ${type} Device`;
    }

    // Cleanup
    cleanup() {
        if (this.isActive) {
            this.restoreOriginalValues();
        }
        console.log('✅ HWID Spoofer cleaned up');
    }
}

module.exports = { HWIDSpoofer }; 