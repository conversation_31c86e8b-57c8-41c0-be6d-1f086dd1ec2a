#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use std::process::Command;

#[derive(serde::Serialize)]
struct InstallProgress {
    percent: u32,
    message: String,
}

#[command]
async fn install_and_exit(window: tauri::Window, install_path: String) -> Result<(), String> {
    // Send progress updates
    window.emit("install-progress", InstallProgress {
        percent: 0,
        message: "Starting installation...".to_string(),
    }).unwrap();
    
    // Simulate installation steps
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    
    window.emit("install-progress", InstallProgress {
        percent: 30,
        message: "Creating directories...".to_string(),
    }).unwrap();
    
    // Create install directory
    std::fs::create_dir_all(&install_path)
        .map_err(|e| format!("Failed to create directory: {}", e))?;
    
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
    
    window.emit("install-progress", InstallProgress {
        percent: 60,
        message: "Copying files...".to_string(),
    }).unwrap();
    
    // Copy files here...
    
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
    
    window.emit("install-progress", InstallProgress {
        percent: 90,
        message: "Creating shortcuts...".to_string(),
    }).unwrap();
    
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    
    window.emit("install-progress", InstallProgress {
        percent: 100,
        message: "Installation complete!".to_string(),
    }).unwrap();
    
    // Wait a moment then close
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
    
    // Force exit
    std::process::exit(0);
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![install_and_exit])
        .setup(|app| {
            let window = app.get_window("main").unwrap();
            
            // Remove all window decorations for clean look
            window.set_decorations(false).unwrap();
            window.set_resizable(false).unwrap();
            
            // Center window
            window.center().unwrap();
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
} 