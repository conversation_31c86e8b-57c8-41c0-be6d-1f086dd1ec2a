<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forums - Armory X Community</title>
    <meta name="description" content="Join the Armory X community forum. Get help, share tips, and discuss features with other users.">
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://armoryx.software/forums">
    <meta property="og:title" content="Armory X Community Forums">
    <meta property="og:description" content="Join the Armory X community forum. Get help, share tips, and discuss features.">
    <meta property="og:image" content="https://armoryx.software/assets/armory-x-preview.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="forum-styles.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase Configuration -->
    <script type="module">
        (async function() {
            try {
                // Prevent infinite refresh loops
                if (window.firebaseInitAttempted) {
                    console.log('🛑 Firebase initialization already attempted');
                    return;
                }
                window.firebaseInitAttempted = true;
                
                console.log('🔧 Starting Firebase initialization...');
                
                // Firebase configuration - REPLACE WITH YOUR OWN FIREBASE CONFIG
                const firebaseConfig = {
                    apiKey: "AIzaSyAto6FZKgKctMQCtD-wrP8GxvXXEsQ4Ads",
                    authDomain: "armoryx-website.firebaseapp.com",
                    projectId: "armoryx-website",
                    storageBucket: "armoryx-website.firebasestorage.app",
                    messagingSenderId: "671977508420",
                    appId: "1:671977508420:web:34b1978ad9011d3d5c3611"
                };

                console.log('📋 Firebase config:', {
                    projectId: firebaseConfig.projectId,
                    authDomain: firebaseConfig.authDomain
                });

                // Check if Firebase config is properly set up
                const isFirebaseConfigured = firebaseConfig.apiKey !== "YOUR_API_KEY_HERE" && 
                                            firebaseConfig.projectId !== "YOUR_PROJECT_ID";

                console.log('🔍 Firebase configured:', isFirebaseConfigured);

                if (isFirebaseConfigured) {
                    console.log('📦 Loading Firebase modules...');
                    
                    // Test network connectivity first
                    console.log('🌐 Testing network connectivity to Firebase...');
                    try {
                        const connectivityTest = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', { method: 'HEAD' });
                        console.log('✅ Network connectivity to Firebase CDN confirmed');
                    } catch (error) {
                        console.warn('⚠️ Network connectivity issue detected:', error.message);
                    }
                    
                    // Import Firebase modules only if configured
                    const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                    console.log('✅ Firebase app module loaded');
                    
                    const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                    console.log('✅ Firebase auth module loaded');
                    
                    const { getFirestore, doc, setDoc, getDoc, getDocs, collection, addDoc, deleteDoc, query, where, orderBy, limit, onSnapshot, serverTimestamp, updateDoc, increment, arrayUnion, arrayRemove } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                    console.log('✅ Firebase firestore module loaded');

                    // Initialize Firebase
                    console.log('🔥 Initializing Firebase app...');
                    const app = initializeApp(firebaseConfig);
                    console.log('✅ Firebase app initialized');
                    
                    console.log('🔐 Initializing Firebase auth...');
                    const auth = getAuth(app);
                    console.log('✅ Firebase auth initialized');
                    
                    console.log('🗄️ Initializing Firestore...');
                    const db = getFirestore(app);
                    console.log('✅ Firestore initialized');

                    // Test Firebase connection
                    console.log('🔗 Testing Firebase Auth connection...');
                    try {
                        // Test auth connection by checking current user (won't fail if not logged in)
                        const currentUser = auth.currentUser;
                        console.log('✅ Firebase Auth connection successful');
                    } catch (error) {
                        console.warn('⚠️ Firebase Auth connection test failed:', error);
                    }

                    console.log('🔗 Testing Firestore connection...');
                    try {
                        // Test Firestore connection (will help identify permission issues)
                        const testRef = doc(db, 'test', 'connection');
                        console.log('✅ Firestore connection test ready (permissions will be tested on first operation)');
                    } catch (error) {
                        console.warn('⚠️ Firestore connection test failed:', error);
                    }

                    // Make Firebase available globally
                    window.firebase = { 
                        auth, 
                        db, 
                        createUserWithEmailAndPassword, 
                        signInWithEmailAndPassword, 
                        signOut, 
                        onAuthStateChanged, 
                        doc, 
                        setDoc, 
                        getDoc, 
                        getDocs,
                        collection, 
                        addDoc, 
                        deleteDoc,
                        query, 
                        where,
                        orderBy, 
                        limit, 
                        onSnapshot, 
                        serverTimestamp, 
                        updateDoc, 
                        increment,
                        arrayUnion,
                        arrayRemove
                    };
                    
                    console.log('✅ Firebase initialized successfully - all services ready');
                    console.log('🔥 Firebase project:', firebaseConfig.projectId);
                    console.log('🔥 Auth domain:', firebaseConfig.authDomain);
                    
                    // Signal successful Firebase initialization immediately
                    window.firebaseInitialized = true;
                    console.log('🏁 Firebase initialization SUCCESS - services ready for use');
                } else {
                    console.log('🔧 Firebase not configured - running in local development mode');
                    window.firebase = null;
                    // Signal that Firebase initialization is complete (but not configured)
                    window.firebaseInitialized = true;
                }
            } catch (error) {
                console.error('❌ Firebase initialization failed:', error);
                console.error('Error details:', error.message);
                console.error('Error stack:', error.stack);
                window.firebase = null;
                // Signal that Firebase initialization is complete (but failed)
                window.firebaseInitialized = true;
            }
            
            console.log('🏁 Firebase initialization process complete');
        })();
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
            <div class="nav-container">
                    <a href="index.html" class="nav-logo">
                <img src="assets/Armory_X.ico" alt="Armory X" class="nav-logo-icon">
                <span>Armory X</span>
            </a>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="index.html#features" class="nav-link">Features</a></li>
                <li><a href="index.html#download" class="nav-link">Download</a></li>
                <li><a href="index.html#updates" class="nav-link">Updates</a></li>
                <li><a href="forums.html" class="nav-link active">Forums</a></li>
                <li><a href="account.html" class="nav-link">Account</a></li>
                <li><a href="http://discord.gg/uq4Zs2G57g" class="nav-link discord-link" target="_blank">
                    <i class="fab fa-discord"></i> Discord
                </a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Forum Header -->
    <section class="forum-header">
        <div class="container">
            <div class="forum-header-content">
                <h1 class="forum-title">
                    <i class="fas fa-comments"></i>
                    Armory X Community Forums
                </h1>
                <p class="forum-description">
                    Connect with other users, get help, share tips, and discuss the latest features
                </p>
                <div class="forum-stats">
                    <div class="stat">
                        <span class="stat-number" id="total-posts">247</span>
                        <span class="stat-label">Posts</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="total-users">89</span>
                        <span class="stat-label">Members</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="online-users">12</span>
                        <span class="stat-label">Online</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Required Message (shown to non-logged-in users) -->
    <section id="login-required" class="login-required">
        <div class="container">
            <div class="login-message">
                <i class="fas fa-lock"></i>
                <h2>Account Required</h2>
                <p>You need to be logged in to access the forums. Create an account or log in to join the community!</p>
                <div class="login-buttons">
                    <a href="account.html" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> Login / Register
                    </a>
                    <a href="index.html" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Forum Content (shown to logged-in users) -->
    <section id="forum-content" class="forum-content" style="display: none;">
        <div class="container">
            <!-- Forum Navigation Tabs -->
            <div class="forum-tabs">
                <button class="forum-tab active" data-tab="categories">
                    <i class="fas fa-folder"></i> Categories
                </button>
                <button class="forum-tab" data-tab="users">
                    <i class="fas fa-users"></i> Users
                </button>
                <button class="forum-tab" data-tab="recent">
                    <i class="fas fa-clock"></i> Recent
                </button>
            </div>

            <!-- Forum Categories -->
            <div class="forum-categories tab-content active" id="categories-tab">
                <div class="category-header">
                    <h2>Forum Categories</h2>
                    <button class="btn btn-primary new-post-btn">
                        <i class="fas fa-plus"></i> New Post
                    </button>
                </div>

                <!-- General Discussion -->
                <div class="forum-category">
                    <div class="category-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-title">General Discussion</h3>
                        <p class="category-description">General discussions about Armory X and PC optimization</p>
                        <div class="category-stats">
                            <span class="posts-count">45 posts</span>
                            <span class="last-post">Last post 2 hours ago</span>
                        </div>
                    </div>
                    <div class="category-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <!-- Bug Reports -->
                <div class="forum-category">
                    <div class="category-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-title">Bug Reports</h3>
                        <p class="category-description">Report bugs and issues with Armory X</p>
                        <div class="category-stats">
                            <span class="posts-count">23 posts</span>
                            <span class="last-post">Last post 1 day ago</span>
                        </div>
                    </div>
                    <div class="category-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <!-- Feature Requests -->
                <div class="forum-category">
                    <div class="category-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-title">Feature Requests</h3>
                        <p class="category-description">Suggest new features and improvements</p>
                        <div class="category-stats">
                            <span class="posts-count">67 posts</span>
                            <span class="last-post">Last post 3 hours ago</span>
                        </div>
                    </div>
                    <div class="category-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <!-- Technical Support -->
                <div class="forum-category">
                    <div class="category-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-title">Technical Support</h3>
                        <p class="category-description">Get help with technical issues and configurations</p>
                        <div class="category-stats">
                            <span class="posts-count">89 posts</span>
                            <span class="last-post">Last post 30 minutes ago</span>
                        </div>
                    </div>
                    <div class="category-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <!-- Gaming & Performance -->
                <div class="forum-category">
                    <div class="category-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="category-info">
                        <h3 class="category-title">Gaming & Performance</h3>
                        <p class="category-description">Discuss gaming optimizations and performance tips</p>
                        <div class="category-stats">
                            <span class="posts-count">112 posts</span>
                            <span class="last-post">Last post 1 hour ago</span>
                        </div>
                    </div>
                    <div class="category-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div class="forum-users tab-content" id="users-tab">
                <div class="users-header">
                    <h2>Community Members</h2>
                    <div class="users-stats">
                        <span>Total Members: <strong id="users-count">Loading...</strong></span>
                        <span>Online Now: <strong id="users-online">Loading...</strong></span>
                    </div>
                </div>
                <div class="users-list" id="users-list">
                    <p class="loading-message">Loading users...</p>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="recent-posts tab-content" id="recent-tab">
                <h2>Recent Posts</h2>
                <div class="posts-list">
                    <div class="post-item">
                        <div class="post-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="post-content">
                            <h4 class="post-title">Speed test showing inconsistent results</h4>
                            <p class="post-meta">
                                <span class="post-author">TechUser92</span>
                                <span class="post-time">2 hours ago</span>
                                <span class="post-category">Technical Support</span>
                            </p>
                            <p class="post-preview">The speed test feature is giving me different results each time I run it...</p>
                        </div>
                        <div class="post-stats">
                            <span class="replies">5 replies</span>
                            <span class="views">23 views</span>
                        </div>
                    </div>

                    <div class="post-item">
                        <div class="post-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="post-content">
                            <h4 class="post-title">Feature Request: Dark mode for gaming widget</h4>
                            <p class="post-meta">
                                <span class="post-author">GameMaster</span>
                                <span class="post-time">3 hours ago</span>
                                <span class="post-category">Feature Requests</span>
                            </p>
                            <p class="post-preview">Would love to see a dark mode option for the desktop widget...</p>
                        </div>
                        <div class="post-stats">
                            <span class="replies">12 replies</span>
                            <span class="views">47 views</span>
                        </div>
                    </div>

                    <div class="post-item">
                        <div class="post-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="post-content">
                            <h4 class="post-title">Best settings for FPS optimization?</h4>
                            <p class="post-meta">
                                <span class="post-author">ProGamer</span>
                                <span class="post-time">1 day ago</span>
                                <span class="post-category">Gaming & Performance</span>
                            </p>
                            <p class="post-preview">What are the optimal settings for maximizing FPS in competitive games?</p>
                        </div>
                        <div class="post-stats">
                            <span class="replies">8 replies</span>
                            <span class="views">156 views</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/Armory_X.ico" alt="Armory X" class="footer-logo-icon">
                        <span>Armory X</span>
                    </div>
                    <p>The ultimate PC tool suite for optimization, gaming, and system management.</p>
                    <div class="social-links">
                        <a href="http://discord.gg/uq4Zs2G57g" target="_blank">
                            <i class="fab fa-discord"></i>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html#features">Features</a></li>
                        <li><a href="index.html#download">Download</a></li>
                        <li><a href="index.html#updates">Updates</a></li>
                        <li><a href="index.html#account">Account</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Community</h4>
                    <ul>
                        <li><a href="forums.html">Forums</a></li>
                        <li><a href="http://discord.gg/uq4Zs2G57g">Discord</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Armory X. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="forum-script.js"></script>
    <script src="animations.js"></script>
    <script src="notifications.js"></script>
</body>
</html> 