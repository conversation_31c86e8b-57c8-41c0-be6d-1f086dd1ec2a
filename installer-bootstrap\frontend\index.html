<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Armory X Setup</title>
  <link rel="stylesheet" href="install.css">
  <style>
    * { box-sizing: border-box; margin: 0; padding: 0; }
    
    :root {
      --primary: #00b4ff;
      --secondary: #0099e6;
      --accent: #00ff88;
      --danger: #ff4444;
      --bg: #0a0a0a;
      --fg: #ffffff;
      --card-bg: #111318;
      --border: #1a1f2e;
    }

    body {
      font-family: "Segoe UI", Arial, sans-serif;
      background: transparent;
      color: var(--fg);
      height: 100vh;
      overflow: hidden;
      position: relative;
    }

    /* Epic animated background - now inside the card */
    .installer-card .matrix-bg {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background: linear-gradient(45deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
      border-radius: 20px;
      z-index: -2;
    }

    .installer-card .particles {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background: 
        radial-gradient(3px 3px at 20px 30px, var(--primary), transparent),
        radial-gradient(3px 3px at 40px 70px, var(--accent), transparent),
        radial-gradient(2px 2px at 90px 40px, var(--secondary), transparent),
        radial-gradient(2px 2px at 130px 80px, var(--primary), transparent),
        radial-gradient(3px 3px at 160px 30px, var(--accent), transparent),
        radial-gradient(1px 1px at 200px 60px, var(--primary), transparent),
        radial-gradient(2px 2px at 250px 20px, var(--accent), transparent),
        radial-gradient(1px 1px at 300px 90px, var(--secondary), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: float 15s infinite linear;
      opacity: 0.4;
      border-radius: 20px;
      z-index: -1;
    }

    @keyframes float {
      0% { transform: translate(0, 0) rotate(0deg); }
      100% { transform: translate(-200px, -100px) rotate(360deg); }
    }

    .installer-card .grid-overlay {
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background-image: 
        linear-gradient(rgba(0,180,255,0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,180,255,0.2) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: pulse 3s ease-in-out infinite, gridShift 20s linear infinite;
      opacity: 0.3;
      border-radius: 20px;
      z-index: -1;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.2; }
      50% { opacity: 0.4; }
    }

    @keyframes gridShift {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }

    /* Main container */
    .installer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      padding: 20px;
    }

    .installer-card {
      width: 600px;
      background: var(--card-bg);
      border: none;
      border-radius: 20px;
      padding: 40px;
      text-align: center;
      backdrop-filter: blur(10px);
      box-shadow: none;
      position: relative;
      overflow: hidden;
    }

    .installer-card::before {
      content: '';
      position: absolute;
      top: 0; left: -100%;
      width: 100%; height: 100%;
      background: linear-gradient(90deg, transparent, rgba(0,180,255,0.1), transparent);
      animation: scan 3s infinite;
    }

    @keyframes scan {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    /* Header */
    .header {
      margin-bottom: 30px;
    }

    .logo {
      font-size: 3rem;
      font-weight: bold;
      background: linear-gradient(135deg, var(--primary), var(--accent));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: glow 2s ease-in-out infinite alternate;
      margin-bottom: 10px;
    }

    @keyframes glow {
      from { filter: drop-shadow(0 0 10px var(--primary)); }
      to { filter: drop-shadow(0 0 20px var(--accent)); }
    }

    .subtitle {
      color: rgba(255,255,255,0.7);
      font-size: 1.1rem;
    }

    /* Screens */
    .screen {
      display: none;
      animation: slideIn 0.5s ease-out;
    }

    .screen.active {
      display: block;
    }

    @keyframes slideIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Welcome screen */
    .feature-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin: 20px 0;
    }

    .feature {
      padding: 15px;
      background: rgba(0,180,255,0.1);
      border: 1px solid rgba(0,180,255,0.2);
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .feature:hover {
      background: rgba(0,180,255,0.2);
      transform: translateY(-2px);
    }

    /* Options screen */
    .option-group {
      margin: 20px 0;
      text-align: left;
    }

    .option {
      display: flex;
      align-items: center;
      padding: 10px;
      margin: 10px 0;
      background: rgba(255,255,255,0.05);
      border-radius: 8px;
      transition: background 0.3s ease;
    }

    .option:hover {
      background: rgba(0,180,255,0.1);
    }

    .option input[type="checkbox"] {
      margin-right: 10px;
      transform: scale(1.2);
    }

    /* Path selector */
    .path-selector {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .path-input {
      flex: 1;
      padding: 12px;
      background: rgba(255,255,255,0.1);
      border: 1px solid var(--border);
      border-radius: 8px;
      color: var(--fg);
      font-size: 0.9rem;
    }

    /* Progress screen */
    .progress-container {
      margin: 30px 0;
    }

    .progress-bar {
      width: 100%;
      height: 12px;
      background: rgba(255,255,255,0.1);
      border-radius: 6px;
      overflow: hidden;
      margin: 15px 0;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--primary), var(--accent));
      width: 0%;
      transition: width 0.5s ease;
      position: relative;
    }

    .progress-fill::after {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .status-text {
      font-size: 1.1rem;
      margin: 10px 0;
      min-height: 1.5em;
    }

    /* Buttons */
    .btn-group {
      display: flex;
      gap: 15px;
      justify-content: center;
      margin-top: 30px;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 50%; left: 50%;
      width: 0; height: 0;
      background: rgba(255,255,255,0.2);
      border-radius: 50%;
      transition: all 0.5s ease;
      transform: translate(-50%, -50%);
    }

    .btn:hover::before {
      width: 300px; height: 300px;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      color: white;
    }

    .btn-secondary {
      background: rgba(255,255,255,0.1);
      color: var(--fg);
      border: 1px solid var(--border);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--danger), #cc0000);
      color: white;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* Exit button */
    .exit-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 30px;
      height: 30px;
      border: none;
      background: rgba(255,68,68,0.8);
      color: white;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      transition: all 0.3s ease;
      z-index: 10;
    }

    .exit-btn:hover {
      background: var(--danger);
      transform: scale(1.1);
    }

    /* Success animation */
    .success-animation {
      margin: 20px 0;
      font-size: 4rem;
      animation: bounce 1s ease infinite;
    }

    @keyframes bounce {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    /* Theme selector */
    .theme-selector {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin: 20px 0;
    }

    .theme-btn {
      width: 40px;
      height: 40px;
      border: 2px solid transparent;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .theme-btn.blue { background: linear-gradient(135deg, #00b4ff, #0099e6); }
    .theme-btn.green { background: linear-gradient(135deg, #00ff88, #00cc66); }
    .theme-btn.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
    .theme-btn.red { background: linear-gradient(135deg, #ff4444, #cc0000); }

    .theme-btn.active {
      border-color: white;
      transform: scale(1.1);
    }
  </style>
</head>
<body>
  <div class="title-bar">
    <div class="title">Armory X Installer</div>
    <button id="close-button" class="close-button">×</button>
  </div>
  <div class="installer">
    <div class="installer-card">
      <!-- Epic animated background inside the card -->
      <div class="matrix-bg"></div>
      <div class="particles"></div>
      <div class="grid-overlay"></div>
      
      <div class="header">
        <div class="logo">ARMORY X</div>
        <div class="subtitle">Professional System Utility Suite</div>
      </div>

      <!-- Welcome Screen -->
      <div class="screen active" id="welcome-screen">
        <h2>Welcome to the Future</h2>
        <div class="feature-grid">
          <div class="feature">
            <div>🧹 System Cleanup</div>
            <small>Advanced junk file removal</small>
          </div>
          <div class="feature">
            <div>🎮 Mod Manager</div>
            <small>Game modification tools</small>
          </div>
          <div class="feature">
            <div>🔧 System Tools</div>
            <small>Automation utilities</small>
          </div>
          <div class="feature">
            <div>🗂️ Desktop Widget</div>
            <small>File organization</small>
          </div>
        </div>
        
        <div class="theme-selector">
          <div class="theme-btn blue active" onclick="setTheme('blue')"></div>
          <div class="theme-btn green" onclick="setTheme('green')"></div>
          <div class="theme-btn purple" onclick="setTheme('purple')"></div>
          <div class="theme-btn red" onclick="setTheme('red')"></div>
        </div>
        
        <div class="btn-group">
          <button class="btn btn-secondary" onclick="exitInstaller()">Cancel</button>
          <button class="btn btn-primary" onclick="showScreen('options')">Customize Install</button>
        </div>
      </div>

      <!-- Options Screen -->
      <div class="screen" id="options-screen">
        <h2>Installation Options</h2>
        
        <div class="option-group">
          <label>Install Location:</label>
          <div class="path-selector">
            <input type="text" class="path-input" id="install-path" value="C:\Program Files\Armory X">
            <button class="btn btn-secondary" onclick="browsePath()">Browse</button>
          </div>
        </div>

        <div class="option-group">
          <h3>Components:</h3>
          <div class="option">
            <input type="checkbox" id="desktop-shortcut" checked>
            <label for="desktop-shortcut">Create Desktop Shortcut</label>
          </div>
          <div class="option">
            <input type="checkbox" id="start-menu" checked>
            <label for="start-menu">Add to Start Menu</label>
          </div>
          <div class="option">
            <input type="checkbox" id="auto-start">
            <label for="auto-start">Launch at Windows Startup</label>
          </div>
          <div class="option">
            <input type="checkbox" id="file-associations">
            <label for="file-associations">Register File Associations</label>
          </div>
        </div>

        <div class="btn-group">
          <button class="btn btn-secondary" onclick="showScreen('welcome')">Back</button>
          <button class="btn btn-primary" onclick="startInstall()">Install Now</button>
        </div>
      </div>

      <!-- Progress Screen -->
      <div class="screen" id="progress-screen">
        <h2>Installing Armory X</h2>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
          </div>
          <div class="status-text" id="status-text">Preparing installation...</div>
        </div>

        <div class="btn-group">
          <button class="btn btn-danger" onclick="cancelInstall()" id="cancel-btn">Cancel</button>
        </div>
      </div>

      <!-- Success Screen -->
      <div class="screen" id="success-screen">
        <div class="success-animation">🚀</div>
        <h2>Installation Complete!</h2>
        <p>Armory X has been successfully installed and is ready to use.</p>
        
        <div class="btn-group">
          <button class="btn btn-secondary" onclick="exitInstaller()">Close</button>
          <button class="btn btn-primary" onclick="launchApp()">Launch Armory X</button>
        </div>
      </div>
    </div>
  </div>

  <script src="install.js"></script>
</body>
</html> 