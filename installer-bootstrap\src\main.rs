#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};
use std::fs::{self, File};
use std::io::Write;
use std::path::{Path, PathBuf};
use tauri::{command, App<PERSON><PERSON><PERSON>, Manager};
use std::process;

// Embedded payload (placeholder for now)
const PAYLOAD_DATA: &[u8] = include_bytes!("payload_stub.bin");

#[derive(Debug, Serialize, Deserialize)]
struct InstallOptions {
    #[serde(rename = "desktopShortcut")]
    desktop_shortcut: bool,
    #[serde(rename = "startMenu")]
    start_menu: bool,
    #[serde(rename = "autoStart")]
    auto_start: bool,
    #[serde(rename = "fileAssociations")]
    file_associations: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct InstallResult {
    success: bool,
    message: String,
    path: String,
}

#[command]
fn close_window() {
    process::exit(0); // Forcefully terminate the process
}

#[command]
fn exit_app(app: AppHandle) -> Result<(), String> {
    println!("🛑 exit_app called - graceful app exit");
    app.exit(0);
    Ok(())
}

#[command]
async fn start_install(target_dir: String, options: Option<InstallOptions>) -> Result<InstallResult, String> {
    println!("🚀 Starting Armory X installation...");
    println!("📂 Target directory: {}", target_dir);
    
    let install_path = PathBuf::from(&target_dir);
    let opts = options.unwrap_or(InstallOptions {
        desktop_shortcut: true,
        start_menu: true,
        auto_start: false,
        file_associations: false,
    });
    
    println!("⚙️ Installation options: {:?}", opts);
    
    // Create installation directory
    if let Err(e) = create_install_directory(&install_path) {
        return Err(format!("Failed to create installation directory: {}", e));
    }
    
    // Extract/copy main application files
    if let Err(e) = install_application_files(&install_path).await {
        return Err(format!("Failed to install application files: {}", e));
    }
    
    // Create shortcuts if requested
    if opts.desktop_shortcut {
        if let Err(e) = create_desktop_shortcut(&install_path) {
            println!("⚠️ Warning: Failed to create desktop shortcut: {}", e);
        }
    }
    
    if opts.start_menu {
        if let Err(e) = create_start_menu_entry(&install_path) {
            println!("⚠️ Warning: Failed to create start menu entry: {}", e);
        }
    }
    
    // Setup auto-start if requested
    if opts.auto_start {
        if let Err(e) = setup_auto_start(&install_path) {
            println!("⚠️ Warning: Failed to setup auto-start: {}", e);
        }
    }
    
    // Register file associations if requested
    if opts.file_associations {
        if let Err(e) = register_file_associations(&install_path) {
            println!("⚠️ Warning: Failed to register file associations: {}", e);
        }
    }
    
    // Create uninstaller
    if let Err(e) = create_uninstaller(&install_path) {
        println!("⚠️ Warning: Failed to create uninstaller: {}", e);
    }
    
    println!("✅ Armory X installation completed successfully!");
    
    Ok(InstallResult {
        success: true,
        message: "Armory X has been installed successfully!".to_string(),
        path: target_dir,
    })
}

fn create_install_directory(install_path: &Path) -> Result<(), std::io::Error> {
    println!("📁 Creating installation directory...");
    fs::create_dir_all(install_path)?;
    
    // Create subdirectories
    fs::create_dir_all(install_path.join("Resources"))?;
    fs::create_dir_all(install_path.join("Data"))?;
    fs::create_dir_all(install_path.join("Logs"))?;
    
    Ok(())
}

async fn install_application_files(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("📦 Installing application files...");
    
    // Main executable (for now, just create a placeholder)
    let exe_path = install_path.join("Armory X.exe");
    let mut exe_file = File::create(&exe_path)?;
    exe_file.write_all(PAYLOAD_DATA)?;
    
    println!("✅ Created main executable: {}", exe_path.display());
    
    // Create configuration files
    create_config_files(install_path)?;
    
    // Create sample data files
    create_sample_data(install_path)?;
    
    Ok(())
}

fn create_config_files(install_path: &Path) -> Result<(), std::io::Error> {
    println!("⚙️ Creating configuration files...");
    
    // Create app config
    let config_content = r#"{
  "version": "1.0.0",
  "theme": "blue",
  "auto_update": true,
  "installation_id": "ARMX-INSTALL-{RANDOM_ID}",
  "features": {
    "system_cleanup": true,
    "mod_manager": true,
    "system_tools": true,
    "desktop_widget": true
  }
}"#;
    
    let config_path = install_path.join("config.json");
    fs::write(config_path, config_content)?;
    
    // Create user preferences template
    let prefs_content = r#"{
  "ui": {
    "theme": "blue",
    "animations": true,
    "sound_effects": false
  },
  "cleanup": {
    "confirm_before_delete": true,
    "deep_scan": false,
    "auto_schedule": false
  },
  "system_tools": {
    "remember_settings": true,
    "global_hotkeys": true
  }
}"#;
    
    let prefs_path = install_path.join("Data").join("preferences.json");
    fs::write(prefs_path, prefs_content)?;
    
    Ok(())
}

fn create_sample_data(install_path: &Path) -> Result<(), std::io::Error> {
    println!("📋 Creating sample data files...");
    
    // Create a welcome file
    let welcome_content = r#"Welcome to Armory X!

Armory X is your professional system utility suite. Here's what you can do:

🧹 System Cleanup - Remove junk files and optimize your system
🎮 Mod Manager - Organize and manage game modifications  
🔧 System Tools - Automation utilities for enhanced productivity
🗂️ Desktop Widget - Keep your desktop organized

For help and support, visit: https://armoryx.com/support

Thank you for choosing Armory X!
"#;
    
    let welcome_path = install_path.join("README.txt");
    fs::write(welcome_path, welcome_content)?;
    
    // Create license file
    let license_content = r#"Armory X End User License Agreement (EULA)

Copyright (c) 2024 Armory X. All rights reserved.

This software is licensed under the terms specified in your license agreement.
For full license terms, visit: https://armoryx.com/license

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND.
"#;
    
    let license_path = install_path.join("LICENSE.txt");
    fs::write(license_path, license_content)?;
    
    Ok(())
}

#[cfg(windows)]
fn create_desktop_shortcut(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 Creating desktop shortcut...");
    
    let desktop_path = get_desktop_path()?;
    let _shortcut_path = desktop_path.join("Armory X.lnk");
    let _exe_path = install_path.join("Armory X.exe");
    
    // Create a batch file that will act as our "shortcut" for now
    // In a real implementation, you'd use the Windows Shell API to create proper .lnk files
    let batch_content = format!(
        r#"@echo off
cd /d "{}"
start "" "Armory X.exe"
"#,
        install_path.display()
    );
    
    let batch_path = desktop_path.join("Armory X.bat");
    fs::write(&batch_path, batch_content)?;
    
    println!("✅ Desktop shortcut created: {}", batch_path.display());
    Ok(())
}

#[cfg(not(windows))]
fn create_desktop_shortcut(_install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("ℹ️ Desktop shortcuts not implemented for this platform");
    Ok(())
}

#[cfg(windows)]
fn create_start_menu_entry(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("📌 Creating start menu entry...");
    
    let start_menu_path = get_start_menu_path()?;
    let armory_folder = start_menu_path.join("Armory X");
    fs::create_dir_all(&armory_folder)?;
    
    // Create start menu shortcuts
    let app_shortcut = format!(
        r#"@echo off
cd /d "{}"
start "" "Armory X.exe"
"#,
        install_path.display()
    );
    
    let uninstall_shortcut = format!(
        r#"@echo off
cd /d "{}"
start "" "uninstall.exe"
"#,
        install_path.display()
    );
    
    fs::write(armory_folder.join("Armory X.bat"), app_shortcut)?;
    fs::write(armory_folder.join("Uninstall Armory X.bat"), uninstall_shortcut)?;
    
    println!("✅ Start menu entries created");
    Ok(())
}

#[cfg(not(windows))]
fn create_start_menu_entry(_install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("ℹ️ Start menu entries not implemented for this platform");
    Ok(())
}

fn setup_auto_start(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔄 Setting up auto-start...");
    
    #[cfg(windows)]
    {
        // On Windows, we'd add a registry entry to HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run
        // For now, just create a startup batch file
        let startup_path = get_startup_path()?;
        let startup_script = format!(
            r#"@echo off
cd /d "{}"
start "" "Armory X.exe" --minimized
"#,
            install_path.display()
        );
        
        let startup_file = startup_path.join("Armory X Startup.bat");
        fs::write(startup_file, startup_script)?;
        println!("✅ Auto-start configured");
    }
    
    #[cfg(not(windows))]
    {
        println!("ℹ️ Auto-start not implemented for this platform");
    }
    
    Ok(())
}

fn register_file_associations(_install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 Registering file associations...");
    
    #[cfg(windows)]
    {
        // In a real implementation, you'd modify the Windows registry to associate
        // specific file types with Armory X
        println!("✅ File associations registered (.armx, .modpack, etc.)");
    }
    
    #[cfg(not(windows))]
    {
        println!("ℹ️ File associations not implemented for this platform");
    }
    
    Ok(())
}

fn create_uninstaller(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    println!("🗑️ Creating uninstaller...");
    
    let uninstaller_content = format!(
        r#"@echo off
echo Uninstalling Armory X...
echo This would remove all files from: {}
echo.
echo Press any key to continue with uninstall (THIS IS JUST A DEMO)
pause
echo Uninstall completed (demo mode - no files actually removed)
pause
"#,
        install_path.display()
    );
    
    let uninstaller_path = install_path.join("uninstall.bat");
    fs::write(uninstaller_path, uninstaller_content)?;
    
    println!("✅ Uninstaller created");
    Ok(())
}

// Helper functions for Windows paths
#[cfg(windows)]
fn get_desktop_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let user_profile = std::env::var("USERPROFILE")?;
    Ok(PathBuf::from(user_profile).join("Desktop"))
}

#[cfg(windows)]
fn get_start_menu_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data = std::env::var("APPDATA")?;
    Ok(PathBuf::from(app_data).join("Microsoft").join("Windows").join("Start Menu").join("Programs"))
}

#[cfg(windows)]
fn get_startup_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data = std::env::var("APPDATA")?;
    Ok(PathBuf::from(app_data).join("Microsoft").join("Windows").join("Start Menu").join("Programs").join("Startup"))
}

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_process::init())
        .invoke_handler(tauri::generate_handler![start_install, close_window])
        .setup(|_app| {
            println!("🎯 Armory X Installer Backend Ready");
            Ok(())
        })
        .build(tauri::generate_context!())
        .expect("error while building tauri application")
        .run(|_app_handle, event| {
            if let tauri::RunEvent::WindowEvent {
                event: tauri::WindowEvent::CloseRequested { .. },
                ..
            } = event
            {
                println!("RUST: Window close requested - terminating process");
                std::process::exit(0);
            }
        });
} 