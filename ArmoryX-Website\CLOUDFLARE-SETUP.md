# ☁️ Deploy with Cloudflare Pages

## Method 1: Direct Upload (Easiest)

### Step 1: Access Pages
1. In Cloudflare dashboard, click **"Workers & Pages"** (left sidebar)
2. Click **"Pages"** tab
3. Click **"Upload assets"**

### Step 2: Upload Your Site
1. **Drag your entire `ArmoryX-Website` folder** into the upload area
2. Project name: `armory-x-website`
3. Click **"Deploy site"**
4. Your site will be live at `[random-name].pages.dev`

### Step 3: Add Custom Domain
1. In your Pages project, go to **"Custom domains"**
2. Click **"Set up a custom domain"**
3. Enter: `armoryx.software`
4. Follow Cloudflare's DNS instructions

## Method 2: Connect GitHub (For Easy Updates)

### Step 1: Upload to GitHub
1. Create new repository on GitHub
2. Upload all your website files
3. Make it public

### Step 2: Connect to Cloudflare
1. In Cloudflare Pages, click **"Connect to Git"**
2. Connect your GitHub account
3. Select your repository
4. Deploy settings:
   - **Build command**: Leave empty
   - **Build output directory**: Leave empty
   - **Root directory**: Leave empty

### Step 3: Configure Domain
Same as Method 1 - add `armoryx.software` in custom domains

## DNS Configuration

### If Domain is with Namecheap:
1. In Namecheap, go to **Advanced DNS**
2. Add these records:
   ```
   Type: CNAME
   Host: @
   Value: [your-pages-url].pages.dev
   TTL: Automatic
   ```

### If Domain is with Cloudflare:
1. Cloudflare will automatically configure DNS
2. No manual setup needed!

## 🚀 Benefits of Cloudflare Pages
- ✅ **Fastest globally**: CDN in 275+ cities
- ✅ **Free SSL**: Automatic HTTPS
- ✅ **Unlimited bandwidth**: No limits
- ✅ **Free custom domains**: No extra cost
- ✅ **Auto deployments**: Updates from GitHub
- ✅ **Advanced security**: DDoS protection included

## 🔄 Update Your Site
- **Method 1**: Re-upload files through dashboard
- **Method 2**: Push to GitHub (auto-deploys)

Your site will be live at `https://armoryx.software` with blazing fast performance worldwide! 