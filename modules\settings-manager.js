/**
 * Settings Manager module for Armory X
 * Extracted from main.js for better maintainability
 */

const path = require('path');
const fs = require('fs-extra');
const { app } = require('electron');

class SettingsManager {
  constructor() {
    // Default settings configuration
    this.defaultSettings = {
      theme: 'dark',
      primaryColor: '#00d4ff',
      secondaryColor: '#ff6b35',
      accentColor: '#00ff88',
      animations: true,
      autoCleanup: false,
      notifications: true,
      minimizeToTray: false,
      startWithWindows: false,
      browser: {
        defaultSite: 'modrinth', // modrinth, curseforge, custom
        customDefaultUrl: '',
        showQuickNav: true,
        enableAdBlocker: true,
        autoOpenDownloads: true
      }
    };
  }

  /**
   * Get the settings file path
   * @returns {string} Settings file path
   */
  getSettingsPath() {
    return path.join(app.getPath('userData'), 'settings.json');
  }

  /**
   * Load settings from file
   * @returns {Object} Settings object
   */
  async loadSettings() {
    const settingsPath = this.getSettingsPath();
    
    try {
      if (await fs.pathExists(settingsPath)) {
        const settings = await fs.readJson(settingsPath);
        console.log('✅ Settings loaded successfully');
        return settings;
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
    
    // Return default settings if file doesn't exist or error occurred
    console.log('📋 Using default settings');
    return { ...this.defaultSettings };
  }

  /**
   * Save settings to file
   * @param {Object} settings - Settings object to save
   * @returns {Object} Save result
   */
  async saveSettings(settings) {
    const settingsPath = this.getSettingsPath();
    
    try {
      await fs.writeJson(settingsPath, settings, { spaces: 2 });
      console.log('✅ Settings saved successfully');
      return { success: true };
    } catch (error) {
      console.error('Error saving settings:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Get default settings
   * @returns {Object} Default settings object
   */
  getDefaultSettings() {
    return { ...this.defaultSettings };
  }

  /**
   * Reset settings to defaults
   * @returns {Object} Reset result
   */
  async resetSettings() {
    try {
      const result = await this.saveSettings(this.defaultSettings);
      if (result.success) {
        console.log('✅ Settings reset to defaults');
        return { success: true, settings: { ...this.defaultSettings } };
      }
      return result;
    } catch (error) {
      console.error('Error resetting settings:', error);
      return { success: false, message: error.message };
    }
  }
}

module.exports = { SettingsManager };