; Armory X Epic Games Style Installer
; Ultra-modern dark theme installer

!include "MUI2.nsh"
!include "nsDialogs.nsh"
!include "LogicLib.nsh"
!include "WinMessages.nsh"

; --------------------------------
; General Configuration

!define PRODUCT_NAME "Armory X"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "Armory X"
!define PRODUCT_WEB_SITE "https://armoryx.com"

Name "${PRODUCT_NAME}"
OutFile "ArmoryX-Epic-Style-Setup.exe"
InstallDir "$LOCALAPPDATA\${PRODUCT_NAME}"
InstallDirRegKey HKCU "Software\${PRODUCT_NAME}" "InstallPath"
RequestExecutionLevel user

; Compression
SetCompressor /SOLID lzma
SetCompressorDictSize 64

; --------------------------------
; Interface Configuration

!define MUI_ABORTWARNING
!define MUI_ICON "..\ArmoryX-Website\assets\Armory_X.ico"
!define MUI_UNICON "..\ArmoryX-Website\assets\Armory_X.ico"

; Custom colors
!define MUI_BGCOLOR 0x1a1a2e
!define MUI_TEXTCOLOR 0xedf2f4

; --------------------------------
; Version Information

VIProductVersion "${PRODUCT_VERSION}.0"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© 2024 ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME} Installer"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"

; --------------------------------
; Modern UI Pages

!insertmacro MUI_PAGE_WELCOME
Page custom ModernInstallPage ModernInstallPageLeave
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

!insertmacro MUI_LANGUAGE "English"

; --------------------------------
; Custom Variables

Var InstallButton
Var Dialog
Var DesktopCheckbox
Var StartMenuCheckbox
Var DirectoryText
Var BrowseButton
Var ProgressBar
Var StatusLabel
Var CreateDesktopShortcut
Var CreateStartMenuShortcut

; --------------------------------
; Custom Modern Page

Function ModernInstallPage
  !insertmacro MUI_HEADER_TEXT "Install ${PRODUCT_NAME}" "Choose installation options"
  
  nsDialogs::Create 1018
  Pop $Dialog
  
  ; Background gradient effect
  SetCtlColors $Dialog "" 0x1a1a2e
  
  ; Installation directory section
  ${NSD_CreateGroupBox} 0 0 100% 40% "Installation Location"
  Pop $0
  SetCtlColors $0 0xedf2f4 transparent
  
  ${NSD_CreateLabel} 10 20 20% 12 "Install to:"
  Pop $0
  SetCtlColors $0 0xedf2f4 transparent
  
  ${NSD_CreateText} 10 35 75% 12 "$INSTDIR"
  Pop $DirectoryText
  SetCtlColors $DirectoryText 0x000000 0xffffff
  
  ${NSD_CreateButton} 87% 34 12% 14 "Browse"
  Pop $BrowseButton
  ${NSD_OnClick} $BrowseButton OnBrowseButton
  
  ; Options section
  ${NSD_CreateGroupBox} 0 45% 100% 35% "Options"
  Pop $0
  SetCtlColors $0 0xedf2f4 transparent
  
  ${NSD_CreateCheckbox} 10 60% 80% 12 "Create desktop shortcut"
  Pop $DesktopCheckbox
  ${NSD_Check} $DesktopCheckbox
  SetCtlColors $DesktopCheckbox 0xedf2f4 transparent
  
  ${NSD_CreateCheckbox} 10 70% 80% 12 "Create Start Menu shortcuts"
  Pop $StartMenuCheckbox
  ${NSD_Check} $StartMenuCheckbox
  SetCtlColors $StartMenuCheckbox 0xedf2f4 transparent
  
  ; Space required
  ${NSD_CreateLabel} 10 85% 80% 12 "Space required: ~50 MB"
  Pop $0
  SetCtlColors $0 0xa0a0a0 transparent
  
  ; Install button (Epic Games style)
  ${NSD_CreateButton} 75% 87% 23% 25 "INSTALL"
  Pop $InstallButton
  
  ; Style the install button
  CreateFont $0 "Arial" 10 700
  SendMessage $InstallButton ${WM_SETFONT} $0 0
  
  nsDialogs::Show
FunctionEnd

Function ModernInstallPageLeave
  ${NSD_GetState} $DesktopCheckbox $CreateDesktopShortcut
  ${NSD_GetState} $StartMenuCheckbox $CreateStartMenuShortcut
  ${NSD_GetText} $DirectoryText $INSTDIR
FunctionEnd

Function OnBrowseButton
  nsDialogs::SelectFolderDialog "Select Installation Directory" "$INSTDIR"
  Pop $0
  ${If} $0 != error
    StrCpy $INSTDIR $0
    ${NSD_SetText} $DirectoryText $INSTDIR
  ${EndIf}
FunctionEnd

; --------------------------------
; Installer Sections

Section "Main Installation" SEC01
  SetOutPath "$INSTDIR"
  
  ; Create installation directory
  CreateDirectory "$INSTDIR"
  CreateDirectory "$INSTDIR\Resources"
  CreateDirectory "$INSTDIR\Data"
  CreateDirectory "$INSTDIR\Logs"
  
  ; Extract files
  File /r "payload\*.*"
  
  ; Write uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Registry entries
  WriteRegStr HKCU "Software\${PRODUCT_NAME}" "InstallPath" "$INSTDIR"
  WriteRegStr HKCU "Software\${PRODUCT_NAME}" "Version" "${PRODUCT_VERSION}"
  
  ; Add to Windows Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "DisplayName" "${PRODUCT_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "DisplayIcon" "$INSTDIR\ArmoryX.exe,0"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "Publisher" "${PRODUCT_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                   "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                     "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" \
                     "NoRepair" 1
  
  ; Create shortcuts based on user selection
  ${If} $CreateDesktopShortcut == 1
    CreateShortcut "$DESKTOP\${PRODUCT_NAME}.lnk" "$INSTDIR\ArmoryX.exe" "" \
                   "$INSTDIR\ArmoryX.exe" 0 SW_SHOWNORMAL
  ${EndIf}
  
  ${If} $CreateStartMenuShortcut == 1
    CreateDirectory "$SMPROGRAMS\${PRODUCT_NAME}"
    CreateShortcut "$SMPROGRAMS\${PRODUCT_NAME}\${PRODUCT_NAME}.lnk" \
                   "$INSTDIR\ArmoryX.exe" "" "$INSTDIR\ArmoryX.exe" 0
    CreateShortcut "$SMPROGRAMS\${PRODUCT_NAME}\Uninstall ${PRODUCT_NAME}.lnk" \
                   "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0
  ${EndIf}
SectionEnd

; --------------------------------
; Uninstaller Section

Section "Uninstall"
  ; Remove files and directories
  Delete "$INSTDIR\*.*"
  RMDir /r "$INSTDIR\Resources"
  RMDir /r "$INSTDIR\Data"
  RMDir /r "$INSTDIR\Logs"
  RMDir /r "$INSTDIR"
  
  ; Remove shortcuts
  Delete "$DESKTOP\${PRODUCT_NAME}.lnk"
  Delete "$SMPROGRAMS\${PRODUCT_NAME}\*.*"
  RMDir "$SMPROGRAMS\${PRODUCT_NAME}"
  
  ; Remove registry entries
  DeleteRegKey HKCU "Software\${PRODUCT_NAME}"
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
  
  ; Show completion message
  MessageBox MB_ICONINFORMATION|MB_OK "${PRODUCT_NAME} has been successfully uninstalled."
SectionEnd

; --------------------------------
; Functions

Function .onInit
  ; Check if already installed
  ReadRegStr $0 HKCU "Software\${PRODUCT_NAME}" "InstallPath"
  ${If} $0 != ""
    MessageBox MB_YESNO|MB_ICONQUESTION \
               "${PRODUCT_NAME} is already installed.$\n$\nDo you want to reinstall?" \
               IDYES +2
    Abort
  ${EndIf}
FunctionEnd 