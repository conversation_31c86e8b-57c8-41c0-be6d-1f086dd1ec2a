# Local Testing Guide

## Quick Test Checklist

After making the changes, test these features locally to ensure everything works:

### ✅ **Basic Website Functionality**
1. **Open `index.html` in your browser**
2. **Check browser console** - you should see:
   ```
   🔧 Firebase not configured - running in local development mode
   🔧 Firebase not available - using basic authentication
   📊 Using fallback statistics (Firebase not available)
   ```
3. **Verify navigation works** - click menu items, scroll to sections
4. **Check live stats** - the "Active Users" should show a number with red "🔴 Live" indicator

### ✅ **Authentication Testing**
1. **Try to register a new account:**
   - Go to Account section
   - Click "Register" tab
   - Use any email (e.g., <EMAIL>)
   - Use password (min 6 characters)
   - Should show "Account created successfully! (Local mode)"

2. **Try to register with same email again:**
   - Should show error: "An account with this email already exists (local)"

3. **Test login:**
   - Click "Login" tab
   - Use the same email/password from registration
   - Should show "Login successful! (Local mode)"
   - Should redirect to logged-in view with license key section

4. **Test logout:**
   - Click "Logout" button
   - Should return to login/register forms

### ✅ **Download Functionality**
1. **Click "Download Now" button**
2. **Should show loading state** then download modal
3. **Modal should close** with X, ESC key, or click outside

### ✅ **Forum Access**
1. **Without login** - visit `forums.html`
   - Should show "Login Required" message
2. **With login** - visit `forums.html` after logging in
   - Should show forum content with stats

## Console Messages (Normal)

You should see these console messages (not errors):
```
🔧 Firebase not configured - running in local development mode
🔧 Firebase not available - using basic authentication  
📊 Using fallback statistics (Firebase not available)
🔧 Using fallback authentication
🔧 Using local logout
```

## If You See Errors

### **JavaScript Errors:**
- Check if you have an ad blocker blocking external scripts
- Try opening in an incognito/private browser window
- Clear browser cache and reload

### **Authentication Not Working:**
- Check browser developer tools > Application > Local Storage
- Should see `armoryX_local_users` array after registration

### **Stats Not Updating:**
- Numbers should change slightly every 30 seconds
- This is normal behavior showing "live" simulation

## Deploy Test

Once local testing passes:
1. **Commit and push** changes to your repository
2. **Cloudflare Pages** will auto-deploy
3. **Test live site** - should work the same as local
4. **Set up Firebase** later using `FIREBASE-SETUP.md` guide

## Browser Compatibility

Tested and working on:
- ✅ Chrome/Edge (latest)
- ✅ Firefox (latest)  
- ✅ Safari (latest)

## Troubleshooting

### Page Still "Spazzing Out"
1. **Hard refresh** - Ctrl+F5 (or Cmd+Shift+R on Mac)
2. **Clear browser cache** completely
3. **Try different browser**
4. **Check console** for any remaining errors

### Console Shows Red Errors
1. **Read error message** carefully
2. **Look for line numbers** in the error
3. **Common issues:**
   - External script blocked by ad blocker
   - CORS issues (should resolve on deployed site)
   - JavaScript syntax errors (check file encoding)

---

## Success Indicators

✅ **Website loads smoothly without "spazzing"**  
✅ **Console shows Firebase fallback messages (not errors)**  
✅ **Can create account and login in local mode**  
✅ **Download button works and shows modal**  
✅ **Live stats update every 30 seconds**  
✅ **Forum access requires login**

If all these work locally, your website is ready for deployment! 🎉 