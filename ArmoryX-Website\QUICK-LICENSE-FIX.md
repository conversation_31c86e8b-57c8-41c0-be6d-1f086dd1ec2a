# 🚀 Quick Fix for License System Errors

## 🔍 **Step 1: Run Debug Script**

1. **Open Browser Console** (F12 → Console)
2. **Run the debug command**:
   ```javascript
   window.debugLicenseSystem()
   ```
3. **Look for error messages** and follow the suggestions

## 🛡️ **Step 2: Update Firebase Security Rules (MOST IMPORTANT)**

This is likely the main issue. The Firebase security rules need to be updated:

1. **Go to [Firebase Console](https://console.firebase.google.com/)**
2. **Select your ArmoryX project**
3. **Go to Firestore Database → Rules**
4. **Replace ALL existing rules** with the rules from `firebase-security-rules.md`
5. **Click "Publish"**

**⚠️ Important**: Make sure you copy the COMPLETE rules including the license system rules at the bottom!

## 👤 **Step 3: Set Admin Role (If Needed)**

If the debug script shows you don't have admin role, run this in console:

```javascript
// Set yourself as admin
if (window.firebase && window.firebase.auth.currentUser) {
    const userId = window.firebase.auth.currentUser.uid;
    window.firebase.setDoc(window.firebase.doc(window.firebase.db, 'users', userId), {
        role: 'admin',
        email: window.firebase.auth.currentUser.email,
        displayName: window.firebase.auth.currentUser.displayName || 'Admin User',
        createdAt: window.firebase.serverTimestamp()
    }, { merge: true }).then(() => {
        console.log('✅ Admin role assigned successfully!');
        location.reload();
    }).catch(error => {
        console.error('❌ Error assigning admin role:', error);
    });
} else {
    console.log('Please log in first');
}
```

## 🌐 **Step 4: Check Network Blocks**

The "ERR_BLOCKED_BY_CLIENT" error suggests:

1. **Disable ad blockers** temporarily (uBlock Origin, AdBlock, etc.)
2. **Disable strict browser privacy settings**
3. **Check if your firewall is blocking Firebase**
4. **Try in incognito/private mode**

## 🔄 **Step 5: Test Again**

1. **Refresh the page**
2. **Go to Account → Premium → Admin Panel**
3. **Try generating a trial key**
4. **Check console for any remaining errors**

## 🆘 **Common Error Fixes**

### "Missing or insufficient permissions"
- ✅ **Solution**: Update Firebase security rules (Step 2)

### "User document does not exist" 
- ✅ **Solution**: Run admin role script (Step 3)

### "ERR_BLOCKED_BY_CLIENT"
- ✅ **Solution**: Disable ad blockers (Step 4)

### "Firebase not initialized"
- ✅ **Solution**: Refresh page and wait for Firebase to load

## 🎯 **Expected Success**

After fixing, you should see:
- ✅ "License Key Manager initialized with Firebase"
- ✅ "User is admin - showing admin tab"
- ✅ Admin Panel tab visible in Premium section
- ✅ Successful license key generation

## 📞 **Still Having Issues?**

Run the debug script again and share the console output. The script will tell you exactly what's wrong and how to fix it!

```javascript
window.debugLicenseSystem()
``` 