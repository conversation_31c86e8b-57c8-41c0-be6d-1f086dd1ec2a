/* ===================================
   Modal System
   =================================== */

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

/* Modal Content */
.modal-content {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-medium {
    max-width: 550px;
}

.modal-large {
    max-width: 700px;
}

.modal-xlarge {
    max-width: 1000px;
    width: 95%;
    height: 85vh;
}

/* Modal Header */
.modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-header p {
    margin: 0.5rem 0 0 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition-fast);
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.modal-close:hover {
    background: var(--background-hover);
    color: var(--text-primary);
}

/* Modal Body */
.modal-body {
    padding: 1.5rem;
}

/* Modal Footer */
.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

/* Modal Sections */
.modal-section {
    margin-bottom: 2rem;
}

.modal-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Quick Access Modal Styles */
.quick-access-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.option-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 180, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.option-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 180, 255, 0.3);
}

.option-card:hover::before {
    left: 100%;
}

.option-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 180, 255, 0.1);
    border-radius: 10px;
    flex-shrink: 0;
}

.option-card:hover .option-icon {
    background: rgba(255, 255, 255, 0.2);
}

.option-content {
    flex: 1;
}

.option-content h5 {
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
    font-weight: 600;
}

.option-content p {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Settings Modal */
.settings-section {
    margin-bottom: 1.5rem;
}

.settings-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.setting-item small {
    display: block;
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.setting-item select {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 150px;
}

.setting-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.toggle-setting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.toggle-setting span {
    font-weight: 500;
    color: var(--text-primary);
}

.custom-url-input {
    margin-top: 0.75rem;
    animation: slideDown 0.3s ease;
}

/* File Browser Modal */
.file-browser-container {
    display: flex;
    flex-direction: column;
    height: 70vh;
}

.breadcrumb {
    margin: 0.5rem 0 0 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.breadcrumb-item {
    color: var(--primary-color);
}

.file-nav {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition-fast);
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.path-input {
    flex: 1;
}

.path-input input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.file-browser-layout {
    display: flex;
    flex: 1;
    min-height: 0;
}

.file-sidebar {
    width: 220px;
    background: var(--background-hover);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section h5 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.85rem;
    color: var(--text-primary);
    transition: var(--transition-fast);
    margin-bottom: 0.25rem;
}

.sidebar-item:hover {
    background: rgba(0, 180, 255, 0.1);
    color: var(--primary-color);
}

.sidebar-icon {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.file-main {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.file-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
}

.file-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0.75rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
}

.file-item:hover {
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.file-item.selected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.file-item-icon {
    font-size: 2rem;
    margin-bottom: 0.25rem;
}

.file-item-name {
    font-size: 0.8rem;
    font-weight: 500;
    word-break: break-word;
    line-height: 1.2;
}

.file-browser-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0 0 0;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.selected-file {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 0.75rem;
}
