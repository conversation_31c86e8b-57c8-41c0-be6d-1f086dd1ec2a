<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Armory X - Login</title>
    <link rel="stylesheet" href="splash.css">
    <link rel="icon" href="assets/Armory_X.ico" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <style>
        /* Ensure clean transparent window */
        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: transparent !important;
            -webkit-app-region: no-drag;
        }
        
        /* Smooth fade-in for splash screen */
        body {
            opacity: 0;
            animation: splashFadeIn 0.3s ease-out forwards;
        }
        
        @keyframes splashFadeIn {
            to {
                opacity: 1;
            }
        }
    </style>
    
    <!-- Animated Background - Hidden for clean look -->
    <!-- <div class="bg-container">
        <div class="bg-gradient"></div>
        <div class="bg-particles"></div>
        <div class="bg-grid"></div>
        <div class="bg-orbs">
            <div class="orb orb-1"></div>
            <div class="orb orb-2"></div>
            <div class="orb orb-3"></div>
        </div>
    </div> -->

    <!-- Main Login Panel -->
    <div class="login-panel">
        <!-- Custom Title Bar -->
        <div class="title-bar">
            <div class="title-bar-left">
                <img src="assets/Armory_X.ico" alt="Armory X" class="title-icon">
                <span class="title-text">Armory X</span>
            </div>
            <div class="title-bar-controls">
                <button class="title-btn minimize-btn" onclick="minimizeWindow()" title="Minimize">
                    <span>−</span>
                </button>
                <button class="title-btn close-btn" onclick="closeWindow()" title="Close">
                    <span>×</span>
                </button>
            </div>
        </div>

        <!-- Panel Content -->
        <div class="panel-content">
            <!-- Header Section -->
            <div class="panel-header">
                <div class="logo-container">
                    <img src="assets/Armory_X.ico" alt="Armory X" class="logo-icon">
                    <div class="logo-glow"></div>
                </div>
                <h1 class="app-title">Armory X</h1>
                <p class="app-subtitle">Professional Desktop Experience</p>
                <div class="version-info">
                    <span class="version-badge">v1.0.0</span>
                    <span class="status-badge" id="connection-status">Connected</span>
                </div>
            </div>

            <!-- Login Section -->
            <div class="login-section">
                <div class="login-header">
                    <h2>Welcome Back</h2>
                    <p>Please sign in to continue to Armory X</p>
                </div>

                <form class="login-form" id="login-form">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <div class="input-container">
                            <span class="input-icon">👤</span>
                            <input type="text" id="email" name="email" placeholder="Enter your email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-container">
                            <span class="input-icon">🔐</span>
                            <input type="password" id="password" name="password" placeholder="Enter your password" required>
                            <button type="button" class="toggle-password" id="toggle-password">
                                <span class="toggle-icon">👁️</span>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="remember-me">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="submit" class="login-btn" id="login-btn">
                        <span class="btn-text">Sign In</span>
                        <span class="btn-loader" id="btn-loader">
                            <div class="spinner"></div>
                        </span>
                    </button>

                    <div class="error-message" id="error-message"></div>
                    <div class="success-message" id="success-message"></div>
                </form>

                <div class="login-footer">
                    <p>Don't have an account? <a href="#" class="register-link">Create one</a></p>
                </div>
            </div>

            <!-- Registration Section -->
            <div class="register-section" style="display: none;">
                <div class="login-header">
                    <h2>Create Account</h2>
                    <p>Create a new Armory X account</p>
                </div>

                <form class="login-form" id="register-form">
                    <div class="form-group">
                        <label for="reg-email">Email</label>
                        <div class="input-container">
                            <span class="input-icon">👤</span>
                            <input type="email" id="reg-email" name="reg-email" placeholder="Enter your email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reg-password">Password</label>
                        <div class="input-container">
                            <span class="input-icon">🔐</span>
                            <input type="password" id="reg-password" name="reg-password" placeholder="Create password" required>
                            <button type="button" class="toggle-password" id="toggle-reg-password">
                                <span class="toggle-icon">👁️</span>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reg-confirm">Confirm Password</label>
                        <div class="input-container">
                            <span class="input-icon">🔐</span>
                            <input type="password" id="reg-confirm" name="reg-confirm" placeholder="Confirm password" required>
                        </div>
                    </div>

                    <button type="submit" class="login-btn" id="register-btn">
                        <span class="btn-text">Create Account</span>
                        <span class="btn-loader" id="reg-btn-loader">
                            <div class="spinner"></div>
                        </span>
                    </button>

                    <div class="error-message" id="reg-error-message"></div>
                    <div class="success-message" id="reg-success-message"></div>
                </form>

                <div class="login-footer">
                    <p>Already have an account? <a href="#" class="login-link">Sign in</a></p>
                </div>
            </div>

            <!-- Email Verification Waiting Section -->
            <div class="verification-section" style="display: none;">
                <div class="verification-header">
                    <div class="verification-icon">
                        <span>📧</span>
                    </div>
                    <h2>Verify Your Email</h2>
                    <p>We've sent a verification email to:</p>
                    <p class="verification-email" id="verification-email"></p>
                </div>

                <div class="verification-content">
                    <div class="verification-status">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                        </div>
                        <p>Waiting for email verification...</p>
                    </div>

                    <div class="verification-info">
                        <p>Please check your inbox and click the verification link to activate your account.</p>
                        <p class="verification-note">This may take a few minutes. Check your spam folder if you don't see it.</p>
                    </div>

                    <div class="verification-actions">
                        <button type="button" class="resend-btn" id="resend-verification-btn">
                            <span class="btn-text">Resend Verification Email</span>
                            <span class="resend-timer" id="resend-timer" style="display: none;"></span>
                        </button>
                        <p class="resend-attempts" id="resend-attempts">Attempts remaining: 3</p>
                    </div>

                    <div class="verification-footer">
                        <p>Change email? <a href="#" class="back-to-register">Create a new account</a></p>
                        <p>Already verified? <a href="#" class="back-to-login">Sign in</a></p>
                    </div>
                </div>
            </div>



            <!-- Footer -->
            <div class="panel-footer">
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Support</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Screen (Discord-style) - Outside login panel for proper layering -->
    <div class="loading-screen" id="loading-screen" style="display: none;">
        <!-- Custom Title Bar for loading screen -->
        <div class="title-bar">
            <div class="title-bar-left">
                <img src="assets/Armory_X.ico" alt="Armory X" class="title-icon">
                <span class="title-text">Armory X</span>
            </div>
            <div class="title-bar-controls">
                <button class="title-btn minimize-btn" onclick="minimizeWindow()" title="Minimize">
                    <span>−</span>
                </button>
                <button class="title-btn close-btn" onclick="closeWindow()" title="Close">
                    <span>×</span>
                </button>
            </div>
        </div>
        
        <div class="loading-backdrop"></div>
        <div class="loading-content">
            <div class="loading-logo-container">
                <div class="loading-logo">
                    <img src="assets/Armory_X.ico" alt="Armory X" class="loading-logo-img">
                    <div class="loading-logo-ring"></div>
                    <div class="loading-logo-glow"></div>
                </div>
            </div>
            <h1 class="loading-title">Armory X</h1>
            <div class="loading-progress">
                <div class="loading-progress-bar">
                    <div class="loading-progress-fill" id="loading-progress-fill"></div>
                </div>
                <p class="loading-status" id="loading-status">Initializing...</p>
            </div>
            <div class="loading-tip">
                <p id="loading-tip-text">Did you know? Armory X provides the ultimate PC optimization experience.</p>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Firebase Configuration -->
    <script type="module">
        (async function() {
            try {
                // Prevent infinite refresh loops
                if (window.firebaseInitAttempted) {
                    console.log('🛑 Firebase initialization already attempted');
                    return;
                }
                window.firebaseInitAttempted = true;
                
                console.log('🔧 Starting Firebase initialization...');
                
                // Firebase configuration - REPLACE WITH YOUR OWN FIREBASE CONFIG
                const firebaseConfig = {
                    apiKey: "AIzaSyAto6FZKgKctMQCtD-wrP8GxvXXEsQ4Ads",
                    authDomain: "armoryx-website.firebaseapp.com",
                    projectId: "armoryx-website",
                    storageBucket: "armoryx-website.firebasestorage.app",
                    messagingSenderId: "671977508420",
                    appId: "1:671977508420:web:34b1978ad9011d3d5c3611"
                };

                console.log('📋 Firebase config:', {
                    projectId: firebaseConfig.projectId,
                    authDomain: firebaseConfig.authDomain
                });

                // Check if Firebase config is properly set up
                const isFirebaseConfigured = firebaseConfig.apiKey !== "YOUR_API_KEY_HERE" && 
                                            firebaseConfig.projectId !== "YOUR_PROJECT_ID";

                console.log('🔍 Firebase configured:', isFirebaseConfigured);

                if (isFirebaseConfigured) {
                    console.log('📦 Loading Firebase modules...');
                    
                    // Test network connectivity first
                    console.log('🌐 Testing network connectivity to Firebase...');
                    try {
                        const connectivityTest = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', { method: 'HEAD' });
                        console.log('✅ Network connectivity to Firebase CDN confirmed');
                    } catch (error) {
                        console.warn('⚠️ Network connectivity issue detected:', error.message);
                    }
                    
                    // Import Firebase modules only if configured
                    const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                    console.log('✅ Firebase app module loaded');
                    
                    const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged, sendPasswordResetEmail, sendEmailVerification } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                    console.log('✅ Firebase auth module loaded');
                    
                    const { getFirestore, doc, setDoc, getDoc, getDocs, collection, addDoc, deleteDoc, query, where, orderBy, limit, onSnapshot, serverTimestamp, updateDoc, increment, arrayUnion, arrayRemove } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                    console.log('✅ Firebase firestore module loaded');

                    // Initialize Firebase
                    console.log('🔥 Initializing Firebase app...');
                    const app = initializeApp(firebaseConfig);
                    console.log('✅ Firebase app initialized');
                    
                    console.log('🔐 Initializing Firebase auth...');
                    const auth = getAuth(app);
                    console.log('✅ Firebase auth initialized');
                    
                    console.log('🗄️ Initializing Firestore...');
                    const db = getFirestore(app);
                    console.log('✅ Firestore initialized');

                    // Test Firebase connection
                    console.log('🔗 Testing Firebase Auth connection...');
                    try {
                        // Test auth connection by checking current user (won't fail if not logged in)
                        const currentUser = auth.currentUser;
                        console.log('✅ Firebase Auth connection successful');
                    } catch (error) {
                        console.warn('⚠️ Firebase Auth connection test failed:', error);
                    }

                    console.log('🔗 Testing Firestore connection...');
                    try {
                        // Test Firestore connection (will help identify permission issues)
                        const testRef = doc(db, 'test', 'connection');
                        console.log('✅ Firestore connection test ready (permissions will be tested on first operation)');
                    } catch (error) {
                        console.warn('⚠️ Firestore connection test failed:', error);
                    }

                    // Make Firebase available globally
                    window.firebase = { 
                        auth, 
                        db, 
                        createUserWithEmailAndPassword, 
                        signInWithEmailAndPassword, 
                        signOut, 
                        onAuthStateChanged, 
                        sendPasswordResetEmail,
                        sendEmailVerification,
                        doc, 
                        setDoc, 
                        getDoc, 
                        getDocs,
                        collection, 
                        addDoc, 
                        deleteDoc,
                        query, 
                        where,
                        orderBy, 
                        limit, 
                        onSnapshot, 
                        serverTimestamp, 
                        updateDoc, 
                        increment,
                        arrayUnion,
                        arrayRemove
                    };
                    
                    console.log('✅ Firebase initialized successfully - all services ready');
                    console.log('🔥 Firebase project:', firebaseConfig.projectId);
                    console.log('🔥 Auth domain:', firebaseConfig.authDomain);
                    
                    // Signal successful Firebase initialization immediately
                    window.firebaseInitialized = true;
                    console.log('🏁 Firebase initialization SUCCESS - services ready for use');
                } else {
                    console.log('🔧 Firebase not configured - running in local development mode');
                    window.firebase = null;
                    // Signal that Firebase initialization is complete (but not configured)
                    window.firebaseInitialized = true;
                }
            } catch (error) {
                console.error('❌ Firebase initialization failed:', error);
                console.error('Error details:', error.message);
                console.error('Error stack:', error.stack);
                window.firebase = null;
                // Signal that Firebase initialization is complete (but failed)
                window.firebaseInitialized = true;
            }
            
            console.log('🏁 Firebase initialization process complete');
        })();
    </script>

    <script src="splash.js"></script>
</body>
</html> 