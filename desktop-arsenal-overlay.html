<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop Arsenal</title>
    <style>
        /* Import website theme styles */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        

        
        /* Overlay Opening/Closing Animations */
        @keyframes overlayFadeIn {
            from {
                opacity: 0;
                transform: scale(0.98) translateY(10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        @keyframes overlayFadeOut {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.98) translateY(10px);
            }
        }
        

        

        
        :root {
            --primary-color: #00b4ff;
            --primary-hover: #0099e6;
            --secondary-color: #1a1a1a;
            --background-dark: #0a0a0a;
            --background-card: #111111;
            --background-hover: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            --border-color: #333333;
            --success-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --border-radius: 12px;
            --transition-smooth: 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: transparent;
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 0;
            user-select: none;
            animation: overlayFadeIn 0.4s ease-out forwards;
        }
        
        body.closing {
            animation: overlayFadeOut 0.3s ease-out forwards;
        }
        
        /* Main Layout - Full Screen */
        .overlay-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            position: relative;
            /* Dynamic background will be set via JavaScript */
            transition: background-color 0.3s ease;
        }
        
        /* Background layer for images and animations */
        .background-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0; /* Changed from -2 to 0 to ensure it's visible but behind content */
            pointer-events: none;
            opacity: 1; /* Default opacity */
            transition: opacity 0.3s ease; /* Smooth opacity transitions */
        }
        
        .background-layer.custom-image {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: none; /* Prevent flashing during updates */
        }
        
        .background-layer video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translate(-50%, -50%);
            object-fit: cover;
            opacity: 1; /* Default opacity for videos */
        }

        /* Top Header Bar */
        .overlay-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background: rgba(0, 0, 0, 0.8);
            border-bottom: 1px solid rgba(51, 51, 51, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
            animation: headerSlideDown 0.4s ease-out 0.1s backwards;
            position: relative; /* Ensure header is above background */
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .arsenal-logo {
            font-size: 28px;
            filter: drop-shadow(0 0 10px rgba(0, 180, 255, 0.5));
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), #0099e6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 180, 255, 0.3);
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
        }
        
        /* Close Button - Top Right */
        .overlay-close {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 68, 68, 0.9);
            color: white;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
        }

        .overlay-close:hover {
            background: #cc0000;
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(255, 68, 68, 0.5);
        }

        /* Content Area */
        .overlay-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0 40px 40px 40px;
            overflow: hidden;
            animation: contentFadeIn 0.5s ease-out 0.2s backwards;
            position: relative; /* Ensure content is above background */
            z-index: 1;
        }
        
        /* Search and Filter Bar */
        .search-filter-bar {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
            align-items: center;
            padding: 20px 0;
        }
        
        .search-container {
            flex: 1;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 16px 20px 16px 54px;
            background: rgba(17, 17, 17, 0.9);
            border: 2px solid transparent;
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 16px;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 180, 255, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 18px;
            color: var(--text-muted);
        }
        
        .filter-select {
            padding: 16px 20px;
            background: rgba(17, 17, 17, 0.9);
            border: 2px solid transparent;
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 16px;
            min-width: 150px;
            cursor: pointer;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 180, 255, 0.1);
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 12px;
            background: rgba(17, 17, 17, 0.9);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }
        
        .btn:hover {
            background: var(--background-hover);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
                 .btn-primary:hover {
             background: var(--primary-hover);
         }
         
         .btn-secondary {
             background: var(--background-card);
             border: 2px solid var(--border-color);
         }
         
         .btn-secondary:hover {
             background: var(--background-hover);
             border-color: var(--primary-color);
         }
         
         .btn-sm {
             padding: 8px 16px;
             font-size: 14px;
         }
        
        .btn-sm:hover {
            background: var(--background-hover);
            transform: translateY(-1px);
        }
        
        /* Dropdown Menu */
        .dropdown {
            position: relative;
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 200px;
            background: rgba(17, 17, 17, 0.98);
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            z-index: 1000;
            margin-top: 8px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .dropdown-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-primary);
        }
        
        .dropdown-item:hover {
            background: rgba(0, 180, 255, 0.2);
            color: var(--primary-color);
        }
        
        .dropdown-item span {
            font-size: 20px;
        }
        
        /* Stats Bar */
        .stats-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: rgba(17, 17, 17, 0.6);
            border-radius: 12px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .stats-info {
            color: var(--text-secondary);
            font-size: 16px;
            font-weight: 500;
        }
        
        .view-mode-buttons {
            display: flex;
            gap: 8px;
        }
        
        .view-btn {
            padding: 10px 16px;
            background: transparent;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 16px;
        }
        
        .view-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .view-btn:hover:not(.active) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        /* File Grid - Full Screen */
        .files-container {
            flex: 1;
            overflow-y: auto;
            padding-right: 8px;
            background: transparent;
        }
        
        .files-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 20px;
            padding: 10px;
        }
        
        .file-card {
            background: rgba(17, 17, 17, 0.8);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .file-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 180, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity var(--transition-smooth);
        }
        
        .file-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 180, 255, 0.2);
        }
        
        .file-card:hover::before {
            opacity: 1;
        }
        
        .file-icon {
            width: 56px;
            height: 56px;
            margin-bottom: 16px;
            font-size: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }
        
        .file-name {
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--text-primary);
            font-size: 16px;
            position: relative;
            z-index: 2;
        }
        
        .file-details {
            color: var(--text-secondary);
            font-size: 14px;
            position: relative;
            z-index: 2;
        }
        
        .file-actions {
            position: absolute;
            top: 12px;
            right: 12px;
            opacity: 0;
            transition: opacity var(--transition-smooth);
        }
        
        .file-card:hover .file-actions {
            opacity: 1;
        }
        
        .file-menu-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition-smooth);
        }

        .file-menu-btn:hover {
            background: rgba(0, 0, 0, 0.9);
        }
        
        /* List View */
        .files-list {
            display: none !important;
        }
        
        .files-list.active {
            display: block !important;
        }
        
        /* Grid View */
        .files-grid {
            display: none !important;
        }
        
        .files-grid.active {
            display: grid !important;
        }
        
        .list-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            background: rgba(17, 17, 17, 0.8);
            border: 2px solid transparent;
            border-radius: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }
        
        .list-item:hover {
            border-color: var(--primary-color);
            background: rgba(26, 26, 26, 0.8);
        }
        
        .list-item.selected {
            border-color: var(--primary-color);
            background: rgba(0, 180, 255, 0.15);
        }
        
        .list-icon {
            width: 40px;
            height: 40px;
            margin-right: 16px;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .list-info {
            flex: 1;
        }
        
        .list-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            font-size: 16px;
        }
        
        .list-details {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* ESC Key Hint */
        .esc-hint {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 14px;
            color: var(--text-secondary);
            opacity: 0.8;
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
        }
        
        .control-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 8px;
            background: rgba(26, 26, 26, 0.9);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-smooth);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }
        
        .control-btn:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .control-btn.close:hover {
            background: var(--error-color);
            color: white;
        }

        /* Multi-select styling */
        .file-card.selected {
            border-color: var(--primary-color);
            background: rgba(0, 180, 255, 0.15);
        }

        .file-card.selected::before {
            opacity: 1;
        }

        .selection-checkbox {
            position: absolute;
            top: 12px;
            left: 12px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .file-card:hover .selection-checkbox,
        .file-card.selected .selection-checkbox {
            opacity: 1;
        }

        .file-card.selected .selection-checkbox {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* Bulk Actions Bar */
        .bulk-actions-bar {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(17, 17, 17, 0.95);
            border: 2px solid var(--primary-color);
            border-radius: 16px;
            padding: 16px 24px;
            display: none;
            align-items: center;
            gap: 20px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            z-index: 1500;
            backdrop-filter: blur(15px);
        }

        .bulk-actions-bar.active {
            display: flex;
        }

        .bulk-count {
            color: var(--text-secondary);
            font-size: 16px;
            font-weight: 500;
        }

        .bulk-actions {
            display: flex;
            gap: 10px;
        }

        /* Empty State */
        .empty-state {
            display: none !important;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-secondary);
            text-align: center;
        }
        
        .empty-state.active {
            display: flex !important;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 24px;
            font-size: 64px;
            opacity: 0.6;
        }
        
        .empty-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .empty-subtitle {
            font-size: 16px;
            text-align: center;
            max-width: 500px;
            line-height: 1.6;
        }

        /* Modal Updates for Full Screen Design */
        .settings-modal, .file-edit-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(20px);
        }
        
        .settings-modal.active, .file-edit-modal.active {
            display: flex;
        }
        
        .settings-content, .file-edit-content {
            background: rgba(17, 17, 17, 0.95);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            width: 90%;
            max-width: 700px;
            max-height: 85vh;
            overflow-y: auto;
            backdrop-filter: blur(15px);
        }
        
        .settings-header, .file-edit-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .settings-title, .file-edit-title {
            font-size: 22px;
            font-weight: 600;
        }
        
        .settings-body, .file-edit-body {
            padding: 24px;
        }
        
        .setting-group, .edit-group {
            margin-bottom: 24px;
        }
        
        .setting-label, .edit-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 16px;
        }
        
        .setting-input, .edit-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(10, 10, 10, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 16px;
            transition: var(--transition-smooth);
        }
        
        .setting-input:focus, .edit-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 180, 255, 0.1);
        }

        .edit-textarea {
            width: 100%;
            min-height: 100px;
            resize: vertical;
            padding: 12px 16px;
            background: rgba(10, 10, 10, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            font-family: inherit;
            font-size: 16px;
            transition: var(--transition-smooth);
        }

        .tags-input {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            min-height: 48px;
            padding: 8px 12px;
            background: rgba(10, 10, 10, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            cursor: text;
            transition: var(--transition-smooth);
        }

        .tags-input:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 180, 255, 0.1);
        }

        .tag-item {
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 14px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tag-remove {
            cursor: pointer;
            font-weight: bold;
            font-size: 12px;
        }

        .icon-preview {
            width: 80px;
            height: 80px;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.3s ease;
            font-size: 14px;
        }

        .icon-preview:hover {
            border-color: var(--primary-color);
        }

        .icon-preview img {
            width: 64px;
            height: 64px;
            object-fit: cover;
            border-radius: 8px;
        }

        /* Hotkey Input Styles */
        .hotkey-input-container {
            position: relative;
        }
        
        .hotkey-input {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .hotkey-input:focus {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .hotkey-input.capturing {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 180, 255, 0.3);
        }
        
        .hotkey-hint {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(10, 10, 10, 0.95);
            border: 2px solid var(--primary-color);
            border-top: none;
            border-radius: 0 0 12px 12px;
            padding: 12px 16px;
            font-size: 14px;
            color: var(--primary-color);
            z-index: 1000;
            animation: slideDown 0.2s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(10, 10, 10, 0.5);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        @keyframes headerSlideDown {
            from {
                opacity: 0;
                transform: translateY(-100%);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes contentFadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
            
        /* Background Library Modal Styles */
        .background-library-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(20px);
        }
        
        .background-library-modal.active {
            display: flex;
        }
        
        .background-library-content {
            background: rgba(17, 17, 17, 0.95);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            overflow: hidden;
            backdrop-filter: blur(15px);
            display: flex;
            flex-direction: column;
        }
        
        .background-library-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .background-library-title {
            font-size: 22px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .background-library-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .background-categories {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
        }
        
        .category-btn {
            padding: 12px 24px;
            background: rgba(10, 10, 10, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition-smooth);
        }
        
        .category-btn:hover {
            border-color: var(--primary-color);
            background: rgba(0, 180, 255, 0.1);
        }
        
        .category-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .background-grid-container {
            position: relative;
            min-height: 400px;
        }
        
        .background-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            padding: 0;
        }
        
        .background-card {
            background: rgba(10, 10, 10, 0.8);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            overflow: hidden;
            cursor: pointer;
            transition: var(--transition-smooth);
            position: relative;
        }
        
        .background-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
        }
        
        .background-card.selected {
            border-color: var(--primary-color);
            background: rgba(0, 180, 255, 0.1);
        }
        
        .background-card-preview {
            height: 180px;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .background-card-info {
            padding: 16px;
        }
        
        .background-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .background-card-description {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        /* None option styles */
        .none-preview {
            background: linear-gradient(45deg, #1a1a1a 25%, transparent 25%), 
                        linear-gradient(-45deg, #1a1a1a 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #1a1a1a 75%), 
                        linear-gradient(-45deg, transparent 75%, #1a1a1a 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            flex-direction: column;
        }
        
        .none-icon {
            font-size: 48px;
            margin-bottom: 8px;
            opacity: 0.7;
        }
        
        .none-text {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* Custom upload styles */
        .custom-preview {
            background: linear-gradient(135deg, rgba(0, 180, 255, 0.1) 0%, rgba(0, 180, 255, 0.05) 100%);
            border: 2px dashed rgba(0, 180, 255, 0.3);
            margin: 12px;
            border-radius: 12px;
            flex-direction: column;
            height: calc(100% - 24px);
        }
        
        .upload-icon {
            font-size: 48px;
            margin-bottom: 8px;
            opacity: 0.7;
        }
        
        .upload-text {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .custom-upload:hover .custom-preview {
            border-color: var(--primary-color);
            background: rgba(0, 180, 255, 0.15);
        }
        
        /* Video preview styles */
        .video-preview {
            position: relative;
            background: #000;
        }
        
        .video-preview video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.8;
        }
        
        .video-preview .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .background-card:hover .play-overlay {
            opacity: 1;
        }
        

        
        .background-library-footer {
            padding: 24px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* Scrollbar styles for background library */
        .background-library-body::-webkit-scrollbar {
            width: 8px;
        }
        
        .background-library-body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .background-library-body::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }
        
        .background-library-body::-webkit-scrollbar-thumb:hover {
            background: #0099e6;
        }
        
        </style>
</head>
<body>
    <div class="overlay-container">
        <!-- Background Layer -->
        <div class="background-layer" id="backgroundLayer"></div>
        
        <!-- Header -->
        <div class="overlay-header">
            <div class="header-left">
                <div class="arsenal-logo">🛡️</div>
                <h1 class="header-title">Desktop Arsenal</h1>
            </div>
            <div class="header-actions">
                <button class="btn" onclick="openSettings()">
                    <span>⚙️</span>
                    Settings
                </button>
                <div class="dropdown" style="display: inline-block;">
                    <button class="btn btn-primary" onclick="toggleAddMenu(event)">
                        <span>➕</span>
                        Add Items
                        <span style="font-size: 10px; margin-left: 4px;">▼</span>
                    </button>
                    <div class="dropdown-menu" id="addMenu" style="display: none;">
                        <div class="dropdown-item" onclick="addFiles('files')">
                            <span>📄</span> Add Files/Shortcuts
                        </div>
                        <div class="dropdown-item" onclick="addFiles('folders')">
                            <span>📁</span> Add Folders
                            <small style="display: block; color: #666; font-size: 11px; margin-top: 2px; padding-left: 20px;">
                                💡 Hold Ctrl to select multiple folders
                            </small>
                        </div>
                    </div>
                </div>
                <button class="overlay-close" onclick="closeOverlay()">✕</button>
            </div>
        </div>
        
        <!-- Content -->
        <div class="overlay-content">
            <!-- Search and Filter Bar -->
            <div class="search-filter-bar">
                <div class="search-container">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="Search files..." id="searchInput">
                </div>
                <select class="filter-select" id="categoryFilter">
                    <option value="">All Categories</option>
                    <option value="documents">Documents</option>
                    <option value="images">Images</option>
                    <option value="videos">Videos</option>
                    <option value="audio">Audio</option>
                    <option value="archives">Archives</option>
                    <option value="executables">Programs</option>
                    <option value="other">Other</option>
                </select>
                <select class="filter-select" id="sortBy">
                    <option value="name">Sort by Name</option>
                    <option value="date">Sort by Date</option>
                    <option value="size">Sort by Size</option>
                    <option value="type">Sort by Type</option>
                </select>
            </div>
            
            <!-- Stats Bar -->
            <div class="stats-bar">
                <div class="stats-info" id="statsInfo">
                    0 files organized
                </div>
                <div class="view-mode-buttons">
                    <button class="view-btn active" onclick="setViewMode('grid')" id="gridBtn">
                        <span>⊞</span>
                    </button>
                    <button class="view-btn" onclick="setViewMode('list')" id="listBtn">
                        <span>≡</span>
                    </button>
                </div>
            </div>
            
            <!-- Files Container -->
            <div class="files-container">
                <!-- Grid View -->
                <div class="files-grid active" id="filesGrid">
                    <!-- Files will be populated here -->
                </div>
                
                <!-- List View -->
                <div class="files-list" id="filesList">
                    <!-- Files will be populated here -->
                </div>
                
                <!-- Empty State -->
                <div class="empty-state active" id="emptyState">
                    <div class="empty-icon">📁</div>
                    <div class="empty-title">No files organized yet</div>
                    <div class="empty-subtitle">
                        Start organizing your desktop by adding files to your Arsenal. 
                        Click "Add Files" to get started.
                    </div>
                </div>
            </div>
        </div>
        
                 <!-- ESC Key Hint -->
         <div class="esc-hint">
             Press ESC or Ctrl+Space to close
         </div>
     </div>

     <!-- Settings Modal -->
     <div class="settings-modal" id="settingsModal">
         <div class="settings-content">
             <div class="settings-header">
                 <h2 class="settings-title">Desktop Arsenal Settings</h2>
                 <button class="control-btn close" onclick="closeSettings()">✕</button>
             </div>
             <div class="settings-body">
                 <div class="setting-group">
                     <label class="setting-label">Hotkey</label>
                     <div class="hotkey-input-container">
                         <input type="text" 
                                class="setting-input hotkey-input" 
                                id="hotkeyInput" 
                                placeholder="Ctrl+Space"
                                readonly>
                         <div class="hotkey-hint" id="hotkeyHint" style="display: none;">
                             Press desired keys + Enter to save, ESC to cancel
                         </div>
                     </div>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Default View Mode</label>
                     <select class="setting-input" id="defaultViewMode">
                         <option value="grid">Grid View</option>
                         <option value="list">List View</option>
                     </select>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">File Monitoring</label>
                     <select class="setting-input" id="fileMonitoring">
                         <option value="disabled">Disabled</option>
                         <option value="enabled">Enabled</option>
                     </select>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Auto-Close Overlay</label>
                     <select class="setting-input" id="autoCloseOverlay">
                         <option value="enabled">Enabled</option>
                         <option value="disabled">Disabled</option>
                     </select>
                     <small style="display: block; margin-top: 4px; color: #666;">
                         Automatically close the overlay when opening files, folders, or shortcuts
                     </small>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Prevent Fullscreen Opening</label>
                     <select class="setting-input" id="preventFullscreen">
                         <option value="enabled">Enabled</option>
                         <option value="disabled">Disabled</option>
                     </select>
                     <small style="display: block; margin-top: 4px; color: #666;">
                         Prevent overlay from opening when fullscreen applications (like games) are running
                     </small>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Background Storage</label>
                     <div style="display: flex; align-items: center; gap: 12px;">
                         <span style="flex: 1; color: #666; font-size: 14px;">
                             Clean up unused background files to free disk space
                         </span>
                         <button class="btn btn-secondary" onclick="cleanupBackgrounds()" style="white-space: nowrap;">
                             🧹 Clean Up
                         </button>
                     </div>
                     <small style="display: block; margin-top: 4px; color: #666;">
                         Automatically removes background files older than 7 days (except current one)
                     </small>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Background</label>
                     <div style="display: flex; align-items: center; gap: 12px;">
                         <div id="currentBackgroundDisplay" style="flex: 1; padding: 8px 12px; background: rgba(0, 180, 255, 0.1); border: 1px solid rgba(0, 180, 255, 0.3); border-radius: 8px; font-size: 14px; color: #00b4ff;">
                             None Selected
                         </div>
                         <button class="btn btn-primary" onclick="openBackgroundLibrary()" style="white-space: nowrap;">
                             Browse Library
                         </button>
                     </div>
                     
                     <!-- Hidden input for custom background upload -->
                     <input type="file" class="setting-input" id="backgroundImage" accept="image/*,image/gif,video/*" style="display: none;">
                     
                     <!-- Hidden elements to maintain compatibility -->
                     <select style="display: none;" id="backgroundType">
                         <option value="none">None</option>
                         <option value="custom">Custom Image</option>
                     </select>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Window Transparency</label>
                     <small style="display: block; margin-bottom: 8px; color: #666;">
                         Controls how see-through the window is to your desktop<br>
                         0% = Solid window (no transparency) | 100% = Fully transparent<br>
                         <span style="color: #999; font-size: 12px;">Custom images/GIFs fade together with the window transparency</span>
                     </small>
                     <div style="display: flex; align-items: center; gap: 10px;">
                         <input type="range" class="setting-input" id="backgroundOpacity" min="0" max="100" value="50" style="flex: 1;">
                         <span id="opacityValue" style="min-width: 40px; text-align: right;">50%</span>
                     </div>
                 </div>

                 <div class="setting-group">
                     <label class="setting-label">Auto-Start Monitoring</label>
                     <select class="setting-input" id="autoStartMonitoring">
                         <option value="disabled">Disabled</option>
                         <option value="enabled">Enabled</option>
                     </select>
                 </div>
                 
                 <div class="setting-group">
                     <label class="setting-label">Start with Windows</label>
                     <select class="setting-input" id="startWithWindows">
                         <option value="disabled">Disabled</option>
                         <option value="enabled">Enabled</option>
                     </select>
                     <small style="display: block; margin-top: 5px; color: #666;">
                         Launch Armory X automatically when Windows starts
                     </small>
                 </div>

                 <div class="setting-group">
                     <button class="btn btn-primary" onclick="saveSettings()">Save Settings</button>
                     <button class="btn btn-secondary" onclick="exportSettings()" style="margin-left: 8px;">Export Settings</button>
                 </div>

                 <div class="setting-group">
                     <label class="setting-label">Import Settings</label>
                     <input type="file" class="setting-input" id="importFile" accept=".json" onchange="importSettings()">
                 </div>
             </div>
         </div>
     </div>

     <!-- Background Library Modal -->
     <div class="background-library-modal" id="backgroundLibraryModal">
         <div class="background-library-content">
             <div class="background-library-header">
                 <h2 class="background-library-title">Background Library</h2>
                 <button class="control-btn close" onclick="closeBackgroundLibrary()">✕</button>
             </div>
             <div class="background-library-body">
                 <div class="background-categories">
                     <button class="category-btn active" data-category="preset" onclick="switchBackgroundCategory('preset')">
                         Preset Backgrounds
                     </button>
                     <button class="category-btn" data-category="custom" onclick="switchBackgroundCategory('custom')">
                         Custom Background
                     </button>
                 </div>
                 
                 <div class="background-grid-container">
                     <!-- Preset Backgrounds Grid -->
                     <div class="background-grid" id="presetBackgroundGrid">
                         <div class="background-card none-option" data-type="none" onclick="selectBackground('none', 'None')">
                             <div class="background-card-preview none-preview">
                                 <div class="none-icon">🚫</div>
                                 <div class="none-text">No Background</div>
                             </div>
                             <div class="background-card-info">
                                 <div class="background-card-title">None</div>
                                 <div class="background-card-description">Disable background</div>
                             </div>
                         </div>
                         
                         <!-- Video Backgrounds will be dynamically inserted here -->
                     </div>
                     
                     <!-- Custom Background Section -->
                     <div class="background-grid custom-grid" id="customBackgroundGrid" style="display: none;">
                         <div class="background-card custom-upload" onclick="document.getElementById('backgroundImage').click()">
                             <div class="background-card-preview custom-preview">
                                 <div class="upload-icon">📁</div>
                                 <div class="upload-text">Upload Custom</div>
                             </div>
                             <div class="background-card-info">
                                 <div class="background-card-title">Custom Background</div>
                                 <div class="background-card-description">Upload your own image/video</div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
             
             <div class="background-library-footer">
                 <button class="btn btn-secondary" onclick="closeBackgroundLibrary()">Cancel</button>
                 <button class="btn btn-primary" id="applyBackgroundBtn" onclick="applySelectedBackground()" disabled>Apply Background</button>
             </div>
         </div>
     </div>

     <!-- File Edit Modal -->
     <div class="file-edit-modal" id="fileEditModal">
         <div class="file-edit-content">
             <div class="file-edit-header">
                 <h2 class="file-edit-title">Edit File</h2>
                 <button class="control-btn close" onclick="closeFileEdit()">✕</button>
             </div>
             <div class="file-edit-body">
                 <div class="edit-group">
                     <label class="edit-label">Display Name</label>
                     <input type="text" class="edit-input" id="editFileName" placeholder="Enter display name">
                 </div>
                 
                 <div class="edit-group">
                     <label class="edit-label">Description</label>
                     <textarea class="edit-textarea" id="editFileDescription" placeholder="Enter file description"></textarea>
                 </div>
                 
                 <div class="edit-group">
                     <label class="edit-label">Category</label>
                     <select class="edit-input" id="editFileCategory">
                         <option value="documents">Documents</option>
                         <option value="images">Images</option>
                         <option value="videos">Videos</option>
                         <option value="audio">Audio</option>
                         <option value="archives">Archives</option>
                         <option value="executables">Programs</option>
                         <option value="other">Other</option>
                     </select>
                 </div>
                 
                 <div class="edit-group">
                     <label class="edit-label">Tags</label>
                     <div class="tags-input" id="editFileTags" onclick="focusTagInput()">
                         <input type="text" id="tagInput" placeholder="Add tag..." style="border: none; background: transparent; outline: none; color: inherit; flex: 1; min-width: 100px;" onkeydown="handleTagInput(event)">
                     </div>
                 </div>
                 
                 <div class="edit-group">
                     <label class="edit-label">Custom Icon</label>
                     <div class="icon-preview" id="iconPreview" onclick="selectCustomIcon()">
                         <span>Click to select icon</span>
                     </div>
                     <input type="file" id="iconFileInput" accept="image/*" style="display: none;" onchange="handleIconSelect(event)">
                 </div>
                 
                 <div class="edit-group">
                     <button class="btn btn-primary" onclick="saveFileEdit()">Save Changes</button>
                     <button class="btn btn-secondary" onclick="closeFileEdit()" style="margin-left: 8px;">Cancel</button>
                 </div>
             </div>
         </div>
     </div>

     <!-- Bulk Actions Bar -->
     <div class="bulk-actions-bar" id="bulkActionsBar">
         <div class="bulk-count" id="bulkCount">0 files selected</div>
         <div class="bulk-actions">
             <button class="btn btn-sm" onclick="bulkClearSelection()">Clear</button>
             <select class="btn btn-sm" id="bulkCategorySelect" onchange="bulkCategorize()">
                 <option value="">Change Category</option>
                 <option value="documents">Documents</option>
                 <option value="images">Images</option>
                 <option value="videos">Videos</option>
                 <option value="audio">Audio</option>
                 <option value="archives">Archives</option>
                 <option value="executables">Programs</option>
                 <option value="other">Other</option>
             </select>
             <button class="btn btn-sm btn-secondary" onclick="bulkRestore()">Restore</button>
             <button class="btn btn-sm" style="background: #ff4444;" onclick="bulkDelete()">Delete</button>
         </div>
     </div>

     <script>
        const { ipcRenderer } = require('electron');
        
        let currentFiles = [];
        let currentViewMode = 'grid';
        let currentFilters = {
            search: '',
            category: '',
            sortBy: 'name'
        };
        let selectedFiles = new Set();
        let editingFileId = null;
        
        let animationStartTime = null;
        let backgroundMediaElement = null;
        let currentBackgroundType = null;
        let currentOpacity = 95; // Default opacity percentage
        
        // Track active notifications for positioning
        let activeNotifications = [];
        const NOTIFICATION_HEIGHT = 60; // Approximate height including margin
        const NOTIFICATION_MARGIN = 10;
        
        // Initialize overlay
        async function initializeOverlay() {
            try {
                const data = await ipcRenderer.invoke('desktop-arsenal-get-files');
                currentFiles = data.files;
                await loadBackgroundImage();
                // Reset view mode to ensure proper display
                currentViewMode = 'grid';
                document.getElementById('gridBtn').classList.add('active');
                document.getElementById('listBtn').classList.remove('active');
                updateFileDisplay();
                updateStats();
                
                // Start periodic background progress saving for safety
                startPeriodicProgressSaving();
            } catch (error) {
                console.error('Error initializing overlay:', error);
            }
        }
        
        // Periodic progress saving to ensure no progress is lost
        let progressSaveInterval = null;
        
        function startPeriodicProgressSaving() {
            // Save progress every 5 seconds to ensure continuity
            progressSaveInterval = setInterval(() => {
                saveBackgroundProgress();
            }, 5000);
        }
        
        function stopPeriodicProgressSaving() {
            if (progressSaveInterval) {
                clearInterval(progressSaveInterval);
                progressSaveInterval = null;
            }
        }
        
        async function loadBackgroundImage() {
            try {
                const settings = await ipcRenderer.invoke('desktop-arsenal-get-settings');
                if (settings.overlay) {
                    // Get the saved opacity value (default to 50 if not set)
                    const opacity = settings.overlay.backgroundOpacity !== undefined ? settings.overlay.backgroundOpacity : 50;
                    currentOpacity = 100 - opacity; // Invert for background darkness
                    
                    if (settings.overlay.backgroundType && settings.overlay.backgroundType !== 'none') {
                        currentBackgroundType = settings.overlay.backgroundType;
                        
                        // Calculate continuous progress considering time elapsed since last save
                        let restoredProgress = settings.overlay.backgroundProgress || 0;
                        if (settings.overlay.backgroundProgressTimestamp && restoredProgress > 0) {
                            const timeElapsed = (Date.now() - settings.overlay.backgroundProgressTimestamp) / 1000;
                            restoredProgress += timeElapsed; // Continue animation from where it left off
                            console.log(`🔄 Restoring background progress: ${restoredProgress.toFixed(2)}s (saved: ${settings.overlay.backgroundProgress?.toFixed(2)}s + elapsed: ${timeElapsed.toFixed(2)}s)`);
                        }
                        
                        if (settings.overlay.backgroundType === 'custom' && settings.overlay.background) {
                            // Convert file path to file:// URL for loading
                            const fileUrl = `file://${settings.overlay.background.replace(/\\/g, '/')}`;
                            
                            // Check if it's a video file
                            const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov'];
                            const isVideo = videoExtensions.some(ext => settings.overlay.background.toLowerCase().endsWith(ext));
                            
                            if (isVideo) {
                                applyVideoBackground(fileUrl, restoredProgress);
                            } else {
                                applyBackgroundImage(fileUrl);
                                // For GIFs, restore animation progress with continuous timing
                                if (settings.overlay.background.toLowerCase().endsWith('.gif') && restoredProgress > 0) {
                                    restoreGifProgress(restoredProgress);
                                }
                            }
                        } else {
                            // For preset CSS animations, restore progress with continuous timing
                            applyAnimatedBackground(settings.overlay.backgroundType, restoredProgress);
                        }
                    } else {
                        // No background selected, just ensure the transparency is applied
                        removeAnimatedBackground();
                    }
                    
                    // Always apply the background opacity after loading
                    // This ensures custom backgrounds get the correct opacity
                    setBackgroundOpacity(opacity);
                }
            } catch (error) {
                console.error('Error loading background:', error);
            }
        }
        
        function applyVideoBackground(videoUrl, progress = 0) {
            const backgroundLayer = document.getElementById('backgroundLayer');
            removeAnimatedBackground();
            
            // Create video element
            const video = document.createElement('video');
            video.autoplay = true;
            video.loop = true;
            video.muted = true;
            video.src = videoUrl;
            
            // Set initial progress with proper looping support
            video.addEventListener('loadedmetadata', () => {
                if (progress > 0 && video.duration > 0) {
                    // Handle looping videos by using modulo to get position within current loop
                    const adjustedProgress = progress % video.duration;
                    video.currentTime = adjustedProgress;
                    console.log(`📹 Video progress restored: ${adjustedProgress.toFixed(2)}s (duration: ${video.duration.toFixed(2)}s)`);
                }
                // Track animation start time for continuous progress calculation
                animationStartTime = Date.now() - (progress * 1000);
            });
            
            // Track video element
            backgroundMediaElement = video;
            currentBackgroundType = 'custom';
            
            backgroundLayer.appendChild(video);
            backgroundLayer.classList.add('custom-image');
            
            // Apply current opacity setting
            const slider = document.getElementById('backgroundOpacity');
            if (slider && currentBackgroundType === 'custom') {
                setBackgroundOpacity(slider.value);
            } else {
                ensureBackgroundVisibility();
            }
        }
        
        function restoreGifProgress(progress) {
            // Calculate elapsed time since the GIF should have started for continuous timing
            animationStartTime = Date.now() - (progress * 1000);
            
            // Apply animation-delay to sync the GIF with continuous timing
            const backgroundLayer = document.getElementById('backgroundLayer');
            if (backgroundLayer.style.backgroundImage) {
                // Force reload with cache-busting parameter and apply negative delay for continuous animation
                const currentUrl = backgroundLayer.style.backgroundImage;
                backgroundLayer.style.backgroundImage = 'none';
                
                setTimeout(() => {
                    backgroundLayer.style.backgroundImage = currentUrl;
                    // Use negative animation delay to "fast-forward" the GIF to the correct position
                    backgroundLayer.style.animationDelay = `-${progress}s`;
                    console.log(`🖼️ GIF progress restored: ${progress.toFixed(2)}s delay applied`);
                }, 10);
            }
        }
        
        function getCurrentBackgroundProgress() {
            // For video backgrounds
            if (backgroundMediaElement && backgroundMediaElement.tagName === 'VIDEO') {
                return backgroundMediaElement.currentTime;
            }
            
            // For CSS animations (estimate based on time)
            if (animationStartTime && currentBackgroundType && currentBackgroundType !== 'custom') {
                return (Date.now() - animationStartTime) / 1000;
            }
            
            // For GIF backgrounds (estimate based on time)
            if (animationStartTime) {
                return (Date.now() - animationStartTime) / 1000;
            }
            
            return 0;
        }
        
        async function saveBackgroundProgress() {
            try {
                const settings = await ipcRenderer.invoke('desktop-arsenal-get-settings');
                if (settings.overlay && settings.overlay.backgroundType && settings.overlay.backgroundType !== 'none') {
                    const progress = getCurrentBackgroundProgress();
                    if (progress > 0) {
                        // Save background progress for ALL background types (custom + presets)
                        await ipcRenderer.invoke('desktop-arsenal-update-settings', {
                            overlay: {
                                backgroundProgress: progress,
                                backgroundProgressTimestamp: Date.now() // Track when progress was saved
                            }
                        });
                        console.log(`✅ Background progress saved: ${progress.toFixed(2)}s for ${settings.overlay.backgroundType}`);
                    }
                }
            } catch (error) {
                console.error('Error saving background progress:', error);
            }
        }
        
        // Update file display
        function updateFileDisplay() {
            // Use requestAnimationFrame to prevent flashing during updates
            requestAnimationFrame(() => {
                const filteredFiles = filterFiles(currentFiles);
                
                if (filteredFiles.length === 0) {
                    // Show empty state and hide both views
                    document.getElementById('emptyState').classList.add('active');
                    document.getElementById('filesGrid').classList.remove('active');
                    document.getElementById('filesList').classList.remove('active');
                    
                    // Clear content to prevent ghost elements
                    document.getElementById('filesGrid').innerHTML = '';
                    document.getElementById('filesList').innerHTML = '';
                } else {
                    // Hide empty state and show appropriate view
                    document.getElementById('emptyState').classList.remove('active');
                    
                    if (currentViewMode === 'grid') {
                        renderGridView(filteredFiles);
                        document.getElementById('filesGrid').classList.add('active');
                        document.getElementById('filesList').classList.remove('active');
                    } else {
                        renderListView(filteredFiles);
                        document.getElementById('filesList').classList.add('active');
                        document.getElementById('filesGrid').classList.remove('active');
                    }
                }
                
                // Update stats with filtered count
                updateStats(filteredFiles.length);
            });
        }
        
        // Filter files
        function filterFiles(files) {
            return files.filter(file => {
                const matchesSearch = !currentFilters.search || 
                    file.displayName.toLowerCase().includes(currentFilters.search.toLowerCase()) ||
                    (file.description && file.description.toLowerCase().includes(currentFilters.search.toLowerCase()));
                
                const matchesCategory = !currentFilters.category || 
                    file.category === currentFilters.category;
                
                return matchesSearch && matchesCategory;
            }).sort((a, b) => {
                switch (currentFilters.sortBy) {
                    case 'name':
                        return a.displayName.localeCompare(b.displayName);
                    case 'date':
                        return new Date(b.dateAdded) - new Date(a.dateAdded);
                    case 'size':
                        return b.size - a.size;
                    case 'type':
                        return (a.extension || '').localeCompare(b.extension || '');
                    default:
                        return 0;
                }
            });
        }
        
        // Render grid view
        function renderGridView(files) {
            const grid = document.getElementById('filesGrid');
            grid.innerHTML = files.map(file => `
                <div class="file-card ${selectedFiles.has(file.id) ? 'selected' : ''}" onclick="handleFileClick(event, '${file.id}')" oncontextmenu="showFileMenu(event, '${file.id}')">
                    <div class="selection-checkbox" onclick="event.stopPropagation(); toggleFileSelection('${file.id}')">
                        ${selectedFiles.has(file.id) ? '✓' : ''}
                    </div>
                    <div class="file-actions">
                        <button class="file-menu-btn" onclick="event.stopPropagation(); showFileMenu(event, '${file.id}')">⋮</button>
                    </div>
                    <div class="file-icon" id="icon-${file.id}">${getFileIcon(file)}</div>
                    <div class="file-name">${file.displayName || 'Unknown File'}</div>
                    <div class="file-details">
                        ${formatFileSize(file.size || 0)} • ${formatDate(file.dateAdded || new Date())}
                        ${file.description ? '<br><em>' + file.description + '</em>' : ''}
                        ${(file.tags && file.tags.length > 0) ? '<br>' + file.tags.map(tag => `<span style="background: #333; padding: 1px 4px; border-radius: 3px; font-size: 10px;">${tag}</span>`).join(' ') : ''}
                    </div>
                </div>
            `).join('');
            
            // Load thumbnails for images
            files.forEach(file => {
                if (file.category === 'images') {
                    loadFileThumbnail(file.id);
                }
            });
        }
        
        // Render list view
        function renderListView(files) {
            const list = document.getElementById('filesList');
            list.innerHTML = files.map(file => `
                <div class="list-item ${selectedFiles.has(file.id) ? 'selected' : ''}" onclick="handleFileClick(event, '${file.id}')" oncontextmenu="showFileMenu(event, '${file.id}')">
                    <div class="list-icon">${getFileIcon(file)}</div>
                    <div class="list-info">
                        <div class="list-name">${file.displayName || 'Unknown File'}</div>
                        <div class="list-details">
                            ${formatFileSize(file.size || 0)} • ${file.extension || 'Unknown'} • ${formatDate(file.dateAdded || new Date())}
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Utility functions
        function getFileIcon(file) {
            // If we have an extracted icon, use it
            if (file.extractedIcon) {
                // Convert the local file path to a file:// URL
                const iconUrl = `file://${file.extractedIcon.replace(/\\/g, '/')}`;
                return `<img src="${iconUrl}" style="width: 100%; height: 100%; object-fit: contain;" onerror="this.outerHTML=getCategoryIcon('${file.category || 'other'}')" />`;
            }
            
            // If it's a directory, use the folder icon
            if (file.isDirectory) {
                return '📁';
            }
            
            // Otherwise, use the category icon
            return getCategoryIcon(file.category || 'other');
        }
        
        function getCategoryIcon(category) {
            const icons = {
                documents: '📄',
                images: '🖼️',
                videos: '🎬',
                audio: '🎵',
                archives: '📦',
                executables: '⚙️',
                other: '📁'
            };
            return icons[category] || '📁';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDate(dateString) {
            if (!dateString) return 'Unknown';
            try {
                return new Date(dateString).toLocaleDateString();
            } catch (error) {
                return 'Unknown';
            }
        }
        
        // Event handlers
        function setViewMode(mode) {
            currentViewMode = mode;
            document.getElementById('gridBtn').classList.toggle('active', mode === 'grid');
            document.getElementById('listBtn').classList.toggle('active', mode === 'list');
            updateFileDisplay();
        }
        
        function openFile(fileId) {
            ipcRenderer.invoke('desktop-arsenal-open-file', fileId);
        }
        
        function showFileMenu(event, fileId) {
            event.preventDefault();
            // Create context menu
            const contextMenu = document.createElement('div');
            contextMenu.style.cssText = `
                position: fixed;
                top: ${event.clientY}px;
                left: ${event.clientX}px;
                background: var(--background-card);
                border: 1px solid var(--border-color);
                border-radius: 8px;
                padding: 8px 0;
                z-index: 3000;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
            `;
            
            const menuItems = [
                { text: 'Edit', action: () => editFile(fileId) },
                { text: 'Open', action: () => openFile(fileId) },
                { text: 'Restore to Desktop', action: () => restoreFile(fileId) },
                { text: 'Delete', action: () => deleteFile(fileId) }
            ];
            
            menuItems.forEach(item => {
                const menuItem = document.createElement('div');
                menuItem.style.cssText = `
                    padding: 8px 16px;
                    cursor: pointer;
                    transition: background 0.2s ease;
                `;
                menuItem.textContent = item.text;
                menuItem.onclick = () => {
                    item.action();
                    document.body.removeChild(contextMenu);
                };
                menuItem.onmouseover = () => {
                    menuItem.style.background = 'var(--background-hover)';
                };
                menuItem.onmouseout = () => {
                    menuItem.style.background = 'transparent';
                };
                contextMenu.appendChild(menuItem);
            });
            
            document.body.appendChild(contextMenu);
            
            // Remove menu when clicking elsewhere
            setTimeout(() => {
                document.addEventListener('click', function removeMenu() {
                    if (document.body.contains(contextMenu)) {
                        document.body.removeChild(contextMenu);
                    }
                    document.removeEventListener('click', removeMenu);
                }, 10);
            });
        }
        
        function toggleAddMenu(event) {
            event.stopPropagation();
            const menu = document.getElementById('addMenu');
            const isVisible = menu.style.display !== 'none';
            
            // Hide all dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(m => m.style.display = 'none');
            
            // Toggle this menu
            menu.style.display = isVisible ? 'none' : 'block';
            
            // Close menu when clicking outside
            if (!isVisible) {
                const closeMenu = (e) => {
                    if (!e.target.closest('.dropdown')) {
                        menu.style.display = 'none';
                        document.removeEventListener('click', closeMenu);
                    }
                };
                setTimeout(() => document.addEventListener('click', closeMenu), 0);
            }
        }
        
        async function addFiles(type = 'files') {
            // Hide the dropdown menu
            document.getElementById('addMenu').style.display = 'none';
            
            try {
                const addedFiles = await ipcRenderer.invoke('desktop-arsenal-add-files', type);
                if (addedFiles && addedFiles.length > 0) {
                    // Refresh the file list to show the newly added files
                    const data = await ipcRenderer.invoke('desktop-arsenal-get-files');
                    currentFiles = data.files;
                    updateFileDisplay();
                    updateStats();
                    
                    // Show notification about added files
                    const fileCount = addedFiles.length;
                    const itemType = type === 'folders' ? 'folder' : 'file';
                    const itemTypePlural = type === 'folders' ? 'folders' : 'files';
                    const message = fileCount === 1 
                        ? `Added "${addedFiles[0].displayName}" to Desktop Arsenal`
                        : `Added ${fileCount} ${itemTypePlural} to Desktop Arsenal`;
                    showNotification(message, 'success');
                }
            } catch (error) {
                console.error(`Error adding ${type}:`, error);
                showNotification(`Failed to add ${type}`, 'error');
            }
        }
        
        async function openSettings() {
            document.getElementById('settingsModal').classList.add('active');
            await loadSettingsValues();
        }
        
        async function loadSettingsValues() {
            try {
                const settings = await ipcRenderer.invoke('desktop-arsenal-get-settings');
                
                // Load current hotkey
                document.getElementById('hotkeyInput').value = settings.hotkeys.toggleKey || 'Ctrl+Space';
                document.getElementById('hotkeyInput').dataset.previousValue = settings.hotkeys.toggleKey || 'Ctrl+Space';
                
                // Load current view mode
                document.getElementById('defaultViewMode').value = settings.overlay.viewMode || 'grid';
                
                // Load current file monitoring setting
                document.getElementById('fileMonitoring').value = settings.fileMonitoring.enabled ? 'enabled' : 'disabled';
                
                // Load auto-close overlay setting
                document.getElementById('autoCloseOverlay').value = settings.overlay.autoClose ? 'enabled' : 'disabled';
                
                // Load prevent fullscreen setting
                document.getElementById('preventFullscreen').value = settings.overlay.preventFullscreen ? 'enabled' : 'disabled';
                
                // Load background settings
                let backgroundType = settings.overlay.backgroundType || 'none';
                
                // Show the actual background type in the dropdown
                document.getElementById('backgroundType').value = backgroundType;
                
                // Update the UI based on background type
                handleBackgroundTypeChange();
                
                // Update current background display
                updateCurrentBackgroundSelection();
                
                // Load opacity
                const opacity = settings.overlay.backgroundOpacity !== undefined ? settings.overlay.backgroundOpacity : 50;
                document.getElementById('backgroundOpacity').value = opacity;
                document.getElementById('opacityValue').textContent = `${opacity}%`;
                
                // Load auto-start monitoring
                document.getElementById('autoStartMonitoring').value = settings.fileMonitoring.autoStart ? 'enabled' : 'disabled';
                
                // Load start with Windows setting
                const autoStartEnabled = await ipcRenderer.invoke('desktop-arsenal-check-autostart');
                document.getElementById('startWithWindows').value = autoStartEnabled ? 'enabled' : 'disabled';
                
            } catch (error) {
                console.error('Error loading settings values:', error);
            }
        }
        
        function closeSettings() {
            document.getElementById('settingsModal').classList.remove('active');
        }
        
        // Background Library Functions
        let selectedBackgroundData = null;
        let presetBackgrounds = [];
        let pendingBackgroundPath = null;
        let pendingBackgroundName = null;
        
        async function openBackgroundLibrary() {
            const modal = document.getElementById('backgroundLibraryModal');
            modal.classList.add('active');
            
            // Clear any pending selections
            selectedBackgroundData = null;
            pendingBackgroundPath = null;
            pendingBackgroundName = null;
            
            // Clear file input
            document.getElementById('backgroundImage').value = '';
            
            // Load preset backgrounds
            await loadPresetBackgrounds();
            
            // Update current selection display
            updateCurrentBackgroundSelection();
            
            // Reset to preset tab by default
            switchBackgroundCategory('preset');
        }
        
        function closeBackgroundLibrary() {
            document.getElementById('backgroundLibraryModal').classList.remove('active');
            selectedBackgroundData = null;
            document.getElementById('applyBackgroundBtn').disabled = true;
            
            // Clear any selected cards
            document.querySelectorAll('.background-card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Stop any preview videos
            document.querySelectorAll('.background-card video').forEach(video => {
                video.pause();
                video.currentTime = 0;
            });
            
            // Clear file input
            const fileInput = document.getElementById('backgroundImage');
            fileInput.value = '';
            
            // Remove any selected custom file card
            const selectedCard = document.querySelector('.custom-selected');
            if (selectedCard) {
                selectedCard.remove();
            }
        }
        
        async function loadPresetBackgrounds() {
            const videoFiles = [
                'Birthday.mp4', 'Blue Comet.mp4', 'Box dropped in hole a frikin dies.mp4',
                'Cabin in the rain.mp4', 'Cafe.mp4', 'Camping In the Rain.mp4',
                'Cavern.mp4', 'Cozy Cabin.mp4', 'Demon Lake.mp4',
                'LoFi Cat by the Fireplace.mp4', 'LoFi Cat Chillin\' on da Couch.mp4',
                'LoFi Cat on the Window Sill.mp4', 'Moon.mp4', 'Much Love.mp4',
                'NFL Football.mp4', 'Psychedelic Monsters.mp4', 'Rain.mp4',
                'Ring Of Fire.mp4', 'Snow.mp4', 'Spoopy Subway Ride.mp4',
                'Stopping at the Motel.mp4', 'Stopping For Gas.mp4',
                'The Diner.mp4', 'TV Static.mp4'
            ];
            
            const grid = document.getElementById('presetBackgroundGrid');
            
            // Clear existing video cards (keep only the "none" option)
            grid.querySelectorAll('.video-bg').forEach(card => card.remove());
            
            // Add video background cards
            videoFiles.forEach(filename => {
                const title = filename.replace('.mp4', '').replace(/([A-Z])/g, ' $1').trim();
                const card = createVideoBackgroundCard(filename, title);
                grid.appendChild(card);
            });
        }
        
        function createVideoBackgroundCard(filename, title) {
            const card = document.createElement('div');
            card.className = 'background-card video-bg';
            card.dataset.type = 'preset-video';
            card.dataset.filename = filename;
            card.onclick = () => selectBackground('preset-video', title, filename);
            
            const videoPath = `./PreSet Backgrounds for Arsenal/${filename}`;
            
            card.innerHTML = `
                <div class="background-card-preview video-preview">
                    <video muted loop preload="metadata">
                        <source src="${videoPath}" type="video/mp4">
                    </video>
                    <div class="play-overlay">▶</div>
                </div>
                <div class="background-card-info">
                    <div class="background-card-title">${title}</div>
                    <div class="background-card-description">Video background</div>
                </div>
            `;
            
            // Add hover to play functionality
            const video = card.querySelector('video');
            
            card.addEventListener('mouseenter', () => {
                video.play().catch(e => console.log('Video play failed:', e));
            });
            
            card.addEventListener('mouseleave', () => {
                video.pause();
                video.currentTime = 0;
            });
            
            return card;
        }
        
        function switchBackgroundCategory(category) {
            // Update active category button
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
            
            // Show/hide appropriate grids
            const presetGrid = document.getElementById('presetBackgroundGrid');
            const customGrid = document.getElementById('customBackgroundGrid');
            
            if (category === 'preset') {
                presetGrid.style.display = 'grid';
                customGrid.style.display = 'none';
            } else {
                presetGrid.style.display = 'none';
                customGrid.style.display = 'grid';
            }
        }
        
        function selectBackground(type, name, filename = null) {
            // Clear previous selection
            document.querySelectorAll('.background-card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Select current card
            event.target.closest('.background-card').classList.add('selected');
            
            // Store selection data
            selectedBackgroundData = {
                type: type,
                name: name,
                filename: filename
            };
            
            // Enable apply button
            document.getElementById('applyBackgroundBtn').disabled = false;
            
            console.log('Selected background:', selectedBackgroundData);
        }
        
        async function applySelectedBackground() {
            if (!selectedBackgroundData) return;
            
            const { type, name, filename } = selectedBackgroundData;
            
            try {
                // Update the hidden backgroundType select for compatibility
                const backgroundTypeSelect = document.getElementById('backgroundType');
                
                if (type === 'preset-video') {
                    // Handle preset video backgrounds
                    backgroundTypeSelect.value = 'custom';
                    
                    // Copy the preset video to the backgrounds folder IMMEDIATELY and store the path
                    const presetPath = `./PreSet Backgrounds for Arsenal/${filename}`;
                    const result = await ipcRenderer.invoke('desktop-arsenal-save-background-from-path', {
                        sourcePath: presetPath,
                        name: filename
                    });
                    
                    // Store the path and name for saveSettings to use
                    pendingBackgroundPath = result.path;
                    pendingBackgroundName = result.originalName || filename;
                    
                    // Update current background display
                    updateCurrentBackgroundDisplay(name);
                    
                    showNotification(`Background "${name}" selected! Click Save Settings to apply.`, 'success');
                } else if (type === 'custom') {
                    // Handle custom file upload
                    const fileInput = document.getElementById('backgroundImage');
                    if (fileInput.files && fileInput.files[0]) {
                        backgroundTypeSelect.value = 'custom';
                        
                        // Mark that we have a pending custom file
                        pendingBackgroundPath = 'CUSTOM_FILE_PENDING';
                        pendingBackgroundName = name;
                        
                        // File will be processed when Save Settings is clicked
                        updateCurrentBackgroundDisplay(name);
                        showNotification(`Custom background "${name}" selected! Click Save Settings to apply.`, 'success');
                    } else {
                        showNotification('No custom file selected', 'error');
                        return;
                    }
                } else if (type === 'none') {
                    // Handle no background
                    backgroundTypeSelect.value = 'none';
                    pendingBackgroundPath = null;
                    pendingBackgroundName = null;
                    updateCurrentBackgroundDisplay('None');
                    showNotification('Background disabled. Click Save Settings to apply.', 'success');
                }
                
                // Close the modal
                closeBackgroundLibrary();
                
                // The actual background will be applied when Save Settings is clicked
                
            } catch (error) {
                console.error('Error applying background:', error);
                showNotification('Failed to apply background', 'error');
            }
        }
        
        function updateCurrentBackgroundDisplay(name) {
            const display = document.getElementById('currentBackgroundDisplay');
            display.textContent = name || 'None Selected';
        }
        
        async function updateCurrentBackgroundSelection() {
            try {
                const settings = await ipcRenderer.invoke('desktop-arsenal-get-settings');
                const backgroundType = settings.overlay?.backgroundType || 'none';
                
                let displayName = 'None Selected';
                
                if (backgroundType === 'none' || !settings.overlay?.background) {
                    displayName = 'None';
                } else if (backgroundType === 'custom') {
                    if (settings.overlay?.backgroundOriginalName) {
                        displayName = settings.overlay.backgroundOriginalName.replace('.mp4', '').replace(/([A-Z])/g, ' $1').trim();
                    } else if (settings.overlay?.background) {
                        // Try to extract filename from path
                        const filename = settings.overlay.background.split(/[\\\/]/).pop();
                        displayName = filename.replace(/^background_\d+\./, '').replace('.mp4', '').replace(/([A-Z])/g, ' $1').trim();
                    } else {
                        displayName = 'Custom';
                    }
                }
                
                updateCurrentBackgroundDisplay(displayName);
            } catch (error) {
                console.error('Error updating current background selection:', error);
            }
        }
        
        async function saveSettings() {
            try {
                // Get current settings to preserve existing values
                const currentSettings = await ipcRenderer.invoke('desktop-arsenal-get-settings');
                
                const backgroundImageInput = document.getElementById('backgroundImage');
                let backgroundPath = currentSettings.overlay?.background || null;
                let backgroundOriginalName = currentSettings.overlay?.backgroundOriginalName || null;
                
                // Check if we have a pending background from the library
                if (pendingBackgroundPath && pendingBackgroundPath !== 'CUSTOM_FILE_PENDING') {
                    // Use the pending background from library selection
                    backgroundPath = pendingBackgroundPath;
                    backgroundOriginalName = pendingBackgroundName;
                    
                    // Clear pending data
                    pendingBackgroundPath = null;
                    pendingBackgroundName = null;
                    
                    console.log('Using pending background from library:', backgroundPath, backgroundOriginalName);
                }
                // Handle background image if selected (custom upload)
                else if (backgroundImageInput.files && backgroundImageInput.files[0]) {
                    const imageFile = backgroundImageInput.files[0];
                    
                    // Check file size (limit to 500MB for videos)
                    const maxSize = 500 * 1024 * 1024; // 500MB
                    if (imageFile.size > maxSize) {
                        showNotification(`File too large. Maximum size is ${maxSize / 1024 / 1024}MB`, 'error');
                        return;
                    }
                    
                    // For very large files (over 50MB), use a different approach
                    const largeFileThreshold = 50 * 1024 * 1024; // 50MB
                    
                    try {
                        if (imageFile.size > largeFileThreshold) {
                            // For large files, read in chunks and save directly
                            showNotification('Processing large file, please wait...', 'info');
                            console.log(`Large file detected: ${imageFile.name} (${(imageFile.size / 1024 / 1024).toFixed(2)}MB)`);
                            
                            // Use the file path if available (Electron specific)
                            const filePath = imageFile.path || imageFile.webkitRelativePath || null;
                            if (filePath) {
                                console.log('Using file path method:', filePath);
                                const result = await ipcRenderer.invoke('desktop-arsenal-save-background-from-path', {
                                    sourcePath: filePath,
                                    name: imageFile.name
                                });
                                backgroundPath = result.path;
                                backgroundOriginalName = result.originalName;
                            } else {
                                // Fallback to data URL but warn about potential issues
                                console.warn('Large file without path property, using data URL (may be slow)');
                                console.log('File properties:', {
                                    name: imageFile.name,
                                    size: imageFile.size,
                                    type: imageFile.type,
                                    lastModified: imageFile.lastModified
                                });
                                const reader = new FileReader();
                                const dataUrl = await new Promise((resolve, reject) => {
                                    reader.onload = (e) => resolve(e.target.result);
                                    reader.onerror = (e) => reject(new Error('Failed to read large file'));
                                    reader.readAsDataURL(imageFile);
                                });
                                
                                const result = await ipcRenderer.invoke('desktop-arsenal-save-background', {
                                    name: imageFile.name,
                                    data: dataUrl
                                });
                                backgroundPath = result.path;
                                backgroundOriginalName = result.originalName;
                            }
                        } else {
                            // For smaller files, use data URL as before
                            console.log(`Small file detected: ${imageFile.name} (${(imageFile.size / 1024 / 1024).toFixed(2)}MB)`);
                            const reader = new FileReader();
                            const dataUrl = await new Promise((resolve, reject) => {
                                reader.onload = (e) => {
                                    console.log('File read successfully, data URL length:', e.target.result?.length);
                                    resolve(e.target.result);
                                };
                                reader.onerror = (e) => {
                                    console.error('FileReader error:', e);
                                    reject(new Error('Failed to read file'));
                                };
                                reader.readAsDataURL(imageFile);
                            });
                            
                            if (!dataUrl) {
                                throw new Error('Failed to read file data');
                            }
                            
                            // Save the file to backgrounds folder via IPC
                            const result = await ipcRenderer.invoke('desktop-arsenal-save-background', {
                                name: imageFile.name,
                                data: dataUrl
                            });
                            backgroundPath = result.path;
                            backgroundOriginalName = result.originalName;
                        }
                        
                        // Reset progress for new background
                        animationStartTime = null;
                        backgroundMediaElement = null;
                        
                        // Update the controls to show the new background
                        setTimeout(() => updateCustomBackgroundControls(), 100);
                        
                        showNotification('Background saved successfully!', 'success');
                    } catch (fileError) {
                        console.error('Error processing background file:', fileError);
                        showNotification('Failed to process background file: ' + fileError.message, 'error');
                        return;
                    }
                }
                
                const backgroundType = document.getElementById('backgroundType').value;
                const backgroundOpacity = parseInt(document.getElementById('backgroundOpacity').value);
                
                // Clear background path only if we're selecting "none"
                if (backgroundType === 'none') {
                    backgroundPath = null;
                    backgroundOriginalName = null;
                }
                
                const settings = {
                    hotkeys: {
                        toggleKey: document.getElementById('hotkeyInput').value || 'Ctrl+Space',
                        enabled: true
                    },
                    overlay: {
                        viewMode: document.getElementById('defaultViewMode').value || 'grid',
                        backgroundType: backgroundType,
                        background: backgroundPath,
                        backgroundOriginalName: backgroundOriginalName,
                        backgroundOpacity: backgroundOpacity,
                        backgroundProgress: 0, // Reset progress on settings change
                        size: currentSettings.overlay?.size || { width: 1650, height: 1000 },
                        position: currentSettings.overlay?.position || { x: 'center', y: 'center' },
                        sortBy: currentSettings.overlay?.sortBy || 'name',
                        autoClose: document.getElementById('autoCloseOverlay').value === 'enabled',
                        preventFullscreen: document.getElementById('preventFullscreen').value === 'enabled'
                    },
                    fileMonitoring: {
                        enabled: document.getElementById('fileMonitoring').value === 'enabled',
                        autoStart: document.getElementById('autoStartMonitoring').value === 'enabled',
                        notificationDuration: currentSettings.fileMonitoring?.notificationDuration || 5000
                    }
                };
                
                // Apply background changes immediately before saving
                setBackgroundOpacity(backgroundOpacity);
                currentOpacity = 100 - backgroundOpacity;
                
                if (backgroundType === 'none') {
                    removeAnimatedBackground();
                } else if (backgroundType === 'custom') {
                    if (backgroundImageInput.files && backgroundImageInput.files[0]) {
                        // New file was selected and saved above - apply it using the saved path
                        if (backgroundPath) {
                            const fileUrl = `file://${backgroundPath.replace(/\\/g, '/')}`;
                            const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov'];
                            const isVideo = videoExtensions.some(ext => backgroundPath.toLowerCase().endsWith(ext));
                            
                            if (isVideo) {
                                applyVideoBackground(fileUrl);
                            } else {
                                applyBackgroundImage(fileUrl);
                            }
                        }
                    } else if (backgroundPath) {
                        // Use existing background
                        const fileUrl = `file://${backgroundPath.replace(/\\/g, '/')}`;
                        const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov'];
                        const isVideo = videoExtensions.some(ext => backgroundPath.toLowerCase().endsWith(ext));
                        
                        if (isVideo) {
                            applyVideoBackground(fileUrl);
                        } else {
                            applyBackgroundImage(fileUrl);
                        }
                    }
                } else {
                    // Apply animated background
                    applyAnimatedBackground(backgroundType);
                }
                
                await ipcRenderer.invoke('desktop-arsenal-update-settings', settings);
                
                // Reload the background to ensure it's properly applied
                await loadBackgroundImage();
                
                // Start/stop monitoring based on settings
                if (settings.fileMonitoring.enabled) {
                    await ipcRenderer.invoke('desktop-arsenal-start-monitoring');
                } else {
                    await ipcRenderer.invoke('desktop-arsenal-stop-monitoring');
                }
                
                // Handle start with Windows setting
                const startWithWindows = document.getElementById('startWithWindows').value === 'enabled';
                await ipcRenderer.invoke('desktop-arsenal-set-autostart', startWithWindows);
                
                closeSettings();
                showNotification('Settings saved successfully!');
            } catch (error) {
                console.error('Error saving settings:', error);
                showNotification('Failed to save settings', 'error');
            }
        }
        
        function applyBackgroundImage(imageUrl) {
            const backgroundLayer = document.getElementById('backgroundLayer');
            removeAnimatedBackground();
            
            if (imageUrl) {
                backgroundLayer.classList.add('custom-image');
                backgroundLayer.style.backgroundImage = `url(${imageUrl})`;
                currentBackgroundType = 'custom';
            } else {
                backgroundLayer.classList.remove('custom-image');
                backgroundLayer.style.backgroundImage = 'none';
            }
            
            // Apply current opacity setting
            const slider = document.getElementById('backgroundOpacity');
            if (slider && currentBackgroundType === 'custom') {
                setBackgroundOpacity(slider.value);
            } else {
                ensureBackgroundVisibility();
            }
        }
        
        function applyAnimatedBackground(type, progress = 0) {
            const backgroundLayer = document.getElementById('backgroundLayer');
            removeAnimatedBackground();
            
            // Remove custom image class if it exists
            backgroundLayer.classList.remove('custom-image');
            backgroundLayer.style.backgroundImage = 'none';
            
            // Apply animation class to background layer
            const animationClass = type.replace(/([A-Z])/g, '-$1').toLowerCase();
            backgroundLayer.className = `background-layer animated-background ${animationClass}`;
            
            // Track animation start time for continuous progress calculation
            animationStartTime = Date.now() - (progress * 1000);
            
            console.log(`🎨 Applying preset animation: ${type} with progress: ${progress.toFixed(2)}s`);
            
            // Add special elements for certain animations with continuous timing
            if (type === 'matrixRain') {
                // Create matrix columns with staggered timing for continuous effect
                for (let i = 0; i < 30; i++) {
                    const column = document.createElement('div');
                    column.className = 'matrix-column';
                    column.style.left = `${Math.random() * 100}%`;
                    const duration = 10 + Math.random() * 20; // Random duration between 10-30s
                    column.style.setProperty('--duration', `${duration}s`);
                    column.style.setProperty('--opacity', `${0.3 + Math.random() * 0.7}`);
                    
                    // Calculate proper animation delay for continuous effect
                    const baseDelay = Math.random() * duration; // Random start point
                    const progressDelay = progress % duration; // Where we should be now
                    const adjustedDelay = (baseDelay - progressDelay) % duration;
                    column.style.animationDelay = `${adjustedDelay}s`;
                    
                    column.textContent = Array(50).fill(0).map(() => 
                        String.fromCharCode(0x30A0 + Math.random() * 96)
                    ).join('');
                    backgroundLayer.appendChild(column);
                }
            } else if (type === 'starryNight') {
                // Add twinkling overlay for starry night with continuous timing
                const twinkleOverlay = document.createElement('div');
                twinkleOverlay.className = 'twinkle-overlay';
                backgroundLayer.appendChild(twinkleOverlay);
                
                // Apply continuous animation progress
                if (progress > 0) {
                    backgroundLayer.style.setProperty('--star-progress', `-${progress}s`);
                }
            }
            
            // For CSS animations with pseudo-elements, apply negative delay for continuous effect
            if (progress > 0) {
                const style = document.createElement('style');
                style.textContent = `
                    .${animationClass}::before {
                        animation-delay: -${progress}s !important;
                    }
                    .${animationClass}::after {
                        animation-delay: -${progress}s !important;
                    }
                    .${animationClass} * {
                        animation-delay: -${progress}s !important;
                    }
                `;
                document.head.appendChild(style);
                backgroundLayer.dataset.animationStyle = 'true';
            }
            
            // Ensure background is visible
            ensureBackgroundVisibility();
        }
        
        function removeAnimatedBackground() {
            const backgroundLayer = document.getElementById('backgroundLayer');
            if (!backgroundLayer) return;
            
            // Remove all animation classes
            backgroundLayer.className = 'background-layer';
            // Clear any child elements (for matrix rain, videos, etc.)
            backgroundLayer.innerHTML = '';
            // Reset animation properties
            backgroundLayer.style.animationDelay = '';
            backgroundLayer.style.removeProperty('--star-progress');
            backgroundLayer.style.backgroundImage = '';
            backgroundLayer.style.opacity = '1';
            // Don't force opacity - let setBackgroundOpacity handle it
            
            // Remove any injected animation styles
            if (backgroundLayer.dataset.animationStyle) {
                const styles = document.head.querySelectorAll('style');
                styles.forEach(style => {
                    if (style.textContent && style.textContent.includes('animation-delay')) {
                        style.remove();
                    }
                });
                delete backgroundLayer.dataset.animationStyle;
            }
            
            // Clear tracked elements
            backgroundMediaElement = null;
            animationStartTime = null;
            currentBackgroundType = null;
        }
        
        function ensureBackgroundVisibility() {
            const backgroundLayer = document.getElementById('backgroundLayer');
            if (backgroundLayer) {
                // For custom backgrounds, respect the current opacity setting
                // For animated backgrounds, always ensure full visibility
                if (currentBackgroundType === 'custom') {
                    // Get current opacity from slider or use stored value
                    const slider = document.getElementById('backgroundOpacity');
                    const sliderValue = slider ? slider.value : (100 - currentOpacity);
                    // Invert the opacity so 0% = solid, 100% = transparent
                    const customOpacity = 1 - (sliderValue / 100);
                    backgroundLayer.style.opacity = customOpacity;
                } else {
                    // Animated backgrounds always at full brightness
                    backgroundLayer.style.opacity = '1';
                    // Ensure all child elements are also visible
                    const children = backgroundLayer.querySelectorAll('*');
                    children.forEach(child => {
                        if (child.style) {
                            child.style.opacity = '1';
                        }
                    });
                }
            }
        }
        
        /**
         * Sets the opacity for both the window and backgrounds
         * - Window: Uses rgba background color for see-through effect
         * - Custom backgrounds (images/GIFs/videos): Match the transparency level (inverted)
         * - Animated backgrounds: Always stay at full brightness
         */
        function setBackgroundOpacity(opacity) {
            const overlayContainer = document.querySelector('.overlay-container');
            const backgroundLayer = document.getElementById('backgroundLayer');
            
            // Calculate the alpha value for the black overlay
            // opacity slider: 0 = fully opaque black overlay, 100 = fully transparent
            const alpha = 1 - (opacity / 100); // Convert to 0-1 range where 0 is transparent
            
            // Apply semi-transparent black background to the container
            // This creates the see-through effect
            overlayContainer.style.backgroundColor = `rgba(0, 0, 0, ${alpha})`;
            
            // For custom backgrounds (images/GIFs/videos), apply matching opacity
            // For animated backgrounds, keep them at full brightness
            if (backgroundLayer && currentBackgroundType === 'custom') {
                // Make custom images/GIFs/videos match the transparency level
                // Invert the opacity so 0% = solid, 100% = transparent
                // Examples:
                //   Slider 0%   → Image opacity 1.0 (fully visible, not transparent)
                //   Slider 50%  → Image opacity 0.5 (half transparent)
                //   Slider 100% → Image opacity 0.0 (fully transparent)
                const customOpacity = 1 - (opacity / 100);
                backgroundLayer.style.opacity = customOpacity;
            } else if (backgroundLayer) {
                // For animated backgrounds, ensure they stay at full brightness
                backgroundLayer.style.opacity = '1';
            }
        }

        function handleBackgroundTypeChange() {
            // This function is kept for compatibility but doesn't do much anymore
            // since we're using the new background library system
            const backgroundType = document.getElementById('backgroundType').value;
            console.log('Background type changed to:', backgroundType);
        }
        

        


        async function exportSettings() {
            try {
                const exportPath = await ipcRenderer.invoke('desktop-arsenal-export-settings');
                showNotification(`Settings exported to: ${exportPath}`);
            } catch (error) {
                console.error('Error exporting settings:', error);
                showNotification('Failed to export settings', 'error');
            }
        }

        async function importSettings() {
            const fileInput = document.getElementById('importFile');
            if (fileInput.files[0]) {
                try {
                    await ipcRenderer.invoke('desktop-arsenal-import-settings', fileInput.files[0].path);
                    showNotification('Settings imported successfully!');
                    // Refresh the overlay
                    await initializeOverlay();
                } catch (error) {
                    console.error('Error importing settings:', error);
                    showNotification('Failed to import settings', 'error');
                }
            }
        }
        
        async function cleanupBackgrounds() {
            try {
                showNotification('Cleaning up background files...', 'info');
                const result = await ipcRenderer.invoke('desktop-arsenal-cleanup-backgrounds');
                
                if (result.deletedCount > 0) {
                    const sizeMB = (result.totalSize / 1024 / 1024).toFixed(2);
                    showNotification(`Cleaned up ${result.deletedCount} files (${sizeMB} MB freed)`, 'success');
                } else {
                    showNotification('No files to clean up', 'info');
                }
            } catch (error) {
                console.error('Error cleaning up backgrounds:', error);
                showNotification('Failed to clean up backgrounds', 'error');
            }
        }
        
        function updateStats(filteredCount) {
            const stats = document.getElementById('statsInfo');
            if (filteredCount !== undefined) {
                stats.textContent = `${filteredCount} files organized`;
            } else {
                stats.textContent = `${currentFiles.length} files organized`;
            }
        }
        
        // Overlay controls
        function closeOverlay() {
            // Stop periodic progress saving
            stopPeriodicProgressSaving();
            
            // Save final background progress before closing
            saveBackgroundProgress();
            
            // Add closing animation
            document.body.classList.add('closing');
            
            // Wait for animation to complete before closing
            setTimeout(() => {
                ipcRenderer.invoke('desktop-arsenal-toggle-overlay');
            }, 300);
        }
        
        // Consolidated ESC key handling system
        let isHotkeyCapturing = false;
        
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                // Priority 1: Hotkey capture mode (highest priority)
                if (isHotkeyCapturing) {
                    // Let the hotkey capture handler deal with it
                    return;
                }
                
                // Priority 2: Close modals
                const settingsModal = document.getElementById('settingsModal');
                const fileEditModal = document.getElementById('fileEditModal');
                
                if (settingsModal && settingsModal.classList.contains('active')) {
                    closeSettings();
                    event.preventDefault();
                    event.stopPropagation();
                    return;
                }
                
                if (fileEditModal && fileEditModal.classList.contains('active')) {
                    closeFileEdit();
                    event.preventDefault();
                    event.stopPropagation();
                    return;
                }
                
                // Priority 3: Close overlay (lowest priority)
                closeOverlay();
            }
        });
        
        // Hotkey Input System
        function setupHotkeyInput() {
            const hotkeyInput = document.getElementById('hotkeyInput');
            const hotkeyHint = document.getElementById('hotkeyHint');
            let capturedKeys = [];
            
            // Handle click to start capturing
            hotkeyInput.addEventListener('click', () => {
                if (!isHotkeyCapturing) {
                    startCapturing();
                }
            });
            
            // Handle focus to start capturing
            hotkeyInput.addEventListener('focus', () => {
                if (!isHotkeyCapturing) {
                    startCapturing();
                }
            });
            
            function startCapturing() {
                isHotkeyCapturing = true;
                capturedKeys = [];
                hotkeyInput.classList.add('capturing');
                hotkeyHint.style.display = 'block';
                hotkeyInput.value = 'Press keys...';
                
                // Add global keydown listener
                document.addEventListener('keydown', captureKeys);
            }
            
            function stopCapturing() {
                isHotkeyCapturing = false;
                hotkeyInput.classList.remove('capturing');
                hotkeyHint.style.display = 'none';
                document.removeEventListener('keydown', captureKeys);
            }
            
            function captureKeys(event) {
                if (!isHotkeyCapturing) return;
                
                event.preventDefault();
                event.stopPropagation();
                
                // Handle ESC to cancel
                if (event.key === 'Escape') {
                    stopCapturing();
                    // Restore previous value
                    hotkeyInput.value = hotkeyInput.dataset.previousValue || 'Ctrl+Space';
                    return;
                }
                
                // Handle Enter to confirm
                if (event.key === 'Enter') {
                    if (capturedKeys.length > 0) {
                        const hotkeyString = formatHotkey(capturedKeys);
                        hotkeyInput.value = hotkeyString;
                        hotkeyInput.dataset.previousValue = hotkeyString;
                        stopCapturing();
                    }
                    return;
                }
                
                // Capture the key combination
                capturedKeys = [];
                
                // Add modifiers
                if (event.ctrlKey) capturedKeys.push('Ctrl');
                if (event.altKey) capturedKeys.push('Alt');
                if (event.shiftKey) capturedKeys.push('Shift');
                if (event.metaKey) capturedKeys.push('Meta');
                
                // Add main key (if not a modifier)
                if (!['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
                    let keyName = event.key;
                    
                    // Format special keys
                    if (keyName === ' ') keyName = 'Space';
                    else if (keyName.length === 1) keyName = keyName.toUpperCase();
                    else keyName = keyName.charAt(0).toUpperCase() + keyName.slice(1);
                    
                    capturedKeys.push(keyName);
                }
                
                // Update display
                if (capturedKeys.length > 0) {
                    hotkeyInput.value = formatHotkey(capturedKeys);
                }
            }
            
            function formatHotkey(keys) {
                // Ensure proper order: Ctrl, Alt, Shift, Meta, then main key
                const modifiers = [];
                const mainKeys = [];
                
                keys.forEach(key => {
                    if (['Ctrl', 'Alt', 'Shift', 'Meta'].includes(key)) {
                        if (!modifiers.includes(key)) {
                            modifiers.push(key);
                        }
                    } else {
                        mainKeys.push(key);
                    }
                });
                
                // Sort modifiers in standard order
                const sortedModifiers = [];
                if (modifiers.includes('Ctrl')) sortedModifiers.push('Ctrl');
                if (modifiers.includes('Alt')) sortedModifiers.push('Alt');
                if (modifiers.includes('Shift')) sortedModifiers.push('Shift');
                if (modifiers.includes('Meta')) sortedModifiers.push('Meta');
                
                return [...sortedModifiers, ...mainKeys].join('+');
            }
            
            // Store initial value
            hotkeyInput.dataset.previousValue = hotkeyInput.value;
        }
        
        // Initialize hotkey input when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setupHotkeyInput();
            
            // Opacity slider event listener
            document.getElementById('backgroundOpacity').addEventListener('input', (e) => {
                const value = e.target.value;
                document.getElementById('opacityValue').textContent = `${value}%`;
                setBackgroundOpacity(value);
                currentOpacity = 100 - value; // Update current opacity
                
                // Debug opacity values
                debugOpacity();
            });
            
            // File input event listener - Handle custom background upload
            document.getElementById('backgroundImage').addEventListener('change', async (e) => {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    console.log('File selected:', file.name);
                    
                    // If we're in the background library modal, handle it there
                    const libraryModal = document.getElementById('backgroundLibraryModal');
                    if (libraryModal.classList.contains('active')) {
                        // Create a special "selected custom file" card
                        const customGrid = document.getElementById('customBackgroundGrid');
                        
                        // Check if we already have a selected file card
                        let selectedCard = customGrid.querySelector('.custom-selected');
                        if (!selectedCard) {
                            selectedCard = document.createElement('div');
                            selectedCard.className = 'background-card custom-selected';
                            customGrid.appendChild(selectedCard);
                        }
                        
                        // Update the selected card
                        selectedCard.innerHTML = `
                            <div class="background-card-preview custom-preview" style="background: linear-gradient(135deg, rgba(0, 180, 255, 0.2) 0%, rgba(0, 180, 255, 0.1) 100%);">
                                <div class="upload-icon">✅</div>
                                <div class="upload-text" style="font-size: 12px; text-align: center; padding: 0 10px;">${file.name}</div>
                            </div>
                            <div class="background-card-info">
                                <div class="background-card-title">Selected File</div>
                                <div class="background-card-description">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                            </div>
                        `;
                        
                        selectedCard.onclick = () => selectBackground('custom', file.name);
                        
                        // Automatically select it
                        selectBackground('custom', file.name);
                        
                        showNotification(`Selected: ${file.name}. Click Apply to use this background.`, 'info');
                    } else {
                        // Legacy handling for direct settings panel usage
                        showNotification(`Selected: ${file.name}. Click Save Settings to apply.`, 'info');
                        updateCurrentBackgroundDisplay(file.name);
                        
                        // Mark pending for saveSettings
                        pendingBackgroundPath = 'CUSTOM_FILE_PENDING';
                        pendingBackgroundName = file.name;
                    }
                }
            });
        });
        

        
        // Search and filter event listeners
        document.getElementById('searchInput').addEventListener('input', (e) => {
            currentFilters.search = e.target.value;
            updateFileDisplay();
        });
        
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            currentFilters.category = e.target.value;
            updateFileDisplay();
        });
        
        document.getElementById('sortBy').addEventListener('change', (e) => {
            currentFilters.sortBy = e.target.value;
            updateFileDisplay();
        });
        
        // File handling functions
        function handleFileClick(event, fileId) {
            if (event.ctrlKey) {
                toggleFileSelection(fileId);
            } else if (selectedFiles.size === 0) {
                openFile(fileId);
            } else {
                // Clear selection and open file
                selectedFiles.clear();
                updateBulkActionsBar();
                updateFileDisplay();
                openFile(fileId);
            }
        }

        function toggleFileSelection(fileId) {
            if (selectedFiles.has(fileId)) {
                selectedFiles.delete(fileId);
            } else {
                selectedFiles.add(fileId);
            }
            updateBulkActionsBar();
            updateFileDisplay();
        }

        function updateBulkActionsBar() {
            const bulkBar = document.getElementById('bulkActionsBar');
            const bulkCount = document.getElementById('bulkCount');
            
            if (selectedFiles.size > 0) {
                bulkBar.classList.add('active');
                bulkCount.textContent = `${selectedFiles.size} file${selectedFiles.size > 1 ? 's' : ''} selected`;
            } else {
                bulkBar.classList.remove('active');
            }
        }

        function bulkClearSelection() {
            selectedFiles.clear();
            updateBulkActionsBar();
            updateFileDisplay();
        }

        async function bulkRestore() {
            if (selectedFiles.size === 0) return;
            
            try {
                const fileIds = Array.from(selectedFiles);
                await ipcRenderer.invoke('desktop-arsenal-bulk-restore', fileIds);
                showNotification(`${fileIds.length} files restored to desktop`);
                selectedFiles.clear();
                await initializeOverlay();
            } catch (error) {
                console.error('Error bulk restoring files:', error);
                showNotification('Failed to restore files', 'error');
            }
        }

        async function bulkDelete() {
            if (selectedFiles.size === 0) return;
            
            if (confirm(`Are you sure you want to delete ${selectedFiles.size} files? This cannot be undone.`)) {
                try {
                    const fileIds = Array.from(selectedFiles);
                    await ipcRenderer.invoke('desktop-arsenal-bulk-delete', fileIds);
                    showNotification(`${fileIds.length} files deleted`);
                    selectedFiles.clear();
                    await initializeOverlay();
                } catch (error) {
                    console.error('Error bulk deleting files:', error);
                    showNotification('Failed to delete files', 'error');
                }
            }
        }

        async function bulkCategorize() {
            const categorySelect = document.getElementById('bulkCategorySelect');
            const category = categorySelect.value;
            
            if (!category || selectedFiles.size === 0) {
                categorySelect.value = '';
                return;
            }
            
            try {
                const fileIds = Array.from(selectedFiles);
                await ipcRenderer.invoke('desktop-arsenal-bulk-categorize', fileIds, category);
                showNotification(`${fileIds.length} files categorized`);
                categorySelect.value = '';
                selectedFiles.clear();
                await initializeOverlay();
            } catch (error) {
                console.error('Error bulk categorizing files:', error);
                showNotification('Failed to categorize files', 'error');
            }
        }

        // File editing functions
        function editFile(fileId) {
            const file = currentFiles.find(f => f.id === fileId);
            if (!file) return;
            
            editingFileId = fileId;
            
            // Populate edit form
            document.getElementById('editFileName').value = file.displayName;
            document.getElementById('editFileDescription').value = file.description || '';
            document.getElementById('editFileCategory').value = file.category;
            
            // Clear and populate tags
            const tagsContainer = document.getElementById('editFileTags');
            const tagInput = document.getElementById('tagInput');
            tagsContainer.innerHTML = '';
            tagsContainer.appendChild(tagInput);
            
            file.tags.forEach(tag => {
                addTagToEdit(tag);
            });
            
            // Update icon preview
            const iconPreview = document.getElementById('iconPreview');
            if (file.customIcon) {
                iconPreview.innerHTML = `<img src="${file.customIcon}" alt="Custom icon">`;
            } else {
                iconPreview.innerHTML = '<span>Click to select icon</span>';
            }
            
            document.getElementById('fileEditModal').classList.add('active');
        }

        function closeFileEdit() {
            document.getElementById('fileEditModal').classList.remove('active');
            editingFileId = null;
        }

        async function saveFileEdit() {
            if (!editingFileId) return;
            
            try {
                const updates = {
                    displayName: document.getElementById('editFileName').value,
                    description: document.getElementById('editFileDescription').value,
                    category: document.getElementById('editFileCategory').value,
                    tags: Array.from(document.querySelectorAll('.tag-item')).map(tag => 
                        tag.textContent.replace('×', '').trim())
                };
                
                await ipcRenderer.invoke('desktop-arsenal-update-file', editingFileId, updates);
                showNotification('File updated successfully!');
                closeFileEdit();
                await initializeOverlay();
            } catch (error) {
                console.error('Error updating file:', error);
                showNotification('Failed to update file', 'error');
            }
        }

        function focusTagInput() {
            document.getElementById('tagInput').focus();
        }

        function handleTagInput(event) {
            if (event.key === 'Enter' || event.key === ',') {
                event.preventDefault();
                const input = event.target;
                const tag = input.value.trim();
                if (tag) {
                    addTagToEdit(tag);
                    input.value = '';
                }
            } else if (event.key === 'Backspace' && event.target.value === '') {
                // Remove last tag
                const tags = document.querySelectorAll('.tag-item');
                if (tags.length > 0) {
                    tags[tags.length - 1].remove();
                }
            }
        }

        function addTagToEdit(tagText) {
            const tagInput = document.getElementById('tagInput');
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `${tagText} <span class="tag-remove" onclick="this.parentElement.remove()">×</span>`;
            
            tagInput.parentElement.insertBefore(tagElement, tagInput);
        }

        function selectCustomIcon() {
            document.getElementById('iconFileInput').click();
        }

        function handleIconSelect(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const iconPreview = document.getElementById('iconPreview');
                    iconPreview.innerHTML = `<img src="${e.target.result}" alt="Custom icon">`;
                };
                reader.readAsDataURL(file);
            }
        }

        // Thumbnail functions
        async function loadFileThumbnail(fileId) {
            try {
                const thumbnailPath = await ipcRenderer.invoke('desktop-arsenal-get-thumbnail', fileId);
                if (thumbnailPath) {
                    const iconElement = document.getElementById(`icon-${fileId}`);
                    if (iconElement) {
                        iconElement.innerHTML = `<img src="file://${thumbnailPath}" style="width: 48px; height: 48px; object-fit: cover; border-radius: 4px;" alt="Thumbnail">`;
                    }
                }
            } catch (error) {
                console.error('Error loading thumbnail:', error);
            }
        }

        // File operations
        async function restoreFile(fileId) {
            try {
                await ipcRenderer.invoke('desktop-arsenal-restore-file', fileId);
                showNotification('File restored to desktop');
                await initializeOverlay();
            } catch (error) {
                console.error('Error restoring file:', error);
                showNotification('Failed to restore file', 'error');
            }
        }

        async function deleteFile(fileId) {
            if (confirm('Are you sure you want to delete this file? This cannot be undone.')) {
                try {
                    await ipcRenderer.invoke('desktop-arsenal-delete-file', fileId);
                    showNotification('File deleted');
                    await initializeOverlay();
                } catch (error) {
                    console.error('Error deleting file:', error);
                    showNotification('Failed to delete file', 'error');
                }
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            
            // Calculate position based on existing notifications
            const topPosition = 20 + (activeNotifications.length * (NOTIFICATION_HEIGHT + NOTIFICATION_MARGIN));
            
            notification.style.cssText = `
                position: fixed;
                top: ${topPosition}px;
                right: 20px;
                background: ${type === 'error' ? '#ff4444' : type === 'warning' ? '#f59e0b' : '#00b4ff'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 4000;
                opacity: 0;
                transform: translateY(-20px);
                transition: all 0.3s ease;
                min-width: 250px;
                max-width: 400px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            activeNotifications.push(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateY(0)';
            }, 10);
            
            // Remove after delay
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                        // Remove from active notifications array
                        const index = activeNotifications.indexOf(notification);
                        if (index > -1) {
                            activeNotifications.splice(index, 1);
                            // Reposition remaining notifications
                            repositionNotifications();
                        }
                    }
                }, 300);
            }, type === 'warning' ? 5000 : 3000); // Warning notifications stay longer
        }
        
        function repositionNotifications() {
            activeNotifications.forEach((notification, index) => {
                const newTop = 20 + (index * (NOTIFICATION_HEIGHT + NOTIFICATION_MARGIN));
                notification.style.transition = 'top 0.3s ease';
                notification.style.top = `${newTop}px`;
            });
        }
        
        // Listen for file refresh events
        ipcRenderer.on('refresh-files', async () => {
            await initializeOverlay();
        });
        
                // Debug function to check current opacity values
        function debugOpacity() {
            const overlayContainer = document.querySelector('.overlay-container');
            const backgroundLayer = document.getElementById('backgroundLayer');
            const slider = document.getElementById('backgroundOpacity');
            
            console.log('=== Opacity Debug ===');
            console.log('Slider value:', slider.value + '%');
            console.log('Container background:', window.getComputedStyle(overlayContainer).backgroundColor);
            console.log('Background layer opacity:', window.getComputedStyle(backgroundLayer).opacity);
            
            if (currentBackgroundType === 'custom') {
                const calculatedOpacity = 1 - (slider.value / 100);
                console.log('Calculated custom opacity:', calculatedOpacity);
                console.log('Expected behavior: 0% = solid (1.0), 100% = transparent (0.0)');
            }
            
            console.log('Background layer classes:', backgroundLayer.className);
            console.log('Current background type:', currentBackgroundType);
            console.log('Is custom background:', currentBackgroundType === 'custom');
            
            // Check children
            const children = backgroundLayer.children;
            if (children.length > 0) {
                console.log('Background layer children:', children.length);
                for (let i = 0; i < Math.min(3, children.length); i++) {
                    console.log(`Child ${i} (${children[i].tagName}) opacity:`, window.getComputedStyle(children[i]).opacity);
                }
            }
        }
        
        // Initialize
        initializeOverlay();


    </script>
</body>
</html>