:root {
  --background-dark: #1a1a1a;
  --text-primary: #ffffff;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--background-dark);
  border-radius: 8px 8px 0 0;
  user-select: none;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.close-button {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 18px;
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
} 