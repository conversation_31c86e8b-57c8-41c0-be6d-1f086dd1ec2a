// ArmoryX Notifications and Direct Messaging System
document.addEventListener('DOMContentLoaded', function() {
    initializeNotificationSystem();
});

// Notification System
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;
        
        console.log('🔔 Initializing notification system...');
        
        // Create notification UI elements immediately
        this.createNotificationUI();
        
        // Load existing notifications in parallel
        this.loadNotifications();
        
        // Set up real-time listeners
        this.setupRealtimeListeners();
        
        this.isInitialized = true;
        console.log('✅ Notification system initialized');
    }

    createNotificationUI() {
        // Only show notifications if user is logged in
        if (!this.isUserLoggedIn()) {
            return;
        }

        // Add notification icon to navigation if it doesn't exist
        const nav = document.querySelector('.nav-menu');
        if (!nav || document.getElementById('notification-icon')) return;

        // Create account dropdown if it doesn't exist
        this.createAccountDropdown();

        const notificationItem = document.createElement('li');
        notificationItem.innerHTML = `
            <a href="#" class="nav-link" id="notification-icon" onclick="notificationManager.toggleNotificationPanel(event)">
                <i class="fas fa-bell"></i>
                <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
            </a>
        `;
        
        // Insert at the very end of nav menu for rightmost position
        nav.appendChild(notificationItem);

        // Create notification panel
        this.createNotificationPanel();
    }

    createAccountDropdown() {
        const nav = document.querySelector('.nav-menu');
        const accountLink = nav.querySelector('a[href="account.html"]');
        
        if (!accountLink || accountLink.closest('.nav-account-container')) return;

        // Get user info for dropdown
        const userEmail = this.getUserEmail();
        const userDisplayName = this.getUserDisplayName();

        // Wrap account link in container
        const accountContainer = document.createElement('div');
        accountContainer.className = 'nav-account-container';
        
        const accountParent = accountLink.parentElement;
        accountParent.parentNode.insertBefore(accountContainer, accountParent);
        accountContainer.appendChild(accountParent);

        // Create dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'account-dropdown';
        dropdown.innerHTML = `
            <div class="account-dropdown-header">
                <div class="account-dropdown-user">
                    <div class="account-dropdown-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="account-dropdown-info">
                        <h4>${userDisplayName || 'User'}</h4>
                        <p>${userEmail || 'Not logged in'}</p>
                    </div>
                </div>
            </div>
            <div class="account-dropdown-menu">
                <a href="account.html#profile" class="account-dropdown-item">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <button class="account-dropdown-item" onclick="notificationManager.openInbox()">
                    <i class="fas fa-inbox"></i>
                    <span>Inbox</span>
                </button>
                <button class="account-dropdown-item" onclick="notificationManager.openFriendsList()">
                    <i class="fas fa-users"></i>
                    <span>Friends</span>
                </button>
                <a href="account.html#settings" class="account-dropdown-item">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
                <div class="account-dropdown-divider"></div>
                <button class="account-dropdown-item account-dropdown-logout" onclick="notificationManager.handleLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        `;

        accountContainer.appendChild(dropdown);
    }

    getUserEmail() {
        if (window.firebase && window.firebase.auth && window.firebase.auth.currentUser) {
            return window.firebase.auth.currentUser.email;
        }
        return localStorage.getItem('armoryX_user_email') || '';
    }

    getUserDisplayName() {
        if (window.firebase && window.firebase.auth && window.firebase.auth.currentUser) {
            return window.firebase.auth.currentUser.displayName;
        }
        return localStorage.getItem('armoryX_display_name') || '';
    }

    openInbox() {
        // Redirect to account page with inbox tab
        window.location.href = 'account.html#inbox';
    }

    openFriendsList() {
        // Redirect to account page with friends tab
        window.location.href = 'account.html#friends';
    }

    async handleLogout() {
        try {
            if (window.firebase && window.firebase.auth) {
                const { auth, signOut } = window.firebase;
                await signOut(auth);
            } else {
                // Fallback logout
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('armoryX_user_token');
                localStorage.removeItem('armoryX_user_email');
                localStorage.removeItem('armoryX_display_name');
            }
            
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Logout error:', error);
            // Force logout anyway
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('armoryX_user_token');
            localStorage.removeItem('armoryX_user_email');
            localStorage.removeItem('armoryX_display_name');
            window.location.href = 'index.html';
        }
    }

    isUserLoggedIn() {
        // Check Firebase auth first
        if (window.firebase && window.firebase.auth) {
            const user = window.firebase.auth.currentUser;
            if (user) return true;
        }
        
        // Check localStorage for demo mode
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        return isLoggedIn;
    }

    createNotificationPanel() {
        if (document.getElementById('notification-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'notification-panel';
        panel.className = 'notification-panel';
        panel.innerHTML = `
            <div class="notification-header">
                <h3><i class="fas fa-bell"></i> Notifications</h3>
                <div class="notification-actions">
                    <button class="btn btn-small" onclick="notificationManager.markAllAsRead()">Mark All Read</button>
                    <button class="btn btn-small" onclick="notificationManager.clearAll()">Clear All</button>
                </div>
            </div>
            <div class="notification-list" id="notification-list">
                <div class="loading-message">Loading notifications...</div>
            </div>
            <div class="notification-footer">
                <a href="#" onclick="notificationManager.openNotificationSettings()">Notification Settings</a>
            </div>
        `;

        document.body.appendChild(panel);

        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#notification-panel') && !e.target.closest('#notification-icon')) {
                panel.style.display = 'none';
            }
        });
    }

    async loadNotifications() {
        try {
            // Check if Firebase is available and working
            if (await this.isFirebaseAvailable()) {
                console.log('📡 Loading notifications from Firebase...');
                const { auth, db, collection, query, where, orderBy, limit, getDocs } = window.firebase;
                const user = auth.currentUser;
                
                if (!user) {
                    console.log('❌ No user logged in, using localStorage');
                    this.loadFromLocalStorage();
                    return;
                }

                const notificationsQuery = query(
                    collection(db, 'notifications'),
                    where('userId', '==', user.uid),
                    orderBy('createdAt', 'desc'),
                    limit(50)
                );

                const snapshot = await getDocs(notificationsQuery);
                const allNotifications = [];
                
                snapshot.forEach((doc) => {
                    allNotifications.push({ id: doc.id, ...doc.data() });
                });
                
                // Filter out locally deleted notifications
                this.notifications = this.filterDeletedNotifications(allNotifications);
                console.log(`📋 Loaded ${allNotifications.length} notifications from Firebase, filtered to ${this.notifications.length}`);

            } else {
                console.log('🔄 Firebase unavailable, using localStorage');
                this.loadFromLocalStorage();
            }

            this.updateNotificationDisplay();
            this.updateUnreadCount();

        } catch (error) {
            console.log('❌ Error loading from Firebase, falling back to localStorage:', error.message);
            this.loadFromLocalStorage();
        }
    }

    async isFirebaseAvailable() {
        try {
            if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
                console.log('🔧 Firebase objects not available');
                return false;
            }
            
            const { auth, db, collection } = window.firebase;
            const user = auth.currentUser;
            
            if (!user) {
                console.log('🔧 No Firebase user logged in');
                return false;
            }
            
            // Try to access Firebase by attempting to get a collection reference
            // This will fail if Firebase is blocked by ad blockers or network issues
            const testRef = collection(db, 'notifications');
            if (!testRef) {
                console.log('🔧 Cannot access Firebase collections');
                return false;
            }
            
            return true;
        } catch (error) {
            console.log('🔧 Firebase connectivity test failed:', error.message);
            // Common errors when Firebase is blocked:
            // - ERR_BLOCKED_BY_CLIENT
            // - Network request failed
            // - Cannot read properties of undefined
            return false;
        }
    }

    loadFromLocalStorage() {
        try {
            const localNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
            this.notifications = this.filterDeletedNotifications(localNotifications);
            console.log(`📋 Loaded ${localNotifications.length} notifications from localStorage, filtered to ${this.notifications.length}`);
        } catch (localError) {
            console.error('Error loading local notifications:', localError);
            this.notifications = [];
        }
    }

    async addNotification(type, title, message, data = {}) {
        const now = new Date();
        const notification = {
            id: Date.now().toString(),
            type: type, // 'like', 'reply', 'dm', 'mention', 'system', 'friend_request'
            title: title,
            message: message,
            data: data,
            isRead: false,
            createdAt: now.toISOString() // Use ISO string for consistent parsing
        };

        try {
            if (await this.isFirebaseAvailable()) {
                console.log('📡 Adding notification to Firebase...');
                const { auth, db, collection, addDoc, serverTimestamp } = window.firebase;
                const user = auth.currentUser;
                
                if (!user) {
                    console.log('❌ No user logged in, storing locally');
                    this.addToLocalStorage(notification);
                    return;
                }

                const notificationData = {
                    ...notification,
                    userId: user.uid,
                    createdAt: serverTimestamp()
                };

                await addDoc(collection(db, 'notifications'), notificationData);
                console.log('✅ Notification added to Firebase');
            } else {
                console.log('🔄 Firebase unavailable, storing locally');
                this.addToLocalStorage(notification);
            }

            // Show toast notification
            this.showToastNotification(notification);
            
            // Update display
            this.updateNotificationDisplay();
            this.updateUnreadCount();

        } catch (error) {
            console.log('❌ Error adding to Firebase, storing locally:', error.message);
            this.addToLocalStorage(notification);
        }
    }

    addToLocalStorage(notification) {
        try {
            // Load existing notifications
            const existingNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
            
            // Add new notification to the beginning
            existingNotifications.unshift(notification);
            
            // Keep only the latest 50 notifications
            const trimmedNotifications = existingNotifications.slice(0, 50);
            
            // Save back to localStorage
            localStorage.setItem('armoryX_notifications', JSON.stringify(trimmedNotifications));
            
            // Update current notifications array
            this.notifications = this.filterDeletedNotifications(trimmedNotifications);
            
            console.log('📋 Notification stored locally');
        } catch (error) {
            console.error('Error storing notification locally:', error);
        }
    }

    showToastNotification(notification) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'notification-toast';
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${notification.title}</div>
                <div class="toast-message">${notification.message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(toast);

        // Show with animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            'like': 'heart',
            'reply': 'comment',
            'dm': 'envelope',
            'mention': 'at',
            'system': 'bell'
        };
        return icons[type] || 'bell';
    }

    toggleNotificationPanel(event) {
        event.preventDefault();
        const panel = document.getElementById('notification-panel');
        if (!panel) return;

        const isCurrentlyVisible = panel.style.display === 'block';
        panel.style.display = isCurrentlyVisible ? 'none' : 'block';
        
        if (!isCurrentlyVisible) {
            // Panel is now visible, load notifications immediately
            this.loadNotifications();
        }
    }

    updateNotificationDisplay() {
        const list = document.getElementById('notification-list');
        if (!list) return;

        // Extra safety: always filter deleted notifications before displaying
        const safeNotifications = this.filterDeletedNotifications(this.notifications);
        console.log(`🖥️ Updating display: ${this.notifications.length} notifications, ${safeNotifications.length} after safety filter`);
        
        if (safeNotifications.length === 0) {
            list.innerHTML = '<div class="no-notifications">No notifications yet</div>';
            return;
        }

        const notificationsHTML = safeNotifications.map(notification => {
            const timeAgo = this.formatTimeAgo(notification.createdAt);
            const isRead = notification.isRead;

            return `
                <div class="notification-item ${isRead ? 'read' : 'unread'}" data-id="${notification.id}">
                    <div class="notification-icon">
                        <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${timeAgo}</div>
                    </div>
                    <div class="notification-actions">
                        ${!isRead ? `<button class="mark-read-btn" onclick="notificationManager.markAsRead('${notification.id}')">
                            <i class="fas fa-check"></i>
                        </button>` : ''}
                        <button class="delete-notification-btn" onclick="notificationManager.deleteNotification('${notification.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        list.innerHTML = notificationsHTML;
    }

    updateUnreadCount() {
        // Extra safety: filter deleted notifications before counting
        const safeNotifications = this.filterDeletedNotifications(this.notifications);
        this.unreadCount = safeNotifications.filter(n => !n.isRead).length;
        
        console.log(`🔢 Unread count: ${this.unreadCount} (from ${safeNotifications.length} safe notifications)`);
        
        const badge = document.getElementById('notification-badge');
        if (!badge) return;

        if (this.unreadCount > 0) {
            badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount.toString();
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }

    async markAsRead(notificationId) {
        try {
            if (window.firebase && window.firebase.db) {
                const { db, doc, updateDoc } = window.firebase;
                await updateDoc(doc(db, 'notifications', notificationId), {
                    isRead: true
                });
            } else {
                // Update in localStorage
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification) {
                    notification.isRead = true;
                    localStorage.setItem('armoryX_notifications', JSON.stringify(this.notifications));
                }
            }

            this.updateNotificationDisplay();
            this.updateUnreadCount();

        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    async markAllAsRead() {
        // Mark all as read
        this.notifications.forEach(n => n.isRead = true);
        
        try {
            if (window.firebase && window.firebase.db) {
                // Update all in Firebase (batch operation would be better)
                const { db, doc, updateDoc } = window.firebase;
                const promises = this.notifications.map(n => 
                    updateDoc(doc(db, 'notifications', n.id), { isRead: true })
                );
                await Promise.all(promises);
            } else {
                localStorage.setItem('armoryX_notifications', JSON.stringify(this.notifications));
            }

            this.updateNotificationDisplay();
            this.updateUnreadCount();

        } catch (error) {
            console.error('Error marking all as read:', error);
        }
    }

    async clearAll() {
        // Use the custom confirmation dialog if available, otherwise fallback to confirm
        const confirmed = window.showConfirmDialog 
            ? await window.showConfirmDialog(
                'Clear All Notifications?',
                'Are you sure you want to clear all notifications? This action cannot be undone.',
                'Clear All',
                'Cancel',
                'danger'
              )
            : confirm('Are you sure you want to clear all notifications?');
            
        if (!confirmed) return;

        try {
            if (window.firebase && window.firebase.db) {
                // Delete all from Firebase
                const { db, doc, deleteDoc } = window.firebase;
                const promises = this.notifications.map(n => 
                    deleteDoc(doc(db, 'notifications', n.id))
                );
                await Promise.all(promises);
            } else {
                localStorage.removeItem('armoryX_notifications');
            }

            this.notifications = [];
            this.updateNotificationDisplay();
            this.updateUnreadCount();

        } catch (error) {
            console.error('Error clearing notifications:', error);
        }
    }

    async deleteNotification(notificationId) {
        try {
            console.log('🗑️ Deleting notification:', notificationId);
            
            // Always mark as deleted locally first for immediate UI feedback
            this.markAsDeletedLocally(notificationId);
            
            // Immediately update UI to reflect deletion
            this.updateNotificationDisplay();
            this.updateUnreadCount();
            
            // Try Firebase deletion in background (won't affect UI if it fails)
            if (await this.isFirebaseAvailable()) {
                try {
                    console.log('🗑️ Attempting to delete from Firebase...');
                    const { db, doc, deleteDoc } = window.firebase;
                    await deleteDoc(doc(db, 'notifications', notificationId));
                    console.log('✅ Notification deleted from Firebase successfully');
                } catch (firebaseError) {
                    console.log('🔧 Firebase deletion failed, but local deletion is sufficient:', firebaseError.message);
                }
            } else {
                console.log('🔄 Firebase unavailable, using local deletion only');
                this.removeFromLocalStorage(notificationId);
            }

        } catch (error) {
            console.log('❌ Error in deletion process:', error.message);
            // Ensure UI reflects deletion even if there are errors
            this.updateNotificationDisplay();
            this.updateUnreadCount();
        }
    }

    removeFromLocalStorage(notificationId) {
        try {
            const existingNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
            const filteredNotifications = existingNotifications.filter(n => n.id !== notificationId);
            localStorage.setItem('armoryX_notifications', JSON.stringify(filteredNotifications));
            console.log('📋 Notification removed from localStorage');
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    }

    markAsDeletedLocally(notificationId) {
        console.log('🔒 Marking notification as deleted locally:', notificationId);
        
        // Remove from current notifications array immediately
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        
        // Store deleted notification IDs to hide them permanently
        const deletedIds = JSON.parse(localStorage.getItem('armoryX_deleted_notifications') || '[]');
        if (!deletedIds.includes(notificationId)) {
            deletedIds.push(notificationId);
            localStorage.setItem('armoryX_deleted_notifications', JSON.stringify(deletedIds));
            console.log('✅ Notification marked as deleted locally:', notificationId);
            
            // Also store timestamp for cleanup purposes
            const deletionRecord = {
                id: notificationId,
                deletedAt: new Date().toISOString()
            };
            
            const deletionHistory = JSON.parse(localStorage.getItem('armoryX_deletion_history') || '[]');
            deletionHistory.push(deletionRecord);
            // Keep only last 100 deletions to prevent localStorage bloat
            if (deletionHistory.length > 100) {
                deletionHistory.splice(0, deletionHistory.length - 100);
            }
            localStorage.setItem('armoryX_deletion_history', JSON.stringify(deletionHistory));
        } else {
            console.log('🔍 Notification already marked as deleted:', notificationId);
        }
    }

    filterDeletedNotifications(notifications) {
        const deletedIds = JSON.parse(localStorage.getItem('armoryX_deleted_notifications') || '[]');
        return notifications.filter(notification => !deletedIds.includes(notification.id));
    }

    clearLocalDeletions() {
        localStorage.removeItem('armoryX_deleted_notifications');
        console.log('🧹 Cleared locally deleted notifications list');
        this.loadNotifications(); // Reload to show any previously hidden notifications
    }

    formatTimeAgo(date) {
        try {
            if (!date) return 'Just now';
            
            const now = new Date();
            let time;
            
            // Handle different date formats
            if (date instanceof Date) {
                time = date;
            } else if (date.toDate && typeof date.toDate === 'function') {
                // Firebase Timestamp
                time = date.toDate();
            } else if (typeof date === 'string' || typeof date === 'number') {
                time = new Date(date);
            } else {
                console.warn('Unknown date format:', date);
                return 'Just now';
            }
            
            // Validate the date
            if (isNaN(time.getTime())) {
                console.warn('Invalid date:', date);
                return 'Just now';
            }
            
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
            
            return time.toLocaleDateString();
        } catch (error) {
            console.warn('Error formatting time:', error, date);
            return 'Just now';
        }
    }

    setupRealtimeListeners() {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) return;

        const { auth, db, collection, query, where, onSnapshot, orderBy } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) return;

        try {
            // Listen for new notifications with error handling
            const notificationsQuery = query(
                collection(db, 'notifications'),
                where('userId', '==', user.uid),
                orderBy('createdAt', 'desc')
            );

            onSnapshot(notificationsQuery, (snapshot) => {
                console.log('🔄 Real-time notification update received');
                
                const allNotifications = [];
                
                snapshot.forEach((doc) => {
                    allNotifications.push({ id: doc.id, ...doc.data() });
                });
                
                // Filter out locally deleted notifications BEFORE updating anything
                const filteredNotifications = this.filterDeletedNotifications(allNotifications);
                console.log(`🔄 Real-time update: ${allNotifications.length} total, ${filteredNotifications.length} after filtering`);
                
                // Only update if there are actual changes to prevent unnecessary UI updates
                const currentIds = this.notifications.map(n => n.id).sort();
                const newIds = filteredNotifications.map(n => n.id).sort();
                
                if (JSON.stringify(currentIds) !== JSON.stringify(newIds)) {
                    console.log('📝 Notification list changed, updating display');
                    this.notifications = filteredNotifications;
                    this.updateNotificationDisplay();
                    this.updateUnreadCount();
                } else {
                    console.log('📝 No changes in notification list, skipping update');
                }
            }, (error) => {
                console.log('🔕 Notifications listener error (using fallback):', error.code);
                // Fallback to localStorage for notifications
                const localNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
                this.notifications = this.filterDeletedNotifications(localNotifications);
                this.updateNotificationDisplay();
                this.updateUnreadCount();
            });
        } catch (error) {
            console.log('🔕 Error setting up notifications listener (using fallback):', error.code);
            // Use localStorage fallback
            const localNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
            this.notifications = this.filterDeletedNotifications(localNotifications);
            this.updateNotificationDisplay();
            this.updateUnreadCount();
        }
    }

    openNotificationSettings() {
        // This would open a settings modal - placeholder for now
        alert('Notification settings coming soon!');
    }

    hideNotifications() {
        // Remove notification icon and panel
        const notificationIcon = document.getElementById('notification-icon');
        if (notificationIcon) {
            notificationIcon.parentElement.remove();
        }
        
        const notificationPanel = document.getElementById('notification-panel');
        if (notificationPanel) {
            notificationPanel.remove();
        }
        
        // Reset initialization flag
        this.isInitialized = false;
    }
}

// Initialize global notification manager
const notificationManager = new NotificationManager();

// Add notification for a specific user (for cross-user notifications like replies)
async function addNotificationForUser(targetUserId, type, title, message, data = {}) {
    if (!targetUserId) {
        console.warn('No target user ID provided for notification');
        return;
    }

    const now = new Date();
    const notification = {
        id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9),
        type: type,
        title: title,
        message: message,
        data: data,
        isRead: false,
        createdAt: now.toISOString()
    };

    try {
        if (window.firebase && window.firebase.db) {
            console.log(`📡 Adding notification for user ${targetUserId}...`);
            const { db, collection, addDoc, serverTimestamp } = window.firebase;
            
            const notificationData = {
                ...notification,
                userId: targetUserId, // Send to specific target user
                createdAt: serverTimestamp()
            };

            await addDoc(collection(db, 'notifications'), notificationData);
            console.log(`✅ Notification added for user ${targetUserId}`);
        } else {
            console.log('🔄 Firebase unavailable, cannot send cross-user notification');
        }

    } catch (error) {
        console.log('❌ Error adding notification for user:', error.message);
    }
}

// Make the function globally available
window.addNotificationForUser = addNotificationForUser;

function initializeNotificationSystem() {
    // Check login status immediately to prevent layout shift
    if (notificationManager.isUserLoggedIn()) {
        notificationManager.initialize();
    }
    
    // Wait for Firebase to be ready if available
    if (window.firebase && window.firebase.auth) {
        // Listen for auth state changes
        window.firebase.auth.onAuthStateChanged((user) => {
            if (user) {
                // User is logged in, show notifications immediately
                if (!document.getElementById('notification-icon')) {
                    notificationManager.initialize();
                }
            } else {
                // User is logged out, hide notifications
                notificationManager.hideNotifications();
            }
        });
    } else {
        // For demo mode, check again every 500ms for login state changes (very responsive)
        setInterval(() => {
            const isLoggedIn = notificationManager.isUserLoggedIn();
            const bellExists = document.getElementById('notification-icon');
            
            if (isLoggedIn && !bellExists) {
                notificationManager.initialize();
            } else if (!isLoggedIn && bellExists) {
                notificationManager.hideNotifications();
            }
        }, 500);
    }
}

// Helper functions for adding notifications from other parts of the app
window.addNotification = (type, title, message, data) => {
    notificationManager.addNotification(type, title, message, data);
};

// Debug functions
window.clearDeletedNotifications = () => {
    notificationManager.clearLocalDeletions();
};

window.debugNotifications = () => {
    console.log('🔧 Notification Debug Info:');
    console.log('Current notifications:', notificationManager.notifications);
    console.log('Deleted notification IDs:', JSON.parse(localStorage.getItem('armoryX_deleted_notifications') || '[]'));
    console.log('Local notifications:', JSON.parse(localStorage.getItem('armoryX_notifications') || '[]'));
    console.log('Unread count:', notificationManager.unreadCount);
    
    // Test Firebase connectivity
    notificationManager.isFirebaseAvailable().then(available => {
        console.log('Firebase available:', available);
    });
};

window.testFriendRequestNotification = () => {
    console.log('🧪 Creating test friend request notification...');
    notificationManager.addNotification(
        'friend_request',
        'Test Friend Request',
        'Test User sent you a friend request',
        { fromUserId: 'test123' }
    );
};

window.forceDeleteNotification = (notificationId) => {
    console.log('🔨 Force deleting notification:', notificationId);
    
    // Get current deleted IDs
    const deletedIds = JSON.parse(localStorage.getItem('armoryX_deleted_notifications') || '[]');
    
    // Add to deleted list if not already there
    if (!deletedIds.includes(notificationId)) {
        deletedIds.push(notificationId);
        localStorage.setItem('armoryX_deleted_notifications', JSON.stringify(deletedIds));
    }
    
    // Remove from current notifications
    notificationManager.notifications = notificationManager.notifications.filter(n => n.id !== notificationId);
    
    // Remove from localStorage notifications
    const localNotifications = JSON.parse(localStorage.getItem('armoryX_notifications') || '[]');
    const filteredLocal = localNotifications.filter(n => n.id !== notificationId);
    localStorage.setItem('armoryX_notifications', JSON.stringify(filteredLocal));
    
    // Update display
    notificationManager.updateNotificationDisplay();
    notificationManager.updateUnreadCount();
    
    console.log('✅ Notification force deleted');
};

window.checkNotificationState = () => {
    console.log('🔍 Current Notification State:');
    console.log('Manager notifications:', notificationManager.notifications.map(n => ({ id: n.id, title: n.title })));
    console.log('Deleted IDs:', JSON.parse(localStorage.getItem('armoryX_deleted_notifications') || '[]'));
    console.log('Local storage notifications:', JSON.parse(localStorage.getItem('armoryX_notifications') || '[]').map(n => ({ id: n.id, title: n.title })));
    console.log('DOM notifications:', Array.from(document.querySelectorAll('.notification-item')).map(el => el.dataset.id));
    console.log('Unread count:', notificationManager.unreadCount);
    console.log('Badge display:', document.getElementById('notification-badge')?.style.display);
    console.log('Badge text:', document.getElementById('notification-badge')?.textContent);
};

// Auto-generate notifications for demo purposes
function createDemoNotifications() {
    if (localStorage.getItem('armoryX_demo_notifications_created')) return;
    
    console.log('🎭 Creating demo notifications...');

    const demoNotifications = [
        {
            type: 'like',
            title: 'Post Liked',
            message: 'ArmoryDev liked your post "Widget loading slowly on high-res displays"',
            data: { postId: '123' }
        },
        {
            type: 'reply',
            title: 'New Reply',
            message: 'TechHelper replied to your post in Bug Reports',
            data: { postId: '123', replyId: '456' }
        },
        {
            type: 'system',
            title: 'Welcome to ArmoryX Forums!',
            message: 'Thanks for joining our community. Check out the Getting Started guide.',
            data: {}
        }
    ];

    // Add demo notifications with a delay to avoid overwhelming the system
    demoNotifications.forEach((notification, index) => {
        setTimeout(() => {
            notificationManager.addNotification(
                notification.type,
                notification.title,
                notification.message,
                notification.data
            );
        }, index * 100); // 100ms delay between each notification
    });

    localStorage.setItem('armoryX_demo_notifications_created', 'true');
}

// Initialize demo notifications immediately when notifications are ready
document.addEventListener('DOMContentLoaded', function() {
    // Only create demo notifications if user is logged in
    setTimeout(() => {
        if (notificationManager.isUserLoggedIn() && notificationManager.isInitialized) {
            createDemoNotifications();
        }
    }, 100);
});

// Force refresh notifications when user logs in (for immediate response)
window.addEventListener('storage', function(e) {
    if (e.key === 'isLoggedIn') {
        if (e.newValue === 'true' && !document.getElementById('notification-icon')) {
            // User just logged in, initialize notifications immediately
            setTimeout(() => notificationManager.initialize(), 50);
        } else if (e.newValue === 'false' && document.getElementById('notification-icon')) {
            // User just logged out, hide notifications immediately
            notificationManager.hideNotifications();
        }
    }
}); 