# 🛡️ HWID Spoofer Feature - Armory X

## Overview

The HWID (Hardware ID) Spoofer is a privacy protection tool integrated into Armory X that helps protect your system's hardware fingerprint from malicious actors who might try to track or identify your device.

## ⚠️ Important Notice

**This tool is designed for defensive privacy protection purposes only.** It helps protect against:
- Malicious players who obtain and misuse hardware identifiers
- Unwanted device tracking and profiling
- Hardware fingerprinting by bad actors

## 🔧 Features

### Core Functionality
- **System GUID Spoofing**: Changes Windows system machine GUID
- **Hardware Profile Spoofing**: Modifies hardware profile identifiers
- **Boot ID Spoofing**: Changes system boot identification
- **System UUID Spoofing**: Alters system UUID values
- **Hardware Hash Spoofing**: Modifies hardware hash identifiers

### Safety Features
- **Automatic Backup**: Creates backup of original values before any changes
- **One-Click Restore**: Easy restoration of original hardware IDs
- **Status Monitoring**: Real-time status of spoofing state
- **Error Handling**: Comprehensive error reporting and recovery

### Advanced Options
- **System Information Display**: View current hardware information
- **MAC Address Detection**: Lists network adapters (spoofing requires additional privileges)
- **Backup Management**: View and manage backup status

## 🚀 How to Use

### Basic Usage

1. **Open Armory X** and navigate to the **Tools** tab
2. **Locate the HWID Spoofer panel** (🛡️ icon)
3. **Create a backup first** (recommended):
   - Click "Create Backup" button
   - Wait for confirmation message
4. **Enable spoofing**:
   - Click "Enable HWID Spoofing"
   - Review any warnings or prompts
   - Wait for completion confirmation

### Advanced Usage

When HWID spoofing is active, additional options become available:

- **Restore Original Values**: Immediately restore your original hardware IDs
- **View System Info**: Display detailed system and spoofer status information  
- **Spoof MAC Addresses**: Advanced network adapter spoofing (requires elevated privileges)

### Restoring Original Values

To restore your original hardware identifiers:

1. **Click "Restore Original Values"** in the advanced options
2. **Confirm the action** in the dialog
3. **Wait for the restoration** to complete
4. **Restart applications** that might cache hardware information

## 🛠️ Technical Details

### Registry Keys Modified

The HWID spoofer modifies the following Windows Registry locations:

- `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography\MachineGuid`
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001\HwProfileGuid`
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation\ComputerHardwareId`
- `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\BuildGUID`
- `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation\ComputerHardwareIds`

### Backup Location

Backups are stored in:
```
%APPDATA%\ArmoryX\hwid-backup.json
```

This file contains:
- Original hardware ID values
- Backup timestamp
- System information snapshot
- Restoration metadata

### Randomization Algorithm

- **GUIDs**: Uses crypto.randomUUID() for RFC 4122 compliant UUIDs
- **Hardware IDs**: Generates alphanumeric strings using secure randomization
- **System IDs**: Creates Windows-compatible identifier formats

## 🔒 Security & Privacy

### Data Protection
- **Local Storage Only**: All backups stored locally on your machine
- **No Network Communication**: No hardware IDs sent over network
- **Secure Randomization**: Uses cryptographically secure random number generation
- **Reversible Changes**: All modifications can be completely undone

### Permissions Required
- **Administrator Rights**: Required for registry modifications
- **Elevated Privileges**: Some features may require running as administrator

## ⚡ Performance Impact

- **Minimal Resource Usage**: Low CPU and memory footprint
- **Instant Changes**: Hardware ID changes take effect immediately
- **Application Restart**: Some games/applications may need restart to detect changes
- **System Restart**: Not required for basic HWID spoofing

## 🎮 Gaming Compatibility

### Supported Scenarios
- **Online Gaming**: Protects against HWID-based tracking
- **Anti-Cheat Systems**: Compatible with most anti-cheat (when used for legitimate privacy)
- **Game Clients**: Works with Steam, Epic Games, etc.

### Important Notes
- **Game Restarts**: Most games need restart to see new hardware IDs
- **Save Data**: Game save data typically unaffected
- **Account Linking**: Existing account associations remain intact

## 🚨 Troubleshooting

### Common Issues

#### "Access Denied" Errors
**Solution**: Run Armory X as Administrator
- Right-click Armory X shortcut
- Select "Run as administrator"
- Try the operation again

#### "Backup Failed" Errors
**Solution**: Check file permissions and disk space
- Ensure %APPDATA% folder is writable
- Verify sufficient disk space
- Check antivirus isn't blocking file creation

#### "Registry Modification Failed"
**Solution**: Administrative privileges required
- Ensure running as administrator
- Check Windows User Account Control settings
- Verify registry isn't locked by other software

### Recovery Procedures

#### Manual Restore (Emergency)
If automatic restore fails:

1. **Locate backup file**: `%APPDATA%\ArmoryX\hwid-backup.json`
2. **Open Windows Registry Editor** (regedit.exe as administrator)
3. **Navigate to each registry key** listed in the backup
4. **Restore original values** from the backup file
5. **Restart affected applications**

#### Backup File Recovery
If backup file is corrupted:
- Check `%APPDATA%\ArmoryX\` for backup copies
- Windows System Restore can revert registry changes
- Contact support for advanced recovery options

## 📖 FAQ

### Q: Is this legal to use?
**A**: Yes, when used for legitimate privacy protection. You own your computer and have the right to modify your own system identifiers for privacy purposes.

### Q: Will this affect my Windows license?
**A**: No, the modified identifiers do not affect Windows licensing or activation.

### Q: Can I use this with multiple games?
**A**: Yes, the spoofed hardware IDs work system-wide across all applications.

### Q: How often should I change my HWID?
**A**: Only when needed for privacy protection. Frequent changes are unnecessary and may cause confusion.

### Q: Will this protect me from all tracking?
**A**: This protects against hardware ID tracking specifically. Other forms of tracking (IP, browser fingerprints, etc.) require additional protection.

## 🔄 Version History

### v1.0.0 (Current)
- Initial HWID spoofer implementation
- Registry-based hardware ID modification
- Automatic backup and restore system
- System information display
- MAC address detection
- Professional UI integration

## 🆘 Support

If you encounter issues with the HWID spoofer:

1. **Check this documentation** for troubleshooting steps
2. **Verify administrator privileges** are granted
3. **Review error messages** for specific guidance
4. **Create a backup** before attempting fixes
5. **Contact support** if issues persist

## ⚖️ Legal Disclaimer

This tool is provided for legitimate privacy protection purposes. Users are responsible for compliance with local laws and terms of service of applications they use. The developers are not responsible for misuse of this software.

---

**Remember**: Always create a backup before enabling HWID spoofing, and only use this tool for legitimate privacy protection purposes. 