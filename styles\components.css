/* ===================================
   Reusable Components
   =================================== */

/* Buttons */
.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    min-width: 150px;
    justify-content: center;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.4);
}

.btn-secondary {
    background: var(--background-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #555, #444);
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: #fff;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f57c00, #e65100);
    transform: translateY(-2px);
}

.btn-browse {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.btn-browse:hover {
    background: rgba(0, 180, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 180, 255, 0.4);
}

.btn-cancel, .btn-confirm {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-cancel {
    background: var(--background-hover);
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.btn-confirm {
    background: var(--primary-color);
    color: white;
}

.btn-confirm:hover:not(:disabled) {
    background: rgba(0, 180, 255, 0.8);
    transform: translateY(-1px);
}

.btn-confirm:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 1.1rem;
}

.add-shortcut-btn, .refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.add-shortcut-btn:hover, .refresh-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.refresh-btn {
    padding: 0.5rem;
    font-size: 1rem;
}

.edit-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-primary);
    font-size: 1rem;
}

.edit-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.clock-edit-btn {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-primary);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.clock-edit-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.3);
}

/* Cards */
.action-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transition: var(--transition-smooth);
}

.action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.action-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.action-card p {
    color: var(--text-secondary);
    margin: 0;
    flex: 1;
}

.status-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    background: var(--success-color);
    color: var(--background-dark);
}

/* Progress Bars */
.progress-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-hover);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width var(--transition-smooth);
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Toggle Switches */
.toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    flex-shrink: 0;
    min-width: 44px;
    max-width: 44px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-secondary, #333);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    border-radius: 24px;
    width: 44px !important;
    height: 24px !important;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px !important;
    width: 18px !important;
    left: 2px;
    bottom: 2px;
    background-color: var(--text-secondary);
    transition: var(--transition-fast);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
    background-color: white;
}

.toggle:hover .slider {
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

/* Form Elements */
.setting-input {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary, var(--background-hover));
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 100%;
}

.setting-input:hover {
    border-color: var(--primary-color);
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.1);
}

.setting-select {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary, var(--background-hover));
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 100%;
}

.setting-select:hover {
    border-color: var(--primary-color);
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.1);
}

/* Checkboxes */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    position: relative;
    font-size: 0.9rem;
}

.checkbox-label.compact {
    padding-left: 1.5rem;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 14px;
    width: 14px;
    background: var(--background-hover);
    border: 2px solid var(--border-color);
    border-radius: 3px;
    transition: var(--transition-fast);
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
}

.checkbox-label input:checked ~ .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-label .checkmark:after {
    left: 3px;
    top: 0px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Range Sliders */
input[type="range"] {
    width: 100%;
    height: 4px;
    background: var(--background-hover);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    margin-top: 0.25rem;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
    transition: var(--transition-fast);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
}

/* Loading and Empty States */
.loading-recent {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 3rem 1rem;
}

.empty-apps {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 3rem 1rem;
}

.placeholder {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem;
    background: var(--background-card);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}
