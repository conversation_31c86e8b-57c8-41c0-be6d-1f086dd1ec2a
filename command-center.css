/* CSS Variables matching the website theme */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #1e293b;
    --accent-color: #06b6d4;
    --text-primary: #ffffff;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    --bg-primary: rgba(15, 23, 42, 0.95);
    --bg-secondary: rgba(30, 41, 59, 0.9);
    --bg-tertiary: rgba(51, 65, 85, 0.8);
    --border-color: rgba(71, 85, 105, 0.5);
    --gradient-primary: linear-gradient(135deg, #3b82f6, #06b6d4);
    --gradient-secondary: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.8));
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --border-radius: 12px;
    --transition-fast: 0.2s ease;
    --transition-smooth: 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100vh;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: transparent;
    color: var(--text-primary);
    user-select: none;
    -webkit-font-smoothing: antialiased;
}

/* Background Effects */
.bg-effects {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.6;
}

.particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.15) 0%, transparent 50%);
    animation: particleFloat 15s infinite linear;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: gridMove 20s infinite linear;
}

@keyframes particleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-50px) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(30px, 30px); }
}

/* Command Center Main Container */
.command-center {
    width: 100vw;
    height: 100vh;
    background: var(--bg-primary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.command-center::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.5;
}

/* Header */
.cc-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    -webkit-app-region: drag;
}

.cc-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    -webkit-app-region: no-drag;
}

.cc-logo-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
}

.cc-title {
    font-weight: 600;
    font-size: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.cc-search-container {
    flex: 1;
    max-width: 300px;
    -webkit-app-region: no-drag;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 12px;
}

#mod-search {
    padding: 8px 12px 8px 36px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 250px;
    transition: var(--transition-smooth);
}

#mod-search:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    pointer-events: none;
}

.search-clear {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition-fast);
}

.search-clear:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.cc-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    font-size: 1.2rem;
    transition: var(--transition-fast);
    -webkit-app-region: no-drag;
}

.cc-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Content Area */
.cc-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Category Tabs */
.cc-tabs {
    display: flex;
    padding: 16px 20px 0;
    gap: 4px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.cc-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    border: none;
    background: transparent;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
}

.cc-tab:hover {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.cc-tab.active {
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow: 0 -2px 0 var(--primary-color);
}

.tab-icon {
    font-size: 0.9rem;
}

.tab-label {
    font-weight: 500;
}

/* Items Container */
.cc-items-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    position: relative;
}

.cc-items-container::-webkit-scrollbar {
    width: 6px;
}

.cc-items-container::-webkit-scrollbar-track {
    background: transparent;
}

.cc-items-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.cc-items-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Loading State */
.cc-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 16px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cc-loading p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Items Grid */
.cc-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Individual Item */
.cc-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.cc-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.cc-item:active {
    transform: translateY(0);
}

.cc-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-smooth);
}

.cc-item:hover::before {
    opacity: 1;
}

.item-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.item-name {
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    color: var(--text-primary);
    word-break: break-word;
    max-width: 100%;
}

.item-type {
    font-size: 0.7rem;
    color: var(--text-muted);
    margin-top: 2px;
}

/* Empty State */
.cc-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 12px;
}

.empty-icon {
    font-size: 3rem;
    opacity: 0.5;
}

.cc-empty h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.cc-empty p {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-align: center;
    max-width: 300px;
}

/* Footer */
.cc-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.cc-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.separator {
    opacity: 0.5;
}

.cc-actions {
    display: flex;
    gap: 4px;
}

.cc-action-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 6px;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.cc-action-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.action-icon {
    font-size: 0.9rem;
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 4px;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    z-index: 1000;
    min-width: 180px;
}

.context-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: 4px;
    font-size: 0.85rem;
    width: 100%;
    text-align: left;
    transition: var(--transition-fast);
}

.context-item:hover {
    background: var(--bg-tertiary);
}

.context-item.danger {
    color: #ef4444;
}

.context-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
}

.context-icon {
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
}

.context-divider {
    height: 1px;
    background: var(--border-color);
    margin: 4px 8px;
}

/* Animations */
.cc-item {
    animation: itemFadeIn 0.3s ease-out;
}

@keyframes itemFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Modal Dialogs */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    padding: 24px;
    max-height: 50vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input[type="file"] {
    width: 100%;
    padding: 12px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.form-group input[type="file"]:hover {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
}

.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.file-info {
    margin-top: 8px;
}

.file-info small {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Selected Files Preview */
.files-preview {
    margin-top: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.files-preview h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.file-name {
    color: var(--text-primary);
    font-weight: 500;
    flex: 1;
}

.file-size {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-left: 12px;
}

.btn-remove {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: var(--transition-smooth);
}

.btn-remove:hover {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

/* Delete Dialog Specific Styles */
.mod-to-delete {
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin: 16px 0;
}

.mod-to-delete strong {
    color: var(--text-primary);
    font-size: 1.1rem;
}

.mod-to-delete small {
    color: var(--text-secondary);
    font-size: 0.85rem;
    word-break: break-all;
}

.warning-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    color: var(--error-color);
    font-size: 0.9rem;
    margin-top: 16px;
}

/* Button Styles */
.btn-danger {
    background: var(--error-color);
    border: 1px solid var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Enhanced mod controls layout */
.mod-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

/* Responsive design for modals */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
    
    #mod-search {
        width: 200px;
    }
    
    .mod-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .file-size {
        margin-left: 0;
    }
}

/* Responsive Design */
@media (max-width: 600px) {
    .cc-header {
        padding: 12px 16px;
    }
    
    .cc-search-container {
        max-width: 200px;
    }
    
    .cc-tabs {
        padding: 12px 16px 0;
    }
    
    .cc-items-container {
        padding: 16px;
    }
    
    .cc-items-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .cc-footer {
        padding: 10px 16px;
    }
    
    .add-files-modal {
        width: 95vw;
        max-height: 90vh;
    }
    
    .modal-body {
        padding: 16px;
    }
}

/* Settings Modal */
.settings-modal {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    width: 550px;
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    animation: slideIn 0.3s ease-out;
}

.settings-sections {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.setting-section {
    padding: 0;
}

.setting-section h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 6px;
    cursor: pointer;
}

.setting-item input[type="number"],
.setting-item select {
    width: 100%;
    padding: 8px 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition-fast);
}

.setting-item input[type="number"]:focus,
.setting-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
    accent-color: var(--primary-color);
}

.setting-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-row input[type="number"] {
    width: auto;
    flex: 1;
}

.setting-row span {
    color: var(--text-muted);
    font-weight: 500;
}

/* Checkbox Labels */
.setting-item label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0;
}

/* Game Selector States */
.game-selector.compact {
    padding: 10px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.game-selector.compact .game-grid {
    display: flex;
    gap: 8px;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.game-selector.compact .game-card {
    min-width: 120px;
    padding: 8px;
    transform: scale(0.8);
    transition: var(--transition-smooth);
}

.game-selector.compact .game-card img {
    height: 60px;
    margin-bottom: 0.5rem;
}

.game-selector.compact .game-card h4 {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.game-selector.compact .game-card p {
    font-size: 0.7rem;
    display: none; /* Hide description in compact mode */
}

.game-card.selected {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.game-card.selected::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Enhanced Mod Category Tabs */
.mod-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 24px;
    padding: 0 0 0 0;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.mod-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    white-space: nowrap;
    position: relative;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 6px 6px 0 0;
}

.mod-tab:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.mod-tab.active {
    background: var(--bg-secondary);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.mod-tab.active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    animation: tabSlideIn 0.3s ease;
}

@keyframes tabSlideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

.tab-icon {
    font-size: 1rem;
}

.tab-label {
    font-weight: 600;
}

.tab-count {
    background: var(--border-color);
    color: var(--text-primary);
    padding: 0.15rem 0.45rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1.2rem;
    text-align: center;
}

.mod-tab.active .tab-count {
    background: var(--primary-color);
    color: white;
}

/* Tab Content Management */
.mod-tab-contents {
    position: relative;
    min-height: 300px;
}

.mod-tab-content {
    display: none;
    padding: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.mod-tab-content.active {
    display: block;
}

.mod-category-info {
    margin-bottom: 1.5rem;
    padding: 0.75rem 1rem;
    background: var(--bg-tertiary);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.mod-category-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-style: italic;
    line-height: 1.4;
}

/* Mod List Content */
.mod-list-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mod-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: var(--transition-smooth);
}

.mod-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.mod-info {
    flex: 1;
}

.mod-info h4 {
    margin: 0 0 0.4rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.mod-info p {
    margin: 0 0 0.2rem 0;
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.mod-path {
    color: var(--text-muted);
    font-size: 0.7rem;
    font-family: 'Consolas', 'Courier New', monospace;
    opacity: 0.8;
    word-break: break-all;
}

.mod-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-icon {
    font-size: 1rem;
}

/* Animation for smooth transitions */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mod-tabs {
    animation: slideInFromTop 0.4s ease-out;
}

.mods-grid {
    animation: fadeInUp 0.5s ease-out;
}

/* Enhanced Category Descriptions */
.category-description {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.category-description p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Improved Mods Grid (for card-style display) */
.mods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.mod-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 16px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.mod-card:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.mod-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-smooth);
}

.mod-card:hover::before {
    opacity: 1;
}

.mod-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
    line-height: 1.3;
    word-break: break-word;
}

.mod-size,
.mod-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 2px 0;
}

.mod-action-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mod-action-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.mod-action-btn svg {
    width: 16px;
    height: 16px;
}

/* Button Styles for Modal Close */
.btn-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition-smooth);
}

.btn-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.btn-tertiary {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-tertiary:hover {
    background: var(--bg-secondary);
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* Responsive design additions */

/* Delete Mode and Edit Button Styles */

/* Delete mode styling for game cards */
.steam-game-card.delete-mode {
    border: 2px solid #ef4444 !important;
    background: rgba(239, 68, 68, 0.1) !important;
    transform: scale(0.98);
    transition: all 0.3s ease;
}

.steam-game-card.delete-mode:hover {
    border-color: #dc2626 !important;
    background: rgba(239, 68, 68, 0.2) !important;
    transform: scale(1);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.steam-game-card.delete-mode .game-title {
    color: #fca5a5 !important;
}

.steam-game-card.delete-mode .game-description {
    color: #f87171 !important;
}

/* Edit button styling */
.edit-button {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 32px;
    height: 32px;
    background: rgba(59, 130, 246, 0.9);
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.edit-button:hover {
    background: rgba(59, 130, 246, 1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.edit-icon {
    font-size: 14px;
    color: white;
}

/* Delete button styling for delete mode */
.delete-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 48px;
    height: 48px;
    background: rgba(239, 68, 68, 0.9);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.delete-button:hover {
    background: rgba(220, 38, 38, 1);
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
}

.delete-icon {
    font-size: 18px;
    color: white;
}

/* Show edit button on hover for non-preset games */
.steam-game-card:not(.preset-game):not(.delete-mode):hover .edit-button {
    opacity: 1;
    transform: translateY(0);
}

/* Delete game confirmation modal styling */
.delete-game-content {
    text-align: center;
    padding: 20px;
}

.delete-game-content .warning-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.delete-message {
    margin-bottom: 16px;
}

.delete-message p {
    margin: 8px 0;
    line-height: 1.5;
}

.delete-message strong {
    color: var(--primary-color);
}

/* Button styling improvements for modals */
.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Ensure buttons have proper spacing and sizing */
.modal-footer .btn {
    min-width: 100px;
    padding: 10px 20px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* Professional Edit Button */
.edit-button-professional {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
    border: 1px solid rgba(59, 130, 246, 0.8);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(-6px);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 70px;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.edit-button-professional:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(37, 99, 235, 1));
    border-color: rgba(59, 130, 246, 1);
    transform: translateY(-8px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.edit-button-professional .edit-icon {
    font-size: 1rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.edit-button-professional .edit-text {
    font-size: 0.85rem;
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Delete Mode Red Overlay */
.delete-mode-indicator-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.95), rgba(185, 28, 28, 0.95));
    border: 1px solid rgba(239, 68, 68, 0.8);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 90px;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    animation: pulseRed 2s infinite;
}

@keyframes pulseRed {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.5);
    }
}

.delete-mode-indicator-overlay .delete-icon {
    font-size: 1rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.delete-mode-indicator-overlay .delete-text {
    font-size: 0.85rem;
    color: white;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}



/* Global Delete Mode Styling */
body.delete-mode-active .steam-game-card:not(.preset-game) {
    border-color: #dc2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.3);
}

body.delete-mode-active .steam-game-card.preset-game {
    opacity: 0.4;
    pointer-events: none;
    filter: grayscale(0.7);
    transition: all 0.3s ease;
}

body.delete-mode-active .steam-game-card.preset-game .game-title {
    color: #6b7280 !important;
}

body.delete-mode-active .steam-game-card.preset-game .game-description {
    color: #9ca3af !important;
}



/* Show edit button on hover for custom games */
.steam-game-card:not(.preset-game):not(.delete-mode):hover .edit-button-professional {
    opacity: 1;
    transform: translateY(0);
}

/* Improved game card styling for delete mode with red theme */
.steam-game-card.delete-mode {
    border-color: #dc2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.4);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(185, 28, 28, 0.05));
    position: relative;
    overflow: hidden;
}

.steam-game-card.delete-mode::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(220, 38, 38, 0.2) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.steam-game-card.delete-mode:hover {
    border-color: #b91c1c;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.6);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(185, 28, 28, 0.1));
    cursor: pointer;
    transform: translateY(-2px);
}

.steam-game-card.delete-mode .game-title {
    color: #f87171;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .game-description {
    color: #fca5a5;
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .last-played {
    color: #fecaca !important;
    position: relative;
    z-index: 2;
}

/* Help Content Styling */
.help-content {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 8px;
}

.help-content::-webkit-scrollbar {
    width: 6px;
}

.help-content::-webkit-scrollbar-track {
    background: transparent;
}

.help-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.help-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

.help-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.help-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.help-section h4 {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.help-section p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 12px;
}

.help-section ul,
.help-section ol {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-left: 20px;
    margin-bottom: 8px;
}

.help-section li {
    margin-bottom: 4px;
}

.help-section strong {
    color: var(--text-primary);
    font-weight: 600;
}

.help-section code {
    background: var(--bg-tertiary);
    color: var(--accent-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9em;
    font-family: 'Courier New', monospace;
} 