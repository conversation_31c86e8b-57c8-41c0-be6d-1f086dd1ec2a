[package]
name = "armoryx-installer"
# build script for tauri context generation
build = "build.rs"
version = "0.1.0"
edition = "2021"
authors = ["ArmoryX Team"]
description = "Bootstrap installer for Armory X"

[[bin]]
name = "armoryx-installer"
path = "src/main.rs"

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
anyhow = "1"
zip = "0.6"
walkdir = "2"
serde = { version = "1", features = ["derive"] }
tauri-plugin-process = "2" 
