#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};
use std::fs::{self, File};
use std::io::Write;
use std::path::{Path, PathBuf};
use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window};
use std::process;

#[derive(Debug, Serialize, Deserialize)]
struct InstallOptions {
    #[serde(rename = "desktopShortcut")]
    desktop_shortcut: bool,
    #[serde(rename = "startMenu")]
    start_menu: bool,
    #[serde(rename = "autoStart")]
    auto_start: bool,
    #[serde(rename = "fileAssociations")]
    file_associations: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct InstallResult {
    success: bool,
    message: String,
    path: String,
}

// Force close - this WILL work
#[command]
fn force_quit() {
    std::thread::spawn(|| {
        std::thread::sleep(std::time::Duration::from_millis(100));
        std::process::exit(0);
    });
}

// Alternative close method
#[command]
async fn close_installer(window: Window) -> Result<(), String> {
    // Try window close first
    let _ = window.close();
    
    // Then force exit after delay
    tokio::spawn(async {
        tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        std::process::exit(0);
    });
    
    Ok(())
}

#[command]
async fn start_install(target_dir: String, options: Option<InstallOptions>) -> Result<InstallResult, String> {
    println!("🚀 Starting Armory X installation...");
    
    let install_path = PathBuf::from(&target_dir);
    let opts = options.unwrap_or(InstallOptions {
        desktop_shortcut: true,
        start_menu: true,
        auto_start: false,
        file_associations: false,
    });
    
    // Create installation directory
    if let Err(e) = create_install_directory(&install_path) {
        return Err(format!("Failed to create installation directory: {}", e));
    }
    
    // Install application files
    if let Err(e) = install_application_files(&install_path).await {
        return Err(format!("Failed to install application files: {}", e));
    }
    
    // Create shortcuts if requested
    if opts.desktop_shortcut {
        create_desktop_shortcut(&install_path)?;
    }
    
    if opts.start_menu {
        create_start_menu_entry(&install_path)?;
    }
    
    // Create proper uninstaller
    create_uninstaller(&install_path)?;
    
    Ok(InstallResult {
        success: true,
        message: "Armory X has been installed successfully!".to_string(),
        path: target_dir,
    })
}

fn create_install_directory(install_path: &Path) -> Result<(), std::io::Error> {
    fs::create_dir_all(install_path)?;
    fs::create_dir_all(install_path.join("Resources"))?;
    fs::create_dir_all(install_path.join("Data"))?;
    fs::create_dir_all(install_path.join("Logs"))?;
    Ok(())
}

async fn install_application_files(install_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
    // Copy actual application files
    let exe_path = install_path.join("ArmoryX.exe");
    
    // In production, copy real files
    // For now, create placeholders
    File::create(&exe_path)?;
    
    // Create config files
    let config_content = r#"{
  "version": "1.0.0",
  "theme": "dark",
  "auto_update": true
}"#;
    
    fs::write(install_path.join("config.json"), config_content)?;
    
    Ok(())
}

#[cfg(windows)]
fn create_desktop_shortcut(install_path: &Path) -> Result<(), String> {
    // Create a batch file as shortcut for now
    let desktop = dirs::desktop_dir().ok_or("Could not find desktop")?;
    let shortcut = desktop.join("Armory X.bat");
    
    let content = format!(
        r#"@echo off
cd /d "{}"
start "" "ArmoryX.exe"
"#,
        install_path.display()
    );
    
    fs::write(&shortcut, content).map_err(|e| e.to_string())?;
    Ok(())
}

#[cfg(windows)]
fn create_start_menu_entry(install_path: &Path) -> Result<(), String> {
    let start_menu = dirs::data_dir()
        .ok_or("Could not find app data")?
        .join("Microsoft")
        .join("Windows")
        .join("Start Menu")
        .join("Programs")
        .join("Armory X");
    
    fs::create_dir_all(&start_menu).map_err(|e| e.to_string())?;
    
    let shortcut = start_menu.join("Armory X.bat");
    let content = format!(
        r#"@echo off
cd /d "{}"
start "" "ArmoryX.exe"
"#,
        install_path.display()
    );
    
    fs::write(&shortcut, content).map_err(|e| e.to_string())?;
    Ok(())
}

fn create_uninstaller(install_path: &Path) -> Result<(), String> {
    let uninstaller_content = format!(
        r#"@echo off
echo Uninstalling Armory X...
rmdir /s /q "{}"
del "%USERPROFILE%\Desktop\Armory X.bat" 2>nul
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Armory X" 2>nul
echo Uninstall complete.
pause
del "%~f0"
"#,
        install_path.display()
    );
    
    let uninstaller_path = install_path.join("Uninstall.bat");
    fs::write(uninstaller_path, uninstaller_content).map_err(|e| e.to_string())?;
    
    Ok(())
}

#[cfg(not(windows))]
fn create_desktop_shortcut(_install_path: &Path) -> Result<(), String> {
    Ok(())
}

#[cfg(not(windows))]
fn create_start_menu_entry(_install_path: &Path) -> Result<(), String> {
    Ok(())
}

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            start_install,
            force_quit,
            close_installer
        ])
        .setup(|app| {
            let window = app.get_window("main").unwrap();
            
            // Remove decorations for custom UI
            window.set_decorations(false).unwrap();
            
            // Handle window close event
            let window_clone = window.clone();
            window.on_window_event(move |event| {
                if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                    // Prevent default close
                    api.prevent_close();
                    
                    // Force quit after small delay
                    std::thread::spawn(|| {
                        std::thread::sleep(std::time::Duration::from_millis(100));
                        std::process::exit(0);
                    });
                }
            });
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
} 