# Armory X WiX Installer Build Script
# Requires WiX Toolset to be installed

Write-Host "Building Armory X WiX Installer..." -ForegroundColor Cyan

# Check if WiX is installed
$wixPath = "${env:ProgramFiles(x86)}\WiX Toolset v3.14\bin"
if (-not (Test-Path $wixPath)) {
    $wixPath = "${env:ProgramFiles}\WiX Toolset v3.14\bin"
}

if (-not (Test-Path $wixPath)) {
    Write-Host "ERROR: WiX Toolset not found. Please install it first." -ForegroundColor Red
    Write-Host "Download from: https://github.com/wixtoolset/wix3/releases" -ForegroundColor Yellow
    exit 1
}

Write-Host "Found WiX at: $wixPath" -ForegroundColor Green

# Set paths
$candle = "$wixPath\candle.exe"
$light = "$wixPath\light.exe"

# Create banner images if they don't exist
if (-not (Test-Path "banner.bmp")) {
    Write-Host "Creating placeholder banner.bmp (493x58)..." -ForegroundColor Yellow
    # In production, you'd use proper graphics
    Add-Type -AssemblyName System.Drawing
    $banner = New-Object System.Drawing.Bitmap 493, 58
    $g = [System.Drawing.Graphics]::FromImage($banner)
    $g.Clear([System.Drawing.Color]::FromArgb(26, 26, 46))
    $banner.Save("banner.bmp", [System.Drawing.Imaging.ImageFormat]::Bmp)
    $g.Dispose()
    $banner.Dispose()
}

if (-not (Test-Path "dialog.bmp")) {
    Write-Host "Creating placeholder dialog.bmp (493x312)..." -ForegroundColor Yellow
    $dialog = New-Object System.Drawing.Bitmap 493, 312
    $g = [System.Drawing.Graphics]::FromImage($dialog)
    $g.Clear([System.Drawing.Color]::FromArgb(22, 33, 62))
    $dialog.Save("dialog.bmp", [System.Drawing.Imaging.ImageFormat]::Bmp)
    $g.Dispose()
    $dialog.Dispose()
}

# Check if payload files exist
if (-not (Test-Path "payload\ArmoryX.exe")) {
    Write-Host "WARNING: payload\ArmoryX.exe not found. Using placeholder." -ForegroundColor Yellow
    # In production, copy the actual executable here
}

# Compile WiX source
Write-Host "Compiling WiX source..." -ForegroundColor Cyan
& $candle -ext WixUIExtension -ext WixUtilExtension "armory-x-installer.wxs" -out "armory-x-installer.wixobj"

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to compile WiX source" -ForegroundColor Red
    exit 1
}

# Link and create MSI
Write-Host "Creating MSI installer..." -ForegroundColor Cyan
& $light -ext WixUIExtension -ext WixUtilExtension -cultures:en-us "armory-x-installer.wixobj" -out "ArmoryX-Setup.msi"

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to create MSI" -ForegroundColor Red
    exit 1
}

Write-Host "SUCCESS: ArmoryX-Setup.msi created!" -ForegroundColor Green
Write-Host "Location: installer-bootstrap\ArmoryX-Setup.msi" -ForegroundColor Cyan

# Cleanup
Remove-Item "armory-x-installer.wixobj" -ErrorAction SilentlyContinue
Remove-Item "ArmoryX-Setup.wixpdb" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "To test the installer, run:" -ForegroundColor Yellow
Write-Host "  msiexec /i ArmoryX-Setup.msi" -ForegroundColor White
Write-Host ""
Write-Host "To uninstall:" -ForegroundColor Yellow
Write-Host "  msiexec /x ArmoryX-Setup.msi" -ForegroundColor White 