// ArmoryX Website License Integration
// Enhances the existing Premium tab with comprehensive license management

class ArmoryXLicenseIntegration {
    constructor() {
        this.licenseManager = null;
        this.currentUser = null;
        this.currentLicense = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    async init() {
        try {
            console.log('🔑 Initializing ArmoryX License Integration...');
            
            // Wait for License Key Manager to be available
            await this.waitForLicenseManager();
            
            // Initialize license manager
            this.licenseManager = new window.LicenseKeyManager();
            
            // Wait for Firebase auth state
            await this.waitForFirebase();
            
            // Listen for auth state changes
            if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
                try {
                    window.firebase.onAuthStateChanged(window.firebase.auth, (user) => {
                        this.currentUser = user;
                        this.updateLicenseUI(user);
                    });
                } catch (error) {
                    console.warn('Error setting up license auth listener:', error);
                }
            }
            
            // Enhance the Premium tab
            this.enhancePremiumTab();
            
            // Handle hash navigation for direct premium tab access
            this.handleHashNavigation();
            
            console.log('✅ ArmoryX License Integration initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize license integration:', error);
        }
    }

    async waitForLicenseManager() {
        return new Promise((resolve) => {
            const checkManager = () => {
                if (window.LicenseKeyManager) {
                    resolve();
                } else {
                    setTimeout(checkManager, 100);
                }
            };
            checkManager();
        });
    }

    async waitForFirebase() {
        return new Promise((resolve) => {
            const checkFirebase = () => {
                if (window.firebaseInitialized) {
                    resolve();
                } else {
                    setTimeout(checkFirebase, 100);
                }
            };
            checkFirebase();
        });
    }

    cleanup() {
        // Clear the countdown interval to prevent memory leaks
        if (window.licenseCountdownInterval) {
            clearInterval(window.licenseCountdownInterval);
            window.licenseCountdownInterval = null;
            console.log('🧹 Cleaned up license countdown interval');
        }
    }

    enhancePremiumTab() {
        const premiumTab = document.getElementById('premium-tab');
        if (!premiumTab) {
            console.warn('Premium tab not found');
            return;
        }

        // Replace the premium tab content with enhanced license management
        premiumTab.innerHTML = this.createEnhancedPremiumContent();
        
        // Add event listeners
        this.setupEventListeners();
        
        // Add CSS for license management
        this.addLicenseStyles();
    }

    createEnhancedPremiumContent() {
        return `
            <div class="premium-section">
                <h2 style="color: #e2e8f0; margin-bottom: 30px;"><i class="fas fa-star"></i> Premium License Management</h2>
                
                <!-- License Status Card -->
                <div class="license-status-card" id="license-status-card">
                    <div class="status-header">
                        <div class="status-icon" id="license-status-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="status-info">
                            <h3 id="license-status-title">Free Plan</h3>
                            <p id="license-status-description">You're currently using the free version of Armory X</p>
                        </div>
                        <div class="status-badge" id="license-status-badge">
                            <span>FREE</span>
                        </div>
                    </div>
                    
                    <div class="license-details" id="license-details" style="display: none;">
                        <div class="license-info-grid">
                            <div class="license-info-item">
                                <i class="fas fa-tag license-info-icon"></i>
                                <div class="license-info-content">
                                    <span class="license-info-label">License Type</span>
                                    <span class="license-info-value" id="license-type">-</span>
                                </div>
                            </div>
                            <div class="license-info-item">
                                <i class="fas fa-check-circle license-info-icon"></i>
                                <div class="license-info-content">
                                    <span class="license-info-label">Status</span>
                                    <span class="license-info-value" id="license-active-status">-</span>
                                </div>
                            </div>
                            <div class="license-info-item">
                                <i class="fas fa-calendar-alt license-info-icon"></i>
                                <div class="license-info-content">
                                    <span class="license-info-label">Expires</span>
                                    <span class="license-info-value" id="license-expires">-</span>
                                </div>
                            </div>
                            <div class="license-info-item">
                                <i class="fas fa-microchip license-info-icon"></i>
                                <div class="license-info-content">
                                    <span class="license-info-label">Hardware ID</span>
                                    <span class="license-info-value" id="license-hwid">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- License Actions -->
                <div class="license-actions">
                    <div class="action-tabs">
                        <button class="action-tab active" data-tab="redeem">
                            <i class="fas fa-key"></i> Activate License
                        </button>
                        <button class="action-tab" data-tab="purchase">
                            <i class="fas fa-shopping-cart"></i> Purchase License
                        </button>
                        <button class="action-tab admin-only" data-tab="admin" style="display: none;">
                            <i class="fas fa-cog"></i> Admin Panel
                        </button>
                    </div>

                    <!-- Redeem Tab -->
                    <div class="action-content active" id="redeem-tab">
                        <h3><i class="fas fa-key"></i> Activate Your License Key</h3>
                        <p>Enter your license key to unlock premium features:</p>
                        
                        <div class="license-input-section">
                            <div class="license-input-group">
                                <input type="text" 
                                       id="license-key-input" 
                                       placeholder="ARMX-XXXX-XXXX-XXXX-XXXX" 
                                       maxlength="24"
                                       class="license-key-field">
                                <button class="btn btn-primary" id="activate-license-btn">
                                    <i class="fas fa-unlock"></i> Activate
                                </button>
                            </div>
                            <div class="license-hint" id="license-hint">
                                Format: ARMX-XXXX-XXXX-XXXX-XXXX
                            </div>
                        </div>
                        
                        <div id="activation-result" class="activation-result"></div>
                    </div>

                    <!-- Purchase Tab -->
                    <div class="action-content" id="purchase-tab">
                        <h3><i class="fas fa-shopping-cart"></i> Choose Your Plan</h3>
                        <p>Select the perfect plan for your needs:</p>
                        
                        <div class="pricing-grid">
                            <div class="pricing-card">
                                <div class="pricing-header">
                                    <h4>Trial License</h4>
                                    <div class="price">FREE</div>
                                    <p class="price-period">7 days</p>
                                </div>
                                                        <div class="pricing-features">
                            <ul>
                                <li><i class="fas fa-check"></i> 7-day access</li>
                                <li><i class="fas fa-check"></i> Basic features</li>
                                <li><i class="fas fa-check"></i> Community support</li>
                                <li><i class="fas fa-check"></i> Perfect for testing</li>
                            </ul>
                        </div>
                        <button class="btn btn-secondary purchase-btn" data-type="trial">
                            <i class="fas fa-gift"></i> Start Trial
                        </button>
                            </div>

                            <div class="pricing-card featured">
                                <div class="popular-badge">Most Popular</div>
                                <div class="pricing-header">
                                    <h4>Lifetime License</h4>
                                    <div class="price">$49</div>
                                    <p class="price-period">one-time payment</p>
                                </div>
                                <div class="pricing-features">
                                    <ul>
                                        <li><i class="fas fa-check"></i> Lifetime access</li>
                                        <li><i class="fas fa-check"></i> All premium features</li>
                                        <li><i class="fas fa-check"></i> Priority support</li>
                                        <li><i class="fas fa-check"></i> Free updates</li>
                                        <li><i class="fas fa-check"></i> Hardware binding</li>
                                    </ul>
                                </div>
                                <button class="btn btn-primary purchase-btn" data-type="lifetime">
                                    <i class="fas fa-star"></i> Buy Lifetime
                                </button>
                            </div>

                            <div class="pricing-card">
                                <div class="pricing-header">
                                    <h4>Monthly License</h4>
                                    <div class="price">$9</div>
                                    <p class="price-period">per month</p>
                                </div>
                                <div class="pricing-features">
                                    <ul>
                                        <li><i class="fas fa-check"></i> Monthly billing</li>
                                        <li><i class="fas fa-check"></i> All premium features</li>
                                        <li><i class="fas fa-check"></i> Priority support</li>
                                        <li><i class="fas fa-check"></i> Cancel anytime</li>
                                    </ul>
                                </div>
                                <button class="btn btn-primary purchase-btn" data-type="monthly">
                                    <i class="fas fa-calendar"></i> Subscribe
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Tab -->
                    <div class="action-content" id="admin-tab">
                        <h3><i class="fas fa-cog"></i> Admin Panel</h3>
                        <p>Administrative functions for license key management:</p>
                        
                        <div class="admin-section">
                            <h4><i class="fas fa-plus"></i> Generate License Keys</h4>
                            <div class="admin-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="admin-key-type">Key Type:</label>
                                        <select id="admin-key-type">
                                            <option value="trial">Trial (7 days)</option>
                                            <option value="monthly">Monthly</option>
                                            <option value="lifetime">Lifetime</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="admin-key-count">Count:</label>
                                        <input type="number" id="admin-key-count" min="1" max="50" value="1">
                                    </div>
                                </div>
                                <button class="btn btn-primary" id="generate-keys-btn">
                                    <i class="fas fa-magic"></i> Generate Keys
                                </button>
                            </div>
                            
                            <div id="generated-keys-result" class="generated-keys-result"></div>
                        </div>

                        <div class="admin-section">
                            <h4><i class="fas fa-ban"></i> Revoke License Key</h4>
                            <div class="admin-form">
                                <div class="form-group">
                                    <label for="revoke-key-input">License Key:</label>
                                    <input type="text" id="revoke-key-input" placeholder="ARMX-XXXX-XXXX-XXXX-XXXX">
                                </div>
                                <div class="form-group">
                                    <label for="revoke-reason">Reason:</label>
                                    <input type="text" id="revoke-reason" placeholder="Reason for revocation">
                                </div>
                                <button class="btn btn-danger" id="revoke-key-btn">
                                    <i class="fas fa-ban"></i> Revoke Key
                                </button>
                            </div>
                            
                            <div id="revoke-result" class="revoke-result"></div>
                        </div>

                        <div class="admin-section">
                            <h4><i class="fas fa-list"></i> License Key Management</h4>
                            <div class="key-management-tabs">
                                <button class="key-tab active" data-status="all">All Keys</button>
                                <button class="key-tab" data-status="unused">Unused</button>
                                <button class="key-tab" data-status="active">Active</button>
                                <button class="key-tab" data-status="revoked">Revoked</button>
                            </div>
                            
                            <div class="key-management-controls">
                                <div class="search-bar">
                                    <input type="text" id="key-search" placeholder="Search keys, users, or emails...">
                                    <button class="btn btn-secondary" id="refresh-keys-btn">
                                        <i class="fas fa-sync"></i> Refresh
                                    </button>
                                    <button class="btn btn-danger" id="cleanup-broken-keys-btn" title="Delete all corrupted keys">
                                        <i class="fas fa-broom"></i> Cleanup Broken Keys
                                    </button>
                                    <button class="btn btn-warning" id="delete-unused-keys-btn" title="Delete all unused keys">
                                        <i class="fas fa-trash-alt"></i> Delete Unused Keys
                                    </button>
                                    <button class="btn btn-danger" id="nuclear-cleanup-btn" title="⚠️ NUCLEAR OPTION: Clear entire license database" style="background: #dc2626; border-color: #dc2626;">
                                        <i class="fas fa-nuclear"></i> Nuclear Cleanup
                                    </button>
                                </div>
                            </div>
                            
                            <div id="key-management-list" class="key-management-list">
                                <div class="loading-message">
                                    <i class="fas fa-spinner fa-spin"></i> Loading license keys...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Premium Features Section -->
                <div class="premium-features-section">
                    <h3><i class="fas fa-rocket"></i> Premium Features</h3>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <h4>Cloud Sync</h4>
                            <p>Sync your settings and preferences across all your devices seamlessly</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h4>Advanced Analytics</h4>
                            <p>Get detailed performance reports and system optimization insights</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <h4>Premium Tools</h4>
                            <p>Access exclusive optimization tools and advanced system utilities</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <h4>Priority Support</h4>
                            <p>Get faster help with premium customer support and direct assistance</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h4>Custom Themes</h4>
                            <p>Personalize your Armory X experience with premium themes and layouts</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>Enhanced Security</h4>
                            <p>Additional security features and privacy controls for power users</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.action-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchActionTab(e.target.dataset.tab);
            });
        });

        // License key input formatting
        const licenseInput = document.getElementById('license-key-input');
        if (licenseInput) {
            licenseInput.addEventListener('input', (e) => {
                this.formatLicenseKeyInput(e.target);
            });
        }

        // Activate license button
        const activateBtn = document.getElementById('activate-license-btn');
        if (activateBtn) {
            activateBtn.addEventListener('click', () => {
                this.activateLicense();
            });
        }

        // Purchase buttons
        document.querySelectorAll('.purchase-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handlePurchase(e.target.dataset.type);
            });
        });

        // Admin functions
        const generateBtn = document.getElementById('generate-keys-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateKeys();
            });
        }

        const revokeBtn = document.getElementById('revoke-key-btn');
        if (revokeBtn) {
            revokeBtn.addEventListener('click', () => {
                this.revokeKey();
            });
        }

        // Key management functionality
        this.initializeKeyManagement();
    }

    switchActionTab(tabName) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.action-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.action-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    formatLicenseKeyInput(input) {
        let value = input.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        
        // Format with dashes: ARMX-XXXX-XXXX-XXXX-XXXX
        if (value.length > 4) {
            value = value.substring(0, 4) + '-' + value.substring(4);
        }
        if (value.length > 9) {
            value = value.substring(0, 9) + '-' + value.substring(9);
        }
        if (value.length > 14) {
            value = value.substring(0, 14) + '-' + value.substring(14);
        }
        if (value.length > 19) {
            value = value.substring(0, 19) + '-' + value.substring(19, 23);
        }
        
        input.value = value;
        
        // Update hint
        const hint = document.getElementById('license-hint');
        if (hint) {
            if (this.isValidLicenseKeyFormat(value)) {
                hint.textContent = '✓ Valid format - Ready to activate';
                hint.className = 'license-hint valid';
            } else if (value.length === 0) {
                hint.textContent = 'Format: ARMX-XXXX-XXXX-XXXX-XXXX';
                hint.className = 'license-hint';
            } else {
                hint.textContent = '⚠ Invalid format - Please check your license key';
                hint.className = 'license-hint invalid';
            }
        }
    }

    isValidLicenseKeyFormat(key) {
        return /^ARMX-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(key);
    }

    async activateLicense() {
        const input = document.getElementById('license-key-input');
        const button = document.getElementById('activate-license-btn');
        const result = document.getElementById('activation-result');
        
        if (!input || !button || !result) return;
        
        const licenseKey = input.value.trim();
        
        if (!this.isValidLicenseKeyFormat(licenseKey)) {
            this.showResult(result, 'error', 'Please enter a valid license key format');
            return;
        }

        if (!this.currentUser) {
            this.showResult(result, 'error', 'Please log in to activate a license key');
            return;
        }

        try {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Activating...';
            
            this.showResult(result, 'info', 'Activating license key...');
            
            const response = await this.licenseManager.activateLicenseKey(licenseKey);
            
            if (response.success) {
                this.showResult(result, 'success', response.message);
                input.value = '';
                
                // Refresh license status
                await this.updateLicenseStatus();
            } else {
                this.showResult(result, 'error', response.error);
            }
        } catch (error) {
            this.showResult(result, 'error', `Error: ${error.message}`);
        } finally {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-unlock"></i> Activate';
        }
    }

    async handlePurchase(type) {
        if (type === 'trial') {
            // For trial, generate a key directly (if admin) or show message
            if (this.currentUser) {
                try {
                    const result = await this.licenseManager.createLicenseKey('trial');
                    if (result.success) {
                        // Auto-activate the trial key
                        const activation = await this.licenseManager.activateLicenseKey(result.licenseKey);
                        if (activation.success) {
                            this.showNotification('Trial license activated successfully!', 'success');
                            await this.updateLicenseStatus();
                        } else {
                            this.showNotification(`Trial created: ${result.licenseKey}`, 'info');
                        }
                    } else {
                        this.showNotification('Trial generation requires admin privileges', 'warning');
                    }
                } catch (error) {
                    this.showNotification('Error creating trial license', 'error');
                }
            } else {
                this.showNotification('Please log in to start a trial', 'warning');
            }
        } else {
            // For paid licenses, would integrate with payment processor
            this.showNotification(`Payment integration for ${type} license would be implemented here`, 'info');
        }
    }

    async generateKeys() {
        const typeSelect = document.getElementById('admin-key-type');
        const countInput = document.getElementById('admin-key-count');
        const result = document.getElementById('generated-keys-result');
        
        if (!typeSelect || !countInput || !result) return;
        
        const keyType = typeSelect.value;
        const count = parseInt(countInput.value);
        
        if (count < 1 || count > 50) {
            this.showResult(result, 'error', 'Please enter a valid count (1-50)');
            return;
        }

        try {
            this.showResult(result, 'info', 'Generating keys...');
            
            const keys = [];
            for (let i = 0; i < count; i++) {
                const response = await this.licenseManager.createLicenseKey(keyType);
                if (response.success) {
                    keys.push(response.licenseKey);
                }
            }
            
            if (keys.length > 0) {
                const keyList = keys.map(key => 
                    `<div class="generated-key">
                        <span class="key-value">${key}</span>
                        <button class="btn btn-sm copy-btn" onclick="navigator.clipboard.writeText('${key}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>`
                ).join('');
                
                this.showResult(result, 'success', 
                    `<h4>Generated ${keys.length} ${keyType} keys:</h4>
                     <div class="keys-list">${keyList}</div>`
                );
                
                // Refresh key management list
                if (document.getElementById('key-management-list')) {
                    this.loadKeyManagementList();
                }
            } else {
                this.showResult(result, 'error', 'Failed to generate keys');
            }
        } catch (error) {
            this.showResult(result, 'error', `Error: ${error.message}`);
        }
    }

    async revokeKey() {
        const keyInput = document.getElementById('revoke-key-input');
        const reasonInput = document.getElementById('revoke-reason');
        const result = document.getElementById('revoke-result');
        
        if (!keyInput || !reasonInput || !result) return;
        
        const licenseKey = keyInput.value.trim();
        const reason = reasonInput.value.trim();
        
        if (!this.isValidLicenseKeyFormat(licenseKey)) {
            this.showResult(result, 'error', 'Please enter a valid license key');
            return;
        }
        
        if (!reason) {
            this.showResult(result, 'error', 'Please enter a reason for revocation');
            return;
        }

        try {
            this.showResult(result, 'info', 'Revoking license key...');
            
            const response = await this.licenseManager.revokeLicenseKey(licenseKey, reason);
            
            if (response.success) {
                this.showResult(result, 'success', response.message);
                keyInput.value = '';
                reasonInput.value = '';
                
                // Refresh key management list
                if (document.getElementById('key-management-list')) {
                    this.loadKeyManagementList();
                }
            } else {
                this.showResult(result, 'error', response.error);
            }
        } catch (error) {
            this.showResult(result, 'error', `Error: ${error.message}`);
        }
    }

    async updateLicenseUI(user) {
        if (!user) {
            this.showFreePlan();
            return;
        }

        try {
            await this.updateLicenseStatus();
            await this.checkAdminStatus(user.uid);
        } catch (error) {
            console.error('Error updating license UI:', error);
        }
    }

    async updateLicenseStatus() {
        if (!this.currentUser || !this.licenseManager) return;

        try {
            console.log('🔄 Updating license status for user:', this.currentUser.uid);
            const status = await this.licenseManager.getUserLicenseStatus(this.currentUser.uid);
            console.log('📊 License status received:', status);
            
            const statusCard = document.getElementById('license-status-card');
            const statusIcon = document.getElementById('license-status-icon');
            const statusTitle = document.getElementById('license-status-title');
            const statusDescription = document.getElementById('license-status-description');
            const statusBadge = document.getElementById('license-status-badge');
            const licenseDetails = document.getElementById('license-details');
            
            if (!statusCard) return;

            if (status.hasLicense && status.subscriptionStatus === 'active') {
                console.log('✅ User has active premium license');
                // Premium user
                statusCard.className = 'license-status-card premium';
                statusIcon.innerHTML = '<i class="fas fa-star"></i>';
                statusTitle.textContent = `${status.accountType.charAt(0).toUpperCase() + status.accountType.slice(1)} Plan`;
                statusDescription.textContent = 'You have access to all premium features';
                statusBadge.textContent = status.accountType.toUpperCase();
                statusBadge.className = 'status-badge premium';
                
                // Show license details
                if (licenseDetails) {
                    licenseDetails.style.display = 'block';
                    const licenseTypeEl = document.getElementById('license-type');
                    const licenseStatusEl = document.getElementById('license-active-status');
                    const licenseHwidEl = document.getElementById('license-hwid');
                    const licenseExpiresEl = document.getElementById('license-expires');
                    
                    if (licenseTypeEl) licenseTypeEl.textContent = status.accountType;
                    if (licenseStatusEl) licenseStatusEl.textContent = status.subscriptionStatus;
                    if (licenseHwidEl) licenseHwidEl.textContent = status.hwid ? 
                        status.hwid.substring(0, 8) + '...' : 'Not set';
                    
                    // Handle expiration date with real-time countdown
                    if (licenseExpiresEl) {
                        if (status.expiresAt) {
                            const expiryDate = new Date(status.expiresAt.toDate ? status.expiresAt.toDate() : status.expiresAt);
                            
                            // Function to update countdown in real-time
                            const updateCountdown = () => {
                                const now = new Date();
                                const timeDiff = expiryDate - now;
                                
                                if (timeDiff > 0) {
                                    // Calculate time components correctly
                                    const totalSeconds = Math.floor(timeDiff / 1000);
                                    const days = Math.floor(totalSeconds / (24 * 60 * 60));
                                    const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
                                    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
                                    const seconds = totalSeconds % 60;
                                    
                                    // Create countdown text with labels
                                    const countdownText = `${days}d ${String(hours).padStart(2, '0')}h ${String(minutes).padStart(2, '0')}m ${String(seconds).padStart(2, '0')}s`;
                                    licenseExpiresEl.textContent = countdownText;
                                    licenseExpiresEl.style.color = days <= 3 ? '#f56565' : '#68d391';
                                } else {
                                    licenseExpiresEl.textContent = 'Expired';
                                    licenseExpiresEl.style.color = '#f56565';
                                    // Stop the interval if expired
                                    if (window.licenseCountdownInterval) {
                                        clearInterval(window.licenseCountdownInterval);
                                    }
                                }
                            };
                            
                            // Update immediately
                            updateCountdown();
                            
                            // Clear any existing interval
                            if (window.licenseCountdownInterval) {
                                clearInterval(window.licenseCountdownInterval);
                            }
                            
                            // Set up real-time updating every second
                            window.licenseCountdownInterval = setInterval(updateCountdown, 1000);
                            
                        } else if (status.accountType === 'lifetime') {
                            licenseExpiresEl.textContent = 'Never';
                            licenseExpiresEl.style.color = '#68d391';
                            
                            // Clear countdown interval for lifetime
                            if (window.licenseCountdownInterval) {
                                clearInterval(window.licenseCountdownInterval);
                            }
                        } else {
                            licenseExpiresEl.textContent = 'Unknown';
                            licenseExpiresEl.style.color = '#a0aec0';
                            
                            // Clear countdown interval for unknown
                            if (window.licenseCountdownInterval) {
                                clearInterval(window.licenseCountdownInterval);
                            }
                        }
                    }
                }
                
            } else {
                console.log('❌ User does not have active license, showing free plan');
                this.showFreePlan();
            }
        } catch (error) {
            console.error('Error updating license status:', error);
            this.showFreePlan();
        }
    }

    showFreePlan() {
        const statusCard = document.getElementById('license-status-card');
        const statusIcon = document.getElementById('license-status-icon');
        const statusTitle = document.getElementById('license-status-title');
        const statusDescription = document.getElementById('license-status-description');
        const statusBadge = document.getElementById('license-status-badge');
        const licenseDetails = document.getElementById('license-details');
        
        if (!statusCard) return;

        statusCard.className = 'license-status-card free';
        statusIcon.innerHTML = '<i class="fas fa-user"></i>';
        statusTitle.textContent = 'Free Plan';
        statusDescription.textContent = "You're currently using the free version of Armory X";
        statusBadge.textContent = 'FREE';
        statusBadge.className = 'status-badge free';
        
        licenseDetails.style.display = 'none';
    }

    async checkAdminStatus(userId) {
        try {
            if (!window.firebase || !window.firebase.getDoc || !window.firebase.db) {
                console.warn('Firebase not fully initialized for admin check');
                return;
            }
            
            console.log('🔍 Checking admin status for user:', userId);
            
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', userId));
            console.log('📄 User document exists:', userDoc.exists());
            
            if (userDoc.exists()) {
                const userData = userDoc.data();
                console.log('👤 User data:', userData);
                console.log('🔑 User role:', userData.role);
                
                if (userData.role === 'admin') {
                    console.log('✅ User is admin - showing admin tab');
                    const adminTab = document.querySelector('[data-tab="admin"]');
                    if (adminTab) {
                        adminTab.style.display = 'flex';
                        console.log('🎯 Admin tab made visible');
                    } else {
                        console.warn('❌ Admin tab element not found');
                    }
                } else {
                    console.log('❌ User is not admin, role:', userData.role);
                }
            } else {
                console.warn('❌ User document does not exist in users collection');
            }
        } catch (error) {
            console.error('❌ Error checking admin status:', error);
        }
    }

    showResult(element, type, message) {
        element.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        element.style.display = 'block';
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    handleHashNavigation() {
        // Check if URL has #premium hash and switch to premium tab
        if (window.location.hash === '#premium') {
            setTimeout(() => {
                const premiumTab = document.querySelector('[data-tab="premium"]');
                if (premiumTab) {
                    premiumTab.click();
                }
            }, 500); // Small delay to ensure tabs are ready
        }
    }

    addLicenseStyles() {
        if (document.getElementById('license-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'license-styles';
        styles.textContent = `
            /* License Management Styles - Dark Theme */
            .license-status-card {
                background: linear-gradient(135deg, #2a3441 0%, #1e2832 100%);
                border: 1px solid #4a5568;
                border-radius: 15px;
                padding: 25px;
                margin-bottom: 30px;
                color: #e2e8f0;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }

            .license-status-card.premium {
                background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                border-color: #667eea;
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.1);
            }

            .license-status-card.free {
                background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                border-color: #4a5568;
            }

            .status-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 20px;
            }

            .status-icon {
                font-size: 2rem;
                margin-right: 15px;
            }

            .status-info h3 {
                margin: 0 0 5px 0;
                font-size: 1.5rem;
            }

            .status-info p {
                margin: 0;
                opacity: 0.9;
            }

            .status-badge {
                background: rgba(255,255,255,0.2);
                padding: 5px 15px;
                border-radius: 20px;
                font-weight: bold;
                font-size: 0.9rem;
            }

            .status-badge.premium {
                background: rgba(255,215,0,0.3);
                color: #ffd700;
            }

            .license-details {
                border-top: 1px solid rgba(255,255,255,0.2);
                padding-top: 20px;
                margin-top: 20px;
            }

            .license-info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .license-info-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .license-info-item:hover {
                background: rgba(255, 255, 255, 0.08);
                transform: translateY(-1px);
            }

            .license-info-icon {
                color: #667eea;
                font-size: 18px;
                width: 20px;
                text-align: center;
                flex-shrink: 0;
            }

            .license-info-content {
                display: flex;
                flex-direction: column;
                gap: 2px;
                flex: 1;
            }

            .license-info-label {
                color: #a0aec0;
                font-size: 12px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .license-info-value {
                color: #e2e8f0;
                font-weight: 600;
                font-size: 14px;
            }

            /* Old styles for backwards compatibility */
            .detail-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .detail-label {
                opacity: 0.8;
                font-weight: 500;
            }

            .detail-value {
                font-weight: bold;
            }

            .license-actions {
                background: #1a202c;
                border: 1px solid #4a5568;
                border-radius: 15px;
                padding: 25px;
                margin-bottom: 30px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            }

            .action-tabs {
                display: flex;
                border-bottom: 2px solid #4a5568;
                margin-bottom: 25px;
            }

            .action-tab {
                flex: 1;
                padding: 15px 20px;
                border: none;
                background: none;
                cursor: pointer;
                font-size: 1rem;
                color: #a0aec0;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }

            .action-tab.active {
                color: #667eea;
                border-bottom: 3px solid #667eea;
                font-weight: 600;
            }

            .action-tab:hover {
                color: #667eea;
            }

            .action-content {
                display: none;
            }

            .action-content.active {
                display: block;
            }
            
            .action-content h3 {
                color: #e2e8f0;
                margin-bottom: 15px;
            }
            
            .action-content p {
                color: #a0aec0;
                margin-bottom: 20px;
            }

            .license-input-section {
                margin: 20px 0;
            }

            .license-input-group {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }

            .license-key-field {
                flex: 1;
                padding: 12px 15px;
                border: 2px solid #4a5568;
                border-radius: 8px;
                background: #2d3748;
                color: #e2e8f0;
                font-family: 'Courier New', monospace;
                font-size: 1.1rem;
                letter-spacing: 1px;
                text-transform: uppercase;
                transition: border-color 0.3s ease;
            }

            .license-key-field:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
            }

            .license-key-field::placeholder {
                color: #718096;
            }

            .license-hint {
                font-size: 0.9rem;
                color: #a0aec0;
                margin-top: 5px;
            }

            .license-hint.valid {
                color: #68d391;
            }

            .license-hint.invalid {
                color: #f56565;
            }

            .pricing-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
                margin-top: 20px;
            }

            .pricing-card {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 15px;
                padding: 25px;
                text-align: center;
                position: relative;
                transition: all 0.3s ease;
                color: #e2e8f0;
            }

            .pricing-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                border-color: #667eea;
            }

            .pricing-card.featured {
                border-color: #667eea;
                background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.2);
                color: #e2e8f0;
            }

            .popular-badge {
                position: absolute;
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
                background: #ff6b6b;
                color: white;
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: bold;
            }

            .pricing-header h4 {
                margin: 0 0 15px 0;
                font-size: 1.3rem;
            }

            .price {
                font-size: 2.5rem;
                font-weight: bold;
                margin: 15px 0;
            }

            .price-period {
                opacity: 0.7;
                margin-bottom: 20px;
            }

            .pricing-features ul {
                list-style: none;
                padding: 0;
                margin: 20px 0;
            }

            .pricing-features li {
                padding: 8px 0;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .pricing-features li i {
                color: #68d391;
                width: 16px;
            }

            .pricing-card.featured .pricing-features li i {
                color: #9ae6b4;
            }

            .admin-section {
                background: #2d3748;
                border: 2px solid #4a5568;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .admin-section h4 {
                margin: 0 0 15px 0;
                color: #e2e8f0;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .admin-form {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .form-row {
                display: flex;
                gap: 15px;
            }

            .form-group {
                flex: 1;
            }

            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 600;
                color: #e2e8f0;
            }

            .form-group input,
            .form-group select {
                width: 100%;
                padding: 10px;
                border: 1px solid #4a5568;
                border-radius: 5px;
                font-size: 0.9rem;
                background: #1a202c;
                color: #e2e8f0;
            }

            .form-group input:focus,
            .form-group select:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 5px rgba(102, 126, 234, 0.2);
            }

            .form-group input::placeholder {
                color: #718096;
            }

            .generated-keys-result,
            .revoke-result,
            .activation-result {
                margin-top: 15px;
            }

            .keys-list {
                margin-top: 10px;
            }

            .generated-key {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: #1a202c;
                border: 1px solid #4a5568;
                padding: 10px 15px;
                margin: 5px 0;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                color: #e2e8f0;
            }

            .copy-btn {
                padding: 5px 10px;
                font-size: 0.8rem;
                background: #667eea;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.3s ease;
            }

            .copy-btn:hover {
                background: #5a67d8;
            }

            .alert {
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border: 1px solid;
            }

            .alert-success {
                background: rgba(72, 187, 120, 0.1);
                color: #68d391;
                border-color: #48bb78;
            }

            .alert-error {
                background: rgba(245, 101, 101, 0.1);
                color: #f56565;
                border-color: #e53e3e;
            }

            .alert-info {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                border-color: #667eea;
            }

            .premium-features-section {
                background: #1a202c;
                border: 1px solid #4a5568;
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            }

            .premium-features-section h3 {
                margin: 0 0 25px 0;
                display: flex;
                align-items: center;
                gap: 10px;
                color: #e2e8f0;
            }

            .features-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .feature-card {
                background: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                transition: all 0.3s ease;
            }

            .feature-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                border-color: #667eea;
            }

            .feature-icon {
                font-size: 2rem;
                color: #667eea;
                margin-bottom: 15px;
            }

            .feature-card h4 {
                margin: 0 0 10px 0;
                color: #e2e8f0;
            }

            .feature-card p {
                color: #a0aec0;
                margin: 0;
                font-size: 0.9rem;
            }

                            /* Dark theme button styling */
                .btn {
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    border: 1px solid;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                }

                .btn-primary {
                    background: #667eea;
                    color: white;
                    border-color: #667eea;
                }

                .btn-primary:hover {
                    background: #5a67d8;
                    border-color: #5a67d8;
                    transform: translateY(-1px);
                }

                .btn-secondary {
                    background: #4a5568;
                    color: #e2e8f0;
                    border-color: #4a5568;
                }

                .btn-secondary:hover {
                    background: #2d3748;
                    border-color: #2d3748;
                    transform: translateY(-1px);
                }

                .btn-danger {
                    background: #e53e3e;
                    color: white;
                    border-color: #e53e3e;
                }

                .btn-danger:hover {
                    background: #c53030;
                    border-color: #c53030;
                    transform: translateY(-1px);
                }

                @media (max-width: 768px) {
                    .action-tabs {
                        flex-direction: column;
                    }
                    
                    .pricing-grid {
                        grid-template-columns: 1fr;
                    }
                    
                    .features-grid {
                        grid-template-columns: 1fr;
                    }
                    
                    .license-input-group {
                        flex-direction: column;
                    }

                    .form-row {
                        flex-direction: column;
                    }

                    .license-info-grid {
                        grid-template-columns: 1fr;
                        gap: 10px;
                    }

                    .license-info-item {
                        padding: 10px 12px;
                    }

                    .license-info-icon {
                        font-size: 16px;
                    }
                }
            `;
            
            document.head.appendChild(styles);
    }

    // Key Management Methods
    initializeKeyManagement() {
        // Tab switching for key status
        document.querySelectorAll('.key-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                document.querySelectorAll('.key-tab').forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                this.filterKeys(e.target.dataset.status);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('key-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchKeys(e.target.value);
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById('refresh-keys-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadKeyManagementList();
            });
        }

        // Cleanup broken keys button
        const cleanupBtn = document.getElementById('cleanup-broken-keys-btn');
        if (cleanupBtn) {
            cleanupBtn.addEventListener('click', () => {
                this.cleanupBrokenKeys();
            });
        }

        // Delete unused keys button
        const deleteUnusedBtn = document.getElementById('delete-unused-keys-btn');
        if (deleteUnusedBtn) {
            deleteUnusedBtn.addEventListener('click', () => {
                this.deleteUnusedKeys();
            });
        }

        // Nuclear cleanup button
        const nuclearBtn = document.getElementById('nuclear-cleanup-btn');
        if (nuclearBtn) {
            nuclearBtn.addEventListener('click', () => {
                this.nuclearCleanup();
            });
        }

        // Load initial data
        this.loadKeyManagementList();
    }

    async loadKeyManagementList() {
        const listContainer = document.getElementById('key-management-list');
        if (!listContainer) return;

        try {
            listContainer.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Loading license keys...</div>';

            if (!window.firebase || !window.firebase.db) {
                throw new Error('Firebase not initialized');
            }

            // Get all license keys
            const keysQuery = window.firebase.query(
                window.firebase.collection(window.firebase.db, 'license_keys'),
                window.firebase.orderBy('createdAt', 'desc')
            );
            
            const keysSnapshot = await window.firebase.getDocs(keysQuery);
            const keys = [];

            for (const doc of keysSnapshot.docs) {
                const keyData = doc.data();
                keys.push({
                    id: doc.id,
                    ...keyData
                });
            }

            // Get user licenses to match keys with users
            const licensesQuery = window.firebase.query(
                window.firebase.collection(window.firebase.db, 'user_licenses')
            );
            const licensesSnapshot = await window.firebase.getDocs(licensesQuery);
            
            const userLicenseMap = {};
            for (const doc of licensesSnapshot.docs) {
                const licenseData = doc.data();
                if (licenseData.licenseKey) {
                    userLicenseMap[licenseData.licenseKey] = {
                        userId: licenseData.userId,
                        activatedAt: licenseData.activatedAt,
                        status: licenseData.status
                    };
                }
            }

            // Get user details for display names
            const userDetailsMap = {};
            for (const license of Object.values(userLicenseMap)) {
                if (license.userId && !userDetailsMap[license.userId]) {
                    try {
                        const userDoc = await window.firebase.getDoc(
                            window.firebase.doc(window.firebase.db, 'users', license.userId)
                        );
                        if (userDoc.exists()) {
                            userDetailsMap[license.userId] = userDoc.data();
                        }
                    } catch (error) {
                        console.error('Error getting user details:', error);
                    }
                }
            }

            this.allKeys = keys.map(key => ({
                ...key,
                userLicense: userLicenseMap[key.licenseKey],
                userDetails: userLicenseMap[key.licenseKey] ? 
                    userDetailsMap[userLicenseMap[key.licenseKey].userId] : null
            }));

            this.displayKeys(this.allKeys);
            
        } catch (error) {
            console.error('Error loading keys:', error);
            listContainer.innerHTML = `
                <div class="no-keys-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading license keys: ${error.message}</p>
                </div>
            `;
        }
    }

    displayKeys(keys) {
        const listContainer = document.getElementById('key-management-list');
        if (!listContainer) return;

        if (keys.length === 0) {
            listContainer.innerHTML = `
                <div class="no-keys-message">
                    <i class="fas fa-key"></i>
                    <p>No license keys found</p>
                </div>
            `;
            return;
        }

        const keysHtml = keys.map(key => {
            const status = this.getKeyStatus(key);
            const userInfo = this.getUserInfo(key);
            
            const displayKey = key.licenseKey || key.keyValue || 'UNDEFINED_KEY';
            const isBrokenKey = !key.licenseKey && !key.keyValue;
            
            return `
                <div class="key-item" data-key="${displayKey}" data-status="${status}" ${isBrokenKey ? 'data-broken="true"' : ''}>
                    <div class="key-info">
                        <div class="key-code ${isBrokenKey ? 'broken-key' : ''}">${displayKey}</div>
                        ${isBrokenKey ? '<div class="broken-key-warning">⚠️ Corrupted key data - recommend deletion</div>' : ''}
                        <div class="key-details">
                            <div><strong>Type:</strong> ${key.keyType || 'unknown'} | <strong>Created:</strong> ${new Date(key.createdAt?.toDate ? key.createdAt.toDate() : key.createdAt).toLocaleDateString()}</div>
                            ${userInfo}
                            ${key.revokedAt ? `<div style="color: #f56565;"><strong>Revoked:</strong> ${new Date(key.revokedAt.toDate ? key.revokedAt.toDate() : key.revokedAt).toLocaleDateString()} - ${key.revocationReason || 'No reason provided'}</div>` : ''}
                        </div>
                    </div>
                    <div class="key-status ${status}">${status.toUpperCase()}</div>
                    <div class="key-actions">
                        ${this.getKeyActions(key, status, isBrokenKey)}
                    </div>
                </div>
            `;
        }).join('');

        listContainer.innerHTML = keysHtml;

        // Add event listeners for action buttons
        this.setupKeyActionListeners();
    }

    getKeyStatus(key) {
        if (key.revokedAt || key.status === 'revoked') return 'revoked';
        if (key.status === 'active' || (key.userId && key.status !== 'unused')) return 'active';
        return 'unused';
    }

    getUserInfo(key) {
        if (!key.userId) {
            return '<div><strong>User:</strong> Not activated</div>';
        }

        // If we have user details from the mapping, use them
        if (key.userDetails) {
            const userDetails = key.userDetails;
            let activatedDate = 'Unknown';
            
            if (key.activatedAt) {
                activatedDate = new Date(key.activatedAt.toDate ? key.activatedAt.toDate() : key.activatedAt).toLocaleDateString();
            } else if (key.userLicense && key.userLicense.activatedAt) {
                activatedDate = new Date(key.userLicense.activatedAt.toDate ? 
                    key.userLicense.activatedAt.toDate() : key.userLicense.activatedAt).toLocaleDateString();
            }

            return `
                <div><strong>User:</strong> <span class="key-user">${userDetails.displayName || userDetails.email || 'Unknown'}</span></div>
                <div><strong>Activated:</strong> ${activatedDate}</div>
            `;
        } else {
            // Fallback to just showing user ID
            let activatedDate = 'Unknown';
            if (key.activatedAt) {
                activatedDate = new Date(key.activatedAt.toDate ? key.activatedAt.toDate() : key.activatedAt).toLocaleDateString();
            }
            
            return `
                <div><strong>User:</strong> <span class="key-user">${key.userId.substring(0, 8)}...</span></div>
                <div><strong>Activated:</strong> ${activatedDate}</div>
            `;
        }
    }

    getKeyActions(key, status, isBrokenKey = false) {
        const displayKey = key.licenseKey || key.keyValue || 'UNDEFINED_KEY';
        
        if (isBrokenKey) {
            // For broken keys, only allow deletion
            return `<button class="btn btn-danger delete-key-btn" data-key-id="${key.id}" title="Delete corrupted key">
                <i class="fas fa-trash"></i> Delete
            </button>`;
        }

        const actions = [`<button class="btn btn-secondary copy-key-btn" data-key="${displayKey}">
            <i class="fas fa-copy"></i> Copy
        </button>`];

        // Add revoke button for active keys
        if (status === 'active') {
            actions.push(`<button class="btn btn-danger revoke-key-btn" data-key="${displayKey}" data-key-id="${key.id}">
                <i class="fas fa-ban"></i> Revoke
            </button>`);
        }

        // Add delete button for unused and revoked keys
        if (status === 'unused' || status === 'revoked') {
            actions.push(`<button class="btn btn-danger delete-key-btn" data-key-id="${key.id}" data-key="${displayKey}" title="Permanently delete this license key">
                <i class="fas fa-trash"></i> Delete
            </button>`);
        }

        return actions.join('');
    }

    setupKeyActionListeners() {
        // Copy key buttons
        document.querySelectorAll('.copy-key-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const key = e.target.closest('button').dataset.key;
                try {
                    await navigator.clipboard.writeText(key);
                    this.showNotification('License key copied to clipboard!', 'success');
                } catch (error) {
                    this.showNotification('Failed to copy key', 'error');
                }
            });
        });

        // Revoke key buttons  
        document.querySelectorAll('.revoke-key-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const key = e.target.closest('button').dataset.key;
                const keyId = e.target.closest('button').dataset.keyId;
                const reason = prompt('Enter reason for revocation:');
                if (reason) {
                    try {
                        console.log('🔒 Attempting to revoke key:', key, 'ID:', keyId);
                        
                        // Try direct revocation first
                        let result = await this.licenseManager.revokeLicenseKey(key, reason);
                        
                        // If that fails and we have a keyId, try direct key ID revocation
                        if (!result.success && keyId) {
                            console.log('🔄 Trying direct key ID revocation...');
                            result = await this.revokeLicenseKeyById(keyId, reason);
                        }
                        
                        if (result.success) {
                            this.showNotification('License key revoked successfully!', 'success');
                            this.loadKeyManagementList();
                        } else {
                            this.showNotification(`Error revoking key: ${result.error}`, 'error');
                        }
                    } catch (error) {
                        console.error('❌ Revocation failed:', error);
                        this.showNotification(`Error revoking key: ${error.message}`, 'error');
                    }
                }
            });
        });

        // Delete key buttons (for any type of key)
        document.querySelectorAll('.delete-key-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const keyId = e.target.closest('button').dataset.keyId;
                const keyValue = e.target.closest('button').dataset.key || 'Unknown';
                const keyItem = e.target.closest('.key-item');
                const isBroken = keyItem.dataset.broken === 'true';
                
                let confirmMessage;
                if (isBroken) {
                    confirmMessage = 'This will permanently delete this corrupted license key. This action cannot be undone. Continue?';
                } else {
                    confirmMessage = `This will permanently delete license key "${keyValue}". This action cannot be undone. Continue?`;
                }
                
                const confirmDelete = confirm(confirmMessage);
                if (confirmDelete) {
                    try {
                        await this.deleteLicenseKey(keyId, keyValue, isBroken);
                        this.showNotification('License key deleted successfully!', 'success');
                        this.loadKeyManagementList();
                    } catch (error) {
                        this.showNotification(`Error deleting key: ${error.message}`, 'error');
                    }
                }
            });
        });
    }

    filterKeys(status) {
        if (!this.allKeys) return;

        let filteredKeys = this.allKeys;
        if (status !== 'all') {
            filteredKeys = this.allKeys.filter(key => this.getKeyStatus(key) === status);
        }

        this.displayKeys(filteredKeys);
    }

    searchKeys(searchTerm) {
        if (!this.allKeys) return;

        if (!searchTerm.trim()) {
            this.displayKeys(this.allKeys);
            return;
        }

        const filtered = this.allKeys.filter(key => {
            const searchLower = searchTerm.toLowerCase();
            return (
                (key.licenseKey || '').toLowerCase().includes(searchLower) ||
                (key.keyType || '').toLowerCase().includes(searchLower) ||
                (key.userDetails && (
                    (key.userDetails.displayName || '').toLowerCase().includes(searchLower) ||
                    (key.userDetails.email || '').toLowerCase().includes(searchLower)
                )) ||
                (key.revocationReason || '').toLowerCase().includes(searchLower)
            );
        });

        this.displayKeys(filtered);
    }

    // Fix license status updates after activation
    async activateLicense() {
        const input = document.getElementById('license-key-input');
        const button = document.getElementById('activate-license-btn');
        const result = document.getElementById('activation-result');
        
        if (!input || !button || !result) return;
        
        const licenseKey = input.value.trim();
        
        if (!this.isValidLicenseKeyFormat(licenseKey)) {
            this.showResult(result, 'error', 'Please enter a valid license key format');
            return;
        }

        if (!this.currentUser) {
            this.showResult(result, 'error', 'Please log in to activate a license key');
            return;
        }

        try {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Activating...';
            
            this.showResult(result, 'info', 'Activating license key...');
            
            const response = await this.licenseManager.activateLicenseKey(licenseKey);
            
            if (response.success) {
                this.showResult(result, 'success', response.message);
                input.value = '';
                
                console.log('✅ Activation response received:', response);
                
                // Force immediate navigation update based on response
                if (response.keyType) {
                    const premiumLinks = document.querySelectorAll('a[href="#premium"], a[href="account.html#premium"]');
                    premiumLinks.forEach(link => {
                        if (response.keyType === 'trial') {
                            link.innerHTML = '<i class="fas fa-star"></i> Trial';
                            link.style.color = '#f59e0b';
                        } else {
                            link.innerHTML = '<i class="fas fa-star"></i> Premium';
                            link.style.color = '#ffd700';
                        }
                    });
                    console.log(`🔄 Immediately updated navigation to show ${response.keyType}`);
                }
                
                // Immediate verification - check if license document was created
                const immediateCheck = async () => {
                    try {
                        console.log('🔍 Immediate license document verification...');
                        const licenseDoc = await window.firebase.getDoc(
                            window.firebase.doc(window.firebase.db, 'user_licenses', this.currentUser.uid)
                        );
                        
                        if (licenseDoc.exists()) {
                            console.log('✅ License document found immediately:', licenseDoc.data());
                        } else {
                            console.log('⚠️ License document not found immediately - may need to wait for propagation');
                        }
                    } catch (error) {
                        console.error('❌ Error in immediate verification:', error);
                    }
                };
                
                immediateCheck();
                
                // Force license status refresh with retry logic
                setTimeout(async () => {
                    console.log('🔄 Starting license status refresh after activation...');
                    
                    // Try multiple times with increasing delays to handle Firebase consistency
                    let attempts = 0;
                    const maxAttempts = 5;
                    
                    const checkLicenseStatus = async () => {
                        attempts++;
                        console.log(`🔍 License status check attempt ${attempts}/${maxAttempts}`);
                        
                        const status = await this.licenseManager.getUserLicenseStatus(this.currentUser.uid);
                        console.log(`📊 Status check result:`, status);
                        
                                                 if (status.hasLicense && status.subscriptionStatus === 'active') {
                             console.log('✅ License status confirmed active - updating UI');
                             await this.updateLicenseStatus();
                             
                             // Force immediate navigation update
                             setTimeout(() => {
                                 this.updateNavigationStatus();
                             }, 100);
                             
                             // Additional notification for successful activation
                             if (!response.isReactivation) {
                                 const accountType = status.accountType === 'trial' ? 'Trial' : 'Premium';
                                 this.showNotification(`${accountType} features unlocked! 🎉`, 'success');
                             }
                         } else if (attempts < maxAttempts) {
                            console.log(`⏳ License not yet active, retrying in ${attempts * 1000}ms...`);
                            setTimeout(checkLicenseStatus, attempts * 1000);
                        } else {
                            console.log('❌ Max attempts reached, forcing final UI update');
                            await this.updateLicenseStatus();
                            this.updateNavigationStatus();
                        }
                    };
                    
                    // Start checking
                    checkLicenseStatus();
                    
                    // Refresh key management list if admin
                    if (document.getElementById('key-management-list')) {
                        this.loadKeyManagementList();
                    }
                    
                }, 2000); // 2 second initial delay to ensure database consistency
                
            } else {
                this.showResult(result, 'error', response.error);
            }
        } catch (error) {
            this.showResult(result, 'error', `Error: ${error.message}`);
        } finally {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-unlock"></i> Activate';
        }
    }

    updateNavigationStatus() {
        // Update premium link in navigation if it exists
        const premiumLinks = document.querySelectorAll('a[href="#premium"], a[href="account.html#premium"]');
        premiumLinks.forEach(link => {
            if (this.currentUser) {
                this.licenseManager.getUserLicenseStatus(this.currentUser.uid).then(status => {
                    console.log('🔄 Updating navigation status with:', status);
                    
                    if (status.hasLicense && status.subscriptionStatus === 'active') {
                        if (status.accountType === 'trial') {
                            link.innerHTML = '<i class="fas fa-star"></i> Trial';
                            link.style.color = '#f59e0b'; // Orange for trial
                        } else {
                            link.innerHTML = '<i class="fas fa-star"></i> Premium';
                            link.style.color = '#ffd700'; // Gold for premium
                        }
                    } else {
                        link.innerHTML = '<i class="fas fa-star"></i> Premium';
                        link.style.color = ''; // Reset to default
                    }
                });
            }
        });
    }

    // Delete license keys (admin only)
    async deleteLicenseKey(keyId, keyValue = 'Unknown', isBroken = false) {
        try {
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            // Check admin permissions
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', this.currentUser.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Admin permissions required to delete license keys');
            }

            console.log('🗑️ Deleting license key:', keyId, keyValue);

            // Get the key data before deletion for logging
            const keyDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'license_keys', keyId));
            const keyData = keyDoc.exists() ? keyDoc.data() : null;

            // If the key was active, clean up user license data
            if (keyData && keyData.userId && keyData.status === 'active') {
                console.log('🧹 Cleaning up user license data for active key');
                const userLicenseRef = window.firebase.doc(window.firebase.db, 'user_licenses', keyData.userId);
                const userLicenseDoc = await window.firebase.getDoc(userLicenseRef);
                
                if (userLicenseDoc.exists()) {
                    const userLicense = userLicenseDoc.data();
                    const updatedActiveKeys = (userLicense.activeKeys || []).filter(id => id !== keyId);
                    
                    await window.firebase.updateDoc(userLicenseRef, {
                        activeKeys: updatedActiveKeys,
                        subscriptionStatus: updatedActiveKeys.length > 0 ? 'active' : 'none',
                        accountType: updatedActiveKeys.length > 0 ? userLicense.accountType : 'free',
                        deletedKeys: window.firebase.arrayUnion(keyId),
                        lastUpdate: window.firebase.serverTimestamp()
                    });
                }
            }

            // Delete the license key document
            await window.firebase.deleteDoc(window.firebase.doc(window.firebase.db, 'license_keys', keyId));

            // Log the deletion for audit purposes
            await window.firebase.addDoc(window.firebase.collection(window.firebase.db, 'key_generation_log'), {
                action: isBroken ? 'DELETE_CORRUPTED' : 'DELETE_KEY',
                deletedBy: this.currentUser.uid,
                keyId: keyId,
                keyValue: keyValue,
                keyData: keyData ? {
                    status: keyData.status,
                    keyType: keyData.keyType,
                    userId: keyData.userId
                } : null,
                deletedAt: window.firebase.serverTimestamp(),
                reason: isBroken ? 'Corrupted key cleanup' : 'Admin deletion'
            });

            console.log('✅ License key deleted successfully');
            
        } catch (error) {
            console.error('❌ Error deleting license key:', error);
            throw error;
        }
    }

    // Cleanup all broken/corrupted keys
    async cleanupBrokenKeys() {
        try {
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            // Check admin permissions
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', this.currentUser.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Admin permissions required to cleanup license keys');
            }

            if (!this.allKeys) {
                this.showNotification('Please refresh the key list first', 'warning');
                return;
            }

            // Find all broken keys
            const brokenKeys = this.allKeys.filter(key => !key.licenseKey && !key.keyValue);
            
            if (brokenKeys.length === 0) {
                this.showNotification('No corrupted keys found to cleanup', 'info');
                return;
            }

            const confirmCleanup = confirm(`Found ${brokenKeys.length} corrupted license keys. This will permanently delete them all. Continue?`);
            if (!confirmCleanup) return;

            console.log(`🧹 Starting cleanup of ${brokenKeys.length} corrupted keys...`);
            this.showNotification(`Cleaning up ${brokenKeys.length} corrupted keys...`, 'info');

            let deletedCount = 0;
            let errorCount = 0;

            // Delete each broken key
            for (const brokenKey of brokenKeys) {
                try {
                    await window.firebase.deleteDoc(window.firebase.doc(window.firebase.db, 'license_keys', brokenKey.id));
                    deletedCount++;
                    console.log(`✅ Deleted corrupted key: ${brokenKey.id}`);
                } catch (error) {
                    console.error(`❌ Failed to delete key ${brokenKey.id}:`, error);
                    errorCount++;
                }
            }

            // Log the bulk cleanup
            await window.firebase.addDoc(window.firebase.collection(window.firebase.db, 'key_generation_log'), {
                action: 'BULK_CLEANUP_CORRUPTED',
                deletedBy: this.currentUser.uid,
                deletedAt: window.firebase.serverTimestamp(),
                deletedCount: deletedCount,
                errorCount: errorCount,
                reason: 'Bulk corrupted key cleanup'
            });

            // Show results
            if (deletedCount > 0) {
                this.showNotification(`Successfully deleted ${deletedCount} corrupted keys${errorCount > 0 ? ` (${errorCount} errors)` : ''}`, 'success');
            } else {
                this.showNotification('No keys were deleted', 'warning');
            }

            // Refresh the list
            this.loadKeyManagementList();

        } catch (error) {
            console.error('❌ Error during bulk cleanup:', error);
            this.showNotification(`Error during cleanup: ${error.message}`, 'error');
        }
    }

    // Revoke license key by document ID (fallback method)
    async revokeLicenseKeyById(keyId, reason) {
        try {
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            // Check admin permissions
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', this.currentUser.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Admin permissions required to revoke license keys');
            }

            console.log('🔒 Revoking license key by ID:', keyId);

            // Get the key document
            const keyDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'license_keys', keyId));
            if (!keyDoc.exists()) {
                return {
                    success: false,
                    error: 'License key not found'
                };
            }

            const keyData = keyDoc.data();
            console.log('📄 Key data for revocation:', keyData);

            // Update the license key status
            await window.firebase.updateDoc(keyDoc.ref, {
                status: 'revoked',
                revokedAt: window.firebase.serverTimestamp(),
                revokedBy: this.currentUser.uid,
                revocationReason: reason
            });

            // If the key was active, update the user's license status
            if (keyData.userId && keyData.status === 'active') {
                console.log('🧹 Updating user license status for revoked key');
                const userLicenseRef = window.firebase.doc(window.firebase.db, 'user_licenses', keyData.userId);
                const userLicenseDoc = await window.firebase.getDoc(userLicenseRef);
                
                if (userLicenseDoc.exists()) {
                    const userLicense = userLicenseDoc.data();
                    const updatedActiveKeys = (userLicense.activeKeys || []).filter(id => id !== keyId);
                    
                    // Update user license
                    await window.firebase.updateDoc(userLicenseRef, {
                        activeKeys: updatedActiveKeys,
                        subscriptionStatus: updatedActiveKeys.length > 0 ? 'active' : 'none',
                        accountType: updatedActiveKeys.length > 0 ? userLicense.accountType : 'free',
                        revokedKeys: window.firebase.arrayUnion(keyId),
                        lastUpdate: window.firebase.serverTimestamp()
                    });
                }
            }

            console.log('✅ License key revoked successfully by ID');
            return {
                success: true,
                message: 'License key revoked successfully'
            };

        } catch (error) {
            console.error('❌ Error revoking license key by ID:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Delete all unused keys
    async deleteUnusedKeys() {
        try {
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            // Check admin permissions
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', this.currentUser.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('Admin permissions required to delete license keys');
            }

            if (!this.allKeys) {
                this.showNotification('Please refresh the key list first', 'warning');
                return;
            }

            // Find all unused keys
            const unusedKeys = this.allKeys.filter(key => key.status === 'unused');
            
            if (unusedKeys.length === 0) {
                this.showNotification('No unused keys found to delete', 'info');
                return;
            }

            const confirmDelete = confirm(`Found ${unusedKeys.length} unused license keys. This will permanently delete them all. Continue?`);
            if (!confirmDelete) return;

            console.log(`🗑️ Starting deletion of ${unusedKeys.length} unused keys...`);
            this.showNotification(`Deleting ${unusedKeys.length} unused keys...`, 'info');

            let deletedCount = 0;
            let errorCount = 0;

            // Delete each unused key
            for (const unusedKey of unusedKeys) {
                try {
                    await window.firebase.deleteDoc(window.firebase.doc(window.firebase.db, 'license_keys', unusedKey.id));
                    deletedCount++;
                    console.log(`✅ Deleted unused key: ${unusedKey.licenseKey || unusedKey.id}`);
                } catch (error) {
                    console.error(`❌ Failed to delete key ${unusedKey.id}:`, error);
                    errorCount++;
                }
            }

            // Log the bulk deletion
            await window.firebase.addDoc(window.firebase.collection(window.firebase.db, 'key_generation_log'), {
                action: 'BULK_DELETE_UNUSED',
                deletedBy: this.currentUser.uid,
                deletedAt: window.firebase.serverTimestamp(),
                deletedCount: deletedCount,
                errorCount: errorCount,
                reason: 'Bulk unused key cleanup'
            });

            // Show results
            if (deletedCount > 0) {
                this.showNotification(`Successfully deleted ${deletedCount} unused keys${errorCount > 0 ? ` (${errorCount} errors)` : ''}`, 'success');
            } else {
                this.showNotification('No keys were deleted', 'warning');
            }

            // Refresh the list
            this.loadKeyManagementList();

        } catch (error) {
            console.error('❌ Error during unused key deletion:', error);
            this.showNotification(`Error during deletion: ${error.message}`, 'error');
        }
    }

    // ⚠️ NUCLEAR OPTION: Complete database cleanup
    async nuclearCleanup() {
        try {
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            // Check admin permissions
            const userDoc = await window.firebase.getDoc(window.firebase.doc(window.firebase.db, 'users', this.currentUser.uid));
            if (!userDoc.exists() || userDoc.data().role !== 'admin') {
                throw new Error('NUCLEAR CLEANUP requires admin permissions');
            }

            // Multiple confirmation dialogs
            const confirm1 = confirm(`⚠️ NUCLEAR CLEANUP WARNING ⚠️

This will PERMANENTLY DELETE:
❌ ALL license keys (used, unused, active, revoked)
❌ ALL user license data
❌ ALL license logs and history
❌ EVERYTHING related to the license system

This action is IRREVERSIBLE and will reset the entire license system.

Are you absolutely sure you want to proceed?`);

            if (!confirm1) return;

            const confirm2 = confirm(`🚨 FINAL WARNING 🚨

You are about to OBLITERATE the entire license database.
All users will lose their premium status.
All license keys will be permanently destroyed.

Type "DELETE EVERYTHING" in the next prompt to confirm.`);

            if (!confirm2) return;

            const finalConfirm = prompt(`Type exactly "DELETE EVERYTHING" to confirm nuclear cleanup:`);
            if (finalConfirm !== "DELETE EVERYTHING") {
                this.showNotification('Nuclear cleanup cancelled - wrong confirmation text', 'info');
                return;
            }

            console.log('☢️ INITIATING NUCLEAR CLEANUP - DELETING ENTIRE LICENSE SYSTEM');
            this.showNotification('☢️ NUCLEAR CLEANUP IN PROGRESS - DO NOT CLOSE BROWSER', 'info');

            let totalDeleted = 0;
            let errors = [];

            // Step 1: Delete all license keys
            console.log('🗑️ Step 1: Deleting all license keys...');
            try {
                const keysSnapshot = await window.firebase.getDocs(window.firebase.collection(window.firebase.db, 'license_keys'));
                const keyDeletePromises = [];
                
                keysSnapshot.forEach(doc => {
                    keyDeletePromises.push(window.firebase.deleteDoc(doc.ref));
                });
                
                await Promise.all(keyDeletePromises);
                totalDeleted += keysSnapshot.size;
                console.log(`✅ Deleted ${keysSnapshot.size} license keys`);
            } catch (error) {
                console.error('❌ Error deleting license keys:', error);
                errors.push(`License keys: ${error.message}`);
            }

            // Step 2: Delete all user licenses
            console.log('🗑️ Step 2: Deleting all user licenses...');
            try {
                const licensesSnapshot = await window.firebase.getDocs(window.firebase.collection(window.firebase.db, 'user_licenses'));
                const licenseDeletePromises = [];
                
                licensesSnapshot.forEach(doc => {
                    licenseDeletePromises.push(window.firebase.deleteDoc(doc.ref));
                });
                
                await Promise.all(licenseDeletePromises);
                totalDeleted += licensesSnapshot.size;
                console.log(`✅ Deleted ${licensesSnapshot.size} user licenses`);
            } catch (error) {
                console.error('❌ Error deleting user licenses:', error);
                errors.push(`User licenses: ${error.message}`);
            }

            // Step 3: Delete all license generation logs
            console.log('🗑️ Step 3: Deleting license generation logs...');
            try {
                const logsSnapshot = await window.firebase.getDocs(window.firebase.collection(window.firebase.db, 'key_generation_log'));
                const logDeletePromises = [];
                
                logsSnapshot.forEach(doc => {
                    logDeletePromises.push(window.firebase.deleteDoc(doc.ref));
                });
                
                await Promise.all(logDeletePromises);
                totalDeleted += logsSnapshot.size;
                console.log(`✅ Deleted ${logsSnapshot.size} generation logs`);
            } catch (error) {
                console.error('❌ Error deleting generation logs:', error);
                errors.push(`Generation logs: ${error.message}`);
            }

            // Step 4: Delete all license validation logs
            console.log('🗑️ Step 4: Deleting license validation logs...');
            try {
                const validationLogsSnapshot = await window.firebase.getDocs(window.firebase.collection(window.firebase.db, 'key_validation_log'));
                const validationLogDeletePromises = [];
                
                validationLogsSnapshot.forEach(doc => {
                    validationLogDeletePromises.push(window.firebase.deleteDoc(doc.ref));
                });
                
                await Promise.all(validationLogDeletePromises);
                totalDeleted += validationLogsSnapshot.size;
                console.log(`✅ Deleted ${validationLogsSnapshot.size} validation logs`);
            } catch (error) {
                console.error('❌ Error deleting validation logs:', error);
                errors.push(`Validation logs: ${error.message}`);
            }

            // Final logging
            await window.firebase.addDoc(window.firebase.collection(window.firebase.db, 'key_generation_log'), {
                action: 'NUCLEAR_CLEANUP',
                executedBy: this.currentUser.uid,
                executedAt: window.firebase.serverTimestamp(),
                totalDeleted: totalDeleted,
                errors: errors,
                collections: ['license_keys', 'user_licenses', 'key_generation_log', 'key_validation_log'],
                reason: 'Complete license system reset - removing legacy problematic data'
            });

            // Results
            console.log('☢️ NUCLEAR CLEANUP COMPLETED');
            console.log(`📊 Total documents deleted: ${totalDeleted}`);
            console.log(`❌ Errors encountered: ${errors.length}`);

            let resultMessage = `☢️ NUCLEAR CLEANUP COMPLETED!\n\n`;
            resultMessage += `💥 Total documents obliterated: ${totalDeleted}\n`;
            
            if (errors.length > 0) {
                resultMessage += `⚠️ Errors encountered: ${errors.length}\n`;
                resultMessage += `Details: ${errors.join(', ')}\n\n`;
            }
            
            resultMessage += `🎯 License system has been completely reset.\n`;
            resultMessage += `✅ You can now start fresh with clean data!`;

            alert(resultMessage);
            this.showNotification('Nuclear cleanup completed! License system reset.', 'success');

            // Refresh the list (should be empty now)
            this.loadKeyManagementList();

        } catch (error) {
            console.error('☢️ NUCLEAR CLEANUP FAILED:', error);
            this.showNotification(`Nuclear cleanup failed: ${error.message}`, 'error');
            alert(`☢️ NUCLEAR CLEANUP FAILED!\n\nError: ${error.message}\n\nThe license system may be in an inconsistent state. Please contact support.`);
        }
    }
}

// Initialize the license integration
window.addEventListener('DOMContentLoaded', () => {
    new ArmoryXLicenseIntegration();
}); 