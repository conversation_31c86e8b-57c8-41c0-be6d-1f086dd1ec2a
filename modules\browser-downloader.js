/**
 * Browser Downloader module for Armory X
 * Handles browser-based mod downloading with automatic detection and installation
 */

const { BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

class BrowserDownloader {
  constructor(dependencies = {}) {
    this.mainWindow = null;
    this.browserWindow = null;
    this.modManager = dependencies.modManager;
    this.downloadQueue = new Map();
    this.activeDownloads = new Map();
    this.tempDownloadDir = path.join(os.tmpdir(), 'ArmoryX_Downloads');
    
    // Ensure temp download directory exists
    this.initTempDirectory();
    
    // Register IPC handlers
    this.registerIPCHandlers();
  }

  /**
   * Set the main window reference
   * @param {BrowserWindow} mainWindow - The main application window
   */
  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
  }

  /**
   * Set the mod manager instance
   * @param {ModManager} modManager - ModManager instance
   */
  setModManager(modManager) {
    this.modManager = modManager;
  }

  /**
   * Initialize temporary download directory
   */
  async initTempDirectory() {
    try {
      await fs.ensureDir(this.tempDownloadDir);
      console.log('📁 Initialized temp download directory:', this.tempDownloadDir);
    } catch (error) {
      console.error('❌ Error creating temp download directory:', error);
    }
  }

  /**
   * Register IPC handlers for browser downloader
   */
  registerIPCHandlers() {
    // Open download browser
    ipcMain.handle('open-download-browser', async (event, url) => {
      return await this.openDownloadBrowser(url);
    });

    // Handle download categorization
    ipcMain.handle('categorize-download', async (event, data) => {
      return await this.categorizeAndInstallDownload(data);
    });

    // Cancel download
    ipcMain.handle('cancel-download', async (event, downloadId) => {
      return this.cancelDownload(downloadId);
    });

    // Get active downloads
    ipcMain.handle('get-active-downloads', async (event) => {
      return Array.from(this.activeDownloads.values());
    });

    // Open external URL in system browser
    ipcMain.handle('open-external', async (event, url) => {
      try {
        await shell.openExternal(url);
        return { success: true };
      } catch (error) {
        console.error('❌ Error opening external URL:', error);
        return { success: false, message: error.message };
      }
    });

    // Browser navigation IPC handlers are now in main.js for global access
  }

  /**
   * Open the download browser window
   * @param {string} url - URL to navigate to
   * @returns {Object} Result object
   */
  async openDownloadBrowser(url) {
    console.log('🌐 Opening download browser for URL:', url);

    try {
      // Close existing browser window if open
      if (this.browserWindow && !this.browserWindow.isDestroyed()) {
        this.browserWindow.close();
        // Add small delay to ensure proper cleanup
        await new Promise(resolve => setTimeout(resolve, 100));
      }

            // Create new browser window with optimized settings
      this.browserWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        title: 'Armory X - Mod Download Browser',
        icon: path.join(__dirname, '..', 'assets', 'Armory_X.ico'),
        backgroundColor: '#1f2937', // Dark background to prevent white flash
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false,
          enableRemoteModule: false,
          webSecurity: false, // Allow mixed content for better compatibility
          allowRunningInsecureContent: true,
          plugins: false,
          experimentalFeatures: false,
          partition: 'mod-downloader', // Use separate session
          // Performance optimizations
          disableBackgroundThrottling: false,
          offscreen: false,
          preload: path.join(__dirname, 'browser-preload.js'), // Navigation bar preload script
          sandbox: false, // Disable sandbox for better performance
          spellcheck: false, // Disable spellcheck for better performance
          devTools: true // Enable devtools for advanced users
        },
        show: false,
        autoHideMenuBar: true, // Hide menu bar since we have custom navigation
        resizable: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        // Performance optimizations
        paintWhenInitiallyHidden: false,
        enableLargerThanScreen: false,
        useContentSize: true
      });

      // Maximize the browser window for better user experience
      this.browserWindow.maximize();

             // Set up download handling first (before loading URL)
       this.setupDownloadHandling();
       
       // Set up content blocking for ads/trackers
       this.setupContentBlocking();

      // Prevent external browser popups by handling all navigation
      this.setupNavigationBlocking();

      // Set up navigation event listeners for the new navigation bar
      this.setupNavigationEvents();
        
        // Show window immediately with dark background
        this.browserWindow.show();
        this.browserWindow.focus();
        console.log('✅ Download browser window shown');

        // Load the loading screen HTML first
        const loadingHTML = this.createLoadingPageHTML();
        await this.browserWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(loadingHTML)}`);
        
        console.log('🎬 Loading screen displayed');
        this.loadingStartTime = Date.now();
        
        // Start loading the actual URL after a delay
        setTimeout(async () => {
          try {
            // Set a custom user agent to improve compatibility
            await this.browserWindow.webContents.setUserAgent(
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 ArmoryX/1.0'
            );
            
            // Determine the URL to load
            let finalUrl = url;
            if (!url || url === 'default') {
              // Load default site from settings
              try {
                const { SettingsManager } = require('./settings-manager');
                const settingsManager = new SettingsManager();
                const settings = await settingsManager.loadSettings();
                
                const defaultSite = settings.browser?.defaultSite || 'modrinth';
                const sites = {
                  modrinth: 'https://modrinth.com/mods',
                  curseforge: 'https://www.curseforge.com/minecraft/mc-mods',
                  custom: settings.browser?.customDefaultUrl || 'https://modrinth.com/mods'
                };
                
                finalUrl = sites[defaultSite] || sites.modrinth;
                console.log(`🎯 Loading default site: ${defaultSite} (${finalUrl})`);
              } catch (error) {
                console.error('❌ Error loading settings, using Modrinth as fallback');
                finalUrl = 'https://modrinth.com/mods';
              }
            }
            
            // Load the URL
            await this.browserWindow.loadURL(finalUrl, {
              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 ArmoryX/1.0'
            });
            
            console.log('✅ Browser URL loaded successfully');
          } catch (error) {
            console.warn('⚠️ URL load error (continuing anyway):', error.message);
          }
        }, 2000); // Show loading screen for exactly 2 seconds

      // Handle window closed
      this.browserWindow.on('closed', () => {
        console.log('🔌 Download browser window closed');
        this.browserWindow = null;
        
        // Cancel any active downloads
        this.cancelAllDownloads();
        
        // Clean up listeners
        if (this.categorizationListener) {
          this.categorizationListener = null;
        }
      });

      // Add more detailed logging with reduced verbosity
       this.browserWindow.webContents.on('dom-ready', () => {
         console.log('✅ Browser DOM ready');
       });

      return { success: true, message: 'Download browser opened successfully' };

    } catch (error) {
      console.error('❌ Error opening download browser:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Set up navigation event listeners for the new navigation bar
   */
  setupNavigationEvents() {
    if (!this.browserWindow) return;

    // Add navigation event listeners to notify the preload script
    this.browserWindow.webContents.on('did-navigate', (event, navigationUrl) => {
      this.updateBrowserTitle(navigationUrl);
      // Small delay to ensure navigation state is updated
      setTimeout(() => {
        this.browserWindow.webContents.send('browser-navigation-changed', {
          url: navigationUrl,
          canGoBack: this.browserWindow.webContents.canGoBack(),
          canGoForward: this.browserWindow.webContents.canGoForward()
        });
      }, 100);
    });

    this.browserWindow.webContents.on('did-navigate-in-page', (event, navigationUrl) => {
      this.updateBrowserTitle(navigationUrl);
      // Small delay to ensure navigation state is updated
      setTimeout(() => {
        this.browserWindow.webContents.send('browser-navigation-changed', {
          url: navigationUrl,
          canGoBack: this.browserWindow.webContents.canGoBack(),
          canGoForward: this.browserWindow.webContents.canGoForward()
        });
      }, 100);
    });

    // Also listen for page title updates
    this.browserWindow.webContents.on('page-title-updated', (event, title) => {
      this.browserWindow.setTitle(`Armory X Browser - ${title}`);
    });

    // Listen for new window requests (links that want to open in new tabs)
    this.browserWindow.webContents.setWindowOpenHandler(({ url }) => {
      console.log('🔗 Intercepted new window request:', url);
      // Load the URL in the same window instead of opening a new one
      this.browserWindow.webContents.loadURL(url);
      return { action: 'deny' };
    });

    // Create a minimal menu with just essential options
    const { Menu } = require('electron');
    const minimalMenu = Menu.buildFromTemplate([
      {
        label: 'View',
        submenu: [
          {
            label: 'Zoom In',
            accelerator: 'CmdOrCtrl+Plus',
            click: () => {
              if (this.browserWindow) {
                const currentZoom = this.browserWindow.webContents.getZoomLevel();
                this.browserWindow.webContents.setZoomLevel(currentZoom + 0.5);
              }
            }
          },
          {
            label: 'Zoom Out',
            accelerator: 'CmdOrCtrl+-',
            click: () => {
              if (this.browserWindow) {
                const currentZoom = this.browserWindow.webContents.getZoomLevel();
                this.browserWindow.webContents.setZoomLevel(currentZoom - 0.5);
              }
            }
          },
          {
            label: 'Reset Zoom',
            accelerator: 'CmdOrCtrl+0',
            click: () => {
              if (this.browserWindow) {
                this.browserWindow.webContents.setZoomLevel(0);
              }
            }
          },
          { type: 'separator' },
          {
            label: 'Toggle Developer Tools',
            accelerator: 'F12',
            click: () => {
              if (this.browserWindow) {
                this.browserWindow.webContents.toggleDevTools();
              }
            }
          }
        ]
      }
    ]);

    // Set the minimal menu (only zoom and dev tools)
    this.browserWindow.setMenu(minimalMenu);

    // Add right-click context menu with essential options
    this.browserWindow.webContents.on('context-menu', (event, params) => {
      const { Menu, MenuItem } = require('electron');
      const contextMenu = new Menu();

      // Add standard context menu items if applicable
      if (params.selectionText) {
        contextMenu.append(new MenuItem({
          label: 'Copy',
          role: 'copy'
        }));
        
        contextMenu.append(new MenuItem({
          label: `Search "${params.selectionText}" on Modrinth`,
          click: () => {
            const searchUrl = `https://modrinth.com/mods?q=${encodeURIComponent(params.selectionText)}`;
            this.browserWindow.webContents.loadURL(searchUrl);
          }
        }));
        
        contextMenu.append(new MenuItem({ type: 'separator' }));
      }

      if (params.linkURL) {
        contextMenu.append(new MenuItem({
          label: 'Copy Link',
          click: () => {
            require('electron').clipboard.writeText(params.linkURL);
          }
        }));
        
        contextMenu.append(new MenuItem({ type: 'separator' }));
      }

      contextMenu.append(new MenuItem({
        label: 'Developer Tools',
        click: () => this.browserWindow.webContents.toggleDevTools()
      }));

      contextMenu.popup({ window: this.browserWindow });
    });

    console.log('🧭 Browser navigation events set up');
  }

  /**
   * Show address bar dialog for navigation
   */
  async showAddressBarDialog() {
    if (!this.browserWindow) return;

    const currentURL = this.browserWindow.webContents.getURL();
    
    // Create a simple input dialog window
    const inputWindow = new BrowserWindow({
      width: 600,
      height: 200,
      parent: this.browserWindow,
      modal: true,
      show: false,
      frame: false,
      transparent: true,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });

    const inputHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
          }
          .dialog {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 2rem;
            width: 100%;
            max-width: 500px;
            color: white;
          }
          .title {
            color: #3b82f6;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
          }
          .input-group {
            margin-bottom: 1.5rem;
          }
          .input {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            font-size: 1rem;
          }
          .input:focus {
            outline: none;
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 0.15);
          }
          .buttons {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
          }
          .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
          }
          .btn-cancel {
            background: rgba(107, 114, 128, 0.3);
            color: #d1d5db;
          }
          .btn-cancel:hover {
            background: rgba(107, 114, 128, 0.5);
          }
          .btn-navigate {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            font-weight: 600;
          }
          .btn-navigate:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
          }
        </style>
      </head>
      <body>
        <div class="dialog">
          <h3 class="title">🌐 Navigate to URL</h3>
          <div class="input-group">
            <input type="text" class="input" id="url-input" value="${currentURL}" placeholder="Enter URL or search term..." autofocus>
          </div>
          <div class="buttons">
            <button class="btn btn-cancel" onclick="cancel()">Cancel</button>
            <button class="btn btn-navigate" onclick="navigate()">Go</button>
          </div>
        </div>
        <script>
          const { ipcRenderer } = require('electron');
          
          function navigate() {
            const url = document.getElementById('url-input').value.trim();
            if (url) {
              ipcRenderer.send('navigate-to-url', url);
            }
            window.close();
          }
          
          function cancel() {
            window.close();
          }
          
          // Handle Enter key
          document.getElementById('url-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
              navigate();
            }
          });
          
          // Handle Escape key
          document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
              cancel();
            }
          });
          
          // Auto-select current URL for easy replacement
          document.getElementById('url-input').select();
        </script>
      </body>
      </html>
    `;

    await inputWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(inputHTML)}`);
    inputWindow.show();

    // Set up IPC listener for navigation
    const { ipcMain } = require('electron');
    
    const handleNavigation = (event, url) => {
      let processedUrl = url.trim();
      
      // Add https:// if no protocol specified
      if (!processedUrl.match(/^https?:\/\//)) {
        // Check if it looks like a domain (contains dots)
        if (processedUrl.includes('.')) {
          processedUrl = 'https://' + processedUrl;
        } else {
          // Treat as search query for popular mod sites
          if (processedUrl.toLowerCase().includes('mod') || processedUrl.toLowerCase().includes('minecraft')) {
            processedUrl = `https://modrinth.com/mods?q=${encodeURIComponent(processedUrl)}`;
          } else {
            processedUrl = `https://www.google.com/search?q=${encodeURIComponent(processedUrl)}`;
          }
        }
      }

      console.log('🌐 Navigating to:', processedUrl);
      this.browserWindow.webContents.loadURL(processedUrl);
      
      // Clean up listener
      ipcMain.removeListener('navigate-to-url', handleNavigation);
    };

    ipcMain.once('navigate-to-url', handleNavigation);

    // Clean up if window is closed without navigation
    inputWindow.on('closed', () => {
      ipcMain.removeListener('navigate-to-url', handleNavigation);
    });
  }

  /**
   * Update browser window title with current URL
   */
  updateBrowserTitle(url) {
    if (!this.browserWindow || this.browserWindow.isDestroyed()) return;
    
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname;
      this.browserWindow.setTitle(`Armory X Browser - ${domain}`);
    } catch (error) {
      this.browserWindow.setTitle('Armory X - Mod Download Browser');
    }
  }

  /**
   * Set up navigation blocking to prevent external browser popups
   */
  setupNavigationBlocking() {
    if (!this.browserWindow) return;

    // Block new window creation and redirect to same window
      this.browserWindow.webContents.setWindowOpenHandler(({ url: newUrl }) => {
      console.log('🔗 Redirecting new window request to same window:', newUrl);
      // Load in the same window instead of opening external browser
        this.browserWindow.loadURL(newUrl);
        return { action: 'deny' };
      });

    // Handle navigation events
    this.browserWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      console.log('🧭 Browser navigating to:', navigationUrl);
      // Allow navigation within the browser window
    });

    // Prevent external protocol handler popups
    this.browserWindow.webContents.on('new-window', (event, navigationUrl) => {
      event.preventDefault();
      console.log('🔗 Prevented external window, loading in same window:', navigationUrl);
      this.browserWindow.loadURL(navigationUrl);
    });
  }

  /**
   * Create a complete HTML page with loading screen
   */
  createLoadingPageHTML() {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Loading...</title>
          <style>
              * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
              }
              
              body {
                  height: 100vh;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  background: linear-gradient(135deg, #1f2937, #111827);
                  color: #f9fafb;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  overflow: hidden;
              }
              
              .loading-container {
                  text-align: center;
                  animation: fadeIn 0.5s ease-in;
              }
              
              .loading-icon {
                  font-size: 4rem;
                  margin-bottom: 1rem;
                  animation: pulse 2s infinite;
              }
              
              .loading-title {
                  color: #3b82f6;
                  margin: 0 0 1rem 0;
                  font-size: 2rem;
                  font-weight: 600;
              }
              
              .loading-subtitle {
                  color: #94a3b8;
                  margin: 0 0 2rem 0;
                  font-size: 1rem;
              }
              
              .progress-container {
                  width: 200px;
                  height: 4px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 2px;
                  overflow: hidden;
                  margin: 0 auto;
              }
              
              .progress-bar {
                  width: 100%;
                  height: 100%;
                  background: linear-gradient(90deg, #3b82f6, #10b981);
                  animation: loading 1.5s infinite linear;
                  border-radius: 2px;
              }
              
              @keyframes loading {
                  0% { transform: translateX(-100%); }
                  100% { transform: translateX(100%); }
              }
              
              @keyframes fadeIn {
                  from { opacity: 0; transform: translateY(20px); }
                  to { opacity: 1; transform: translateY(0); }
              }
              
              @keyframes pulse {
                  0%, 100% { transform: scale(1); }
                  50% { transform: scale(1.1); }
              }
          </style>
      </head>
      <body>
          <div class="loading-container">
              <div class="loading-icon">🌐</div>
              <h1 class="loading-title">Loading Website...</h1>
              <p class="loading-subtitle">Please wait while we load the mod website</p>
              <div class="progress-container">
                  <div class="progress-bar"></div>
              </div>
          </div>
      </body>
      </html>
    `;
  }

  /**
   * Set up content blocking for ads and trackers
   */
  setupContentBlocking() {
    if (!this.browserWindow) return;

    const session = this.browserWindow.webContents.session;
    
    // List of ad/tracker domains to block
    const blockedDomains = [
      'doubleclick.net',
      'googleadservices.com',
      'googlesyndication.com',
      'googletagmanager.com',
      'google-analytics.com',
      'facebook.com/tr',
      'facebook.net',
      'pubmatic.com',
      'yahoo.com/sync',
      'mrtnsvr.com',
      'aidemsrv.com',
      'omnitagjs.com',
      'audienceexposure.com',
      'sitescout.com',
      'creativedot2.net',
      'dotomi.com',
      'amazon-adsystem.com',
      'adsystem.amazon.com'
    ];

    // Set up request filter to block ads/trackers
    session.webRequest.onBeforeRequest((details, callback) => {
      const url = details.url.toLowerCase();
      
      // Check if URL contains blocked domains
      const isBlocked = blockedDomains.some(domain => url.includes(domain));
      
      if (isBlocked) {
        console.log('🚫 Blocked ad/tracker request:', details.url);
        callback({ cancel: true });
      } else {
        callback({ cancel: false });
      }
    });

    // Handle certificate errors gracefully
    this.browserWindow.webContents.on('certificate-error', (event, url, error, certificate, callback) => {
      console.warn('⚠️ Certificate error (ignoring):', url, error);
      event.preventDefault();
      callback(true); // Ignore certificate errors
    });

    console.log('🛡️ Content blocking enabled for browser');
  }

  /**
   * Set up download detection and handling
   */
  setupDownloadHandling() {
    if (!this.browserWindow) return;

    const session = this.browserWindow.webContents.session;

    // Clear any previous download handlers
    session.removeAllListeners('will-download');

    // Handle downloads
    session.on('will-download', (event, item, webContents) => {
      console.log('📥 Download detected:', item.getFilename());
      
      const downloadId = this.generateDownloadId();
      const filename = item.getFilename();
      const totalBytes = item.getTotalBytes();
      const downloadPath = path.join(this.tempDownloadDir, filename);

      // Set the download path
      item.setSavePath(downloadPath);

      // Create download tracking object
      const downloadInfo = {
        id: downloadId,
        filename: filename,
        downloadPath: downloadPath,
        totalBytes: totalBytes,
        receivedBytes: 0,
        progress: 0,
        speed: 0,
        timeRemaining: 0,
        status: 'downloading',
        startTime: Date.now()
      };

      this.activeDownloads.set(downloadId, downloadInfo);

      // Show in-browser progress modal immediately
      this.showInBrowserProgressModal(downloadInfo);

      // Add artificial delay to slow down the download process for better UX
      let artificialDelay = 0;
      const minDownloadTime = 2000; // 2 seconds minimum

      // Track download progress
      item.on('updated', (event, state) => {
                 if (state === 'interrupted') {
          console.log('⚠️ Download interrupted');
           downloadInfo.status = 'interrupted';
         } else if (state === 'progressing') {
          if (item.isPaused()) {
            downloadInfo.status = 'paused';
          } else {
            const receivedBytes = item.getReceivedBytes();
            const progress = totalBytes > 0 ? (receivedBytes / totalBytes) * 100 : 0;
            
            // Calculate speed and time remaining
            const elapsed = Date.now() - downloadInfo.startTime;
            const speed = elapsed > 0 ? receivedBytes / (elapsed / 1000) : 0;
            const timeRemaining = speed > 0 ? (totalBytes - receivedBytes) / speed : 0;
            
            downloadInfo.receivedBytes = receivedBytes;
            downloadInfo.progress = progress;
            downloadInfo.speed = speed;
            downloadInfo.timeRemaining = timeRemaining;
            downloadInfo.status = 'downloading';
            
            // Update in-browser progress modal
            this.updateInBrowserProgressModal(downloadInfo);
          }
        }
      });

      // Handle download completion
      item.once('done', async (event, state) => {
        const downloadDuration = Date.now() - downloadInfo.startTime;
        const remainingDelay = Math.max(0, minDownloadTime - downloadDuration);
        
        if (state === 'completed') {
          downloadInfo.status = 'completed';
          downloadInfo.progress = 100;
          
          // Wait for artificial delay if download was too fast
          if (remainingDelay > 0) {
            console.log(`⏱️ Adding ${remainingDelay}ms delay for better UX`);
            downloadInfo.status = 'processing';
            this.updateInBrowserProgressModal(downloadInfo);
            await new Promise(resolve => setTimeout(resolve, remainingDelay));
          }
          
          console.log('✅ Download completed:', filename);
          this.activeDownloads.delete(downloadId);

          // Close progress modal and show categorization dialog smoothly
          await this.closeInBrowserProgressModal();
          await new Promise(resolve => setTimeout(resolve, 300)); // Brief transition
           await this.showCategorizationDialog(downloadInfo);
          
                 } else {
          console.log('❌ Download failed:', filename, 'State:', state);
           downloadInfo.status = 'failed';
          this.activeDownloads.delete(downloadId);
          await this.closeInBrowserProgressModal();
          this.sendToMainWindow('download-failed', { filename, reason: state });
        }
      });
    });
  }

  /**
   * Create overlay window for progress and categorization dialogs
   */
  createOverlayWindow() {
    if (this.overlayWindow && !this.overlayWindow.isDestroyed()) {
      return this.overlayWindow;
    }

    // Get browser window position and size to cover the entire browser window
    let parentBounds = { x: 100, y: 100, width: 1400, height: 900 };
    if (this.browserWindow && !this.browserWindow.isDestroyed()) {
      parentBounds = this.browserWindow.getBounds();
    }

    this.overlayWindow = new BrowserWindow({
      width: parentBounds.width,
      height: parentBounds.height,
      x: parentBounds.x,
      y: parentBounds.y,
      frame: false,
      transparent: true,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      show: false,
      parent: this.browserWindow, // Attach to browser window
      modal: true, // Make it modal to ensure proper overlay behavior
      webPreferences: {
        nodeIntegration: true, // Enable for IPC functionality
        contextIsolation: false, // Disable for simpler IPC access
        enableRemoteModule: false
      }
    });

    // Keep overlay positioned correctly when browser moves or resizes
    if (this.browserWindow && !this.browserWindow.isDestroyed()) {
      const updateOverlayPosition = () => {
        if (this.overlayWindow && !this.overlayWindow.isDestroyed()) {
          const newBounds = this.browserWindow.getBounds();
          this.overlayWindow.setBounds(newBounds);
        }
      };

      this.browserWindow.on('moved', updateOverlayPosition);
      this.browserWindow.on('resized', updateOverlayPosition);
      
      // Clean up listeners when overlay is closed
      this.overlayWindow.on('closed', () => {
        if (this.browserWindow && !this.browserWindow.isDestroyed()) {
          this.browserWindow.removeListener('moved', updateOverlayPosition);
          this.browserWindow.removeListener('resized', updateOverlayPosition);
        }
      });
    }

    return this.overlayWindow;
  }

  /**
   * Show in-browser progress modal using overlay window
   * @param {Object} downloadInfo - Download information
   */
  async showInBrowserProgressModal(downloadInfo) {
    try {
      const overlay = this.createOverlayWindow();
      
      const progressHTML = this.createProgressOverlayHTML(downloadInfo);
      await overlay.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(progressHTML)}`);
      overlay.show();
      overlay.focus();
      
      console.log('📊 Overlay progress modal shown');
    } catch (error) {
      console.error('❌ Error showing progress modal:', error);
    }
  }

  /**
   * Update in-browser progress modal
   * @param {Object} downloadInfo - Download information
   */
  async updateInBrowserProgressModal(downloadInfo) {
    if (!this.overlayWindow || this.overlayWindow.isDestroyed()) return;

    try {
      const formatBytes = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
      };

      const formatSpeed = (bytesPerSecond) => {
        return formatBytes(bytesPerSecond) + '/s';
      };

      const formatTime = (seconds) => {
        if (seconds < 60) return Math.round(seconds) + 's';
        const minutes = Math.floor(seconds / 60);
        return minutes + 'm ' + Math.round(seconds % 60) + 's';
      };

      const statusText = downloadInfo.status === 'processing' ? 'Processing...' : 
                        downloadInfo.status === 'completed' ? 'Completed!' : 'Downloading...';

      await this.overlayWindow.webContents.executeJavaScript(`
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const statusElement = document.getElementById('download-status');
        const speedElement = document.getElementById('download-speed');
        const etaElement = document.getElementById('download-eta');
        
        if (progressBar) {
          progressBar.style.width = '${Math.round(downloadInfo.progress)}%';
        }
        if (progressText) {
          progressText.textContent = '${Math.round(downloadInfo.progress)}%';
        }
        if (statusElement) {
          statusElement.textContent = '${statusText}';
        }
        if (speedElement && ${downloadInfo.speed > 0}) {
          speedElement.textContent = '${formatSpeed(downloadInfo.speed)}';
        }
        if (etaElement && ${downloadInfo.timeRemaining > 0 && downloadInfo.status === 'downloading'}) {
          etaElement.textContent = 'ETA: ${formatTime(downloadInfo.timeRemaining)}';
        }
      `);
    } catch (error) {
      console.error('❌ Error updating progress modal:', error);
    }
  }

  /**
   * Close in-browser progress modal
   */
  async closeInBrowserProgressModal() {
    if (!this.overlayWindow || this.overlayWindow.isDestroyed()) return;

    try {
      this.overlayWindow.close();
      this.overlayWindow = null;
      console.log('📊 Overlay progress modal closed');
    } catch (error) {
      console.error('❌ Error closing progress modal:', error);
    }
  }

  /**
   * Create progress overlay HTML
   * @param {Object} downloadInfo - Download information
   * @returns {string} HTML string
   */
  createProgressOverlayHTML(downloadInfo) {
    const formatBytes = (bytes) => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            margin: 0;
            padding: 0;
          }
          
          .modal {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            color: white;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease;
            width: 400px;
          }
          
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px) scale(0.9); }
            to { opacity: 1; transform: translateY(0) scale(1); }
          }
          
          .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
          }
          
          .title {
            color: #3b82f6;
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
            font-weight: 600;
          }
          
          .filename {
            color: #94a3b8;
            margin: 0 0 1.5rem 0;
            font-size: 0.9rem;
            word-break: break-all;
          }
          
          .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
          }
          
          .status {
            color: #10b981;
            font-weight: 600;
          }
          
          .percentage {
            color: #3b82f6;
            font-weight: 600;
          }
          
          .progress-container {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
          }
          
          .progress-bar {
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
          
          .stats-row {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #6b7280;
            margin-bottom: 1.5rem;
          }
          
          .next-step {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.05));
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
          }
          
          .next-step-text {
            color: #10b981;
            font-size: 0.85rem;
          }
        </style>
      </head>
      <body>
        <div class="modal">
          <div class="icon">📥</div>
          <h2 class="title">Downloading File</h2>
          <p class="filename">${downloadInfo.filename}</p>
          
          <div class="status-row">
            <span id="download-status" class="status">Downloading...</span>
            <span id="progress-text" class="percentage">0%</span>
          </div>
          
          <div class="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
          </div>
          
          <div class="stats-row">
            <span id="download-speed">Calculating speed...</span>
            <span id="download-eta"></span>
          </div>
          
          <div class="next-step">
            <span>🎯</span>
            <span class="next-step-text"><strong>Next:</strong> You'll be asked to categorize this file for proper installation</span>
          </div>
        </div>
      </body>
      </html>
    `;
  }

    /**
   * Show file categorization dialog using overlay window
   * @param {Object} downloadInfo - Download information
   */
  async showCategorizationDialog(downloadInfo) {
    try {
      // Store download info in class property to avoid cloning issues
      this.pendingDownloadInfo = downloadInfo;
      
      // Analyze file to suggest category
      const suggestedCategory = this.analyzeFileCategory(downloadInfo.filename, downloadInfo.downloadPath);
      
      // Create a new overlay window for categorization (or reuse existing)
      const overlay = this.createOverlayWindow();
      
      // Create categorization HTML
      const categorizationHTML = this.createCategorizationOverlayHTML(downloadInfo, suggestedCategory);
      
      // Load the categorization dialog
      await overlay.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(categorizationHTML)}`);
      overlay.show();
      overlay.focus();
      
      // Set up IPC listeners for categorization responses
      this.setupCategorizationIPCListeners();
      
      console.log('🎯 Categorization dialog shown in overlay');

    } catch (error) {
      console.error('❌ Error showing categorization dialog:', error);
    }
  }

  /**
   * Set up IPC listeners for categorization responses
   */
  setupCategorizationIPCListeners() {
    const { ipcMain } = require('electron');
    
    // Remove existing listeners if any
    ipcMain.removeAllListeners('categorization-response');
    
    // Set up new listener
    ipcMain.once('categorization-response', (event, data) => {
      try {
        if (data.action === 'install' && data.category) {
          this.handleCategorizationResponse({
            action: 'install',
            category: data.category,
            downloadInfo: this.pendingDownloadInfo
          });
        } else if (data.action === 'cancel') {
          this.handleCategorizationResponse({
            action: 'cancel',
            downloadInfo: this.pendingDownloadInfo
          });
        }
        
        // Clean up
        this.pendingDownloadInfo = null;
        
        // Close overlay window
        if (this.overlayWindow && !this.overlayWindow.isDestroyed()) {
          this.overlayWindow.close();
          this.overlayWindow = null;
        }
    } catch (error) {
        console.error('❌ Error handling categorization response:', error);
    }
    });
  }

  /**
   * Analyze file to suggest category
   * @param {string} filename - File name
   * @param {string} filePath - File path
   * @returns {string} Suggested category
   */
  analyzeFileCategory(filename, filePath) {
    const name = filename.toLowerCase();
    const ext = path.extname(filename).toLowerCase();

    // Analyze by file extension
    if (ext === '.jar') {
      return 'Mods';
    } else if (ext === '.zip' || ext === '.rar' || ext === '.7z') {
      // Analyze by name patterns for zip files
      if (name.includes('shader') || name.includes('seus') || name.includes('bsl') ||
          name.includes('complementary') || name.includes('sildurs') || name.includes('chocapic')) {
        return 'Shaders';
      } else if (name.includes('texture') || name.includes('resource') || name.includes('pack') ||
                 name.includes('faithful') || name.includes('sphax') || name.includes('graphics')) {
        return 'Resource Packs';
      } else {
        return 'Mods';
      }
    }

    // Default to Mods
    return 'Mods';
  }

  /**
   * Create categorization overlay HTML
   * @param {Object} downloadInfo - Download information
   * @param {string} suggestedCategory - Suggested category
   * @returns {string} HTML string
   */
  createCategorizationOverlayHTML(downloadInfo, suggestedCategory) {
    const formatBytes = (bytes) => {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
                    body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            padding: 1rem;
            margin: 0;
          }
          
          .modal {
            background: linear-gradient(135deg, #1e293b, #0f172a);
          border: 2px solid rgba(59, 130, 246, 0.3);
          border-radius: 16px;
          padding: 2rem;
            text-align: center;
            color: white;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease;
            width: 100%;
            max-width: 480px;
            max-height: 90vh;
            overflow-y: auto;
          }
          
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px) scale(0.9); }
            to { opacity: 1; transform: translateY(0) scale(1); }
          }
          
          .title {
            color: #3b82f6;
            margin: 0 0 1.5rem 0;
            font-size: 1.4rem;
            font-weight: 600;
          }
          
          .file-info {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            margin-bottom: 2rem;
            text-align: left;
          }
          
          .file-icon {
            font-size: 3rem;
            margin-right: 1rem;
          }
          
          .file-details h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            word-break: break-all;
          }
          
          .file-details p {
            margin: 0;
            color: #94a3b8;
            font-size: 0.9rem;
          }
          
          .instruction {
            color: #e2e8f0;
            margin-bottom: 1.5rem;
            line-height: 1.5;
            text-align: left;
          }
          
          .categories {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            margin-bottom: 1.5rem;
          }
          
          .category-option {
               display: block;
               cursor: pointer;
               padding: 1rem;
               background: linear-gradient(135deg, #1f2937, #111827);
            border: 2px solid rgba(255, 255, 255, 0.1);
               border-radius: 8px;
               transition: all 0.3s ease;
            text-align: left;
          }
          
          .category-option:hover {
            border-color: rgba(59, 130, 246, 0.3);
          }
          
          .category-option.selected {
            border-color: rgba(16, 185, 129, 0.5);
          }
          
          .category-content {
            display: flex;
            align-items: center;
            gap: 1rem;
          }
          
          .category-icon {
            font-size: 1.5rem;
          }
          
          .category-text strong {
               display: block;
            color: #f8fafc;
            font-size: 1rem;
            margin-bottom: 0.2rem;
          }
          
          .category-text small {
            color: #94a3b8;
            font-size: 0.85rem;
          }
          
          .ai-suggestion {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              padding: 0.8rem;
              background: rgba(16, 185, 129, 0.1);
              border: 1px solid rgba(16, 185, 129, 0.3);
              border-radius: 6px;
              color: #d1fae5;
              font-size: 0.9rem;
              margin-bottom: 1.5rem;
          }
          
          .ai-suggestion strong {
            color: #10b981;
          }
          
          .buttons {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
          }
          
          .btn {
              padding: 0.8rem 1.5rem;
              border-radius: 8px;
              cursor: pointer;
              font-size: 1rem;
              transition: all 0.3s ease;
              border: none;
          }
          
          .btn-cancel {
            background: rgba(107, 114, 128, 0.2);
            border: 1px solid rgba(107, 114, 128, 0.3);
            color: #d1d5db;
          }
          
          .btn-cancel:hover {
            background: rgba(107, 114, 128, 0.3);
          }
          
          .btn-install {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
              color: white;
              font-weight: 600;
            opacity: 0.5;
          }
          
          .btn-install.enabled {
            opacity: 1;
          }
          
          .btn-install.enabled:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
          }
          
          .error-message {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(239, 68, 68, 0.9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.85rem;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
          }
        </style>
      </head>
      <body>
        <div class="modal">
          <h2 class="title">🎯 Categorize Downloaded File</h2>
          
          <div class="file-info">
            <div class="file-icon">📦</div>
            <div class="file-details">
              <h3>${downloadInfo.filename}</h3>
              <p>${formatBytes(downloadInfo.totalBytes)}</p>
          </div>
        </div>
          
          <p class="instruction">
            Please select what type of file this is so Armory X can install it to the correct location:
          </p>
          
          <div class="categories">
            <label class="category-option ${suggestedCategory === 'Mods' ? 'selected' : ''}" data-value="Mods">
              <input type="radio" name="file-category" value="Mods" ${suggestedCategory === 'Mods' ? 'checked' : ''} style="display: none;">
              <div class="category-content">
                <span class="category-icon">🧩</span>
                <div class="category-text">
                  <strong>Mod</strong>
                  <small>Game modification (.jar files)</small>
      </div>
              </div>
            </label>
            
            <label class="category-option ${suggestedCategory === 'Resource Packs' ? 'selected' : ''}" data-value="Resource Packs">
              <input type="radio" name="file-category" value="Resource Packs" ${suggestedCategory === 'Resource Packs' ? 'checked' : ''} style="display: none;">
              <div class="category-content">
                <span class="category-icon">🎨</span>
                <div class="category-text">
                  <strong>Resource Pack</strong>
                  <small>Texture and sound packs</small>
                </div>
              </div>
            </label>
            
            <label class="category-option ${suggestedCategory === 'Shaders' ? 'selected' : ''}" data-value="Shaders">
              <input type="radio" name="file-category" value="Shaders" ${suggestedCategory === 'Shaders' ? 'checked' : ''} style="display: none;">
              <div class="category-content">
                <span class="category-icon">✨</span>
                <div class="category-text">
                  <strong>Shader Pack</strong>
                  <small>Visual enhancement shaders</small>
                </div>
              </div>
            </label>
          </div>
          
          ${suggestedCategory ? `
            <div class="ai-suggestion">
              <span>🤖</span>
              <span>AI suggests: <strong>${suggestedCategory}</strong> (pre-selected)</span>
            </div>
          ` : ''}
          
          <div class="buttons">
            <button class="btn btn-cancel" onclick="cancel()">
              Cancel
            </button>
            <button class="btn btn-install ${suggestedCategory ? 'enabled' : ''}" id="install-btn" onclick="install()">
              Install & Categorize
            </button>
          </div>
        </div>
        
        <script>
          let ipcRenderer;
          let selectedCategory = '${suggestedCategory || ''}';
          
          // Initialize IPC renderer
          try {
            const { ipcRenderer: ipc } = require('electron');
            ipcRenderer = ipc;
            console.log('IPC Renderer initialized successfully');
          } catch (error) {
            console.error('Failed to initialize IPC Renderer:', error);
            // Fallback: try to send message to parent window
            ipcRenderer = {
              send: (channel, data) => {
                console.log('Fallback IPC send:', channel, data);
                if (window.opener) {
                  window.opener.postMessage({ channel, data }, '*');
                }
              }
            };
          }
          
          // Wait for DOM to be ready
          document.addEventListener('DOMContentLoaded', function() {
            setupUI();
          });
          
          // If DOM is already ready
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupUI);
          } else {
            setupUI();
          }
          
          function setupUI() {
            console.log('Setting up UI...');
            
            // Set up category selection
            document.querySelectorAll('.category-option').forEach(option => {
              console.log('Setting up category option:', option.dataset.value);
              
              option.addEventListener('click', function() {
                console.log('Category clicked:', option.dataset.value);
                
                // Remove selected state from all
                document.querySelectorAll('.category-option').forEach(opt => {
                  opt.classList.remove('selected');
                });
                document.querySelectorAll('input[name="file-category"]').forEach(input => {
                  input.checked = false;
                });
                
                // Add selected state to clicked option
                option.classList.add('selected');
                const input = option.querySelector('input');
                if (input) {
                  input.checked = true;
                }
                selectedCategory = option.dataset.value;
                
                console.log('Selected category:', selectedCategory);
                
                // Enable install button
                const installBtn = document.getElementById('install-btn');
                if (installBtn) {
                  installBtn.classList.add('enabled');
                  console.log('Install button enabled');
                }
              });
            });
            
            // Set up button click handlers
            const installBtn = document.getElementById('install-btn');
            if (installBtn) {
              installBtn.addEventListener('click', install);
              console.log('Install button handler attached');
            }
            
            const cancelBtn = document.querySelector('.btn-cancel');
            if (cancelBtn) {
              cancelBtn.addEventListener('click', cancel);
              console.log('Cancel button handler attached');
            }
            
            console.log('UI setup complete');
          }
          
          function install() {
            console.log('Install clicked, selected category:', selectedCategory);
            
            if (!selectedCategory) {
              showError('Please select a category first');
              return;
            }
            
            console.log('Sending categorization response via IPC');
            ipcRenderer.send('categorization-response', {
              action: 'install',
              category: selectedCategory
            });
          }
          
          function cancel() {
            console.log('Cancel clicked');
            ipcRenderer.send('categorization-response', {
              action: 'cancel'
            });
          }
          
          function showError(message) {
            console.log('Showing error:', message);
            const error = document.createElement('div');
            error.className = 'error-message';
            error.textContent = message;
            document.body.appendChild(error);
            
            setTimeout(() => {
              if (error.parentNode) {
                error.remove();
              }
            }, 3000);
          }
        </script>
      </body>
      </html>
    `;
  }

  /**
   * Handle categorization response from browser
   * @param {Object} data - Response data
   */
  async handleCategorizationResponse(data) {
    console.log('📦 Handling categorization response:', data);
    
    if (data.action === 'install') {
      const result = await this.categorizeAndInstallDownload({
        downloadInfo: data.downloadInfo,
        category: data.category
      });
      
      if (result.success) {
        // Send success notification to main window
        this.sendToMainWindow('download-installed', {
          filename: data.downloadInfo.filename,
          category: data.category,
          message: result.message
        });
      } else {
        // Send error to main window
        this.sendToMainWindow('download-error', {
          id: data.downloadInfo.id,
          error: result.message
        });
      }
    } else if (data.action === 'cancel') {
      // Cancel the download
      this.cancelDownload(data.downloadInfo.id);
    }
  }

  /**
   * Categorize and install downloaded file
   * @param {Object} data - Categorization data
   * @returns {Object} Installation result
   */
  async categorizeAndInstallDownload(data) {
    console.log('📦 Categorizing and installing download:', data);

    try {
      const { downloadInfo, category } = data;
      
      if (!downloadInfo || !category) {
        throw new Error('Missing download info or category');
      }

      if (!this.modManager) {
        throw new Error('Mod manager not available');
      }

      // Prepare installation data for the mod manager
      const installData = {
        gameId: 'minecraft', // Currently only supporting Minecraft
        modFileData: [{
          filePath: downloadInfo.downloadPath,
          fileName: downloadInfo.filename,
          category: category
        }]
      };

      // Install using mod manager
      const result = await this.modManager.installModsWithCategories(null, installData);

      if (result.success) {
        console.log('✅ Download installed successfully:', downloadInfo.filename);
        console.log('📂 Installation details:', result);
        
        // Clean up temporary file only after successful installation
        try {
        await this.cleanupTempFile(downloadInfo.downloadPath);
        } catch (cleanupError) {
          console.warn('⚠️ Could not clean up temp file (installation still successful):', cleanupError);
        }
        
        // Send detailed success notification
        this.sendToMainWindow('download-installed', {
          filename: downloadInfo.filename,
          category: category,
          message: result.message,
          installedMods: result.installedMods || 1,
          targetPaths: result.targetPaths || []
        });
        
        return { success: true, message: result.message };
      } else {
        throw new Error(result.message || 'Installation failed');
      }

    } catch (error) {
      console.error('❌ Error categorizing and installing download:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Cancel a download
   * @param {string} downloadId - Download ID to cancel
   * @returns {boolean} Success status
   */
  cancelDownload(downloadId) {
    const downloadInfo = this.activeDownloads.get(downloadId);
    
    if (downloadInfo) {
      console.log('🚫 Cancelling download:', downloadInfo.filename);
      downloadInfo.status = 'cancelled';
      this.activeDownloads.delete(downloadId);
      
      // Clean up temp file if it exists
      this.cleanupTempFile(downloadInfo.downloadPath);
      
      this.sendToMainWindow('download-cancelled', { id: downloadId });
      return true;
    }
    
    return false;
  }

  /**
   * Cancel all active downloads
   */
  cancelAllDownloads() {
    console.log('🚫 Cancelling all active downloads');
    
    for (const [downloadId, downloadInfo] of this.activeDownloads) {
      downloadInfo.status = 'cancelled';
      this.cleanupTempFile(downloadInfo.downloadPath);
    }
    
    this.activeDownloads.clear();
  }

  /**
   * Clean up temporary download file
   * @param {string} filePath - Path to file to clean up
   */
  async cleanupTempFile(filePath) {
    try {
      if (await fs.pathExists(filePath)) {
        await fs.remove(filePath);
        console.log('🧹 Cleaned up temp file:', path.basename(filePath));
      }
    } catch (error) {
      console.error('❌ Error cleaning up temp file:', error);
    }
  }

  /**
   * Generate unique download ID
   * @returns {string} Download ID
   */
  generateDownloadId() {
    return `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Send message to main window
   * @param {string} event - Event name
   * @param {Object} data - Event data
   */
  sendToMainWindow(event, data) {
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send(event, data);
    }
  }

  /**
   * Close browser window
   */
  closeBrowser() {
    if (this.browserWindow && !this.browserWindow.isDestroyed()) {
      this.browserWindow.close();
    }
  }

  /**
   * Get browser window instance
   * @returns {BrowserWindow|null} Browser window instance
   */
  getBrowserWindow() {
    return this.browserWindow;
  }
}

module.exports = { BrowserDownloader }; 