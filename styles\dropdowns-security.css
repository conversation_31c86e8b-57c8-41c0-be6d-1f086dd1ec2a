/* ===================================
   Dropdowns and Security Components
   =================================== */

/* File Types Dropdown Styling */
.file-types-section {
    margin-top: 10px;
}

.file-types-section label {
    display: block;
    margin-bottom: 5px;
    color: #e5e7eb;
    font-size: 0.9rem;
    font-weight: 500;
}

.file-types-dropdown {
    position: relative;
    width: 100%;
}

.dropdown-toggle {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-toggle:hover {
    border-color: var(--primary-color);
    background: var(--background-hover);
}

.dropdown-toggle:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.dropdown-arrow {
    font-size: 0.8rem;
    color: var(--text-secondary);
    transition: transform 0.3s ease;
}

.file-types-dropdown.open .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.file-types-dropdown.open .dropdown-options {
    display: block;
}

.dropdown-option {
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-option:last-child {
    border-bottom: none;
}

.dropdown-option:hover {
    background: var(--background-hover);
    color: var(--primary-color);
}

.dropdown-option.selected {
    background: rgba(0, 180, 255, 0.1);
    color: var(--primary-color);
}

.dropdown-option-icon {
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.dropdown-option-text {
    flex: 1;
    font-size: 0.9rem;
}

.dropdown-option-check {
    font-size: 0.8rem;
    color: var(--success-color);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dropdown-option.selected .dropdown-option-check {
    opacity: 1;
}

/* Custom scrollbar for dropdown */
.dropdown-options::-webkit-scrollbar {
    width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
}

.dropdown-options::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Multi-select dropdown */
.dropdown-multi-select {
    position: relative;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.selected-item {
    background: rgba(0, 180, 255, 0.2);
    color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.selected-item-remove {
    cursor: pointer;
    font-size: 0.7rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.selected-item-remove:hover {
    opacity: 1;
}

/* Security Options */
.security-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1.5rem 0;
}

.security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.security-item:hover {
    background: var(--background-hover);
    border-color: rgba(255, 255, 255, 0.2);
}

.security-info {
    flex: 1;
    margin-right: 1rem;
}

.security-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    font-weight: 600;
}

.security-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    margin: 0;
}

.security-control {
    flex-shrink: 0;
}

.security-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
}

.security-status.enabled {
    color: var(--success-color);
}

.security-status.disabled {
    color: var(--error-color);
}

.security-status.warning {
    color: var(--warning-color);
}

.security-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Data Options */
.data-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin: 1.5rem 0;
}

.data-option {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 200px;
    text-align: center;
}

.data-option:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.data-option.selected {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.1);
}

.data-option-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.data-option-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.data-option-description {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Advanced Dropdown Styles */
.advanced-dropdown {
    position: relative;
}

.dropdown-search {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-bottom: none;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
}

.dropdown-search:focus {
    border-color: var(--primary-color);
}

.dropdown-search::placeholder {
    color: var(--text-muted);
}

.dropdown-group {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-group:last-child {
    border-bottom: none;
}

.dropdown-group-header {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-no-results {
    padding: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
    font-style: italic;
}

/* Dropdown Loading State */
.dropdown-loading {
    padding: 1rem;
    text-align: center;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.dropdown-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dropdown-options {
        max-height: 150px;
    }
    
    .security-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .security-info {
        margin-right: 0;
    }
    
    .security-control {
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
    
    .data-options {
        flex-direction: column;
    }
    
    .data-option {
        min-width: auto;
    }
    
    .selected-items {
        max-height: 100px;
        overflow-y: auto;
    }
    
    .dropdown-toggle {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .dropdown-option {
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .security-options {
        gap: 0.75rem;
    }
    
    .security-item {
        padding: 0.75rem;
    }
    
    .data-option {
        padding: 0.75rem;
    }
    
    .data-option-icon {
        font-size: 1.5rem;
    }
    
    .dropdown-options {
        max-height: 120px;
    }
}
