/* ===================================
   Confirmation Dialogs and Game Integration
   =================================== */

/* Confirmation Modal Base */
.confirm-modal {
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90vw;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

/* Steam and Xbox Game Confirmation Dialogs */
.steam-confirmation, .xbox-confirmation {
    text-align: center;
    padding: 1rem;
}

.confirmation-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    color: var(--primary-color);
}

.steam-confirmation h3, .xbox-confirmation h3 {
    color: #3b82f6;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.steam-options, .xbox-info {
    margin: 1.5rem 0;
    text-align: left;
}

.steam-options p, .xbox-info p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.steam-options ul, .xbox-info ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.steam-options li, .xbox-info li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.steam-options li::before, .xbox-info li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    font-size: 1rem;
}

/* Confirmation Buttons */
.confirmation-buttons, .reselect-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.confirmation-buttons .btn, .reselect-buttons .btn {
    min-width: 180px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.confirmation-buttons .btn:hover, .reselect-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.confirmation-buttons .btn-primary, .reselect-buttons .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
}

.confirmation-buttons .btn-primary:hover, .reselect-buttons .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.confirmation-buttons .btn-secondary, .reselect-buttons .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.confirmation-buttons .btn-secondary:hover, .reselect-buttons .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Game Type Confirmation Dialog */
.game-type-confirmation {
    text-align: center;
    padding: 1rem;
}

.game-type-confirmation h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.type-options-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.type-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.type-option:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.type-option.selected {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.15);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.type-option-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
    color: var(--primary-color);
}

.type-option h4 {
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.type-option p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Professional Help Dialog Styling */
.help-content {
    display: grid;
    gap: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    padding: 1rem;
}

.help-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
}

.help-section h4 {
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.help-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.help-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-section li {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.help-section li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    margin-top: 0.1rem;
}

/* Add Game Dialog Styling */
.add-game-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.75rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Confirmation Note */
.confirmation-note {
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.confirmation-note strong {
    color: #f59e0b;
}

/* Responsive design for confirmation dialogs */
@media (max-width: 768px) {
    .confirmation-buttons, .reselect-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .confirmation-buttons .btn, .reselect-buttons .btn {
        width: 100%;
        max-width: 300px;
        min-width: auto;
    }
    
    .confirm-modal {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    /* Game type confirmation responsive */
    .type-options-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .type-option {
        padding: 1rem;
    }
    
    .type-option-icon {
        font-size: 2rem;
    }
    
    .help-content {
        gap: 1rem;
        padding: 0.5rem;
    }
    
    .help-section {
        padding: 1rem;
    }
}
