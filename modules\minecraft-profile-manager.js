/**
 * Minecraft Profile Manager module for Armory X
 * Handles Minecraft profile management and launcher integration
 * NOTE: Fabric/Forge installer functionality has been removed as requested
 */

const path = require('path');
const fs = require('fs-extra');
const os = require('os');

class MinecraftProfileManager {
  constructor() {
    this.minecraftDir = this.getMinecraftDirectory();
    this.profilesDir = path.join(this.minecraftDir, 'profiles');
    this.launcherProfilesPath = path.join(this.minecraftDir, 'launcher_profiles.json');
  }

  /**
   * Get the Minecraft directory based on the operating system
   * @returns {string} Minecraft directory path
   */
  getMinecraftDirectory() {
    const home = os.homedir();
    switch (os.platform()) {
      case 'win32':
        return path.join(process.env.APPDATA, '.minecraft');
      case 'darwin':
        return path.join(home, 'Library', 'Application Support', 'minecraft');
      default:
        return path.join(home, '.minecraft');
    }
  }

  /**
   * Create or update launcher profile
   * @param {Object} profileData - Profile configuration
   * @returns {Promise<Object>} Operation result
   */
  async createLauncherProfile(profileData) {
    console.log('📝 Creating launcher profile:', profileData);
    
    try {
      let launcherProfiles = {};
      
      // Read existing launcher profiles if they exist
      if (await fs.pathExists(this.launcherProfilesPath)) {
        const content = await fs.readFile(this.launcherProfilesPath, 'utf8');
        launcherProfiles = JSON.parse(content);
      }
      
      // Ensure the profiles object exists
      if (!launcherProfiles.profiles) {
        launcherProfiles.profiles = {};
      }
      
      // Create the profile
      const profileId = profileData.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const javaArgs = `-Xmx${profileData.ramAllocation || '4G'} -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC -XX:G1NewSizePercent=20 -XX:G1ReservePercent=20 -XX:MaxGCPauseMillis=50 -XX:G1HeapRegionSize=32M`;
      
      launcherProfiles.profiles[profileId] = {
        name: profileData.name,
        type: 'custom',
        created: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        icon: 'Crafting_Table',
        javaArgs: javaArgs,
        lastVersionId: profileData.mcVersion || '1.20.1',
        gameDir: path.join(this.profilesDir, profileData.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase())
      };
      
      // Set as selected profile if it's the first one
      if (!launcherProfiles.selectedProfile) {
        launcherProfiles.selectedProfile = profileId;
      }
      
      // Write back to file
      await fs.writeFile(this.launcherProfilesPath, JSON.stringify(launcherProfiles, null, 2));
      
      return {
        success: true,
        profileId: profileId,
        message: 'Launcher profile created successfully'
      };
      
    } catch (error) {
      console.error('❌ Error creating launcher profile:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Create a new Minecraft profile directory structure
   * @param {Object} profileData - Profile configuration
   * @returns {Promise<Object>} Operation result
   */
  async createProfile(profileData) {
    console.log('➕ Creating new Minecraft profile:', profileData);
    
    try {
      const profileId = profileData.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const profileDir = path.join(this.profilesDir, profileId);
      
      // Create profile directory structure
      await fs.ensureDir(profileDir);
      await fs.ensureDir(path.join(profileDir, 'mods'));
      await fs.ensureDir(path.join(profileDir, 'config'));
      await fs.ensureDir(path.join(profileDir, 'shaderpacks'));
      await fs.ensureDir(path.join(profileDir, 'resourcepacks'));
      
      // Copy from existing profile if specified
      if (profileData.copyFrom && profileData.copyFrom !== 'default') {
        const sourceDir = profileData.copyFrom === 'default' ? 
          this.minecraftDir : 
          path.join(this.profilesDir, profileData.copyFrom);
        
        const foldersToCopy = ['mods', 'config', 'shaderpacks', 'resourcepacks'];
        
        for (const folder of foldersToCopy) {
          const sourcePath = path.join(sourceDir, folder);
          const targetPath = path.join(profileDir, folder);
          
          if (await fs.pathExists(sourcePath)) {
            await fs.copy(sourcePath, targetPath);
            console.log(`📁 Copied ${folder} from ${profileData.copyFrom}`);
          }
        }
      }
      
      return {
        success: true,
        profileId: profileId,
        message: `Profile "${profileData.name}" created successfully`
      };
      
    } catch (error) {
      console.error('❌ Error creating profile:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Switch between Minecraft profiles
   * @param {string} fromProfile - Current profile ID
   * @param {string} toProfile - Target profile ID
   * @returns {Promise<Object>} Operation result
   */
  async switchProfile(fromProfile, toProfile) {
    console.log(`🔄 Switching profiles: ${fromProfile} → ${toProfile}`);
    
    try {
      const folders = ['mods', 'config', 'shaderpacks', 'resourcepacks'];
      
      // Handle default profile (main .minecraft folders)
      const fromDir = fromProfile === 'default' ? 
        this.minecraftDir : 
        path.join(this.profilesDir, fromProfile);
      
      const toDir = toProfile === 'default' ? 
        this.minecraftDir : 
        path.join(this.profilesDir, toProfile);
      
      // Ensure target profile directory exists
      if (toProfile !== 'default') {
        await fs.ensureDir(toDir);
        for (const folder of folders) {
          await fs.ensureDir(path.join(toDir, folder));
        }
      }
      
      // Move files from main directories to old profile
      if (fromProfile !== 'default') {
        const fromProfileDir = path.join(this.profilesDir, fromProfile);
        await fs.ensureDir(fromProfileDir);
        
        for (const folder of folders) {
          await fs.ensureDir(path.join(fromProfileDir, folder));
        }
      }
      
      for (const folder of folders) {
        const mainPath = path.join(this.minecraftDir, folder);
        const fromPath = fromProfile === 'default' ? mainPath : path.join(fromDir, folder);
        const toPath = toProfile === 'default' ? mainPath : path.join(toDir, folder);
        
        // Ensure directories exist
        await fs.ensureDir(mainPath);
        if (fromProfile !== 'default') await fs.ensureDir(fromPath);
        if (toProfile !== 'default') await fs.ensureDir(toPath);
        
        // Move current main files to old profile location
        if (fromProfile !== 'default') {
          const files = await fs.readdir(mainPath);
          for (const file of files) {
            const sourcePath = path.join(mainPath, file);
            const targetPath = path.join(fromPath, file);
            
            // Only move if target doesn't exist to prevent conflicts
            if (!(await fs.pathExists(targetPath))) {
              await fs.move(sourcePath, targetPath);
            }
          }
        }
        
        // Move new profile files to main location
        if (toProfile !== 'default') {
          const files = await fs.readdir(toPath);
          for (const file of files) {
            const sourcePath = path.join(toPath, file);
            const targetPath = path.join(mainPath, file);
            
            // Only move if target doesn't exist to prevent conflicts
            if (!(await fs.pathExists(targetPath))) {
              await fs.move(sourcePath, targetPath);
            }
          }
        }
        
        console.log(`📁 Switched ${folder} for profile: ${toProfile}`);
      }
      
      return {
        success: true,
        message: `Successfully switched to profile: ${toProfile}`
      };
      
    } catch (error) {
      console.error('❌ Error switching profiles:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Get list of available profiles
   * @returns {Promise<Object>} Available profiles
   */
  async getAvailableProfiles() {
    try {
      const profiles = ['default']; // Always include default profile
      
      if (await fs.pathExists(this.profilesDir)) {
        const dirs = await fs.readdir(this.profilesDir);
        for (const dir of dirs) {
          const dirPath = path.join(this.profilesDir, dir);
          const stats = await fs.stat(dirPath);
          if (stats.isDirectory()) {
            profiles.push(dir);
          }
        }
      }
      
      return {
        success: true,
        profiles: profiles
      };
      
    } catch (error) {
      console.error('❌ Error getting available profiles:', error);
      return {
        success: false,
        message: error.message,
        profiles: ['default']
      };
    }
  }

  /**
   * Delete a profile
   * @param {string} profileId - Profile ID to delete
   * @returns {Promise<Object>} Operation result
   */
  async deleteProfile(profileId) {
    console.log(`🗑️ Deleting profile: ${profileId}`);
    
    try {
      if (profileId === 'default') {
        return {
          success: false,
          message: 'Cannot delete the default profile'
        };
      }
      
      const profileDir = path.join(this.profilesDir, profileId);
      
      if (await fs.pathExists(profileDir)) {
        await fs.remove(profileDir);
        console.log(`✅ Deleted profile: ${profileId}`);
        
        return {
          success: true,
          message: `Deleted profile "${profileId}"`
        };
      } else {
        return {
          success: false,
          message: 'Profile not found'
        };
      }
      
    } catch (error) {
      console.error('❌ Error deleting profile:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }
}

module.exports = { MinecraftProfileManager }; 