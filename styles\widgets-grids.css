/* ===================================
   Widgets and Grid Systems
   =================================== */

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.option-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.option-card.active {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 180, 255, 0.3);
}

.option-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: block;
}

.option-title {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.option-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Game Grid */
.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.game-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.game-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.game-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 180, 255, 0.4);
}

.game-image {
    width: 100%;
    height: 120px;
    background: var(--background-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-muted);
}

.game-info {
    padding: 1rem;
}

.game-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.game-status {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.tool-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tool-card:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.tool-card.active {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.05);
}

.tool-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tool-icon {
    font-size: 2rem;
    color: var(--primary-color);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 180, 255, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.tool-info {
    flex: 1;
    min-width: 0;
}

.tool-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.tool-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Main Tools Grid Layout */
.main-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.main-tool-card {
    background: linear-gradient(135deg, var(--background-card), var(--background-hover));
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.main-tool-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.main-tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main-tool-card:hover::before {
    opacity: 1;
}

.tools-grid.compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.tools-grid.compact .tool-card {
    padding: 1rem;
}

.tools-grid.compact .tool-icon {
    font-size: 1.5rem;
    width: 36px;
    height: 36px;
}

.tools-grid.compact .tool-title {
    font-size: 1rem;
}

.tools-grid.compact .tool-description {
    font-size: 0.8rem;
}

/* Game Selector Grid */
.game-selector .game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.game-selector .game-card {
    background: linear-gradient(135deg, var(--background-card), var(--background-hover));
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.game-selector .game-card:hover {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.05);
    transform: scale(1.02);
}

.game-selector .game-card.selected {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.1);
    box-shadow: 0 0 25px rgba(0, 180, 255, 0.3);
}

/* Type Options Grid */
.type-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.type-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.type-option:hover {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.type-option.selected {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.15);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Widget Container */
.widget-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin: 2rem 0;
}

/* Widget Status */
.widget-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.widget-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.widget-status.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.widget-status.inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.widget-status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Widget Actions */
.widget-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.widget-action-btn {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.widget-action-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.widget-action-btn:disabled {
    background: var(--background-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

/* Action Grid */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.action-item {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-item:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.action-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.action-description {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .options-grid, .tools-grid, .game-grid {
        grid-template-columns: 1fr;
    }
    
    .main-tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .tools-grid.compact {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .widget-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .game-selector .game-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .type-options-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .widget-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .widget-action-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .tools-grid.compact {
        grid-template-columns: 1fr;
    }
    
    .option-card,
    .tool-card,
    .game-card {
        padding: 1rem;
    }
    
    .main-tool-card {
        padding: 1.5rem;
    }
    
    .tool-icon,
    .option-icon {
        font-size: 1.5rem;
    }
    
    .widget-actions {
        flex-direction: column;
    }
    
    .widget-action-btn {
        width: 100%;
    }
}
