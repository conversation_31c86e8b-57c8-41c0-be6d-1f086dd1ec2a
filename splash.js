// Splash Screen Application Logic
console.log('🚀 Armory X Splash Screen loading...');

// Global variables
let ipc<PERSON><PERSON><PERSON> = null;
let authToken = null;
let isAuthenticated = false;
let verificationEmail = null;
let resendAttempts = 3;
let resendCooldown = false;
let verificationCheckInterval = null;

// Global rate limiting for email changes
const emailChangeTracker = new Map();
const MAX_EMAIL_CHANGES_PER_HOUR = 3;

// Initialize Electron IPC if available
function initializeElectronIPC() {
    if (typeof require !== 'undefined') {
        try {
            const { ipcRenderer: electronIpcRenderer } = require('electron');
            ipcRenderer = electronIpcRenderer;
            console.log('✅ Electron IPC initialized in splash');
        } catch (error) {
            console.log('⚠️ Electron IPC not available in splash:', error.message);
        }
    }
}

// DOM Ready Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 Splash Screen DOM loaded');
    
    // Initialize Electron IPC
    initializeElectronIPC();
    
    // Initialize splash screen
    initializeSplashScreen();
    
    // Setup event listeners
    setupEventListeners();
    
    // Start connection check
    checkSystemConnection();
    
    console.log('✅ Splash screen initialization complete');
});

// Initialize Splash Screen
function initializeSplashScreen() {
    if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
        const { auth, onAuthStateChanged } = window.firebase;
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                showNotification('Restoring previous session...', 'info');
                const token = await user.getIdToken();
                loginSuccess({
                    token,
                    username: user.email,
                    rememberMe: true,
                    timestamp: Date.now()
                });
            }
        });
    } else {
        // Fallback to localStorage
    const savedAuth = localStorage.getItem('armoryXAuth');
    if (savedAuth) {
        try {
            const authData = JSON.parse(savedAuth);
            if (authData.rememberMe && authData.token) {
                showNotification('Restoring previous session...', 'info');
                setTimeout(() => {
                    loginSuccess(authData);
                }, 1000);
                return;
            }
        } catch (error) {
            console.warn('Invalid saved auth data:', error);
            localStorage.removeItem('armoryXAuth');
            }
        }
    }
    
    // Initialize UI components
    updateConnectionStatus('Ready');
}

// Setup Event Listeners
function setupEventListeners() {
    // Login form submission
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Registration form submission
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Password toggle for login
    const togglePassword = document.getElementById('toggle-password');
    if (togglePassword) {
        togglePassword.addEventListener('click', togglePasswordVisibility);
    }
    
    // Password toggle for registration
    const toggleRegPassword = document.getElementById('toggle-reg-password');
    if (toggleRegPassword) {
        toggleRegPassword.addEventListener('click', toggleRegPasswordVisibility);
    }
    
    // Remember me checkbox
    const rememberMe = document.getElementById('remember-me');
    if (rememberMe) {
        rememberMe.addEventListener('change', handleRememberMeChange);
    }
    
    // Forgot password link
    const forgotPassword = document.querySelector('.forgot-password');
    if (forgotPassword) {
        forgotPassword.addEventListener('click', handleForgotPassword);
    }
    
    // Register link
    const registerLink = document.querySelector('.register-link');
    if (registerLink) {
        registerLink.addEventListener('click', handleRegisterLink);
    }
    
    // Login link in register section
    const loginLink = document.querySelector('.login-link');
    if (loginLink) {
        loginLink.addEventListener('click', switchToLogin);
    }
    
    // Resend verification button in verification section
    const resendVerificationBtn = document.getElementById('resend-verification-btn');
    if (resendVerificationBtn) {
        resendVerificationBtn.addEventListener('click', handleResendFromVerificationScreen);
    }
    
    // Back to register link in verification section
    const backToRegister = document.querySelector('.back-to-register');
    if (backToRegister) {
        backToRegister.addEventListener('click', handleBackToRegister);
    }
    
    // Back to login link in verification section
    const backToLogin = document.querySelector('.back-to-login');
    if (backToLogin) {
        backToLogin.addEventListener('click', handleBackToLogin);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// Handle Login Form Submission
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    // Validate input
    if (!email || !password) {
        showNotification('Please enter both email and password', 'error');
        return;
    }
    
    // Show loading state
    setLoginLoading(true);
    
    try {
        // Authenticate using Firebase
        const authResult = await authenticateUser(email, password);
        
        if (authResult.success) {
            // Save authentication data if remember me is checked
            if (rememberMe) {
                const authData = {
                    token: authResult.token,
                    username: email,
                    rememberMe: true,
                    timestamp: Date.now()
                };
                localStorage.setItem('armoryXAuth', JSON.stringify(authData));
            }
            
            // Show loading screen instead of notification
            loginSuccess(authResult);
        }
        // Error handling is now done in authenticateUser function
        
    } catch (error) {
        console.error('Unexpected login error:', error);
        showNotification('An unexpected error occurred. Please try again.', 'error');
    } finally {
        setLoginLoading(false);
    }
}

// Firebase Authentication Function
async function authenticateUser(email, password) {
    if (window.firebase && window.firebase.auth && window.firebase.signInWithEmailAndPassword) {
        try {
            const { auth, signInWithEmailAndPassword } = window.firebase;
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Check if email is verified
            if (!user.emailVerified) {
                // Sign out the unverified user
                await auth.signOut();
                
                // Show special notification with action button
                const notificationContainer = document.getElementById('notification-container');
                const notification = document.createElement('div');
                notification.className = 'notification warning';
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-icon">⚠️</span>
                        <div class="notification-text">
                            <strong>Email not verified</strong><br>
                            Please verify your email before logging in.
                        </div>
                        <button class="notification-close">&times;</button>
                    </div>
                    <div class="notification-actions">
                        <button class="notification-action-btn" id="resend-btn-${Date.now()}">
                            Resend Verification
                        </button>
                    </div>
                `;
                
                // Add close functionality
                const closeBtn = notification.querySelector('.notification-close');
                closeBtn.addEventListener('click', () => {
                    removeNotification(notification);
                });
                
                // Add resend functionality
                const resendBtn = notification.querySelector('.notification-action-btn');
                resendBtn.addEventListener('click', () => {
                    removeNotification(notification);
                    window.resendVerificationForEmail(email);
                });
                
                notificationContainer.appendChild(notification);
                
                // Auto-remove after 10 seconds
                setTimeout(() => {
                    removeNotification(notification);
                }, 10000);
                
                return {
                    success: false,
                    message: 'Email not verified'
                };
            }
            
            const token = await user.getIdToken();
        return {
            success: true,
                token,
                username: user.email,
                userLevel: 'user',
            timestamp: Date.now()
        };
        } catch (error) {
            console.error('Login error:', error);
            let message = 'Login failed';
            
            switch (error.code) {
                case 'auth/invalid-email':
                    message = 'Please enter a valid email address';
                    break;
                case 'auth/user-not-found':
                    message = 'No account found with this email address';
                    break;
                case 'auth/wrong-password':
                    message = 'Incorrect password';
                    break;
                case 'auth/too-many-requests':
                    message = 'Too many failed login attempts. Please try again later';
                    break;
                case 'auth/network-request-failed':
                    message = 'Network error. Please check your internet connection';
                    break;
                case 'auth/invalid-credential':
                    message = 'Invalid email or password';
                    break;
                default:
                    message = error.message || 'Login failed. Please try again';
            }
            
            showNotification(message, 'error');
            return {
                success: false,
                message
            };
        }
    } else {
        showNotification('Authentication service unavailable. Please check your internet connection.', 'error');
        return {
            success: false,
            message: 'Authentication system unavailable'
        };
    }
}

// Generate Authentication Token
function generateToken() {
    return 'armx_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// Handle Successful Login
function loginSuccess(authData) {
    authToken = authData.token;
    isAuthenticated = true;
    
    // Show loading screen immediately
    const loadingScreen = document.getElementById('loading-screen');
    loadingScreen.style.display = 'flex';
    loadingScreen.style.opacity = '0';
    
    // Fade in loading screen
    setTimeout(() => {
        loadingScreen.style.transition = 'opacity 0.3s ease-in';
        loadingScreen.style.opacity = '1';
    }, 10);
    
    // Add shrinking animation to login panel after loading screen is visible
    setTimeout(() => {
        const loginPanel = document.querySelector('.login-panel');
        if (loginPanel) {
            loginPanel.classList.add('loading-transition');
        }
        
        // Add hiding animation to current sections
        const loginSection = document.querySelector('.login-section');
        const registerSection = document.querySelector('.register-section');
        const verificationSection = document.querySelector('.verification-section');
        
        if (loginSection && loginSection.style.display !== 'none') {
            loginSection.classList.add('hiding');
        }
        if (registerSection && registerSection.style.display !== 'none') {
            registerSection.classList.add('hiding');
        }
        if (verificationSection && verificationSection.style.display !== 'none') {
            verificationSection.classList.add('hiding');
        }
        
        // Start loading animation
        setTimeout(() => {
            startDiscordStyleLoading();
        }, 200);
    }, 150);
}

// Start Discord-style Loading Process
function startDiscordStyleLoading() {
    const loadingSteps = [
        { message: 'Connecting to Armory X servers...', progress: 15, delay: 400 },
        { message: 'Loading user preferences...', progress: 30, delay: 600 },
        { message: 'Initializing modules...', progress: 45, delay: 800 },
        { message: 'Checking system compatibility...', progress: 60, delay: 700 },
        { message: 'Loading game profiles...', progress: 75, delay: 600 },
        { message: 'Preparing dashboard...', progress: 90, delay: 500 },
        { message: 'Almost ready...', progress: 95, delay: 400 },
        { message: 'Welcome to Armory X!', progress: 100, delay: 300 }
    ];
    
    const tips = [
        "Armory X provides real-time system monitoring for optimal performance.",
        "Use the Desktop Arsenal for quick access to your favorite tools.",
        "Enable HWID Spoofer for enhanced privacy protection.",
        "Mod Manager helps you organize all your game modifications.",
        "Quick Launch lets you start any application with custom shortcuts.",
        "System cleanup can free up gigabytes of wasted space.",
        "Join our Discord community for tips and support.",
        "Premium features unlock advanced optimization tools."
    ];
    
    let currentStep = 0;
    const progressFill = document.getElementById('loading-progress-fill');
    const statusText = document.getElementById('loading-status');
    const tipText = document.getElementById('loading-tip-text');
    
    // Set initial tip
    tipText.textContent = tips[Math.floor(Math.random() * tips.length)];
    
    // Change tip every 3 seconds
    const tipInterval = setInterval(() => {
        tipText.style.opacity = '0';
        setTimeout(() => {
            tipText.textContent = tips[Math.floor(Math.random() * tips.length)];
            tipText.style.opacity = '1';
        }, 300);
    }, 3000);
    
    const updateProgress = () => {
        if (currentStep < loadingSteps.length) {
            const step = loadingSteps[currentStep];
            
            // Update status text with fade effect
            statusText.style.opacity = '0.5';
            setTimeout(() => {
                statusText.textContent = step.message;
                statusText.style.opacity = '1';
            }, 150);
            
            // Update progress bar with smooth animation
            progressFill.style.width = step.progress + '%';
            
            currentStep++;
            
            // Use predefined delay for each step
            setTimeout(updateProgress, step.delay);
        } else {
            // Loading complete
            clearInterval(tipInterval);
            
            // Final flourish - wait for progress to reach 100%
            setTimeout(() => {
                statusText.style.opacity = '0.5';
                setTimeout(() => {
                    statusText.textContent = 'Launching...';
                    statusText.style.opacity = '1';
                }, 150);
                
                // Launch after final message
                setTimeout(() => {
                    launchMainApplication();
                }, 500);
            }, 400);
        }
    };
    
    // Start after initial animations complete
    setTimeout(updateProgress, 1500);
}

// Launch Main Application
function launchMainApplication() {
    // Add exit animation to loading screen
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        loadingScreen.classList.add('exiting');
    }
    
    // Wait for animation to complete before launching
    setTimeout(() => {
        // Notify main process if available
        if (ipcRenderer) {
            ipcRenderer.send('splash-login-complete', {
                token: authToken,
                timestamp: Date.now()
            });
            // Main process will handle window creation
            // No need to navigate, just let main process handle it
        } else {
            // Only navigate if not in Electron (for web testing)
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 300);
        }
    }, 600);
}

// Set Login Loading State
function setLoginLoading(loading) {
    const loginBtn = document.getElementById('login-btn');
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoader = loginBtn.querySelector('.btn-loader');
    
    if (loading) {
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
    } else {
        loginBtn.classList.remove('loading');
        loginBtn.disabled = false;
    }
}

// Toggle Password Visibility
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.querySelector('.toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.textContent = '🙈';
    } else {
        passwordInput.type = 'password';
        toggleIcon.textContent = '👁️';
    }
}

// Toggle Registration Password Visibility
function toggleRegPasswordVisibility() {
    const passwordInput = document.getElementById('reg-password');
    const toggleIcon = document.getElementById('toggle-reg-password').querySelector('.toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.textContent = '🙈';
    } else {
        passwordInput.type = 'password';
        toggleIcon.textContent = '👁️';
    }
}

// Fill Demo Credentials
function fillDemoCredentials() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    if (usernameInput && passwordInput) {
        usernameInput.value = 'demo';
        passwordInput.value = 'demo123';
        
        // Add visual feedback
        usernameInput.style.borderColor = 'var(--primary-color)';
        passwordInput.style.borderColor = 'var(--primary-color)';
        
        setTimeout(() => {
            usernameInput.style.borderColor = '';
            passwordInput.style.borderColor = '';
        }, 1000);
        
        showNotification('Demo credentials filled!', 'info');
    }
}

// Setup Demo Credentials
function setupDemoCredentials() {
    // Add click event to demo credentials for easy testing
    const demoInfo = document.querySelector('.demo-info');
    if (demoInfo) {
        demoInfo.style.cursor = 'pointer';
        demoInfo.title = 'Click to fill credentials';
    }
}

// Handle Remember Me Change
function handleRememberMeChange(event) {
    const isChecked = event.target.checked;
    if (isChecked) {
        showNotification('Your session will be remembered', 'info');
    } else {
        // Clear saved auth data
        localStorage.removeItem('armoryXAuth');
    }
}

// Handle Forgot Password
function handleForgotPassword(event) {
    event.preventDefault();
    
    const email = prompt('Enter your email to reset password:');
    if (email) {
        if (window.firebase && window.firebase.auth && window.firebase.sendPasswordResetEmail) {
            const { auth, sendPasswordResetEmail } = window.firebase;
            sendPasswordResetEmail(auth, email)
                .then(() => {
                    showNotification('Password reset email sent! Check your inbox.', 'success');
                })
                .catch(error => {
                    showNotification(error.message, 'error');
                });
        } else {
            showNotification('Password reset not available in demo mode', 'info');
        }
    }
}

// Handle Register Link
function handleRegisterLink(event) {
    event.preventDefault();
    document.querySelector('.login-section').style.display = 'none';
    document.querySelector('.register-section').style.display = 'block';
}

// Switch to Login
function switchToLogin(event) {
    event.preventDefault();
    document.querySelector('.register-section').style.display = 'none';
    document.querySelector('.login-section').style.display = 'block';
}

// Handle Registration Form Submission
async function handleRegister(event) {
    event.preventDefault();
    
    const email = document.getElementById('reg-email').value.trim();
    const password = document.getElementById('reg-password').value;
    const confirm = document.getElementById('reg-confirm').value;
    
    // Validate inputs
    if (!email || !password || !confirm) {
        showNotification('Please fill all fields', 'error');
        return;
    }
    
    if (password !== confirm) {
        showNotification('Passwords do not match', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('Password must be at least 6 characters', 'error');
        return;
    }
    
    setRegisterLoading(true);
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.createUserWithEmailAndPassword) {
            const { auth, createUserWithEmailAndPassword } = window.firebase;
            
            // Create user account
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Send email verification
            const { sendEmailVerification } = window.firebase;
            await sendEmailVerification(user);
            
            // Store email and password for verification checking
            verificationEmail = email;
            window.tempPassword = password; // Store temporarily for auto-login after verification
            
            showNotification('Account created! Please check your email.', 'success');
            
            // Sign out the user since they need to verify email first
            await auth.signOut();
            
            // Show verification waiting screen
            showVerificationScreen(email);
            
            // Start checking for verification
            startVerificationCheck();
            
        } else {
            showNotification('Registration service unavailable. Please try again later.', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        let message = 'Registration failed';
        
        switch (error.code) {
            case 'auth/email-already-in-use':
                message = 'An account with this email already exists';
                break;
            case 'auth/invalid-email':
                message = 'Please enter a valid email address';
                break;
            case 'auth/weak-password':
                message = 'Password is too weak. Please use at least 6 characters';
                break;
            case 'auth/network-request-failed':
                message = 'Network error. Please check your internet connection';
                break;
            default:
                message = error.message || 'Registration failed. Please try again';
        }
        
        showNotification(message, 'error');
    } finally {
        setRegisterLoading(false);
    }
}

// Show Verification Screen
function showVerificationScreen(email) {
    document.querySelector('.register-section').style.display = 'none';
    document.querySelector('.login-section').style.display = 'none';
    document.querySelector('.verification-section').style.display = 'block';
    document.getElementById('verification-email').textContent = email;
    
    // Reset resend attempts
    resendAttempts = 3;
    updateResendAttemptsDisplay();
}

// Start Verification Check
function startVerificationCheck() {
    // Check every 3 seconds
    verificationCheckInterval = setInterval(async () => {
        if (window.firebase && window.firebase.auth && window.firebase.signInWithEmailAndPassword) {
            try {
                const { auth, signInWithEmailAndPassword } = window.firebase;
                
                // Try to sign in to check if email is verified
                const userCredential = await signInWithEmailAndPassword(auth, verificationEmail, window.tempPassword);
                const user = userCredential.user;
                
                if (user.emailVerified) {
                    // Email verified! Stop checking and proceed to login
                    clearInterval(verificationCheckInterval);
                    
                    const token = await user.getIdToken();
                    loginSuccess({
                        token,
                        username: user.email,
                        userLevel: 'user',
                        timestamp: Date.now()
                    });
                    
                    // Clear temporary password
                    window.tempPassword = null;
                } else {
                    // Still not verified, sign out and continue checking
                    await auth.signOut();
                }
            } catch (error) {
                // Ignore errors during checking
                console.log('Verification check error:', error.message);
            }
        }
    }, 3000);
}

// Handle Resend from Verification Screen
async function handleResendFromVerificationScreen() {
    if (resendCooldown || resendAttempts <= 0) {
        return;
    }
    
    const resendBtn = document.getElementById('resend-verification-btn');
    const btnText = resendBtn.querySelector('.btn-text');
    const timer = document.getElementById('resend-timer');
    
    // Check if we have the necessary data
    if (!verificationEmail || !window.tempPassword) {
        showNotification('Session expired. Please create your account again.', 'error');
        handleBackToRegister({ preventDefault: () => {} });
        return;
    }
    
    // Decrease attempts
    resendAttempts--;
    updateResendAttemptsDisplay();
    
    if (resendAttempts === 0) {
        // No more attempts - show 5 minute cooldown
        showNotification('Maximum resend attempts reached. Please try again in 5 minutes.', 'error');
        startLongCooldown();
        return;
    }
    
    // Start 60 second cooldown
    resendCooldown = true;
    resendBtn.disabled = true;
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.signInWithEmailAndPassword) {
            const { auth, signInWithEmailAndPassword, sendEmailVerification } = window.firebase;
            
            // Sign in to resend verification
            const userCredential = await signInWithEmailAndPassword(auth, verificationEmail, window.tempPassword);
            const user = userCredential.user;
            
            if (!user.emailVerified) {
                await sendEmailVerification(user);
                showNotification('Verification email resent! Check your inbox.', 'success');
            } else {
                showNotification('Your email is already verified!', 'success');
                // Auto redirect to login
    setTimeout(() => {
                    handleBackToLogin({ preventDefault: () => {} });
    }, 2000);
            }
            
            await auth.signOut();
        }
    } catch (error) {
        console.error('Resend verification error:', error);
        showNotification('Unable to resend verification email. Please try again.', 'error');
        // Don't count this as an attempt if it failed
        resendAttempts++;
        updateResendAttemptsDisplay();
    }
    
    // Start cooldown timer
    let cooldownTime = 60;
    btnText.style.display = 'none';
    timer.style.display = 'inline';
    timer.textContent = `${cooldownTime}s`;
    
    const cooldownInterval = setInterval(() => {
        cooldownTime--;
        timer.textContent = `${cooldownTime}s`;
        
        if (cooldownTime <= 0) {
            clearInterval(cooldownInterval);
            resendCooldown = false;
            resendBtn.disabled = false;
            btnText.style.display = 'inline';
            timer.style.display = 'none';
        }
    }, 1000);
}

// Start Long Cooldown (5 minutes)
function startLongCooldown() {
    const resendBtn = document.getElementById('resend-verification-btn');
    const btnText = resendBtn.querySelector('.btn-text');
    const timer = document.getElementById('resend-timer');
    
    resendBtn.disabled = true;
    
    // After 5 minute cooldown, redirect to registration
    setTimeout(() => {
        clearInterval(verificationCheckInterval);
        showNotification('Please create a new account with the correct email address.', 'info');
        handleBackToRegister({ preventDefault: () => {} });
    }, 5 * 60 * 1000);
    
    // Show countdown
    let cooldownTime = 300; // 5 minutes
    btnText.style.display = 'none';
    timer.style.display = 'inline';
    
    const updateTimer = () => {
        const minutes = Math.floor(cooldownTime / 60);
        const seconds = cooldownTime % 60;
        timer.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };
    
    updateTimer();
    
    const cooldownInterval = setInterval(() => {
        cooldownTime--;
        updateTimer();
        
        if (cooldownTime <= 0) {
            clearInterval(cooldownInterval);
        }
    }, 1000);
}

// Update Resend Attempts Display
function updateResendAttemptsDisplay() {
    const attemptsText = document.getElementById('resend-attempts');
    if (resendAttempts > 0) {
        attemptsText.textContent = `Attempts remaining: ${resendAttempts}`;
    } else {
        attemptsText.textContent = 'No attempts remaining';
        attemptsText.style.color = '#f44336';
    }
}

// Handle Back to Register
function handleBackToRegister(event) {
    event.preventDefault();
    
    // Rate limit check for email changes
    const now = Date.now();
    const hourAgo = now - (60 * 60 * 1000);
    const recentChanges = Array.from(emailChangeTracker.values()).filter(t => t > hourAgo).length;
    
    if (recentChanges >= MAX_EMAIL_CHANGES_PER_HOUR) {
        showNotification('Too many email change attempts. Please try again later.', 'error');
        return;
    }
    
    // Track this email change attempt
    if (verificationEmail) {
        emailChangeTracker.set(`change_${verificationEmail}_${now}`, now);
    }
    
    // Clear verification check
    if (verificationCheckInterval) {
        clearInterval(verificationCheckInterval);
        verificationCheckInterval = null;
    }
    
    // Clear temporary data
    verificationEmail = null;
    window.tempPassword = null;
    
    // Reset form
    document.getElementById('reg-email').value = '';
    document.getElementById('reg-password').value = '';
    document.getElementById('reg-confirm').value = '';
    
    // Reset resend attempts for new registration
    resendAttempts = 3;
    resendCooldown = false;
    
    // Show register screen
    document.querySelector('.verification-section').style.display = 'none';
    document.querySelector('.register-section').style.display = 'block';
}

// Handle Back to Login
function handleBackToLogin(event) {
    event.preventDefault();
    document.querySelector('.verification-section').style.display = 'none';
    document.querySelector('.login-section').style.display = 'block';
}

// Set Register Loading State
function setRegisterLoading(loading) {
    const registerBtn = document.getElementById('register-btn');
    if (registerBtn) {
        if (loading) {
            registerBtn.classList.add('loading');
            registerBtn.disabled = true;
        } else {
            registerBtn.classList.remove('loading');
            registerBtn.disabled = false;
        }
    }
}

// Handle Keyboard Shortcuts
function handleKeyboardShortcuts(event) {
    // Prevent shortcuts when typing in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }
    
    switch (event.key) {
        case 'Enter':
            const loginBtn = document.getElementById('login-btn');
            if (loginBtn && !loginBtn.disabled) {
                loginBtn.click();
            }
            break;
        case 'Escape':
            // Clear notifications
            clearAllNotifications();
            break;
        case 'F1':
            event.preventDefault();
            showHelpDialog();
            break;
        case 'F5':
            event.preventDefault();
            window.location.reload();
            break;
    }
    
    // Ctrl combinations
    if (event.ctrlKey) {
        switch (event.key) {
            case 'r':
                event.preventDefault();
                window.location.reload();
                break;
            case 'l':
                event.preventDefault();
                document.getElementById('email').focus();
                break;
        }
    }
}

// Show Help Dialog
function showHelpDialog() {
    const helpMessage = `
        <h3>Armory X Login Help</h3>
        <p><strong>Demo Credentials:</strong></p>
        <ul>
            <li>Username: demo, Password: demo123</li>
            <li>Username: admin, Password: admin</li>
            <li>Username: test, Password: test123</li>
        </ul>
        <p><strong>Keyboard Shortcuts:</strong></p>
        <ul>
            <li>Enter - Submit login form</li>
            <li>Escape - Clear notifications</li>
            <li>F1 - Show this help</li>
            <li>F5 - Reload page</li>
            <li>Ctrl+L - Focus username field</li>
        </ul>
    `;
    
    showNotification(helpMessage, 'info', 10000);
}

// Check System Connection
function checkSystemConnection() {
    updateConnectionStatus('Checking connection...');
    
    // Simulate connection check
    setTimeout(() => {
        if (ipcRenderer) {
            // Test IPC connection
            ipcRenderer.invoke('test-connection')
                .then(result => {
                    if (result.success) {
                        updateConnectionStatus('Connected');
                    } else {
                        updateConnectionStatus('Connection failed');
                    }
                })
                .catch(error => {
                    console.error('Connection test failed:', error);
                    updateConnectionStatus('Connection error');
                });
        } else {
            updateConnectionStatus('Web mode');
        }
    }, 1000);
}

// Update Connection Status
function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
        statusElement.textContent = status;
        
        // Add visual feedback based on status
        statusElement.classList.remove('status-success', 'status-error', 'status-warning');
        
        if (status === 'Connected') {
            statusElement.classList.add('status-success');
        } else if (status.includes('error') || status.includes('failed')) {
            statusElement.classList.add('status-error');
        } else if (status.includes('Checking')) {
            statusElement.classList.add('status-warning');
        }
    }
}

// Notification System
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${icons[type] || icons.info}</span>
            <div class="notification-text">${message}</div>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        removeNotification(notification);
    });
    
    container.appendChild(notification);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }
}

// Remove Notification
function removeNotification(notification) {
    if (notification && notification.parentNode) {
        notification.style.animation = 'slideOutRight 0.3s ease-out forwards';
        setTimeout(() => {
            notification.parentNode.removeChild(notification);
        }, 300);
    }
}

// Clear All Notifications
function clearAllNotifications() {
    const container = document.getElementById('notification-container');
    if (container) {
        const notifications = container.querySelectorAll('.notification');
        notifications.forEach(notification => {
            removeNotification(notification);
        });
    }
}

// Utility Functions
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
}

function generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9);
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function sanitizeInput(input) {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        authenticateUser,
        generateToken,
        showNotification,
        updateConnectionStatus
    };
}

// Add CSS animation for slide out
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
    
    .status-success {
        color: var(--success-color) !important;
    }
    
    .status-error {
        color: var(--error-color) !important;
    }
    
    .status-warning {
        color: var(--warning-color) !important;
    }
`;
document.head.appendChild(style);

// Resend verification for specific email
window.resendVerificationForEmail = async function(email) {
    // Rate limit check
    const now = Date.now();
    const hourAgo = now - (60 * 60 * 1000);
    
    // Clean up old entries
    for (const [key, timestamp] of emailChangeTracker.entries()) {
        if (timestamp < hourAgo) {
            emailChangeTracker.delete(key);
        }
    }
    
    // Check rate limit
    const userKey = `${email}_${navigator.userAgent}`;
    const attempts = Array.from(emailChangeTracker.values()).filter(t => t > hourAgo).length;
    
    if (attempts >= MAX_EMAIL_CHANGES_PER_HOUR) {
        showNotification('Too many verification attempts. Please try again later.', 'error');
        return;
    }
    
    // Track this attempt
    emailChangeTracker.set(userKey + '_' + now, now);
    
    // Prompt for password
    const password = prompt('Enter your password to resend verification:');
    if (!password) return;
    
    try {
        if (window.firebase && window.firebase.auth) {
            const { auth, signInWithEmailAndPassword, sendEmailVerification } = window.firebase;
            
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            if (!user.emailVerified) {
                await sendEmailVerification(user);
                
                // Show success notification
                showNotification('Verification email sent successfully! Check your inbox.', 'success');
                
                // Store for verification screen
                verificationEmail = email;
                window.tempPassword = password;
                
                // Show verification screen after a short delay
                setTimeout(() => {
                    showVerificationScreen(email);
                    startVerificationCheck();
                }, 1500);
            } else {
                showNotification('Your email is already verified! You can log in now.', 'success');
            }
            
            await auth.signOut();
        }
    } catch (error) {
        console.error('Resend verification error:', error);
        showNotification('Failed to resend verification. Please check your password.', 'error');
    }
};

console.log('✅ Splash screen JavaScript loaded successfully'); 

// Window control functions
function minimizeWindow() {
    try {
        const { ipcRenderer } = require('electron');
        ipcRenderer.send('splash-minimize-window');
    } catch (error) {
        console.log('Minimize window - Electron not available:', error);
    }
}

function closeWindow() {
    try {
        const { ipcRenderer } = require('electron');
        ipcRenderer.send('splash-close-window');
    } catch (error) {
        console.log('Close window - Electron not available, trying fallback:', error);
        if (window.close) {
            window.close();
        } else {
            // Fallback: hide the panel
            document.querySelector('.login-panel').style.display = 'none';
        }
    }
}

// Modern login feedback functions
function showError(message) {
    const errorMsg = document.getElementById('error-message');
    const successMsg = document.getElementById('success-message');
    const loginBtn = document.getElementById('login-btn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    // Hide success message
    successMsg.classList.remove('show');
    
    // Show error message
    errorMsg.textContent = message;
    errorMsg.classList.add('show');
    
    // Add error state to button and inputs
    loginBtn.classList.add('error');
    loginBtn.querySelector('.btn-text').textContent = 'Error';
    usernameInput.classList.add('error');
    passwordInput.classList.add('error');
    
    // Reset after 3 seconds
    setTimeout(() => {
        errorMsg.classList.remove('show');
        loginBtn.classList.remove('error');
        loginBtn.querySelector('.btn-text').textContent = 'Sign In';
        usernameInput.classList.remove('error');
        passwordInput.classList.remove('error');
    }, 3000);
}

function showSuccess(message) {
    const errorMsg = document.getElementById('error-message');
    const successMsg = document.getElementById('success-message');
    const loginBtn = document.getElementById('login-btn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    // Hide error message
    errorMsg.classList.remove('show');
    
    // Show success message
    successMsg.textContent = message;
    successMsg.classList.add('show');
    
    // Add success state to button and inputs
    loginBtn.classList.add('success');
    loginBtn.querySelector('.btn-text').textContent = 'Success!';
    usernameInput.classList.add('success');
    passwordInput.classList.add('success');
}

// Enhanced login form handling
document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    
    if (loginForm) {
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showError('Please enter both username and password');
                return;
            }
            
            // Add loading state
            loginBtn.classList.add('loading');
            
            // Simulate login validation (replace with actual validation)
            setTimeout(() => {
                if (username === 'demo' && password === 'demo123') {
                    showSuccess('Login successful! Launching application...');
                    
                    // Launch main app after short delay
                    setTimeout(() => {
                        try {
                            const { ipcRenderer } = require('electron');
                            ipcRenderer.send('splash-login-complete', { username, password });
                        } catch (error) {
                            console.log('Login complete - Electron not available:', error);
                        }
                    }, 1500);
                } else {
                    showError('Invalid username or password');
                }
                
                loginBtn.classList.remove('loading');
            }, 1000);
        });
    }
}); 