const { app, BrowserWindow, ipcMain, shell, dialog, Menu, screen } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { exec, spawn } = require('child_process');
const si = require('systeminformation');
const { machineIdSync } = require('node-machine-id');
const chokidar = require('chokidar');
const glob = require('glob');

let mainWindow;
let cleanupProgress = {
  is_running: false,
  files_processed: 0,
  bytes_freed: 0,
  current_operation: 'Ready'
};

// App configuration
const isDev = process.argv.includes('--dev');
const APP_NAME = 'Armory X';

function createWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  mainWindow = new BrowserWindow({
    width: Math.min(1650, width - 50),
    height: Math.min(1000, height - 50),
    minWidth: 1000,
    minHeight: 650,
    frame: false, // Custom title bar
    titleBarStyle: 'hidden',
    backgroundColor: '#0a0a0a',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false,
      webSecurity: !isDev
    },
    icon: path.join(__dirname, 'assets/Armory_X.ico'),
    show: false // Don't show until ready
  });

  // Load the frontend
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('✅ Armory X window shown');
  });

  // Open DevTools in development
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // Prevent new window creation
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// App event handlers
app.whenReady().then(() => {
  console.log(`🚀 ${APP_NAME} starting...`);
  console.log(`📍 Platform: ${process.platform}`);
  console.log(`📍 Electron: ${process.versions.electron}`);
  console.log(`📍 Node: ${process.versions.node}`);
  console.log(`📍 Development mode: ${isDev}`);

  createWindow();
  setupMenu();
  
  console.log('✅ App initialization complete');
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Setup application menu
function setupMenu() {
  const template = [
    {
      label: APP_NAME,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Window control handlers
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.on('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.on('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Test connection handler for debugging
ipcMain.handle('test-connection', async () => {
  return { success: true, message: 'IPC connection working' };
});

// System information handlers
ipcMain.handle('get-system-info', async () => {
  return {
    os: {
      platform: os.platform(),
      release: os.release(),
      type: os.type()
    },
    cpu: {
      brand: os.cpus()[0].model,
      cores: os.cpus().length
    },
    memory: {
      total: os.totalmem(),
      free: os.freemem()
    }
  };
});

ipcMain.handle('get-system-stats', async () => {
  const stats = process.getSystemMemoryInfo();
  const cpuUsage = process.getCPUUsage();
  
  return {
    memory: Math.round((stats.total - stats.free) / stats.total * 100),
    cpu: Math.round(cpuUsage.percentCPUUsage)
  };
});

// Cleanup functionality
ipcMain.handle('clean-junk-files', async () => {
  console.log('🧹 Starting cleanup process...');
  
  cleanupProgress = {
    is_running: true,
    files_processed: 0,
    bytes_freed: 0,
    current_operation: 'Initializing cleanup...'
  };

  try {
    const tempPaths = [
      path.join(os.tmpdir()),
      path.join(os.homedir(), 'AppData', 'Local', 'Temp'),
      path.join(os.homedir(), 'AppData', 'Local', 'Microsoft', 'Windows', 'INetCache'),
      path.join(os.homedir(), 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Cache'),
      path.join(os.homedir(), 'AppData', 'Roaming', 'Mozilla', 'Firefox', 'Profiles')
    ];

    let totalDeleted = 0;
    let totalSize = 0;

    for (const tempPath of tempPaths) {
      if (await fs.pathExists(tempPath)) {
        cleanupProgress.current_operation = `Cleaning ${path.basename(tempPath)}...`;
        
        const { deleted, size } = await cleanDirectory(tempPath);
        totalDeleted += deleted;
        totalSize += size;
        
        cleanupProgress.files_processed = totalDeleted;
        cleanupProgress.bytes_freed = totalSize;
      }
    }

    cleanupProgress.is_running = false;
    cleanupProgress.current_operation = 'Cleanup complete';

    return {
      success: true,
      message: `Cleanup completed! Deleted ${totalDeleted} files, freed ${formatBytes(totalSize)}`,
      files_deleted: totalDeleted,
      bytes_freed: totalSize
    };

  } catch (error) {
    cleanupProgress.is_running = false;
    cleanupProgress.current_operation = 'Cleanup failed';
    console.error('Cleanup error:', error);
    
    return {
      success: false,
      message: `Cleanup failed: ${error.message}`
    };
  }
});

async function cleanDirectory(dirPath) {
  let deleted = 0;
  let size = 0;
  
  try {
    const files = await fs.readdir(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      
      try {
        const stats = await fs.stat(filePath);
        
        if (stats.isFile()) {
          // Skip files that are currently in use
          const age = Date.now() - stats.mtime.getTime();
          if (age > 24 * 60 * 60 * 1000) { // Older than 24 hours
            size += stats.size;
            await fs.unlink(filePath);
            deleted++;
          }
        } else if (stats.isDirectory()) {
          const subResult = await cleanDirectory(filePath);
          deleted += subResult.deleted;
          size += subResult.size;
          
          // Try to remove empty directory
          try {
            await fs.rmdir(filePath);
          } catch (e) {
            // Directory not empty, that's ok
          }
        }
      } catch (error) {
        // File in use or permission denied, skip
        console.log(`Skipping ${filePath}: ${error.message}`);
      }
    }
  } catch (error) {
    console.log(`Cannot access ${dirPath}: ${error.message}`);
  }
  
  return { deleted, size };
}

ipcMain.handle('get-cleanup-progress', () => {
  return cleanupProgress;
});

// Helper function to get all available drives
async function getAllDrives() {
  const drives = [];
  
  try {
    // On Windows, check drives A through Z
    for (let i = 65; i <= 90; i++) {
      const driveLetter = String.fromCharCode(i);
      const drivePath = `${driveLetter}:`;
      
      try {
        await fs.access(drivePath);
        drives.push(driveLetter);
      } catch (error) {
        // Drive doesn't exist or isn't accessible
      }
    }
  } catch (error) {
    console.log('Error checking drives:', error.message);
    // Fallback to just C drive
    drives.push('C');
  }
  
  console.log(`🗂️ Found ${drives.length} available drives: ${drives.join(', ')}`);
  return drives;
}

// Mod management
ipcMain.handle('get-mods-for-game', async (event, gameId) => {
  console.log(`📦 Getting mods for game: ${gameId}`);
  
  // Check if this is a custom game
  if (gameId && gameId.startsWith('custom_')) {
    console.log(`🎮 Processing custom game: ${gameId}`);
    
    // Get custom game data from renderer
    const customGameData = await event.sender.executeJavaScript(`
      customGames.find(game => game.id === '${gameId}')
    `).catch(() => null);
    
    if (!customGameData || !customGameData.modFolders) {
      console.log(`❌ No custom game data or mod folders found for ${gameId}`);
      return { 
        mods: [], 
        total: 0,
        path: null,
        isDemo: true,
        checkedPaths: []
      };
    }
    
    console.log(`🔍 Found custom game: ${customGameData.name} with ${customGameData.modFolders.length} mod folders`);
    
    // Get supported file types for this custom game
    const supportedFileTypes = customGameData.supportedFileTypes || ['.zip', '.rar', '.7z'];
    console.log(`📋 Supported file types: ${supportedFileTypes.join(', ')}`);
    
    let foundMods = [];
    let usedPath = null;
    const checkedPaths = [];
    
    // Check each mod folder defined for this custom game
    for (const modFolder of customGameData.modFolders) {
      const modPath = modFolder.path;
      checkedPaths.push(modPath);
      console.log(`   Checking custom mod folder: ${modPath}`);
      
      try {
        if (await fs.pathExists(modPath)) {
          console.log(`   ✅ Path exists: ${modPath}`);
          
          const files = await fs.readdir(modPath);
          const mods = [];
          
          for (const file of files) {
            const filePath = path.join(modPath, file);
            const stats = await fs.stat(filePath);
            
            if (stats.isFile()) {
              const ext = path.extname(file).toLowerCase();
              
              if (supportedFileTypes.includes(ext)) {
                mods.push({
                  name: file,
                  path: filePath,
                  size: stats.size,
                  modified: stats.mtime,
                  category: modFolder.name || 'Mods'
                });
              }
            }
          }
          
          if (mods.length > 0) {
            console.log(`   🎯 Found ${mods.length} mods in: ${modPath}`);
            foundMods.push(...mods);
            if (!usedPath) usedPath = modPath; // Use first path with mods as primary
          } else {
            console.log(`   📁 Path exists but no valid mod files found: ${modPath}`);
          }
        } else {
          console.log(`   ❌ Path does not exist: ${modPath}`);
        }
      } catch (error) {
        console.log(`   ⚠️ Error checking path ${modPath}:`, error.message);
      }
    }
    
    if (foundMods.length > 0) {
      console.log(`✅ Successfully found ${foundMods.length} mods for custom game ${customGameData.name}`);
      return { 
        mods: foundMods, 
        total: foundMods.length,
        path: usedPath,
        isDemo: false
      };
    } else {
      console.log(`❌ No mods found for custom game ${customGameData.name}`);
      return { 
        mods: [], 
        total: 0,
        path: null,
        isDemo: true,
        checkedPaths: checkedPaths
      };
    }
  }
  
  // Handle preset games (existing code)
  // Get all available drives for Schedule 1
  const availableDrives = await getAllDrives();
  
  // Generate Schedule 1 paths for all drives (Steam-only locations)
  const schedule1Paths = [];
  for (const drive of availableDrives) {
    schedule1Paths.push(
      // Steam library paths only (where Schedule I is actually installed)
      path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
      path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
      path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
    );
  }
  
  // Special handling for Minecraft - scan multiple folders separately
  if (gameId === 'minecraft') {
    console.log(`🎮 Scanning Minecraft folders separately for comprehensive mod detection`);
    
    const minecraftFolders = [
      { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'), name: 'Mods' },
      { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'shaderpacks'), name: 'Shaders' },
      { path: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'resourcepacks'), name: 'Resource Packs' }
    ];
    
    let allMinecraftMods = [];
    let foundAnyMods = false;
    let usedPaths = [];
    
    for (const folder of minecraftFolders) {
      console.log(`   🔍 Checking ${folder.name}: ${folder.path}`);
      
      try {
        if (await fs.pathExists(folder.path)) {
          console.log(`   ✅ ${folder.name} folder exists`);
          
          const files = await fs.readdir(folder.path);
          const mods = [];
          
          for (const file of files) {
            const filePath = path.join(folder.path, file);
            const stats = await fs.stat(filePath);
            
            if (stats.isFile()) {
              const ext = path.extname(file).toLowerCase();
              const validExtensions = ['.jar', '.zip', '.rar', '.7z'];
              
              if (validExtensions.includes(ext)) {
                mods.push({
                  name: file,
                  path: filePath,
                  size: stats.size,
                  modified: stats.mtime,
                  category: getModCategory(file, gameId, filePath)
                });
              }
            }
          }
          
          if (mods.length > 0) {
            console.log(`   🎯 Found ${mods.length} items in ${folder.name}: ${folder.path}`);
            allMinecraftMods = allMinecraftMods.concat(mods);
            foundAnyMods = true;
            usedPaths.push(folder.path);
          } else {
            console.log(`   📁 ${folder.name} folder exists but no valid files found`);
          }
        } else {
          console.log(`   ❌ ${folder.name} folder does not exist: ${folder.path}`);
        }
      } catch (error) {
        console.log(`   ⚠️ Error checking ${folder.name} folder:`, error.message);
      }
    }
    
    if (foundAnyMods) {
      console.log(`✅ Successfully found ${allMinecraftMods.length} total Minecraft items across ${usedPaths.length} folders`);
      return { 
        mods: allMinecraftMods, 
        total: allMinecraftMods.length,
        path: usedPaths.join('; '),
        isDemo: false
      };
    } else {
      console.log(`❌ No Minecraft mods found in any folder`);
      return { 
        mods: [], 
        total: 0,
        path: null,
        isDemo: true,
        checkedPaths: minecraftFolders.map(f => f.path)
      };
    }
  }

  // Handle other games with original logic
  const gameModPaths = {
    fs22: [
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods'),
      path.join(os.homedir(), 'OneDrive', 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      path.join('C:', 'Users', os.userInfo().username, 'Documents', 'My Games', 'FarmingSimulator2022', 'mods')
    ],
    schedule1: schedule1Paths
  };
  
  const possiblePaths = gameModPaths[gameId] || [];
  console.log(`🔍 Checking ${possiblePaths.length} possible paths for ${gameId}:`);
  
  let foundMods = [];
  let usedPath = null;
  
  // Check each possible path
  for (const modPath of possiblePaths) {
    console.log(`   Checking: ${modPath}`);
    
    try {
      if (await fs.pathExists(modPath)) {
        console.log(`   ✅ Path exists: ${modPath}`);
        
        const files = await fs.readdir(modPath);
        const mods = [];
        
        for (const file of files) {
          const filePath = path.join(modPath, file);
          const stats = await fs.stat(filePath);
          
          if (stats.isFile()) {
            // Filter for common mod file extensions based on game
            const ext = path.extname(file).toLowerCase();
            let validExtensions = [];
            
            switch(gameId) {
              case 'fs22':
                validExtensions = ['.zip', '.rar', '.7z', '.mod'];
                break;
              case 'schedule1':
                validExtensions = ['.dll', '.zip', '.rar', '.7z', '.mod'];
                break;
              default:
                validExtensions = ['.jar', '.zip', '.rar', '.7z', '.mod', '.dll'];
            }
            
            if (validExtensions.includes(ext)) {
              mods.push({
                name: file,
                path: filePath,
                size: stats.size,
                modified: stats.mtime,
                category: getModCategory(file, gameId, filePath)
              });
            }
          }
        }
        
        if (mods.length > 0) {
          console.log(`   🎯 Found ${mods.length} mods in: ${modPath}`);
          foundMods = mods;
          usedPath = modPath;
          break; // Use the first path that has mods
        } else {
          console.log(`   📁 Path exists but no valid mod files found: ${modPath}`);
        }
      } else {
        console.log(`   ❌ Path does not exist: ${modPath}`);
      }
    } catch (error) {
      console.log(`   ⚠️ Error checking path ${modPath}:`, error.message);
    }
  }
  
  if (foundMods.length > 0) {
    console.log(`✅ Successfully found ${foundMods.length} mods for ${gameId} in: ${usedPath}`);
    return { 
      mods: foundMods, 
      total: foundMods.length,
      path: usedPath,
      isDemo: false
    };
  } else {
    console.log(`❌ No mods found for ${gameId} in any of the checked paths`);
    return { 
      mods: [], 
      total: 0,
      path: null,
      isDemo: true,
      checkedPaths: possiblePaths
    };
  }
});

function getModCategory(fileName, gameId, filePath = '') {
  const ext = path.extname(fileName).toLowerCase();
  const name = fileName.toLowerCase();
  
  if (gameId === 'minecraft') {
    // Check folder-based categorization first (more reliable)
    if (filePath.includes('shaderpacks')) {
      return 'Graphics & Shaders';
    }
    if (filePath.includes('resourcepacks')) {
      return 'Graphics & Shaders';
    }
    
    // Continue with existing name-based detection for mods folder
    // Performance & Optimization
    if (name.includes('optifine') || name.includes('sodium') || name.includes('phosphor') || 
        name.includes('lithium') || name.includes('fps') || name.includes('performance') ||
        name.includes('fastcraft') || name.includes('foamfix') || name.includes('betterfps') ||
        name.includes('vanillafix') || name.includes('surge') || name.includes('ferrite')) {
      return 'Performance & Optimization';
    }
    
    // Graphics & Shaders
    if (name.includes('shader') || name.includes('seus') || name.includes('bsl') ||
        name.includes('complementary') || name.includes('sildurs') || name.includes('chocapic') ||
        name.includes('kuda') || name.includes('continuum') || name.includes('graphics') ||
        name.includes('lighting') || name.includes('dynamic') || name.includes('visual')) {
      return 'Graphics & Shaders';
    }
    
    // Storage & Organization
    if (name.includes('jei') || name.includes('nei') || name.includes('inventory') || 
        name.includes('storage') || name.includes('chest') || name.includes('backpack') ||
        name.includes('bag') || name.includes('drawer') || name.includes('cabinet') ||
        name.includes('sorting') || name.includes('refined') || name.includes('ae2') ||
        name.includes('applied') || name.includes('energistics') || name.includes('organize')) {
      return 'Storage & Organization';
    }
    
    // Technology & Machinery
    if (name.includes('tech') || name.includes('machine') || name.includes('industrial') ||
        name.includes('mekanism') || name.includes('thermal') || name.includes('enderio') ||
        name.includes('buildcraft') || name.includes('computercraft') || name.includes('opencomputers') ||
        name.includes('immersive') || name.includes('engineering') || name.includes('factory') ||
        name.includes('automation') || name.includes('power') || name.includes('energy') ||
        name.includes('nuclear') || name.includes('reactor') || name.includes('generator')) {
      return 'Technology & Machinery';
    }
    
    // Magic & Enchanting
    if (name.includes('magic') || name.includes('witch') || name.includes('spell') ||
        name.includes('thaumcraft') || name.includes('botania') || name.includes('blood') ||
        name.includes('arcane') || name.includes('mystical') || name.includes('enchant') ||
        name.includes('astral') || name.includes('sorcery') || name.includes('wizard') ||
        name.includes('rune') || name.includes('ritual') || name.includes('crystal') ||
        name.includes('forbidden') || name.includes('eldritch')) {
      return 'Magic & Enchanting';
    }
    
    // World Generation & Biomes
    if (name.includes('biome') || name.includes('world') || name.includes('terrain') ||
        name.includes('bioplenty') || name.includes('biomesoplenty') || name.includes('climate') ||
        name.includes('weather') || name.includes('season') || name.includes('dimension') ||
        name.includes('abyss') || name.includes('twilight') || name.includes('nether') ||
        name.includes('end') || name.includes('cave') || name.includes('underground') ||
        name.includes('surface') || name.includes('exploration') || name.includes('structure')) {
      return 'World Generation & Biomes';
    }
    
    // Food & Agriculture
    if (name.includes('food') || name.includes('crop') || name.includes('farm') ||
        name.includes('agriculture') || name.includes('cooking') || name.includes('kitchen') ||
        name.includes('harvest') || name.includes('plant') || name.includes('seed') ||
        name.includes('grow') || name.includes('fruit') || name.includes('vegetable') ||
        name.includes('meal') || name.includes('hunger') || name.includes('nutrition') ||
        name.includes('pam') || name.includes('spice') || name.includes('culinary')) {
      return 'Food & Agriculture';
    }
    
    // Building & Decoration
    if (name.includes('decoration') || name.includes('furniture') || name.includes('decor') ||
        name.includes('building') || name.includes('architecture') || name.includes('construct') ||
        name.includes('chisel') || name.includes('carpenter') || name.includes('block') ||
        name.includes('brick') || name.includes('paint') || name.includes('design') ||
        name.includes('aesthetic') || name.includes('pretty') || name.includes('beautiful') ||
        name.includes('chair') || name.includes('table') || name.includes('lamp')) {
      return 'Building & Decoration';
    }
    
    // Transportation
    if (name.includes('transport') || name.includes('vehicle') || name.includes('car') ||
        name.includes('plane') || name.includes('train') || name.includes('boat') ||
        name.includes('ship') || name.includes('horse') || name.includes('travel') ||
        name.includes('rail') || name.includes('road') || name.includes('bridge') ||
        name.includes('tunnel') || name.includes('portal') || name.includes('teleport') ||
        name.includes('waypoint') || name.includes('journey')) {
      return 'Transportation';
    }
    
    // Combat & Weapons
    if (name.includes('combat') || name.includes('weapon') || name.includes('sword') ||
        name.includes('bow') || name.includes('armor') || name.includes('shield') ||
        name.includes('battle') || name.includes('war') || name.includes('fight') ||
        name.includes('damage') || name.includes('defense') || name.includes('protection') ||
        name.includes('tinker') || name.includes('construct') || name.includes('epic') ||
        name.includes('legendary') || name.includes('deadly')) {
      return 'Combat & Weapons';
    }
    
    // Adventure & Exploration
    if (name.includes('adventure') || name.includes('explore') || name.includes('quest') ||
        name.includes('dungeon') || name.includes('treasure') || name.includes('loot') ||
        name.includes('rogue') || name.includes('journey') || name.includes('discovery') ||
        name.includes('mystery') || name.includes('ancient') || name.includes('relic') ||
        name.includes('artifact') || name.includes('expedition') || name.includes('map') ||
        name.includes('compass') || name.includes('milestone')) {
      return 'Adventure & Exploration';
    }
    
    // Animals & Pets
    if (name.includes('animal') || name.includes('pet') || name.includes('dog') ||
        name.includes('cat') || name.includes('horse') || name.includes('bird') ||
        name.includes('fish') || name.includes('mob') || name.includes('creature') ||
        name.includes('spawn') || name.includes('breed') || name.includes('tame') ||
        name.includes('zoo') || name.includes('wildlife') || name.includes('domestic')) {
      return 'Animals & Pets';
    }
    
    // Utilities & Quality of Life
    if (name.includes('util') || name.includes('helper') || name.includes('tool') ||
        name.includes('waila') || name.includes('hwyla') || name.includes('top') ||
        name.includes('gui') || name.includes('hud') || name.includes('tooltip') ||
        name.includes('info') || name.includes('display') || name.includes('indicator') ||
        name.includes('overlay') || name.includes('status') || name.includes('meter') ||
        name.includes('gauge') || name.includes('monitor') || name.includes('scanner')) {
      return 'Utilities & Quality of Life';
    }
    
    // API & Core Libraries
    if (name.includes('api') || name.includes('core') || name.includes('lib') ||
        name.includes('library') || name.includes('base') || name.includes('common') ||
        name.includes('shared') || name.includes('framework') || name.includes('foundation') ||
        name.includes('support') || name.includes('dependency') || name.includes('requirement') ||
        name.includes('codechicken') || name.includes('cofh') || name.includes('mantle') ||
        name.includes('forge') || name.includes('fabric') || name.includes('quilt')) {
      return 'API & Core Libraries';
    }
    
    // Multiplayer & Social
    if (name.includes('multiplayer') || name.includes('server') || name.includes('chat') ||
        name.includes('social') || name.includes('friend') || name.includes('guild') ||
        name.includes('team') || name.includes('party') || name.includes('claim') ||
        name.includes('protection') || name.includes('grief') || name.includes('anti') ||
        name.includes('permission') || name.includes('rank') || name.includes('economy')) {
      return 'Multiplayer & Social';
    }
    
    // Default to Miscellaneous for mods that don't fit other categories
    return 'Miscellaneous';
  }
  
  // For other games, return simple category
  return 'Mods';
}

// Enhanced mod folder opening with multiple path support
ipcMain.handle('open-mod-folder', async (event, gameId) => {
  console.log(`📁 Opening mod folder for: ${gameId}`);
  
  // Check if this is a custom game
  if (gameId && gameId.startsWith('custom_')) {
    console.log(`🎮 Opening mod folder for custom game: ${gameId}`);
    
    // Get custom game data from renderer
    const customGameData = await event.sender.executeJavaScript(`
      customGames.find(game => game.id === '${gameId}')
    `).catch(() => null);
    
    if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
      console.log(`❌ No custom game data or mod folders found for ${gameId}`);
      return { success: false, message: 'No mod folders configured for this custom game' };
    }
    
    // Try to open the first available mod folder
    for (const modFolder of customGameData.modFolders) {
      const modPath = modFolder.path;
      console.log(`   Trying to open: ${modPath}`);
      
      try {
        if (await fs.pathExists(modPath)) {
          shell.openPath(modPath);
          console.log(`✅ Opened custom game mod folder: ${modPath}`);
          return { success: true, path: modPath };
        }
      } catch (error) {
        console.log(`❌ Error opening path ${modPath}:`, error.message);
      }
    }
    
    // If no folder exists, try to create the first one
    const primaryModFolder = customGameData.modFolders[0];
    if (primaryModFolder) {
      try {
        await fs.ensureDir(primaryModFolder.path);
        shell.openPath(primaryModFolder.path);
        console.log(`✅ Created and opened custom game mod folder: ${primaryModFolder.path}`);
        return { success: true, path: primaryModFolder.path, created: true };
      } catch (error) {
        console.log(`❌ Error creating folder ${primaryModFolder.path}:`, error.message);
        return { success: false, message: `Failed to create mod folder: ${error.message}` };
      }
    }
    
    return { success: false, message: 'No valid mod folder paths found for this custom game' };
  }
  
  // Handle preset games with proper path detection
  let possiblePaths = [];
  
  if (gameId === 'schedule1') {
    // For Schedule I, use the same logic as scanning and installation
    const availableDrives = await getAllDrives();
    
    // Generate Schedule 1 paths for all drives (Steam locations)
    for (const drive of availableDrives) {
      possiblePaths.push(
        path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
        path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
      );
    }
    
    // Add fallback to Documents (but it should be last priority)
    possiblePaths.push(
      path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
      path.join(os.homedir(), 'Documents', 'Schedule 1')
    );
  } else {
    // Handle other preset games
    const gameModPaths = {
      minecraft: [
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
        path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft')
      ],
      fs22: [
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
        path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022')
      ]
    };
    
    possiblePaths = gameModPaths[gameId] || [];
  }
  
  // Try to find an existing path
  for (const modPath of possiblePaths) {
    try {
      if (await fs.pathExists(modPath)) {
        shell.openPath(modPath);
        console.log(`✅ Opened folder: ${modPath}`);
        return { success: true, path: modPath };
      }
    } catch (error) {
      console.log(`❌ Error opening path ${modPath}:`, error.message);
    }
  }
  
  // If no path exists, create the primary mod directory
  const primaryPath = possiblePaths[0];
  if (primaryPath) {
    try {
      await fs.ensureDir(primaryPath);
      shell.openPath(primaryPath);
      console.log(`✅ Created and opened folder: ${primaryPath}`);
      return { success: true, path: primaryPath, created: true };
    } catch (error) {
      console.log(`❌ Error creating folder ${primaryPath}:`, error.message);
      return { success: false, message: `Failed to create mod folder: ${error.message}` };
    }
  }
  
  return { success: false, message: 'No valid mod paths configured for this game' };
});

// Add a new handler to get mod directory info
ipcMain.handle('get-mod-directories', async (event, gameId) => {
  const gameModPaths = {
    minecraft: [
      path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      path.join(os.homedir(), 'curseforge', 'minecraft', 'Instances'),
      path.join(os.homedir(), 'AppData', 'Roaming', 'PrismLauncher', 'instances')
    ],
    fs22: [
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
      path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2025', 'mods')
    ],
    schedule1: [
      path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods'),
      path.join(os.homedir(), 'AppData', 'LocalLow', 'Schedule 1', 'mods')
    ]
  };
  
  const result = {};
  
  if (gameId) {
    // Get info for specific game
    const paths = gameModPaths[gameId] || [];
    result[gameId] = {
      paths: paths,
      existing: []
    };
    
    for (const modPath of paths) {
      try {
        if (await fs.pathExists(modPath)) {
          result[gameId].existing.push(modPath);
        }
      } catch (error) {
        console.log(`Error checking path ${modPath}:`, error.message);
      }
    }
  } else {
    // Get info for all games
    for (const [game, paths] of Object.entries(gameModPaths)) {
      result[game] = {
        paths: paths,
        existing: []
      };
      
      for (const modPath of paths) {
        try {
          if (await fs.pathExists(modPath)) {
            result[game].existing.push(modPath);
          }
        } catch (error) {
          console.log(`Error checking path ${modPath}:`, error.message);
        }
      }
    }
  }
  
  return result;
});

// Add handler to create test mods for testing
ipcMain.handle('create-test-mods', async (event, gameId) => {
  console.log(`🧪 Creating test mods for: ${gameId}`);
  
  const gameModPaths = {
    minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
    fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
    schedule1: path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods')
  };
  
  const modPath = gameModPaths[gameId];
  if (!modPath) {
    return { success: false, message: 'Invalid game ID' };
  }
  
  try {
    // Ensure the directory exists
    await fs.ensureDir(modPath);
    
    const testMods = {
      minecraft: [
        { name: 'TestMod-OptiFine-HD.jar', size: 2500000 },
        { name: 'JustEnoughItems-1.20.1.jar', size: 1800000 },
        { name: 'BiomesOPlenty-1.20.1.jar', size: 3200000 },
        { name: 'Waila-1.8.26.jar', size: 450000 },
        { name: 'IronChests-1.20.1.jar', size: 890000 }
      ],
      fs22: [
        { name: 'TestTractor-JohnDeere8R.zip', size: 15000000 },
        { name: 'FS22_RealisticVehicles.zip', size: 8500000 },
        { name: 'FS22_SeasonsMod.zip', size: 45000000 }
      ],
      schedule1: [
        { name: 'EnhancedChemistry.zip', size: 25600000 },
        { name: 'BusinessExpansion.zip', size: 18900000 },
        { name: 'SecurityUpgrade.zip', size: 31200000 }
      ]
    };
    
    const modsToCreate = testMods[gameId] || [];
    let createdCount = 0;
    
    for (const mod of modsToCreate) {
      const filePath = path.join(modPath, mod.name);
      
      // Check if file already exists
      if (await fs.pathExists(filePath)) {
        console.log(`   Skipping existing file: ${mod.name}`);
        continue;
      }
      
      // Create a dummy file with some content
      const content = `# Test Mod File: ${mod.name}\n` +
                     `# Created by Armory X for testing purposes\n` +
                     `# Game: ${gameId}\n` +
                     `# Size: ${mod.size} bytes\n` +
                     `# Created: ${new Date().toISOString()}\n\n` +
                     `This is a test mod file created for testing the mod manager functionality.\n` +
                     `${'='.repeat(Math.floor(mod.size / 100))}`;
      
      await fs.writeFile(filePath, content);
      console.log(`   ✅ Created test mod: ${mod.name}`);
      createdCount++;
    }
    
    console.log(`✅ Created ${createdCount} test mods in: ${modPath}`);
    
    return {
      success: true,
      message: `Created ${createdCount} test mods`,
      createdCount,
      skipped: modsToCreate.length - createdCount,
      path: modPath
    };
    
  } catch (error) {
    console.error(`❌ Error creating test mods:`, error);
    return {
      success: false,
      message: `Failed to create test mods: ${error.message}`
    };
  }
});

// File operations
ipcMain.handle('show-in-folder', async (event, filePath) => {
  shell.showItemInFolder(filePath);
  return { success: true };
});

ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  
  return result.canceled ? null : result.filePaths[0];
});

// Add native file selection for custom games
ipcMain.handle('select-file', async (event, options = {}) => {
  const dialogOptions = {
    properties: ['openFile'],
    filters: options.filters || [
      { name: 'All Files', extensions: ['*'] }
    ]
  };

  const result = await dialog.showOpenDialog(mainWindow, dialogOptions);
  return result;
});

// Add icon extraction handler
ipcMain.handle('extract-icon', async (event, filePath) => {
  try {
    console.log(`🎨 Extracting icon from: ${filePath}`);
    
    // Use Electron's built-in icon extraction
    const icon = await app.getFileIcon(filePath, { size: 'large' });
    
    if (icon) {
      // Convert icon to base64 data URL
      const iconBuffer = icon.toPNG();
      const iconBase64 = `data:image/png;base64,${iconBuffer.toString('base64')}`;
      
      console.log(`✅ Icon extracted successfully from: ${path.basename(filePath)}`);
      return { 
        success: true, 
        iconData: iconBase64,
        message: 'Icon extracted successfully'
      };
    } else {
      console.log(`❌ No icon found in: ${filePath}`);
      return { 
        success: false, 
        message: 'No icon found in file'
      };
    }
  } catch (error) {
    console.error(`❌ Error extracting icon: ${error.message}`);
    return { 
      success: false, 
      message: `Failed to extract icon: ${error.message}`
    };
  }
});

ipcMain.handle('select-folder-dialog', async (event, options = {}) => {
  const dialogOptions = {
    properties: ['openDirectory']
  };

  const result = await dialog.showOpenDialog(mainWindow, dialogOptions);
  return result;
});

// Settings management
ipcMain.handle('load-settings', async () => {
  const settingsPath = path.join(app.getPath('userData'), 'settings.json');
  
  try {
    if (await fs.pathExists(settingsPath)) {
      const settings = await fs.readJson(settingsPath);
      return settings;
    }
  } catch (error) {
    console.error('Error loading settings:', error);
  }
  
  // Return default settings
  return {
    theme: 'dark',
    primaryColor: '#00d4ff',
    secondaryColor: '#ff6b35',
    accentColor: '#00ff88',
    animations: true,
    autoCleanup: false,
    notifications: true,
    minimizeToTray: false,
    startWithWindows: false
  };
});

ipcMain.handle('save-settings', async (event, settings) => {
  const settingsPath = path.join(app.getPath('userData'), 'settings.json');
  
  try {
    await fs.writeJson(settingsPath, settings, { spaces: 2 });
    return { success: true };
  } catch (error) {
    console.error('Error saving settings:', error);
    return { success: false, message: error.message };
  }
});

// License management
ipcMain.handle('get-machine-id', () => {
  try {
    return machineIdSync();
  } catch (error) {
    console.error('Error getting machine ID:', error);
    return null;
  }
});

// Add mod deletion handler
ipcMain.handle('delete-mod', async (event, modPath) => {
  try {
    console.log(`🗑️ Deleting mod: ${modPath}`);
    
    // Check if file exists
    if (await fs.pathExists(modPath)) {
      // Move to trash/recycle bin instead of permanent deletion
      await shell.trashItem(modPath);
      console.log(`✅ Mod deleted successfully: ${modPath}`);
      return { success: true, message: 'Mod deleted successfully' };
    } else {
      console.log(`❌ Mod file not found: ${modPath}`);
      return { success: false, message: 'Mod file not found' };
    }
  } catch (error) {
    console.error(`❌ Error deleting mod: ${error.message}`);
    return { success: false, message: `Failed to delete mod: ${error.message}` };
  }
});

// Install mods with individual categories handler
ipcMain.handle('install-mods-with-categories', async (event, data) => {
  console.log(`📦 Installing mods with categories for game: ${data.gameId}`);
  console.log(`   Files to install: ${data.modFileData.length}`);
  
  // Get mod path for the specified game - uses the SAME logic as scanning!
  async function getGameModPath(gameId, category) {
    // Check if it's a custom game
    if (gameId && gameId.startsWith('custom_')) {
      console.log(`🎮 Installing mods for custom game: ${gameId}`);
      
      // Get custom game data from renderer
      const customGameData = await event.sender.executeJavaScript(`
        customGames.find(game => game.id === '${gameId}')
      `).catch(() => null);
      
      if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
        throw new Error('No mod folders configured for this custom game');
      }
      
      // For custom games, use the first mod folder or find a matching one
      const targetFolder = customGameData.modFolders.find(folder => 
        folder.name.toLowerCase() === category.toLowerCase()
      ) || customGameData.modFolders[0];
      
      return targetFolder.path;
    }
    
    // Handle preset games - FOR SCHEDULE I, REUSE THE SCANNING LOGIC!
    if (gameId === 'schedule1') {
      // Get all available drives for Schedule 1 (same as scanning)
      const availableDrives = await getAllDrives();
      
      // Generate Schedule 1 paths for all drives (Steam-only locations)
      const schedule1Paths = [];
      for (const drive of availableDrives) {
        schedule1Paths.push(
          path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
        );
      }
      
      // Check each path and use the first one that exists (same as scanning)
      for (const modPath of schedule1Paths) {
        try {
          if (await fs.pathExists(modPath)) {
            console.log(`🎯 Using Schedule I mod path: ${modPath}`);
            return modPath;
          }
        } catch (error) {
          console.log(`❌ Error checking Schedule I path ${modPath}:`, error.message);
        }
      }
      
      // Fallback to documents if not found
      const fallbackPath = path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods');
      console.log(`⚠️ Schedule I not found in Steam locations, using fallback: ${fallbackPath}`);
      return fallbackPath;
    }
    
    // Handle other preset games
    const gameModPaths = {
      minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods')
    };
    
    // For Minecraft, we might want to organize by category
    if (gameId === 'minecraft' && category && category !== 'Mods') {
      const minecraftRoot = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
      
      if (category === 'Graphics & Shaders') {
        // For Graphics & Shaders category, determine folder based on file type/name
        // This will be handled per-file in the installation loop below
        return null; // Special flag to handle per-file
      }
      // For other categories, use regular mods folder
    }
    
    return gameModPaths[gameId];
  }
  
  try {
    let installedCount = 0;
    let skippedCount = 0;
    const results = [];
    const targetPaths = new Set(); // Track unique paths used
    
    for (const modData of data.modFileData) {
      const { filePath, category, fileName } = modData;
      console.log(`   📁 Processing: ${fileName} → ${category}`);
      
      // Get base target directory for this file's category
      const baseTargetDir = await getGameModPath(data.gameId, category);
      
      // Determine final target directory per file for Graphics & Shaders category
      let targetDir = baseTargetDir;
      if (data.gameId === 'minecraft' && category === 'Graphics & Shaders' && baseTargetDir === null) {
        const minecraftRoot = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
        const lowerFileName = fileName.toLowerCase();
        
        // Determine folder based on file name patterns
        if (lowerFileName.includes('shader') || lowerFileName.includes('seus') || 
            lowerFileName.includes('bsl') || lowerFileName.includes('complementary') ||
            lowerFileName.includes('sildurs') || lowerFileName.includes('chocapic') ||
            lowerFileName.includes('kuda') || lowerFileName.includes('continuum')) {
          targetDir = path.join(minecraftRoot, 'shaderpacks');
        } else if (lowerFileName.includes('texture') || lowerFileName.includes('resource') || 
                   lowerFileName.includes('pack') || lowerFileName.includes('faithful') ||
                   lowerFileName.includes('sphax') || lowerFileName.includes('dokucraft')) {
          targetDir = path.join(minecraftRoot, 'resourcepacks');
        } else {
          // Default to shaderpacks for unknown Graphics & Shaders files
          targetDir = path.join(minecraftRoot, 'shaderpacks');
        }
      }
      
      if (!targetDir) {
        console.log(`   ❌ No target directory found for ${fileName}`);
        results.push({ name: fileName, status: 'failed', reason: 'No target directory configured' });
        continue;
      }
      
      // Ensure target directory exists
      await fs.ensureDir(targetDir);
      targetPaths.add(targetDir);
      console.log(`   📁 Target directory for ${fileName}: ${targetDir}`);
      
      const targetPath = path.join(targetDir, fileName);
      
      try {
        // Check if source file exists
        if (await fs.pathExists(filePath)) {
          // Check if target already exists
          if (await fs.pathExists(targetPath)) {
            console.log(`   ⚠️ Skipping existing file: ${fileName}`);
            skippedCount++;
            results.push({ name: fileName, status: 'skipped', reason: 'File already exists' });
            continue;
          }
          
          // Copy the file
          await fs.copy(filePath, targetPath);
          console.log(`   ✅ Installed: ${fileName} → ${category}`);
          installedCount++;
          results.push({ name: fileName, status: 'installed', category: category });
          
        } else {
          console.log(`   ❌ Source file not found: ${filePath}`);
          results.push({ name: fileName, status: 'failed', reason: 'Source file not found' });
        }
      } catch (fileError) {
        console.log(`   ❌ Error installing ${fileName}: ${fileError.message}`);
        results.push({ name: fileName, status: 'failed', reason: fileError.message });
      }
    }
    
    console.log(`✅ Mod installation complete: ${installedCount} installed, ${skippedCount} skipped`);
    
    return {
      success: true,
      message: `Installed ${installedCount} mod${installedCount !== 1 ? 's' : ''}${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
      installedMods: installedCount,
      skippedMods: skippedCount,
      results: results,
      targetPaths: Array.from(targetPaths)
    };
    
  } catch (error) {
    console.error(`❌ Error installing mods with categories:`, error);
    return {
      success: false,
      message: `Failed to install mods: ${error.message}`
    };
  }
});

// Install mods handler (legacy - keeping for compatibility)
ipcMain.handle('install-mods', async (event, data) => {
  console.log(`📦 Installing mods for game: ${data.gameId}`);
  console.log(`   Files to install: ${data.filePaths.length}`);
  console.log(`   Target category: ${data.category}`);
  
  // Get mod path for the specified game - uses the SAME logic as scanning!
  async function getGameModPath(gameId, category) {
    // Check if it's a custom game
    if (gameId && gameId.startsWith('custom_')) {
      console.log(`🎮 Installing mods for custom game: ${gameId}`);
      
      // Get custom game data from renderer
      const customGameData = await event.sender.executeJavaScript(`
        customGames.find(game => game.id === '${gameId}')
      `).catch(() => null);
      
      if (!customGameData || !customGameData.modFolders || customGameData.modFolders.length === 0) {
        throw new Error('No mod folders configured for this custom game');
      }
      
      // For custom games, use the first mod folder or find a matching one
      const targetFolder = customGameData.modFolders.find(folder => 
        folder.name.toLowerCase() === category.toLowerCase()
      ) || customGameData.modFolders[0];
      
      return targetFolder.path;
    }
    
    // Handle preset games - FOR SCHEDULE I, REUSE THE SCANNING LOGIC!
    if (gameId === 'schedule1') {
      // Get all available drives for Schedule 1 (same as scanning)
      const availableDrives = await getAllDrives();
      
      // Generate Schedule 1 paths for all drives (Steam-only locations)
      const schedule1Paths = [];
      for (const drive of availableDrives) {
        schedule1Paths.push(
          path.join(`${drive}:`, 'SteamLibrary', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Steam', 'steamapps', 'common', 'Schedule I', 'mods'),
          path.join(`${drive}:`, 'Program Files (x86)', 'Steam', 'steamapps', 'common', 'Schedule I', 'mods')
        );
      }
      
      // Check each path and use the first one that exists (same as scanning)
      for (const modPath of schedule1Paths) {
        try {
          if (await fs.pathExists(modPath)) {
            console.log(`🎯 Using Schedule I mod path: ${modPath}`);
            return modPath;
          }
        } catch (error) {
          console.log(`❌ Error checking Schedule I path ${modPath}:`, error.message);
        }
      }
      
      // Fallback to documents if not found
      const fallbackPath = path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods');
      console.log(`⚠️ Schedule I not found in Steam locations, using fallback: ${fallbackPath}`);
      return fallbackPath;
    }
    
    // Handle other preset games
    const gameModPaths = {
      minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
      fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods')
    };
    
    // For Minecraft, we might want to organize by category
    if (gameId === 'minecraft' && category && category !== 'Mods' && category !== 'auto') {
      const minecraftRoot = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
      
      if (category === 'Graphics & Shaders') {
        // For Graphics & Shaders category, determine folder based on file type/name
        // This will be handled per-file in the installation loop below
        return null; // Special flag to handle per-file
      }
      // For other categories, use regular mods folder
    }
    
    return gameModPaths[gameId];
  }
  
  try {
    const baseTargetDir = await getGameModPath(data.gameId, data.category);
    
    if (baseTargetDir === undefined) {
      return { success: false, message: 'Invalid game ID or no mod path configured' };
    }
    
    let installedCount = 0;
    let skippedCount = 0;
    const results = [];
    
    for (const filePath of data.filePaths) {
      const fileName = path.basename(filePath);
      
      // Determine target directory per file for Graphics & Shaders category
      let targetDir = baseTargetDir;
      if (data.gameId === 'minecraft' && data.category === 'Graphics & Shaders' && baseTargetDir === null) {
        const minecraftRoot = path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft');
        const lowerFileName = fileName.toLowerCase();
        
        // Determine folder based on file name patterns
        if (lowerFileName.includes('shader') || lowerFileName.includes('seus') || 
            lowerFileName.includes('bsl') || lowerFileName.includes('complementary') ||
            lowerFileName.includes('sildurs') || lowerFileName.includes('chocapic') ||
            lowerFileName.includes('kuda') || lowerFileName.includes('continuum')) {
          targetDir = path.join(minecraftRoot, 'shaderpacks');
        } else if (lowerFileName.includes('texture') || lowerFileName.includes('resource') || 
                   lowerFileName.includes('pack') || lowerFileName.includes('faithful') ||
                   lowerFileName.includes('sphax') || lowerFileName.includes('dokucraft')) {
          targetDir = path.join(minecraftRoot, 'resourcepacks');
        } else {
          // Default to shaderpacks for unknown Graphics & Shaders files
          targetDir = path.join(minecraftRoot, 'shaderpacks');
        }
      }
      
      // Ensure target directory exists
      await fs.ensureDir(targetDir);
      console.log(`📁 Target directory for ${fileName}: ${targetDir}`);
      
      const targetPath = path.join(targetDir, fileName);
      
      try {
        // Check if source file exists
        if (await fs.pathExists(filePath)) {
          // Check if target already exists
          if (await fs.pathExists(targetPath)) {
            console.log(`   ⚠️ Skipping existing file: ${fileName}`);
            skippedCount++;
            results.push({ name: fileName, status: 'skipped', reason: 'File already exists' });
            continue;
          }
          
          // Copy the file
          await fs.copy(filePath, targetPath);
          console.log(`   ✅ Installed: ${fileName}`);
          installedCount++;
          results.push({ name: fileName, status: 'installed' });
          
        } else {
          console.log(`   ❌ Source file not found: ${filePath}`);
          results.push({ name: fileName, status: 'failed', reason: 'Source file not found' });
        }
      } catch (fileError) {
        console.log(`   ❌ Error installing ${fileName}: ${fileError.message}`);
        results.push({ name: fileName, status: 'failed', reason: fileError.message });
      }
    }
    
    console.log(`✅ Mod installation complete: ${installedCount} installed, ${skippedCount} skipped`);
    
    return {
      success: true,
      message: `Installed ${installedCount} mod${installedCount !== 1 ? 's' : ''}${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
      installedMods: installedCount,
      skippedMods: skippedCount,
      results: results,
      targetPath: baseTargetDir || 'Multiple locations'
    };
    
  } catch (error) {
    console.error(`❌ Error installing mods:`, error);
    return {
      success: false,
      message: `Failed to install mods: ${error.message}`
    };
  }
});

// Add mods handler (legacy - keeping for compatibility)
ipcMain.handle('add-mods', async (event, data) => {
  console.log(`➕ Adding mods for game: ${data.gameId}`);
  console.log(`   Files to add: ${data.files.length}`);
  
  const gameModPaths = {
    minecraft: path.join(os.homedir(), 'AppData', 'Roaming', '.minecraft', 'mods'),
    fs22: path.join(os.homedir(), 'Documents', 'My Games', 'FarmingSimulator2022', 'mods'),
    schedule1: path.join(os.homedir(), 'Documents', 'Schedule 1', 'mods')
  };
  
  const targetDir = gameModPaths[data.gameId];
  if (!targetDir) {
    return { success: false, message: 'Invalid game ID' };
  }
  
  try {
    // Ensure target directory exists
    await fs.ensureDir(targetDir);
    
    let addedCount = 0;
    let skippedCount = 0;
    const results = [];
    
    for (const fileInfo of data.files) {
      const sourcePath = fileInfo.path;
      const targetPath = path.join(targetDir, fileInfo.name);
      
      // Check if source file exists (for real files)
      if (sourcePath && await fs.pathExists(sourcePath)) {
        // Check if target already exists
        if (await fs.pathExists(targetPath)) {
          console.log(`   ⚠️ Skipping existing file: ${fileInfo.name}`);
          skippedCount++;
          results.push({ name: fileInfo.name, status: 'skipped', reason: 'File already exists' });
          continue;
        }
        
        // Copy the file
        await fs.copy(sourcePath, targetPath);
        console.log(`   ✅ Added: ${fileInfo.name}`);
        addedCount++;
        results.push({ name: fileInfo.name, status: 'added' });
        
      } else {
        console.log(`   ❌ Source file not found: ${fileInfo.name}`);
        results.push({ name: fileInfo.name, status: 'failed', reason: 'Source file not found' });
      }
    }
    
    console.log(`✅ Add mods complete: ${addedCount} added, ${skippedCount} skipped`);
    
    return {
      success: true,
      message: `Added ${addedCount} mods${skippedCount > 0 ? `, ${skippedCount} skipped` : ''}`,
      added: addedCount,
      skipped: skippedCount,
      results: results
    };
    
  } catch (error) {
    console.error(`❌ Error adding mods:`, error);
    return {
      success: false,
      message: `Failed to add mods: ${error.message}`
    };
  }
});

// Utility functions
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Error handling
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('✅ Main process setup complete'); 