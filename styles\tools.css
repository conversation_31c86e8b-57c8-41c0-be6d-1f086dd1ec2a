/* ===================================
   Tools and System Interface
   =================================== */

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.tool-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
    position: relative;
}

.tool-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.tool-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.tool-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tool-card p {
    color: var(--text-secondary);
}

.coming-soon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--warning-color);
    color: var(--background-dark);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* System Tools Interface */
.tools-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Main Tools Grid Layout */
.main-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.tool-panel {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.tool-panel.compact {
    padding: 1rem;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.tool-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.tool-status {
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-status.active {
    background: var(--success-color);
    color: var(--background-dark);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.tool-status.inactive {
    background: var(--border-color);
    color: var(--text-secondary);
}

.tool-config {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tool-config.compact {
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.config-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.config-row {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.config-row.inline {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
}

.config-row label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    white-space: nowrap;
}

.config-row select,
.config-row input[type="text"] {
    padding: 0.5rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition-fast);
    min-width: 0;
    flex: 1;
}

.config-row.inline select {
    max-width: 80px;
    flex: 0 0 auto;
}

.config-row select:focus,
.config-row input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.config-row input[type="range"] {
    width: 100%;
    height: 4px;
    background: var(--background-hover);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    margin-top: 0.25rem;
}

.config-row input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
    transition: var(--transition-fast);
}

.config-row input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.config-row input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Cleanup Tab Styles */
.cleanup-container {
    display: block;
}

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.option-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Mod List Enhancements */
.mod-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.mod-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.mod-header-actions {
    display: flex;
    gap: 0.5rem;
}

.mod-category-header {
    margin: 1.5rem 0 1rem 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.mod-category-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.mod-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition-smooth);
}

.mod-item:hover {
    border-color: var(--primary-color);
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.1);
}

.mod-info {
    flex: 1;
    min-width: 0;
}

.mod-info h4 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mod-info p {
    margin: 0 0 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.mod-path {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-family: monospace;
    word-break: break-all;
    display: block;
    margin-top: 0.25rem;
}

.mod-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    margin-left: 1rem;
}

.mod-list-content {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}
