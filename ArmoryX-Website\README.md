# Armory X Website

Professional website for Armory X - Ultimate PC Tool Suite

## 🌟 Features

- **Modern Design**: Dark theme with smooth animations
- **Responsive**: Works perfectly on desktop, tablet, and mobile
- **SEO Optimized**: Meta tags, sitemap, and structured data
- **PWA Ready**: Installable on mobile devices
- **Analytics Ready**: Google Analytics integration
- **Social Media Ready**: Open Graph and Twitter Card meta tags

## 📁 File Structure

```
ArmoryX-Website/
├── index.html              # Main website file
├── styles.css              # All styling and responsive design
├── script.js               # Interactive functionality
├── robots.txt              # Search engine crawler instructions
├── sitemap.xml             # SEO sitemap
├── site.webmanifest        # PWA manifest
├── favicon.ico             # Website icon (placeholder)
└── README.md               # This file
```

## 🚀 Quick Deployment

### Option 1: Netlify (Recommended)

1. **Sign up at [Netlify](https://netlify.com)**
2. **Connect your repository or drag & drop the folder**
3. **Custom domain setup:**
   - Go to Domain Settings
   - Add custom domain: `software.armoryx.com`
   - Follow DNS configuration instructions

### Option 2: GitHub Pages

1. **Create a GitHub repository**
2. **Upload all files to the repository**
3. **Enable GitHub Pages in repository settings**
4. **Configure custom domain in settings**

### Option 3: Vercel

1. **Sign up at [Vercel](https://vercel.com)**
2. **Import your repository or upload folder**
3. **Configure custom domain in project settings**

## 🔧 Custom Domain Setup

### For `armoryx.software`:

1. **Buy domain `armoryx.software` from a registrar like:**
   - Namecheap (recommended)
   - GoDaddy
   - Cloudflare

2. **Configure DNS records:**
   ```
   Type: CNAME
   Name: software
   Value: [your-hosting-provider-url]
   ```

3. **Alternative: Use Cloudflare for free SSL:**
   - Add your domain to Cloudflare
   - Use Cloudflare Pages for hosting
   - Automatic SSL and CDN

## 📝 Configuration Needed

### Before Going Live:

1. **Replace placeholder content:**
   - [ ] Add real Discord invite link (currently: `https://discord.gg/armoryx`)
   - [ ] Add actual download URLs in `script.js`
   - [ ] Replace Google Analytics ID
   - [ ] Add real favicon files

2. **Create favicon files:**
   - Use [Favicon.io](https://favicon.io) to generate from your logo
   - Replace placeholder favicon.ico
   - Add all icon sizes (16x16, 32x32, 180x180, etc.)

3. **Update social media preview:**
   - Create preview image: `assets/armory-x-preview.png` (1200x630px)
   - Update paths in meta tags

4. **Configure analytics:**
   - Get Google Analytics tracking ID
   - Uncomment and update the analytics code in `index.html`

## 🌐 Free Hosting Options

| Provider | Custom Domain | SSL | Bandwidth | Best For |
|----------|---------------|-----|-----------|----------|
| **Netlify** | ✅ Yes | ✅ Free | 100GB/month | **Recommended** |
| **Vercel** | ✅ Yes | ✅ Free | 100GB/month | Great performance |
| **GitHub Pages** | ✅ Yes | ✅ Free | 1GB storage | Simple setup |
| **Cloudflare Pages** | ✅ Yes | ✅ Free | Unlimited | Best speed |

## ⚡ Performance Features

- **Optimized images**: Compressed and properly sized
- **Efficient CSS**: Minimal and modern
- **Fast loading**: External resources properly loaded
- **Caching ready**: All static assets cacheable

## 🔄 Update Process

1. **Update version information** in `script.js`
2. **Add new updates** to the timeline in `script.js`
3. **Update planned features** as they're completed
4. **Modify download links** when new versions are available

## 📱 Mobile Optimization

- Responsive design works on all screen sizes
- Touch-friendly navigation
- Installable as PWA on mobile devices
- Optimized performance on slower connections

## 🛡️ Security Features

- No server-side code (static site = more secure)
- HTTPS enforced by hosting providers
- No sensitive data stored
- Content Security Policy ready

## 📞 Support

For website issues or customization requests, contact through Discord or create an issue in the repository.

---

**Ready to deploy!** Choose your hosting provider and follow the setup instructions above. 