// Epic Installer Script for Armory X
// We will call <PERSON><PERSON> backend functions via window.__TAURI__.invoke directly when available.

let currentTheme = 'blue';
let installationCancelled = false;
let installationOptions = {
  path: 'C:\\Program Files\\Armory X',
  desktopShortcut: true,
  startMenu: true,
  autoStart: false,
  fileAssociations: false
};

// Theme configurations
const themes = {
  blue: { primary: '#00b4ff', secondary: '#0099e6', accent: '#00ff88' },
  green: { primary: '#00ff88', secondary: '#00cc66', accent: '#00b4ff' },
  purple: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#00ff88' },
  red: { primary: '#ff4444', secondary: '#cc0000', accent: '#00ff88' }
};

// Installation phases with realistic timing
const installPhases = [
  { message: "Initializing installer...", duration: 800 },
  { message: "Checking system requirements...", duration: 1200 },
  { message: "Creating installation directory...", duration: 600 },
  { message: "Extracting core files...", duration: 2500 },
  { message: "Installing system utilities...", duration: 1800 },
  { message: "Setting up mod manager...", duration: 1400 },
  { message: "Configuring desktop widget...", duration: 1000 },
  { message: "Installing system tools...", duration: 1600 },
  { message: "Creating shortcuts...", duration: 800 },
  { message: "Registering file associations...", duration: 700 },
  { message: "Finalizing installation...", duration: 1000 },
  { message: "Installation complete!", duration: 500 }
];

// Initialize installer
document.addEventListener('DOMContentLoaded', () => {
  console.log('🚀 Armory X Installer initialized');
  updateInstallPath();
  
  // Add some startup flair
  setTimeout(() => {
    playStartupAnimation();
  }, 500);

  document.getElementById('close-button').addEventListener('click', () => {
    window.__TAURI__.invoke('close_window').catch(console.error);
  });
});

// Startup animation
function playStartupAnimation() {
  const logo = document.querySelector('.logo');
  if (logo) {
    logo.style.transform = 'scale(0.8)';
    logo.style.opacity = '0.5';
    setTimeout(() => {
      logo.style.transform = 'scale(1)';
      logo.style.opacity = '1';
      logo.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
    }, 100);
  }
}

// Screen management
function showScreen(screenId) {
  // Hide all screens
  document.querySelectorAll('.screen').forEach(screen => {
    screen.classList.remove('active');
  });
  
  // Show target screen with animation
  const targetScreen = document.getElementById(`${screenId}-screen`);
  if (targetScreen) {
    setTimeout(() => {
      targetScreen.classList.add('active');
    }, 150);
  }
  
  console.log(`📱 Switched to ${screenId} screen`);
}

// Theme switching with epic transitions
function setTheme(theme) {
  currentTheme = theme;
  const themeConfig = themes[theme];
  
  // Update CSS variables
  const root = document.documentElement;
  root.style.setProperty('--primary', themeConfig.primary);
  root.style.setProperty('--secondary', themeConfig.secondary);
  root.style.setProperty('--accent', themeConfig.accent);
  
  // Update theme selector buttons
  document.querySelectorAll('.theme-btn').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector(`.theme-btn.${theme}`)?.classList.add('active');
  
  // Add theme change effect
  createThemeChangeEffect();
  
  console.log(`🎨 Theme changed to ${theme}`);
}

// Epic theme change effect
function createThemeChangeEffect() {
  const effect = document.createElement('div');
  effect.style.cssText = `
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: radial-gradient(circle at center, var(--primary)22, transparent 70%);
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    animation: themeWave 1s ease-out;
  `;
  
  document.body.appendChild(effect);
  
  setTimeout(() => {
    document.body.removeChild(effect);
  }, 1000);
}

// Add CSS animation for theme effect
const style = document.createElement('style');
style.textContent = `
  @keyframes themeWave {
    0% { opacity: 0; transform: scale(0); }
    50% { opacity: 0.3; transform: scale(1.5); }
    100% { opacity: 0; transform: scale(3); }
  }
`;
document.head.appendChild(style);

// Installation options handling
function updateInstallPath() {
  const pathInput = document.getElementById('install-path');
  if (pathInput) {
    pathInput.addEventListener('input', (e) => {
      installationOptions.path = e.target.value;
    });
  }
}

// Path browser (simplified - in production this would use Tauri's dialog plugin)
async function browsePath() {
  try {
    // Tauri file dialog would go here
    // For now, show a realistic path selection
    const newPath = prompt('Enter installation path:', installationOptions.path);
    if (newPath && newPath.trim()) {
      installationOptions.path = newPath.trim();
      document.getElementById('install-path').value = installationOptions.path;
      console.log(`📁 Installation path updated: ${installationOptions.path}`);
    }
  } catch (error) {
    console.error('Path selection error:', error);
  }
}

// Start installation process
async function startInstall() {
  // Gather installation options
  installationOptions.desktopShortcut = document.getElementById('desktop-shortcut')?.checked || false;
  installationOptions.startMenu = document.getElementById('start-menu')?.checked || false;
  installationOptions.autoStart = document.getElementById('auto-start')?.checked || false;
  installationOptions.fileAssociations = document.getElementById('file-associations')?.checked || false;
  
  console.log('⚙️ Installation options:', installationOptions);
  
  // Switch to progress screen
  showScreen('progress');
  installationCancelled = false;
  
  // Start epic installation process
  await runInstallationProcess();
}

// Epic installation process with realistic progress
async function runInstallationProcess() {
  const progressFill = document.getElementById('progress-fill');
  const statusText = document.getElementById('status-text');
  let totalProgress = 0;
  
  try {
    for (let i = 0; i < installPhases.length; i++) {
      if (installationCancelled) {
        statusText.textContent = 'Installation cancelled';
        return;
      }
      
      const phase = installPhases[i];
      statusText.textContent = phase.message;
      
      // Animate progress
      const targetProgress = ((i + 1) / installPhases.length) * 100;
      await animateProgress(progressFill, totalProgress, targetProgress, phase.duration);
      totalProgress = targetProgress;
      
      // Add some random variation for realism
      if (i < installPhases.length - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100));
      }
    }
    
    // Call actual installation
    if (!installationCancelled) {
      await performActualInstallation();
      showSuccessScreen();
    }
    
  } catch (error) {
    console.error('Installation failed:', error);
    statusText.textContent = 'Installation failed: ' + error.message;
    document.getElementById('cancel-btn').textContent = 'Close';
  }
}

// Smooth progress animation
function animateProgress(element, startWidth, endWidth, duration) {
  return new Promise(resolve => {
    const startTime = Date.now();
    const widthDiff = endWidth - startWidth;
    
    function updateProgress() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const easedProgress = 1 - Math.pow(1 - progress, 3);
      const currentWidth = startWidth + (widthDiff * easedProgress);
      
      element.style.width = currentWidth + '%';
      
      if (progress < 1) {
        requestAnimationFrame(updateProgress);
      } else {
        resolve();
      }
    }
    
    updateProgress();
  });
}

// Actual installation via Tauri backend
async function performActualInstallation() {
  try {
    if (window.__TAURI__?.invoke) {
      await window.__TAURI__.invoke('start_install', { 
        targetDir: installationOptions.path,
        options: installationOptions 
      });
      console.log('✅ Backend installation completed');
    } else {
      console.log('🔧 Demo mode - no backend available');
      // Simulate installation delay
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.error('Backend installation error:', error);
    throw new Error('Failed to install application files');
  }
}

// Show success screen with celebration
function showSuccessScreen() {
  showScreen('success');
  
  // Add celebration effect
  setTimeout(() => {
    createCelebrationEffect();
  }, 300);
  
  console.log('🎉 Installation completed successfully!');
}

// Epic celebration effect
function createCelebrationEffect() {
  for (let i = 0; i < 20; i++) {
    setTimeout(() => {
      createConfettiParticle();
    }, i * 100);
  }
}

function createConfettiParticle() {
  const colors = ['#00b4ff', '#00ff88', '#ff4444', '#8b5cf6'];
  const particle = document.createElement('div');
  
  particle.style.cssText = `
    position: fixed;
    width: 8px;
    height: 8px;
    background: ${colors[Math.floor(Math.random() * colors.length)]};
    border-radius: 50%;
    pointer-events: none;
    z-index: 1000;
    left: ${Math.random() * window.innerWidth}px;
    top: -10px;
    animation: confettiFall ${Math.random() * 2 + 2}s ease-out forwards;
  `;
  
  document.body.appendChild(particle);
  
  setTimeout(() => {
    document.body.removeChild(particle);
  }, 4000);
}

// Add confetti animation
const confettiStyle = document.createElement('style');
confettiStyle.textContent = `
  @keyframes confettiFall {
    to {
      transform: translateY(${window.innerHeight + 100}px) rotate(720deg);
      opacity: 0;
    }
  }
`;
document.head.appendChild(confettiStyle);

// =============================
//  Custom Confirmation Modal
// =============================

function showConfirmationModal(title, message, onConfirm) {
  // Prevent multiple modals
  if (document.querySelector('.ax-modal-overlay')) return;

  console.log('🔍 Creating confirmation modal:', { title, message });

  const overlay = document.createElement('div');
  overlay.className = 'ax-modal-overlay';
  overlay.innerHTML = `
    <div class="ax-modal-card">
      <h3 class="ax-modal-title">${title}</h3>
      <p class="ax-modal-message">${message}</p>
      <div class="ax-modal-buttons">
        <button class="btn btn-secondary ax-cancel">No</button>
        <button class="btn btn-primary ax-confirm">Yes</button>
      </div>
    </div>`;

  // Click-outside to close
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) dismiss();
  });

  // Button actions
  overlay.querySelector('.ax-cancel').addEventListener('click', dismiss);
  overlay.querySelector('.ax-confirm').addEventListener('click', () => {
    console.log('✅ User confirmed modal - executing callback');
    dismiss();
    onConfirm?.();
  });

  // Escape key
  const escHandler = (e) => {
    if (e.key === 'Escape') dismiss();
  };
  document.addEventListener('keydown', escHandler);

  function dismiss() {
    overlay.classList.add('closing');
    overlay.addEventListener('animationend', () => {
      overlay.remove();
      document.removeEventListener('keydown', escHandler);
    }, { once: true });
  }

  document.body.appendChild(overlay);
}

// Inject modal styles
const modalCss = document.createElement('style');
modalCss.textContent = `
  .ax-modal-overlay {
    position: fixed; inset: 0; display: flex; align-items: center; justify-content: center;
    background: rgba(0,0,0,0.6); z-index: 2000; animation: axFadeIn 0.25s ease;
  }
  .ax-modal-overlay.closing { animation: axFadeOut 0.25s ease forwards; }
  .ax-modal-card {
    background: var(--card-bg); padding: 30px 40px; border-radius: 16px; text-align: center;
    box-shadow: 0 8px 20px rgba(0,0,0,0.3); max-width: 400px; width: 100%;
    animation: axPopIn 0.35s cubic-bezier(0.175,0.885,0.32,1.275);
  }
  .ax-modal-title { font-size: 1.4rem; margin-bottom: 10px; }
  .ax-modal-message { color: rgba(255,255,255,0.8); margin-bottom: 25px; }
  .ax-modal-buttons { display: flex; gap: 15px; justify-content: center; }

  @keyframes axFadeIn { from { opacity:0; } to { opacity:1; } }
  @keyframes axFadeOut { to { opacity:0; } }
  @keyframes axPopIn { 0% { transform: scale(0.9); opacity:0; } 100% { transform: scale(1); opacity:1; } }
`;
document.head.appendChild(modalCss);

// =============================================
//  Cancel button – ask confirmation then exit
// =============================================

function cancelInstall() {
  console.log('❌ Cancel pressed – requesting confirmation');

  showConfirmationModal(
    'Cancel Installation',
    'The installation is currently in progress. Are you sure you want to cancel and exit?',
    () => {
      installationCancelled = true;
      exitInstaller();
    }
  );
}

// Launch the installed application
async function launchApp() {
  try {
    console.log('🚀 Launching Armory X...');
    
    if (window.__TAURI__?.shell) {
      await window.__TAURI__.shell.open(installationOptions.path + '\\Armory X.exe');
    } else {
      // Fallback for demo mode
      console.log('🔧 Demo mode - would launch:', installationOptions.path);
    }
    
    // Close installer after launching
    setTimeout(() => {
      exitInstaller();
    }, 2000);
    
  } catch (error) {
    console.error('Failed to launch application:', error);
    alert('Installation completed, but failed to launch the application automatically.\nYou can find Armory X in: ' + installationOptions.path);
  }
}

// Inject exit animation CSS
const exitCss = document.createElement('style');
exitCss.textContent = `
  @keyframes exitShrinkFade {
    0%   { transform: scale(1);   opacity: 1; }
    100% { transform: scale(0.85); opacity: 0; }
  }
  .installer-card.exiting {
    animation: exitShrinkFade 0.45s ease-out forwards;
  }
`;
document.head.appendChild(exitCss);

// Exit installer with confirmation & slick animation
function exitInstaller(_internalForce) {
  // If not forced, show confirmation first
  if (!_internalForce) {
    const currentScreen = document.querySelector('.screen.active')?.id;

    const msg = currentScreen === 'progress-screen' && !installationCancelled
      ? 'Installation is in progress. Are you sure you want to cancel and exit?'
      : 'Are you sure you want to exit the Armory X installer?';

    showConfirmationModal('Exit Installer', msg, () => {
      if (currentScreen === 'progress-screen') installationCancelled = true;
      exitInstaller(true); // call again with force flag
    });
    return;
  }

  // === Forced exit - skip animation for now ===
  console.log('👋 Exiting Armory X Installer');
  closeInstallerWindow();
}

// Attach top-right exit button handler (not present in HTML attributes)
document.addEventListener('DOMContentLoaded', () => {
  document.querySelector('.exit-btn')?.addEventListener('click', () => exitInstaller());
});

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
  switch(e.key) {
    case 'Escape':
      if (document.querySelector('.screen.active')?.id !== 'progress-screen') {
        exitInstaller();
      }
      break;
    case 'Enter':
      const activeScreen = document.querySelector('.screen.active')?.id;
      if (activeScreen === 'welcome-screen') {
        showScreen('options');
      } else if (activeScreen === 'options-screen') {
        startInstall();
      }
      break;
  }
});

// Add some extra polish
window.addEventListener('load', () => {
  // Smooth entrance animation for the entire installer
  document.querySelector('.installer-card').style.animation = 'slideIn 0.8s ease-out';
  
  console.log('✨ Armory X Installer ready!');
});

// Helper to close the Tauri / browser window
function closeInstallerWindow() {
  if (window.__TAURI__?.plugin?.process?.exit) {
    // Force kill the process using the plugin
    window.__TAURI__.plugin.process.exit(0);
  } else if (window.__TAURI__?.invoke) {
    window.__TAURI__.invoke('close_window');
  } else {
    window.close();
  }
}

function tryBrowserClose() {
  // This function is no longer needed with the simplified approach,
  // but we'll keep it to avoid breaking any other part of the code that might call it.
  console.log("Attempting browser close as a fallback.");
  try {
    window.close();
  } catch (e) {
    console.error("window.close() failed:", e);
  }
} 