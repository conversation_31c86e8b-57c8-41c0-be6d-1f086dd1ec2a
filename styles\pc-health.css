/* PC Health Module Styles */

/* === PC HEALTH MAIN VIEW === */
.pc-health-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

/* === PC HEALTH TOOL VIEWS === */
.pc-health-tool-view {
    display: none;
    flex-direction: column;
    height: 100%;
    position: relative;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pc-health-tool-view.active {
    display: flex;
    opacity: 1;
}

/* === PC HEALTH MAIN STYLES === */
.health-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.health-action-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.health-action-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 180, 255, 0.2);
}

.health-action-card.active {
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.15), rgba(0, 180, 255, 0.05));
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.health-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.health-action-card:hover::before {
    opacity: 1;
}

.card-icon {
    font-size: 3rem;
    flex-shrink: 0;
    z-index: 1;
}

.card-content {
    flex: 1;
    z-index: 1;
}

.card-content h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
}

.card-content p {
    margin: 0 0 0.75rem 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.4;
}

.card-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-value {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.card-arrow {
    position: absolute;
    right: 2rem;
    font-size: 1.5rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    z-index: 1;
}

.health-action-card:hover .card-arrow {
    transform: translateX(4px);
    color: var(--primary-color);
}

.health-action-card.active .card-arrow {
    transform: rotate(90deg);
    color: var(--primary-color);
}

/* === TOOL HEADER WITH BACK BUTTON === */
.tool-header {
    padding: 1.5rem 2rem;
    background: var(--background-card);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.tool-header .back-btn {
    background: linear-gradient(135deg, #64748b, #475569);
    color: #f1f5f9;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tool-header .back-btn:hover {
    background: linear-gradient(135deg, #475569, #334155);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* === PREMIUM FEATURE NOTICE === */
.premium-feature-notice {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    height: 100%;
}

.premium-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.3));
}

.premium-feature-notice h4 {
    color: #ffd700;
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 600;
}

.premium-feature-notice p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 500px;
}

.tool-body {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--background-dark);
}

.tool-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* === JUNK CLEANER STYLES === */
.status-card {
    background: var(--bg-secondary, #1a1a1a);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h4 {
    margin: 0;
    color: var(--text-primary);
}

.status-indicator {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.progress-section {
    margin-top: 1rem;
}

.progress-bar {
    height: 8px;
    background: var(--bg-tertiary, #2a2a2a);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.cleanup-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 0.75rem;
    margin: 1.5rem 0;
}

.cleanup-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cleanup-option:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.cleanup-option input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 1rem;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.option-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.option-icon {
    font-size: 1.5rem;
}

.option-text {
    display: flex;
    flex-direction: column;
}

.option-text strong {
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.option-text small {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

/* === BENCHMARK STYLES === */
.benchmark-section {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.benchmark-hero {
    text-align: center;
    padding: 2rem;
    max-width: 600px;
}

.hero-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

.benchmark-hero h4 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.benchmark-hero p {
    margin: 0 0 2rem 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Benchmark Progress */
.benchmark-progress-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    min-width: 500px;
}

.benchmark-progress-card h4 {
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
}

.benchmark-progress-visual {
    position: relative;
    margin-bottom: 2rem;
}

.benchmark-progress-bar {
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.benchmark-progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-animation {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.pulse-circle {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulseScale 1.5s ease-in-out infinite;
}

.pulse-circle:nth-child(2) {
    animation-delay: 0.3s;
}

.pulse-circle:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes pulseScale {
    0%, 100% {
        transform: scale(0.5);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.benchmark-status {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.test-indicators {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.test-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.3;
    transition: all 0.3s ease;
}

.test-indicator.active {
    opacity: 1;
    transform: scale(1.1);
}

.test-indicator.complete {
    opacity: 1;
}

.test-indicator.complete .test-icon {
    color: var(--success-color);
}

.test-icon {
    font-size: 2rem;
}

.test-name {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Benchmark Results */
.overall-score-card {
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    margin-bottom: 2rem;
}

.score-circle {
    width: 150px;
    height: 150px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 1.5rem auto;
    box-shadow: 0 8px 32px rgba(0, 180, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.score-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.score-value {
    font-size: 3rem;
    font-weight: bold;
    color: white;
    z-index: 1;
}

.score-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    z-index: 1;
}

.score-rating {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.score-description {
    color: var(--text-secondary);
    max-width: 400px;
    margin: 0 auto;
}

.benchmark-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.result-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.result-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.result-card h5 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.result-details {
    text-align: left;
}

.result-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.result-item strong {
    color: var(--primary-color);
    font-weight: 600;
}

.result-item small {
    display: block;
    color: var(--text-muted, #a0a0a0);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    line-height: 1.3;
}

.benchmark-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    .health-actions-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .benchmark-results-grid {
        grid-template-columns: 1fr;
    }
    
    .card-arrow {
        display: none;
    }
    
    .health-action-card {
        flex-direction: column;
        text-align: center;
    }
    
    .pc-health-tool-section {
        height: 60%;
    }
    
    .test-indicators {
        gap: 1rem;
    }
    
    .benchmark-progress-card {
        min-width: auto;
        width: 100%;
    }
} 