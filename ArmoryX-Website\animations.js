/* ========================================
   ARMORY X - ADVANCED ANIMATIONS & EFFECTS JS
   ======================================== */

class ArmoryXAnimations {
    constructor() {
        this.shootingStars = [];
        this.particles = [];
        this.init();
    }

    init() {
        this.createStarfield();
        this.createShootingStars();
        this.createFloatingParticles();
        this.initScrollReveal();
        this.enhanceInteractivity();
        this.startAnimationLoop();
    }

    createStarfield() {
        // Create the main starfield background
        const starfield = document.createElement('div');
        starfield.className = 'starfield';
        document.body.appendChild(starfield);
    }

    createShootingStars() {
        // Create shooting stars at random intervals
        const createShootingStar = () => {
            const shootingStar = document.createElement('div');
            shootingStar.className = 'shooting-star';
            
            // Random starting position (top of screen)
            const startX = Math.random() * window.innerWidth;
            const startY = Math.random() * (window.innerHeight * 0.3);
            
            shootingStar.style.left = startX + 'px';
            shootingStar.style.top = startY + 'px';
            
            // Random delay before starting
            const delay = Math.random() * 2;
            shootingStar.style.animationDelay = delay + 's';
            
            document.body.appendChild(shootingStar);
            
            // Remove after animation completes
            setTimeout(() => {
                if (shootingStar.parentNode) {
                    shootingStar.parentNode.removeChild(shootingStar);
                }
            }, 3000 + (delay * 1000));
        };

        // Create shooting stars periodically
        const createStarsInterval = () => {
            createShootingStar();
            
            // Random interval between 3-8 seconds
            const nextInterval = (Math.random() * 5 + 3) * 1000;
            setTimeout(createStarsInterval, nextInterval);
        };

        // Start after initial delay
        setTimeout(createStarsInterval, 2000);
    }

    createFloatingParticles() {
        const particleCount = window.innerWidth > 768 ? 15 : 8;
        
        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createParticle();
            }, i * 200);
        }
    }

    createParticle() {
        const particle = document.createElement('div');
        const types = ['particle-1', 'particle-2', 'particle-3'];
        const type = types[Math.floor(Math.random() * types.length)];
        
        particle.className = `particle ${type}`;
        
        // Random starting position
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.top = Math.random() * window.innerHeight + 'px';
        
        // Random animation delay
        particle.style.animationDelay = Math.random() * 3 + 's';
        
        document.body.appendChild(particle);
        
        // Move particle randomly every few seconds
        const moveParticle = () => {
            const newX = Math.random() * window.innerWidth;
            const newY = Math.random() * window.innerHeight;
            
            particle.style.transition = 'all 8s cubic-bezier(0.4, 0, 0.2, 1)';
            particle.style.left = newX + 'px';
            particle.style.top = newY + 'px';
            
            setTimeout(moveParticle, 8000);
        };
        
        setTimeout(moveParticle, Math.random() * 5000);
    }

    initScrollReveal() {
        const revealElements = document.querySelectorAll('.reveal-on-scroll');
        
        const revealObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        revealElements.forEach(el => {
            revealObserver.observe(el);
        });
        
        // Auto-add reveal class to common elements
        const autoRevealSelectors = [
            '.feature-card',
            '.forum-category',
            '.post-item',
            '.user-card'
        ];
        
        autoRevealSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                if (!el.classList.contains('reveal-on-scroll')) {
                    el.classList.add('reveal-on-scroll');
                    revealObserver.observe(el);
                }
            });
        });
    }

    enhanceInteractivity() {
        // Add glow effects to important buttons
        document.querySelectorAll('.btn-primary, .new-post-btn').forEach(btn => {
            btn.classList.add('glow-effect');
        });

        // Add pulse effect to important elements
        document.querySelectorAll('.live-indicator, .status-indicator.online').forEach(el => {
            el.classList.add('pulse-glow');
        });

        // Enhanced hover effects for cards
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.forum-category, .user-card, .feature-card, .post-item')) {
                this.createHoverParticles(e.target.closest('.forum-category, .user-card, .feature-card, .post-item'));
            }
        });

        // Animate gradient text
        document.querySelectorAll('.gradient-text').forEach(el => {
            el.classList.add('gradient-text-animated');
        });
    }

    createHoverParticles(element) {
        const rect = element.getBoundingClientRect();
        const particleCount = 3;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = 'rgba(45, 114, 255, 0.6)';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '1000';
            
            const startX = rect.left + Math.random() * rect.width;
            const startY = rect.top + Math.random() * rect.height;
            
            particle.style.left = startX + 'px';
            particle.style.top = startY + 'px';
            
            document.body.appendChild(particle);
            
            // Animate particle
            const endX = startX + (Math.random() - 0.5) * 100;
            const endY = startY - Math.random() * 50;
            
            particle.animate([
                {
                    transform: 'translate(0, 0) scale(1)',
                    opacity: 1
                },
                {
                    transform: `translate(${endX - startX}px, ${endY - startY}px) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: 1000,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }).onfinish = () => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            };
        }
    }

    startAnimationLoop() {
        // Performance monitoring and cleanup
        let lastTime = 0;
        const maxParticles = window.innerWidth > 768 ? 20 : 10;
        
        const animate = (currentTime) => {
            // Throttle to ~60fps
            if (currentTime - lastTime >= 16) {
                // Clean up old particles
                const particles = document.querySelectorAll('.particle');
                if (particles.length > maxParticles) {
                    particles[0].remove();
                }
                
                // Clean up old shooting stars
                const shootingStars = document.querySelectorAll('.shooting-star');
                if (shootingStars.length > 5) {
                    shootingStars[0].remove();
                }
                
                lastTime = currentTime;
            }
            
            requestAnimationFrame(animate);
        };
        
        requestAnimationFrame(animate);
    }

    // Public methods for controlling animations
    pauseAnimations() {
        document.body.style.animationPlayState = 'paused';
        document.querySelectorAll('*').forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        document.body.style.animationPlayState = 'running';
        document.querySelectorAll('*').forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }

    toggleAnimations() {
        const isPaused = document.body.style.animationPlayState === 'paused';
        if (isPaused) {
            this.resumeAnimations();
        } else {
            this.pauseAnimations();
        }
    }
}

// Additional utility functions
function addSparkleEffect(element) {
    const sparkle = document.createElement('div');
    sparkle.innerHTML = '✨';
    sparkle.style.position = 'absolute';
    sparkle.style.pointerEvents = 'none';
    sparkle.style.zIndex = '1000';
    sparkle.style.fontSize = '20px';
    
    const rect = element.getBoundingClientRect();
    sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
    sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';
    
    document.body.appendChild(sparkle);
    
    sparkle.animate([
        { transform: 'scale(0) rotate(0deg)', opacity: 1 },
        { transform: 'scale(1.5) rotate(180deg)', opacity: 0 }
    ], {
        duration: 1000,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }).onfinish = () => {
        if (sparkle.parentNode) {
            sparkle.parentNode.removeChild(sparkle);
        }
    };
}

function addSuccessRipple(element) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.width = '4px';
    ripple.style.height = '4px';
    ripple.style.background = 'rgba(16, 185, 129, 0.6)';
    ripple.style.borderRadius = '50%';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '1000';
    
    const rect = element.getBoundingClientRect();
    ripple.style.left = (rect.left + rect.width / 2) + 'px';
    ripple.style.top = (rect.top + rect.height / 2) + 'px';
    
    document.body.appendChild(ripple);
    
    ripple.animate([
        { transform: 'scale(0)', opacity: 1 },
        { transform: 'scale(20)', opacity: 0 }
    ], {
        duration: 600,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }).onfinish = () => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    };
}

// Initialize animations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (!prefersReducedMotion) {
        window.armoryXAnimations = new ArmoryXAnimations();
        
        // Add success effects to forum interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-button.liked')) {
                addSparkleEffect(e.target.closest('.like-button'));
            }
            
            if (e.target.closest('.btn-primary')) {
                addSuccessRipple(e.target.closest('.btn-primary'));
            }
        });
        
        // Console commands for debugging
        window.toggleAnimations = () => window.armoryXAnimations.toggleAnimations();
        window.pauseAnimations = () => window.armoryXAnimations.pauseAnimations();
        window.resumeAnimations = () => window.armoryXAnimations.resumeAnimations();
        
        console.log('🎨 Armory X Animations loaded! Try these commands:');
        console.log('- toggleAnimations() - Toggle all animations');
        console.log('- pauseAnimations() - Pause all animations');
        console.log('- resumeAnimations() - Resume all animations');
    } else {
        console.log('⚡ Reduced motion detected - animations disabled for accessibility');
    }
});

// Handle visibility change to pause animations when tab is not active
document.addEventListener('visibilitychange', () => {
    if (window.armoryXAnimations) {
        if (document.hidden) {
            window.armoryXAnimations.pauseAnimations();
        } else {
            window.armoryXAnimations.resumeAnimations();
        }
    }
}); 