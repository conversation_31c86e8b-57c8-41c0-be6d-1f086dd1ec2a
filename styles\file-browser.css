/* ===================================
   File Browser System
   =================================== */

/* File Browser Container */
.file-browser-container {
    display: flex;
    flex-direction: column;
    height: 70vh;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.file-browser-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--background-hover);
    border-bottom: 1px solid var(--border-color);
}

.file-browser-title {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.file-browser-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.file-browser-layout {
    display: flex;
    flex: 1;
    min-height: 0;
}

.file-sidebar {
    width: 220px;
    background: var(--background-hover);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section h5 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.25rem;
}

.sidebar-item:hover {
    background: rgba(0, 180, 255, 0.1);
    color: var(--primary-color);
}

.sidebar-item.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-icon {
    font-size: 1rem;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.file-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.file-path-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
    overflow-x: auto;
}

.path-segment {
    color: var(--text-primary);
    cursor: pointer;
    transition: color 0.3s ease;
}

.path-segment:hover {
    color: var(--primary-color);
}

.path-separator {
    color: var(--text-muted);
    margin: 0 0.25rem;
}

.file-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.25rem;
}

.file-item:hover {
    background: var(--background-hover);
}

.file-item.selected {
    background: rgba(0, 180, 255, 0.1);
    border: 1px solid rgba(0, 180, 255, 0.3);
}

.file-icon {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.file-icon.folder {
    color: #fbbf24;
}

.file-icon.file {
    color: var(--text-secondary);
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-details {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.file-size,
.file-date {
    white-space: nowrap;
}

.file-browser-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--background-hover);
    border-top: 1px solid var(--border-color);
}

.file-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.file-actions {
    display: flex;
    gap: 0.75rem;
}

.file-action-btn {
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-action-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.file-action-btn:disabled {
    background: var(--background-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

/* File Browser Download Integration */
.file-browser-download {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 6px;
    padding: 0.5rem;
    margin: 0.5rem 0;
}

.file-browser-download-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.file-browser-download-icon {
    color: var(--primary-color);
    font-size: 1rem;
}

.file-browser-download-text {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.file-browser-download-path {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-family: monospace;
    word-break: break-all;
}

/* Directory Information Styling */
.directory-info-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.directory-info-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.directory-info-item h4 {
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.directory-details {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.directory-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.directory-detail:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
}

/* Mod Folder Items */
.mod-folder-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mod-folder-item .modal-input {
    margin: 0;
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .file-browser-container {
        height: 60vh;
    }
    
    .file-browser-layout {
        flex-direction: column;
    }
    
    .file-sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .file-browser-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .file-browser-controls {
        width: 100%;
        justify-content: flex-end;
    }
    
    .file-details {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .file-browser-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .file-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .mod-folder-item {
        padding: 0.75rem;
        gap: 0.5rem;
        flex-direction: column;
        align-items: stretch;
    }
}
