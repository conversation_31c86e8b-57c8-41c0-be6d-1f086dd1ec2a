; Armory X Modern Installer
; Custom UI similar to Epic Games/Battle.net

!include "MUI2.nsh"
!include "nsDialogs.nsh"

; --------------------------------
; General Configuration

Name "Armory X"
OutFile "ArmoryX-Setup-Modern.exe"
InstallDir "$LOCALAPPDATA\ArmoryX"
RequestExecutionLevel user

; Custom UI colors (dark theme)
!define MUI_BGCOLOR 1a1a2e
!define MUI_TEXTCOLOR edf2f4

; --------------------------------
; Custom Page Variables

Var Dialog
Var Label
Var ProgressBar
Var InstallButton
Var Image
Var DirectoryText
Var BrowseButton
Var DesktopCheckbox
Var StartupCheckbox

; --------------------------------
; Interface Configuration

!define MUI_ICON "..\ArmoryX-Website\assets\Armory_X.ico"
!define MUI_UNICON "..\ArmoryX-Website\assets\Armory_X.ico"

; Remove default pages - we'll create custom ones
!define MUI_PAGE_CUSTOMFUNCTION_PRE skipPage
!define MUI_PAGE_CUSTOMFUNCTION_SHOW skipPage
!define MUI_PAGE_CUSTOMFUNCTION_LEAVE skipPage

; --------------------------------
; Custom Modern UI Page

Page custom CreateModernUI LeaveModernUI
Page instfiles

; --------------------------------
; Installer Sections

Section "MainSection" SEC01
  SetOutPath "$INSTDIR"
  
  ; Add files
  File /r "payload\*.*"
  
  ; Create directories
  CreateDirectory "$INSTDIR\Resources"
  CreateDirectory "$INSTDIR\Data"
  CreateDirectory "$INSTDIR\Logs"
  
  ; Create shortcuts if selected
  ${If} $DesktopCheckbox == 1
    CreateShortcut "$DESKTOP\Armory X.lnk" "$INSTDIR\ArmoryX.exe"
  ${EndIf}
  
  ; Create Start Menu shortcuts
  CreateDirectory "$SMPROGRAMS\Armory X"
  CreateShortcut "$SMPROGRAMS\Armory X\Armory X.lnk" "$INSTDIR\ArmoryX.exe"
  CreateShortcut "$SMPROGRAMS\Armory X\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
  
  ; Write uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Registry entries
  WriteRegStr HKCU "Software\ArmoryX" "InstallPath" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayName" "Armory X"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayIcon" "$INSTDIR\ArmoryX.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "Publisher" "Armory X"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayVersion" "1.0.0"
SectionEnd

; --------------------------------
; Custom UI Function

Function CreateModernUI
  ; Create custom dialog
  nsDialogs::Create 1018
  Pop $Dialog
  
  ; Set background color
  SetCtlColors $Dialog "" 1a1a2e
  
  ; Add logo/image at top
  ${NSD_CreateBitmap} 0 0 100% 40% ""
  Pop $Image
  ${NSD_SetImage} $Image "$PLUGINSDIR\header.bmp" $Image
  
  ; Title
  ${NSD_CreateLabel} 10% 45% 80% 20 "Ready to install Armory X"
  Pop $Label
  SetCtlColors $Label edf2f4 1a1a2e
  CreateFont $0 "Arial" 16 700
  SendMessage $Label ${WM_SETFONT} $0 0
  
  ; Subtitle
  ${NSD_CreateLabel} 10% 50% 80% 20 "Your professional system utility suite"
  Pop $Label
  SetCtlColors $Label a0a0a0 1a1a2e
  
  ; Install directory
  ${NSD_CreateLabel} 10% 60% 20% 12 "Install to:"
  Pop $Label
  SetCtlColors $Label edf2f4 1a1a2e
  
  ${NSD_CreateText} 10% 65% 60% 12 "$INSTDIR"
  Pop $DirectoryText
  SetCtlColors $DirectoryText edf2f4 16213e
  
  ${NSD_CreateButton} 72% 65% 18% 12 "Browse..."
  Pop $BrowseButton
  SetCtlColors $BrowseButton edf2f4 0f3460
  ${NSD_OnClick} $BrowseButton BrowseForFolder
  
  ; Options
  ${NSD_CreateCheckbox} 10% 75% 80% 12 "Create desktop shortcut"
  Pop $DesktopCheckbox
  SetCtlColors $DesktopCheckbox edf2f4 1a1a2e
  ${NSD_Check} $DesktopCheckbox
  
  ${NSD_CreateCheckbox} 10% 80% 80% 12 "Launch on system startup"
  Pop $StartupCheckbox
  SetCtlColors $StartupCheckbox edf2f4 1a1a2e
  
  ; Install button
  ${NSD_CreateButton} 35% 88% 30% 25 "INSTALL"
  Pop $InstallButton
  SetCtlColors $InstallButton ffffff e94560
  CreateFont $0 "Arial" 11 700
  SendMessage $InstallButton ${WM_SETFONT} $0 0
  ${NSD_OnClick} $InstallButton StartInstall
  
  ; Cancel link
  ${NSD_CreateLink} 45% 95% 10% 12 "Cancel"
  Pop $Label
  SetCtlColors $Label 808080 1a1a2e
  ${NSD_OnClick} $Label CancelInstall
  
  nsDialogs::Show
FunctionEnd

Function LeaveModernUI
  ; Get checkbox states
  ${NSD_GetState} $DesktopCheckbox $DesktopCheckbox
  ${NSD_GetState} $StartupCheckbox $StartupCheckbox
FunctionEnd

Function BrowseForFolder
  nsDialogs::SelectFolderDialog "Select Installation Directory" "$INSTDIR"
  Pop $0
  ${If} $0 != error
    StrCpy $INSTDIR $0
    ${NSD_SetText} $DirectoryText $INSTDIR
  ${EndIf}
FunctionEnd

Function StartInstall
  ; Proceed to installation
FunctionEnd

Function CancelInstall
  MessageBox MB_YESNO "Are you sure you want to cancel the installation?" IDYES +2
  Abort
  Quit
FunctionEnd

Function skipPage
  Abort
FunctionEnd

; --------------------------------
; Uninstaller Section

Section "Uninstall"
  ; Remove files
  Delete "$INSTDIR\*.*"
  RMDir /r "$INSTDIR"
  
  ; Remove shortcuts
  Delete "$DESKTOP\Armory X.lnk"
  Delete "$SMPROGRAMS\Armory X\*.*"
  RMDir "$SMPROGRAMS\Armory X"
  
  ; Remove registry entries
  DeleteRegKey HKCU "Software\ArmoryX"
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX"
SectionEnd

; --------------------------------
; Languages

!insertmacro MUI_LANGUAGE "English" 