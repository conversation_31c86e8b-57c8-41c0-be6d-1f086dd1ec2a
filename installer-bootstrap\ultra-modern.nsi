; Armory X Ultra-Modern Installer
; HTML/CSS based UI like Discord, Slack, etc.

!include "MUI2.nsh"
!include "nsDialogs.nsh"
!include "LogicLib.nsh"

; --------------------------------
; Configuration

Name "Armory X"
OutFile "ArmoryX-Installer.exe"
InstallDir "$LOCALAPPDATA\ArmoryX"
RequestExecutionLevel user
ShowInstDetails nevershow
ShowUninstDetails nevershow

; No default UI
SilentInstall silent

; --------------------------------
; Embedded HTML UI

!define HTMLUI_PATH "installer-ui"

; --------------------------------
; Installer Attributes

VIProductVersion "*******"
VIAddVersionKey "ProductName" "Armory X"
VIAddVersionKey "CompanyName" "Armory X"
VIAddVersionKey "FileDescription" "Armory X Installer"
VIAddVersionKey "FileVersion" "1.0.0"

; --------------------------------
; Pages

Page custom ShowHTMLInstaller

; --------------------------------
; Variables

Var hwndHTMLWindow

; --------------------------------
; Main Install Section

Section "Install" SEC_MAIN
  SetOutPath "$INSTDIR"
  
  ; Extract files
  File /r "payload\*.*"
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Registry
  WriteRegStr HKCU "Software\ArmoryX" "InstallPath" "$INSTDIR"
  
  ; Add to Windows Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayName" "Armory X"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayIcon" "$INSTDIR\ArmoryX.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayVersion" "1.0.0"
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\Armory X"
  CreateShortcut "$SMPROGRAMS\Armory X\Armory X.lnk" "$INSTDIR\ArmoryX.exe"
  CreateShortcut "$DESKTOP\Armory X.lnk" "$INSTDIR\ArmoryX.exe"
  
  ; Launch if requested
  Exec "$INSTDIR\ArmoryX.exe"
SectionEnd

; --------------------------------
; HTML UI Function

Function ShowHTMLInstaller
  ; This would launch a custom HTML window
  ; For now, using MessageBox as placeholder
  MessageBox MB_OK "This would show a beautiful HTML/CSS installer UI"
FunctionEnd

; --------------------------------
; Uninstaller

Section "Uninstall"
  Delete "$INSTDIR\*.*"
  RMDir /r "$INSTDIR"
  
  Delete "$DESKTOP\Armory X.lnk"
  Delete "$SMPROGRAMS\Armory X\*.*"
  RMDir "$SMPROGRAMS\Armory X"
  
  DeleteRegKey HKCU "Software\ArmoryX"
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX"
SectionEnd 