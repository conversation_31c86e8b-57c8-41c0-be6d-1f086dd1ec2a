/**
 * File Operations module for Armory X
 * Extracted from main.js for better maintainability
 */

const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { shell, dialog, app } = require('electron');
const { formatBytes } = require('./utils');

class FileOperations {
  constructor(dependencies = {}) {
    this.mainWindow = dependencies.mainWindow;
    
    // Initialize cleanup progress state
    this.cleanupProgress = {
      is_running: false,
      files_processed: 0,
      bytes_freed: 0,
      current_operation: 'Ready'
    };
  }

  /**
   * Update the main window reference
   * @param {BrowserWindow} mainWindow - The main window instance
   */
  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
  }

  /**
   * Get current cleanup progress
   * @returns {Object} Cleanup progress object
   */
  getCleanupProgress() {
    return this.cleanupProgress;
  }

  /**
   * Clean directory recursively
   * @param {string} dirPath - Directory path to clean
   * @returns {Object} Result with deleted count and size
   */
  async cleanDirectory(dirPath) {
    let deleted = 0;
    let size = 0;
    
    try {
      const files = await fs.readdir(dirPath);
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        
        try {
          const stats = await fs.stat(filePath);
          
          if (stats.isFile()) {
            // Skip files that are currently in use
            const age = Date.now() - stats.mtime.getTime();
            if (age > 24 * 60 * 60 * 1000) { // Older than 24 hours
              size += stats.size;
              await fs.unlink(filePath);
              deleted++;
            }
          } else if (stats.isDirectory()) {
            const subResult = await this.cleanDirectory(filePath);
            deleted += subResult.deleted;
            size += subResult.size;
            
            // Try to remove empty directory
            try {
              await fs.rmdir(filePath);
            } catch (e) {
              // Directory not empty, that's ok
            }
          }
        } catch (error) {
          // File in use or permission denied, skip
          console.log(`Skipping ${filePath}: ${error.message}`);
        }
      }
    } catch (error) {
      console.log(`Cannot access ${dirPath}: ${error.message}`);
    }
    
    return { deleted, size };
  }

  /**
   * Clean junk files from temporary directories
   * @returns {Object} Cleanup result
   */
  async cleanJunkFiles() {
    console.log('🧹 Starting cleanup process...');
    
    this.cleanupProgress = {
      is_running: true,
      files_processed: 0,
      bytes_freed: 0,
      current_operation: 'Initializing cleanup...'
    };

    try {
      const tempPaths = [
        path.join(os.tmpdir()),
        path.join(os.homedir(), 'AppData', 'Local', 'Temp'),
        path.join(os.homedir(), 'AppData', 'Local', 'Microsoft', 'Windows', 'INetCache'),
        path.join(os.homedir(), 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Cache'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Mozilla', 'Firefox', 'Profiles')
      ];

      let totalDeleted = 0;
      let totalSize = 0;

      for (const tempPath of tempPaths) {
        if (await fs.pathExists(tempPath)) {
          this.cleanupProgress.current_operation = `Cleaning ${path.basename(tempPath)}...`;
          
          const { deleted, size } = await this.cleanDirectory(tempPath);
          totalDeleted += deleted;
          totalSize += size;
          
          this.cleanupProgress.files_processed = totalDeleted;
          this.cleanupProgress.bytes_freed = totalSize;
        }
      }

      this.cleanupProgress.is_running = false;
      this.cleanupProgress.current_operation = 'Cleanup complete';

      return {
        success: true,
        message: `Cleanup completed! Deleted ${totalDeleted} files, freed ${formatBytes(totalSize)}`,
        files_deleted: totalDeleted,
        bytes_freed: totalSize
      };

    } catch (error) {
      this.cleanupProgress.is_running = false;
      this.cleanupProgress.current_operation = 'Cleanup failed';
      console.error('Cleanup error:', error);
      
      return {
        success: false,
        message: `Cleanup failed: ${error.message}`
      };
    }
  }

  /**
   * Show file in folder using system file manager
   * @param {string} filePath - Path to the file
   * @returns {Object} Success result
   */
  async showInFolder(filePath) {
    shell.showItemInFolder(filePath);
    return { success: true };
  }

  /**
   * Show folder selection dialog
   * @returns {string|null} Selected folder path or null if canceled
   */
  async selectFolder() {
    const result = await dialog.showOpenDialog(this.mainWindow, {
      properties: ['openDirectory']
    });
    
    return result.canceled ? null : result.filePaths[0];
  }

  /**
   * Show file selection dialog
   * @param {Object} options - Dialog options
   * @returns {Object} Dialog result
   */
  async selectFile(options = {}) {
    const dialogOptions = {
      properties: ['openFile'],
      filters: options.filters || [
        { name: 'All Files', extensions: ['*'] }
      ]
    };

    const result = await dialog.showOpenDialog(this.mainWindow, dialogOptions);
    return result;
  }

  /**
   * Extract icon from file
   * @param {string} filePath - Path to the file
   * @returns {Object} Icon extraction result
   */
  async extractIcon(filePath) {
    try {
      console.log(`🎨 Extracting icon from: ${filePath}`);
      
      // Use Electron's built-in icon extraction
      const icon = await app.getFileIcon(filePath, { size: 'large' });
      
      if (icon) {
        // Convert icon to base64 data URL
        const iconBuffer = icon.toPNG();
        const iconBase64 = `data:image/png;base64,${iconBuffer.toString('base64')}`;
        
        console.log(`✅ Icon extracted successfully from: ${path.basename(filePath)}`);
        return { 
          success: true, 
          iconData: iconBase64,
          message: 'Icon extracted successfully'
        };
      } else {
        console.log(`❌ No icon found in: ${filePath}`);
        return { 
          success: false, 
          message: 'No icon found in file'
        };
      }
    } catch (error) {
      console.error(`❌ Error extracting icon: ${error.message}`);
      return { 
        success: false, 
        message: `Failed to extract icon: ${error.message}`
      };
    }
  }

  /**
   * Show folder selection dialog (alternative version)
   * @param {Object} options - Dialog options
   * @returns {Object} Dialog result
   */
  async selectFolderDialog(options = {}) {
    const dialogOptions = {
      properties: ['openDirectory']
    };

    const result = await dialog.showOpenDialog(this.mainWindow, dialogOptions);
    return result;
  }
}

module.exports = { FileOperations };