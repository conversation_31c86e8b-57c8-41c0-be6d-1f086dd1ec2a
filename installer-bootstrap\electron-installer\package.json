{"name": "armory-x-installer", "version": "1.0.0", "description": "Armory X Custom Installer", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "dist": "electron-builder --win"}, "devDependencies": {"electron": "^37.1.0", "electron-builder": "^26.0.12", "fs-extra": "^11.3.0"}, "build": {"appId": "com.armoryx.installer", "productName": "Armory X Installer", "directories": {"output": "dist"}, "win": {"target": "nsis", "icon": "../../ArmoryX-Website/assets/Armory_X.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "uninstallDisplayName": "Armory X"}}}