// Enhanced Authentication & Real-time Data System
document.addEventListener('DOMContentLoaded', function() {
    // Prevent infinite reload loops
    if (window.scriptInitialized) {
        console.log('🛑 Script already initialized, preventing reload loop');
        return;
    }
    window.scriptInitialized = true;
    
    try {
        // Initialize basic UI components first
        initializeNavigation();
        initializeForms();
        initEnhancedTitle(); // Initialize enhanced title effects
        loadUpdates();
        loadPlannedFeatures();
        setupDownloadButton();
        updateVersionInfo();
        
        // Wait for Firebase initialization before setting up auth and real-time features
        waitForFirebaseInit().then(() => {
            initializeAuth();
            startRealtimeStats();
            initializeLicenseIntegration();
        }).catch(error => {
            console.warn('Firebase initialization failed, continuing with fallback mode:', error);
            initializeLicenseIntegration(); // Still try to initialize license system
        });
    } catch (error) {
        console.error('Script initialization error:', error);
        // Don't reload on error, just log it
    }
});

// Wait for Firebase to be initialized (or fail)
function waitForFirebaseInit() {
    return new Promise((resolve) => {
        // Check if Firebase is already available
        if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
            console.log('🏎️ Firebase already available, proceeding immediately');
            resolve();
            return;
        }
        
        let attempts = 0;
        const maxAttempts = 150; // 15 seconds at 100ms intervals
        
        const checkInterval = setInterval(() => {
            attempts++;
            
            // Check for either firebaseInitialized flag OR firebase object
            if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
                clearInterval(checkInterval);
                console.log(`✅ Firebase ready after ${attempts * 100}ms`);
                resolve();
                return;
            }
            
            // Timeout after max attempts
            if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.log('🕒 Firebase initialization timeout - proceeding without Firebase');
                console.log('⚠️ Firebase may be blocked by network or taking too long to load');
                console.log('⚠️ This will cause fallback to localStorage authentication');
                console.log(`⏱️ Waited ${attempts * 100}ms for Firebase initialization`);
                window.firebase = null;
                resolve();
            }
        }, 100);
    });
}

// Initialize navigation and basic UI
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    if (hamburger && navMenu) {
        // Mobile menu toggle
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (hamburger && navMenu) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// REAL-TIME AUTHENTICATION SYSTEM
function initializeAuth() {
    // Check if Firebase is available and properly initialized
    if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
        console.log('🔥 Setting up Firebase authentication');
        const { auth, onAuthStateChanged } = window.firebase;
        
        try {
            // Listen for authentication state changes
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    console.log('User is signed in:', user.email);
                    updateAccountDisplay(true, user.email);
                    updateUserLastSeen(user.uid);
                } else {
                    console.log('User is signed out');
                    updateAccountDisplay(false);
                }
            });
        } catch (error) {
            console.warn('Firebase auth listener failed:', error);
            // Fall back to basic form handling
            initializeBasicAuth();
        }
    } else {
        console.log('🔧 Firebase not available - using basic authentication');
        initializeBasicAuth();
    }
}

// Basic authentication fallback for local development
function initializeBasicAuth() {
    // Check for existing session
    const userToken = localStorage.getItem('armoryX_user_token');
    const userEmail = localStorage.getItem('armoryX_user_email');
    
    if (userToken && userEmail) {
        updateAccountDisplay(true, userEmail);
    }
}

// REAL-TIME STATISTICS TRACKING
function startRealtimeStats() {
    // Always start with fallback stats
    updateSiteStats();
    
    // Set up periodic updates (every 30 seconds)
    setInterval(updateSiteStats, 30000);
    
    // If Firebase is available, try to set up real-time listeners
    if (window.firebase && window.firebase.db) {
        console.log('🔥 Setting up Firebase real-time listeners');
        try {
            setupRealtimeListeners();
        } catch (error) {
            console.warn('Firebase real-time listeners failed:', error);
            console.log('📊 Continuing with fallback statistics');
        }
    } else {
        console.log('📊 Using fallback statistics (Firebase not available)');
    }
}

function setupRealtimeListeners() {
    if (!window.firebase || !window.firebase.db) {
        console.warn('Firebase database not available for real-time listeners');
        return;
    }
    
    const { db, collection, onSnapshot, query, orderBy } = window.firebase;
    
    try {
        // Listen for real-time user count updates
        const usersRef = collection(db, 'users');
        onSnapshot(usersRef, (snapshot) => {
            const totalUsers = snapshot.size;
            updateStatElement('active-users', totalUsers.toLocaleString());
        }, (error) => {
            console.warn('Users snapshot listener error:', error);
        });
        
        // Listen for real-time forum post updates
        const postsRef = collection(db, 'forum_posts');
        onSnapshot(postsRef, (snapshot) => {
            const totalPosts = snapshot.size;
            updateStatElement('total-posts', totalPosts);
        }, (error) => {
            console.warn('Posts snapshot listener error:', error);
        });
        
        // Listen for online users (users active in last 5 minutes)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const onlineUsersRef = query(
            collection(db, 'users'),
            orderBy('lastSeen', 'desc')
        );
        
        onSnapshot(onlineUsersRef, (snapshot) => {
            let onlineCount = 0;
            snapshot.forEach((doc) => {
                const userData = doc.data();
                if (userData.lastSeen && userData.lastSeen.toDate && userData.lastSeen.toDate() > fiveMinutesAgo) {
                    onlineCount++;
                }
            });
            updateStatElement('online-users', onlineCount);
        }, (error) => {
            console.warn('Online users snapshot listener error:', error);
        });
        
        console.log('✅ Firebase real-time listeners set up successfully');
    } catch (error) {
        console.warn('Error setting up Firebase listeners:', error);
    }
}

function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

function updateSiteStats() {
    // Enhanced fallback stats with more realistic variation
    const baseStats = {
        activeUsers: 10000,
        totalPosts: 280,
        onlineUsers: 15
    };
    
    const stats = {
        activeUsers: baseStats.activeUsers + Math.floor(Math.random() * 200) - 100, // ±100 variation
        totalPosts: baseStats.totalPosts + Math.floor(Math.random() * 50),          // +0-50 variation
        onlineUsers: baseStats.onlineUsers + Math.floor(Math.random() * 20) - 5     // ±5 variation
    };
    
    // Only update if Firebase real-time listeners aren't active
    if (!window.firebase || !window.firebase.db) {
        updateStatElement('active-users', stats.activeUsers.toLocaleString() + '+');
        updateStatElement('total-posts', stats.totalPosts);
        updateStatElement('online-users', stats.onlineUsers);
    }
}

// ENHANCED FORM HANDLING
function initializeForms() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all tabs and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            document.getElementById(`${targetTab}-form`).classList.add('active');
        });
    });

    // Handle form submissions
    const loginForm = document.querySelector('#login-form form');
    const registerForm = document.querySelector('#register-form form');

    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
}

// SECURE LOGIN FUNCTION
async function handleLogin(e) {
    e.preventDefault();
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    // Validate input
    if (!email || !password) {
        showAuthMessage('Please enter both email and password.', 'error');
        return;
    }

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Logging in...';
    submitButton.disabled = true;

    try {
        if (window.firebase && window.firebase.auth && window.firebase.signInWithEmailAndPassword) {
            console.log('🔥 Using Firebase authentication');
            const { auth, signInWithEmailAndPassword } = window.firebase;
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            showAuthMessage('Login successful! Welcome back.', 'success');
            
            // Update user data
            await updateUserData(user.uid, {
                email: user.email,
                lastLogin: new Date(),
                lastSeen: new Date()
            });
            
        } else {
            console.log('🔧 Using fallback authentication');
            // Fallback authentication for local development
            await simulateAuthentication(email, password, false);
            
            // Store in localStorage for session persistence
            localStorage.setItem('armoryX_user_token', 'local_token_' + Date.now());
            localStorage.setItem('armoryX_user_email', email);
            
            showAuthMessage('Login successful! (Local mode)', 'success');
            updateAccountDisplay(true, email);
        }
    } catch (error) {
        console.error('Login error:', error);
        let errorMessage = 'Login failed. ';
        
        if (error.code) {
            // Firebase errors
            switch(error.code) {
                case 'auth/user-not-found':
                    errorMessage += 'No account found with this email.';
                    break;
                case 'auth/wrong-password':
                    errorMessage += 'Incorrect password.';
                    break;
                case 'auth/invalid-email':
                    errorMessage += 'Invalid email address.';
                    break;
                case 'auth/too-many-requests':
                    errorMessage += 'Too many failed attempts. Please try again later.';
                    break;
                default:
                    errorMessage += error.message || 'Please try again.';
            }
        } else {
            // Local authentication errors
            errorMessage += error.message || 'Please check your credentials and try again.';
        }
        
        showAuthMessage(errorMessage, 'error');
    } finally {
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
}

// SECURE REGISTRATION FUNCTION
async function handleRegister(e) {
    e.preventDefault();
    const email = document.getElementById('register-email').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm').value;

    // Validate input
    if (!email || !password || !confirmPassword) {
        showAuthMessage('Please fill in all fields.', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showAuthMessage('Passwords do not match!', 'error');
        return;
    }

    if (password.length < 6) {
        showAuthMessage('Password must be at least 6 characters long.', 'error');
        return;
    }

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Creating Account...';
    submitButton.disabled = true;

    try {
        if (window.firebase && window.firebase.auth && window.firebase.createUserWithEmailAndPassword) {
            console.log('🔥 Using Firebase authentication');
            const { auth, createUserWithEmailAndPassword } = window.firebase;
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Create user profile in database
            await createUserProfile(user.uid, {
                email: user.email,
                displayName: '', // Will be set by user later
                createdAt: new Date(),
                lastLogin: new Date(),
                lastSeen: new Date(),
                licenseKey: null,
                forumPosts: 0,
                reputation: 0,
                role: 'user', // Default role for new users
                banned: false,
                bannedUntil: null,
                bannedReason: null
            });
            
            showAuthMessage('Account created successfully! Welcome to Armory X.', 'success');
            
        } else {
            console.log('🔧 Using fallback authentication');
            // Fallback authentication for local development
            await simulateAuthentication(email, password, true);
            
            // Store in localStorage for session persistence
            localStorage.setItem('armoryX_user_token', 'local_token_' + Date.now());
            localStorage.setItem('armoryX_user_email', email);
            
            showAuthMessage('Account created successfully! (Local mode)', 'success');
            updateAccountDisplay(true, email);
        }
    } catch (error) {
        console.error('Registration error:', error);
        let errorMessage = 'Registration failed. ';
        
        if (error.code) {
            // Firebase errors
            switch(error.code) {
                case 'auth/email-already-in-use':
                    errorMessage += 'An account with this email already exists.';
                    break;
                case 'auth/invalid-email':
                    errorMessage += 'Invalid email address.';
                    break;
                case 'auth/weak-password':
                    errorMessage += 'Password is too weak. Use at least 6 characters.';
                    break;
                default:
                    errorMessage += error.message || 'Please try again.';
            }
        } else {
            // Local authentication errors
            errorMessage += error.message || 'Please try again.';
        }
        
        showAuthMessage(errorMessage, 'error');
    } finally {
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
}

// Simulate authentication for local development
async function simulateAuthentication(email, password, isRegistration) {
    return new Promise((resolve, reject) => {
        // Simulate network delay
        setTimeout(() => {
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                reject(new Error('Invalid email format'));
                return;
            }
            
            if (isRegistration) {
                // For registration, check if email is already "registered" in localStorage
                const existingUsers = JSON.parse(localStorage.getItem('armoryX_local_users') || '[]');
                if (existingUsers.includes(email)) {
                    reject(new Error('An account with this email already exists (local)'));
                    return;
                }
                
                // "Register" the user
                existingUsers.push(email);
                localStorage.setItem('armoryX_local_users', JSON.stringify(existingUsers));
            } else {
                // For login, check if user exists in localStorage
                const existingUsers = JSON.parse(localStorage.getItem('armoryX_local_users') || '[]');
                if (!existingUsers.includes(email)) {
                    reject(new Error('No account found with this email (local)'));
                    return;
                }
            }
            
            resolve({ success: true, email: email });
        }, 1000);
    });
}

// DATABASE OPERATIONS
async function createUserProfile(uid, userData) {
    if (!window.firebase || !window.firebase.db) {
        console.log('🔧 Firebase database not available - skipping user profile creation');
        return;
    }
    
    const { db, doc, setDoc, serverTimestamp } = window.firebase;
    
    try {
        await setDoc(doc(db, 'users', uid), {
            ...userData,
            createdAt: serverTimestamp(),
            lastLogin: serverTimestamp(),
            lastSeen: serverTimestamp()
        });
        console.log('✅ User profile created in Firebase');
    } catch (error) {
        console.warn('Error creating user profile:', error);
    }
}

async function updateUserData(uid, updates) {
    if (!window.firebase || !window.firebase.db) {
        console.log('🔧 Firebase database not available - skipping user data update');
        return;
    }
    
    const { db, doc, updateDoc, serverTimestamp } = window.firebase;
    
    try {
        await updateDoc(doc(db, 'users', uid), {
            ...updates,
            lastSeen: serverTimestamp()
        });
        console.log('✅ User data updated in Firebase');
    } catch (error) {
        console.warn('Error updating user data:', error);
    }
}

async function updateUserLastSeen(uid) {
    if (!window.firebase || !window.firebase.db) {
        return; // Silently skip if Firebase not available
    }
    
    const { db, doc, updateDoc, serverTimestamp } = window.firebase;
    
    try {
        await updateDoc(doc(db, 'users', uid), {
            lastSeen: serverTimestamp()
        });
    } catch (error) {
        // Don't log this error as it's not critical
        console.debug('Error updating last seen:', error);
    }
}

// UI HELPERS
function showAuthMessage(message, type) {
    // Remove any existing messages
    const existingMessage = document.querySelector('.auth-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create new message element
    const messageElement = document.createElement('div');
    messageElement.className = `auth-message ${type}`;
    messageElement.textContent = message;
    
    // Add to form container
    const formContainer = document.querySelector('.form-container');
    if (formContainer) {
        formContainer.insertBefore(messageElement, formContainer.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.remove();
            }
        }, 5000);
    }
}

function updateAccountDisplay(isLoggedIn, email = '') {
    const accountSection = document.getElementById('account');
    if (!accountSection) return;
    
    if (isLoggedIn) {
        // Show logged-in state
        accountSection.innerHTML = `
            <div class="container">
                <div class="logged-in-state">
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <div class="user-details">
                            <h3>Welcome back!</h3>
                            <p class="user-email">${email}</p>
                        </div>
                    </div>
                    
                    <div class="account-actions">
                        <div class="profile-section">
                            <h4><i class="fas fa-user"></i> Profile Settings</h4>
                            <div class="profile-form">
                                <div class="form-group">
                                    <label for="profile-display-name">Display Name</label>
                                    <input type="text" id="profile-display-name" placeholder="Enter your display name for forums" maxlength="30">
                                    <small class="form-hint">This name will be shown in forums and community features</small>
                                </div>
                                <button onclick="saveUserProfile()" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Profile
                                </button>
                                <div id="profile-status" class="profile-status"></div>
                            </div>
                        </div>
                        
                        <div class="license-section">
                            <h4><i class="fas fa-key"></i> License Key</h4>
                            <div class="license-input-group">
                                <input type="text" id="license-key-input" class="license-input" placeholder="Enter your license key (XXXX-XXXX-XXXX-XXXX)">
                                <button onclick="validateLicenseKey()" class="btn btn-primary">Validate</button>
                            </div>
                            <div id="license-status" class="license-status"></div>
                        </div>
                        
                        <div class="user-actions">
                            <a href="forums.html" class="btn btn-primary">
                                <i class="fas fa-comments"></i> Access Forums
                            </a>
                            <button onclick="handleLogout()" class="btn btn-secondary">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Load user profile data after UI is created
        setTimeout(() => loadUserProfile(email), 100);
    } else {
        // Show login/register forms (restore original content)
        console.log('🔄 User logged out, account display updated');
        // Note: Removed automatic reload to prevent infinite refresh loops
    }
}

// Profile Management Functions
async function loadUserProfile(email) {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        console.log('🔧 Firebase not available - profile management disabled');
        return;
    }
    
    const { auth, db, doc, getDoc } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) return;
    
    try {
        const userDocRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            const displayNameInput = document.getElementById('profile-display-name');
            
            if (displayNameInput) {
                displayNameInput.value = userData.displayName || '';
            }
            
            console.log('✅ User profile loaded successfully');
        } else {
            console.log('⚠️ No user profile found in database');
        }
    } catch (error) {
        console.error('Error loading user profile:', error);
    }
}

async function saveUserProfile() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        showProfileStatus('Profile management requires Firebase connection.', 'error');
        return;
    }
    
    const { auth, db, doc, updateDoc, serverTimestamp } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) {
        showProfileStatus('You must be logged in to save profile.', 'error');
        return;
    }
    
    const displayNameInput = document.getElementById('profile-display-name');
    const displayName = displayNameInput ? displayNameInput.value.trim() : '';
    
    // Validate display name
    if (displayName && (displayName.length < 2 || displayName.length > 30)) {
        showProfileStatus('Display name must be between 2 and 30 characters.', 'error');
        return;
    }
    
    // Check for inappropriate content (basic filter)
    const inappropriateWords = ['admin', 'moderator', 'staff', 'official'];
    if (displayName && inappropriateWords.some(word => displayName.toLowerCase().includes(word))) {
        showProfileStatus('Display name contains restricted words.', 'error');
        return;
    }
    
    const saveButton = document.querySelector('[onclick="saveUserProfile()"]');
    const originalText = saveButton ? saveButton.innerHTML : '';
    
    try {
        if (saveButton) {
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            saveButton.disabled = true;
        }
        
        const userDocRef = doc(db, 'users', user.uid);
        await updateDoc(userDocRef, {
            displayName: displayName,
            lastProfileUpdate: serverTimestamp()
        });
        
        showProfileStatus('Profile saved successfully!', 'success');
        console.log('✅ User profile saved successfully');
        
    } catch (error) {
        console.error('Error saving user profile:', error);
        showProfileStatus('Failed to save profile. Please try again.', 'error');
    } finally {
        if (saveButton) {
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        }
    }
}

function showProfileStatus(message, type) {
    const statusElement = document.getElementById('profile-status');
    if (!statusElement) return;
    
    statusElement.className = `profile-status ${type}`;
    statusElement.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            statusElement.innerHTML = '';
            statusElement.className = 'profile-status';
        }, 3000);
    }
}

// Function to get user's display name (for use in forums)
async function getUserDisplayName(userId) {
    if (!window.firebase || !window.firebase.db) {
        return null;
    }
    
    const { db, doc, getDoc } = window.firebase;
    
    try {
        const userDocRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            return userData.displayName || null;
        }
    } catch (error) {
        console.error('Error getting user display name:', error);
    }
    
    return null;
}

async function handleLogout() {
    try {
        if (window.firebase && window.firebase.auth && window.firebase.signOut) {
            console.log('🔥 Using Firebase logout');
            const { auth, signOut } = window.firebase;
            await signOut(auth);
        } else {
            console.log('🔧 Using local logout');
            // Clear local storage for fallback authentication
            localStorage.removeItem('armoryX_user_token');
            localStorage.removeItem('armoryX_user_email');
        }
        
        showAuthMessage('You have been logged out successfully.', 'success');
        // Update display without reloading to prevent infinite refresh
        updateAccountDisplay(false);
        
    } catch (error) {
        console.error('Logout error:', error);
        // Force logout by clearing localStorage anyway
        localStorage.removeItem('armoryX_user_token');
        localStorage.removeItem('armoryX_user_email');
        
        showAuthMessage('Logged out successfully.', 'success');
        // Update display without reloading to prevent infinite refresh
        updateAccountDisplay(false);
    }
}

// Download functionality
function setupDownloadButton() {
    const downloadButton = document.getElementById('download-btn');
    
    if (downloadButton) {
        downloadButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update button state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing Download...';
            this.style.pointerEvents = 'none';
            
            // Simulate download preparation
            setTimeout(() => {
                // Get download URL
                const downloadUrl = getLatestDownloadUrl();
                if (downloadUrl) {
                    // Check if it's a direct download link or folder link
                    if (downloadUrl.includes('github.com') && downloadUrl.includes('/download/')) {
                        // GitHub Releases - direct download
                        const downloadLink = document.createElement('a');
                        downloadLink.href = downloadUrl;
                        downloadLink.download = 'ArmoryX.exe';
                        downloadLink.style.display = 'none';
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                        
                        setTimeout(() => {
                            showDownloadModal();
                        }, 500);
                    } else if (downloadUrl.startsWith('./downloads/')) {
                        // Direct website hosting
                        const downloadLink = document.createElement('a');
                        downloadLink.href = downloadUrl;
                        downloadLink.download = 'ArmoryX.exe';
                        downloadLink.style.display = 'none';
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                        
                        setTimeout(() => {
                            showDownloadModal();
                        }, 500);
                    } else {
                        // Google Drive or other folder links - open in new tab
                        window.open(downloadUrl, '_blank');
                        
                        setTimeout(() => {
                            showDownloadModal(true); // true indicates it's a folder link
                        }, 1000);
                    }
                } else {
                    alert('Download link not available. Please check back later or join our Discord for support.');
                }
                
                // Reset button
                this.innerHTML = originalText;
                this.style.pointerEvents = 'auto';
            }, 2000);
        });
    }
}

// Create beautiful download modal
function showDownloadModal(isFolder = false) {
    // Remove any existing modal
    const existingModal = document.getElementById('download-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Create modal HTML
    const modalHTML = `
        <div id="download-modal" class="download-modal-overlay">
            <div class="download-modal">
                <div class="download-modal-header">
                    <div class="download-icon">
                        <i class="fas fa-${isFolder ? 'folder-open' : 'download'}"></i>
                    </div>
                    <h2>${isFolder ? 'Download Folder Opened!' : 'Thanks for Downloading!'}</h2>
                    <button class="modal-close" onclick="closeDownloadModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="download-modal-content">
                    <div class="download-message">
                        ${isFolder ? 
                            '<p class="main-message">The download folder has been opened in a new tab!</p><p class="sub-message">Please download "ArmoryX.exe" from the folder.</p>' :
                            '<p class="main-message">ArmoryX is downloading now!</p><p class="sub-message">Your download should start automatically. Check your downloads folder.</p>'
                        }
                    </div>
                    
                    <div class="download-features">
                        <div class="feature-highlight">
                            <i class="fas fa-rocket"></i>
                            <span>Ultimate PC optimization</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-gamepad"></i>
                            <span>Gaming enhancement tools</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>System performance boost</span>
                        </div>
                    </div>
                    
                    <div class="download-actions">
                        <a href="http://discord.gg/uq4Zs2G57g" target="_blank" class="btn btn-primary modal-btn">
                            <i class="fab fa-discord"></i> Join Our Discord
                        </a>
                        <a href="forums.html" class="btn btn-secondary modal-btn">
                            <i class="fas fa-comments"></i> Visit Forums
                        </a>
                    </div>
                    
                    <div class="download-footer">
                        <p><i class="fas fa-shield-alt"></i> Safe & Secure Download</p>
                        <p><i class="fas fa-users"></i> Join 10K+ Happy Users</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Add click-outside-to-close functionality
    const overlay = document.getElementById('download-modal');
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeDownloadModal();
        }
    });
    
    // Add ESC key to close
    const handleEscKey = function(e) {
        if (e.key === 'Escape') {
            closeDownloadModal();
            document.removeEventListener('keydown', handleEscKey);
        }
    };
    document.addEventListener('keydown', handleEscKey);
}

function closeDownloadModal() {
    const modal = document.getElementById('download-modal');
    if (modal) {
        modal.classList.add('modal-closing');
        setTimeout(() => {
            modal.remove();
            // Clean up any remaining event listeners
            document.removeEventListener('keydown', arguments.callee);
        }, 300);
    }
}

function getLatestDownloadUrl() {
    // GitHub "latest" release - AUTOMATICALLY updates to newest version!
    // This URL always points to whatever release is marked as "Latest"
    
    // Option A: Use consistent filename (ACTIVE - you're using this!)
    // Since you renamed your exe to "ArmoryX.exe", this will work for ALL future releases
    return "https://github.com/Mericanized/ArmoryX/releases/latest/download/ArmoryX.exe";
    
    // Option B: Version-specific filename (not needed anymore)
    // return "https://github.com/Mericanized/ArmoryX/releases/latest/download/ArmoryX_v0.2.4b.exe";
    
    // Option C: Redirect to releases page (users pick the file)
    // return "https://github.com/Mericanized/ArmoryX/releases/latest";
}

// Update version information
function updateVersionInfo() {
    const versionElements = {
        version: document.getElementById('current-version'),
        releaseDate: document.getElementById('release-date'),
        fileSize: document.getElementById('file-size')
    };

    // Try to get latest version from GitHub API (automatic!)
    fetchLatestVersionFromGitHub()
        .then(versionData => {
            if (versionElements.version) {
                versionElements.version.textContent = versionData.version;
            }
            if (versionElements.releaseDate) {
                versionElements.releaseDate.textContent = versionData.releaseDate;
            }
            if (versionElements.fileSize) {
                versionElements.fileSize.textContent = versionData.fileSize;
            }
        })
        .catch(error => {
            console.log('Could not fetch from GitHub, using fallback data');
            // Fallback to static data if GitHub API fails
            const fallbackData = {
                version: 'v0.2.4b',
                releaseDate: 'June 6, 2025',
                fileSize: '175.3 MB'
            };
            
            if (versionElements.version) {
                versionElements.version.textContent = fallbackData.version;
            }
            if (versionElements.releaseDate) {
                versionElements.releaseDate.textContent = fallbackData.releaseDate;
            }
            if (versionElements.fileSize) {
                versionElements.fileSize.textContent = fallbackData.fileSize;
            }
        });
}

// Automatically fetch latest version info from GitHub
async function fetchLatestVersionFromGitHub() {
    try {
        const response = await fetch('https://api.github.com/repos/Mericanized/ArmoryX/releases/latest');
        const release = await response.json();
        
        // Extract version info
        const version = release.tag_name;
        const releaseDate = new Date(release.published_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        // Get file size from the first asset (your exe file)
        let fileSize = '175.3 MB'; // fallback
        if (release.assets && release.assets.length > 0) {
            const bytes = release.assets[0].size;
            fileSize = (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }
        
        return {
            version: version,
            releaseDate: releaseDate,
            fileSize: fileSize
        };
    } catch (error) {
        throw error;
    }
}

// Load and display updates
function loadUpdates() {
    const timelineContainer = document.getElementById('updates-timeline');
    
    if (!timelineContainer) return;

    // Mock update data (replace with actual API call or JSON file)
    const updates = [
        {
            version: 'v1.2.0',
            date: '2024-12-14',
            title: 'Major Performance Update',
            changes: [
                'Improved internet speed test accuracy and reliability',
                'Enhanced widget performance with image caching system',
                'Fixed Windows Defender disabling issues during installation',
                'Added better error handling and timeout management',
                'Updated UI with smoother animations and transitions'
            ],
            type: 'major'
        },
        {
            version: 'v1.1.5',
            date: '2024-12-10',
            title: 'Bug Fixes and Improvements',
            changes: [
                'Fixed widget slow opening with high-resolution graphics',
                'Improved junk file cleanup efficiency',
                'Enhanced mod manager stability',
                'Fixed various UI scaling issues'
            ],
            type: 'patch'
        },
        {
            version: 'v1.1.0',
            date: '2024-12-01',
            title: 'Feature Expansion',
            changes: [
                'Added desktop widget functionality',
                'Integrated advanced speed testing',
                'Enhanced gaming tools and mod support',
                'Improved system optimization algorithms'
            ],
            type: 'minor'
        }
    ];

    // Generate timeline HTML
    const timelineHTML = updates.map(update => `
        <div class="timeline-item ${update.type}">
            <div class="timeline-marker"></div>
            <div class="timeline-content">
                <div class="timeline-header">
                    <h3>${update.title}</h3>
                    <div class="timeline-meta">
                        <span class="version-tag">${update.version}</span>
                        <span class="date-tag">${formatDate(update.date)}</span>
                    </div>
                </div>
                <div class="timeline-changes">
                    <ul>
                        ${update.changes.map(change => `<li>${change}</li>`).join('')}
                    </ul>
                </div>
            </div>
        </div>
    `).join('');

    timelineContainer.innerHTML = timelineHTML;
}

// Load planned features
function loadPlannedFeatures() {
    const featuresContainer = document.getElementById('planned-features');
    
    if (!featuresContainer) return;

    // Mock planned features data
    const plannedFeatures = [
        {
            title: 'Advanced Windows Defender Management',
            description: 'Complete control over Windows Defender with safer disable/enable functionality',
            priority: 'high',
            status: 'in-progress'
        },
        {
            title: 'Widget Performance Optimization',
            description: 'Pre-caching system for faster hi-res graphics loading',
            priority: 'high',
            status: 'completed'
        },
        {
            title: 'User Account System',
            description: 'Login system with HWID-based license management for premium features',
            priority: 'medium',
            status: 'planned'
        },
        {
            title: 'Enhanced Speed Testing',
            description: 'More accurate internet speed measurements with multiple server endpoints',
            priority: 'high',
            status: 'completed'
        },
        {
            title: 'Custom Website Dashboard',
            description: 'Personalized user dashboard with download history and settings sync',
            priority: 'medium',
            status: 'in-progress'
        }
    ];

    const featuresHTML = plannedFeatures.map(feature => `
        <div class="planned-feature ${feature.status}">
            <div class="feature-header">
                <h4>${feature.title}</h4>
                <span class="status-badge ${feature.status}">${formatStatus(feature.status)}</span>
            </div>
            <p>${feature.description}</p>
            <div class="feature-meta">
                <span class="priority-badge ${feature.priority}">${feature.priority.toUpperCase()}</span>
            </div>
        </div>
    `).join('');

    featuresContainer.innerHTML = featuresHTML;
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatStatus(status) {
    const statusMap = {
        'completed': '✅ Completed',
        'in-progress': '🔄 In Progress',
        'planned': '📋 Planned'
    };
    return statusMap[status] || status;
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .timeline-item, .planned-feature').forEach(el => {
        observer.observe(el);
    });
}

// Add CSS for timeline and planned features
const additionalStyles = `
<style>
.timeline-item {
    position: relative;
    padding-left: 2rem;
    padding-bottom: 2rem;
    border-left: 2px solid var(--border-color);
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-marker {
    position: absolute;
    left: -6px;
    top: 0;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
}

.timeline-item.major .timeline-marker {
    background: #10b981;
}

.timeline-item.minor .timeline-marker {
    background: var(--primary-color);
}

.timeline-item.patch .timeline-marker {
    background: #f59e0b;
}

.timeline-content {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.timeline-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.timeline-meta {
    display: flex;
    gap: 0.5rem;
}

.version-tag, .date-tag {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.version-tag {
    background: var(--gradient-primary);
    color: white;
}

.date-tag {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.timeline-changes ul {
    list-style: none;
    padding: 0;
}

.timeline-changes li {
    padding: 0.25rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 1rem;
}

.timeline-changes li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.planned-feature {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.planned-feature:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.planned-feature.completed {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.05);
}

.planned-feature.in-progress {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.feature-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.feature-header h4 {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-weight: 500;
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-badge.in-progress {
    background: rgba(59, 130, 246, 0.2);
    color: var(--primary-color);
}

.status-badge.planned {
    background: rgba(156, 163, 175, 0.2);
    color: var(--text-secondary);
}

.planned-feature p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.feature-meta {
    display: flex;
    justify-content: flex-end;
}

.priority-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-weight: 600;
}

.priority-badge.high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.priority-badge.medium {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.priority-badge.low {
    background: rgba(156, 163, 175, 0.2);
    color: var(--text-secondary);
}

.animate-in {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
`;

// Inject additional styles
document.head.insertAdjacentHTML('beforeend', additionalStyles);

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeScrollAnimations);

// License Key Management
function validateLicenseKey() {
    const licenseInput = document.getElementById('license-key-input');
    const licenseStatus = document.getElementById('license-status');
    
    if (!licenseInput || !licenseStatus) return;
    
    const licenseKey = licenseInput.value.trim();
    
    if (!licenseKey) {
        showLicenseStatus('Please enter a license key.', 'error');
        return;
    }
    
    // Show loading
    licenseStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating license key...';
    licenseStatus.className = 'license-status loading';
    
    // Simulate license validation (TODO: Replace with real API call)
    setTimeout(() => {
        if (isValidLicenseFormat(licenseKey)) {
            // Mock successful validation
            localStorage.setItem('armoryX_license_key', licenseKey);
            localStorage.setItem('armoryX_license_status', 'active');
            showLicenseStatus('License key activated successfully!', 'success');
            licenseInput.value = '';
        } else {
            showLicenseStatus('Invalid license key format. Please check and try again.', 'error');
        }
    }, 2000);
}

function isValidLicenseFormat(key) {
    // Mock license validation (format: XXXX-XXXX-XXXX-XXXX)
    const licensePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    return licensePattern.test(key.toUpperCase());
}

function showLicenseStatus(message, type) {
    const licenseStatus = document.getElementById('license-status');
    if (!licenseStatus) return;
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        loading: 'fas fa-spinner fa-spin'
    };
    
    licenseStatus.innerHTML = `<i class="${icons[type]}"></i> ${message}`;
    licenseStatus.className = `license-status ${type}`;
}

// License integration for main page
function initializeLicenseIntegration() {
    // Check for authentication state changes
    if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
        try {
            window.firebase.onAuthStateChanged(window.firebase.auth, (user) => {
                updateDownloadButtonForPremium(user);
                updateAccountDropdownForPremium(user);
            });
        } catch (error) {
            console.warn('Error setting up auth state listener:', error);
        }
    }
    
    // Initialize account dropdown
    initializeAccountDropdown();
}

function initializeAccountDropdown() {
    // Update dropdown based on current auth state
    updateAccountDropdownForPremium();
    
    // Set up logout functionality
    const logoutBtn = document.getElementById('dropdown-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            if (window.firebase && window.firebase.auth && window.firebase.signOut) {
                try {
                    await window.firebase.signOut(window.firebase.auth);
                    window.location.reload();
                } catch (error) {
                    console.error('Logout error:', error);
                }
            }
        });
    }
}

async function updateAccountDropdownForPremium(user = null) {
    const premiumLink = document.getElementById('dropdown-premium-link');
    const userName = document.getElementById('dropdown-user-name');
    const userStatus = document.getElementById('dropdown-user-status');
    
    if (!premiumLink || !userName || !userStatus) return;
    
    if (!user) {
        // Not logged in
        userName.textContent = 'Guest User';
        userStatus.textContent = 'Not logged in';
        premiumLink.innerHTML = '<i class="fas fa-star"></i> Premium';
        return;
    }
    
    try {
        // Update user info
        userName.textContent = user.displayName || user.email.split('@')[0];
        userStatus.textContent = 'Logged in';
        
        // Check license status
        if (window.LicenseKeyManager) {
            const licenseManager = new window.LicenseKeyManager();
            await licenseManager.init();
            
            const status = await licenseManager.getUserLicenseStatus(user.uid);
            
            if (status.hasLicense && status.subscriptionStatus === 'active') {
                // User has active premium
                premiumLink.innerHTML = `<i class="fas fa-crown"></i> ${status.accountType.charAt(0).toUpperCase() + status.accountType.slice(1)}`;
                premiumLink.style.color = '#ffd700';
                userStatus.textContent = `${status.accountType.charAt(0).toUpperCase() + status.accountType.slice(1)} Member`;
            } else {
                // User logged in but no premium
                premiumLink.innerHTML = '<i class="fas fa-star"></i> Get Premium';
                premiumLink.style.color = '#667eea';
            }
        }
    } catch (error) {
        console.error('Error updating account dropdown:', error);
        // Fallback
        premiumLink.innerHTML = '<i class="fas fa-star"></i> Premium';
        userName.textContent = user.displayName || user.email.split('@')[0];
        userStatus.textContent = 'Logged in';
    }
}

// Function removed - Premium moved to account dropdown

async function updateDownloadButtonForPremium(user = null) {
    const downloadBtn = document.getElementById('download-btn');
    if (!downloadBtn) return;
    
    if (!user) {
        // Not logged in - show regular download
        downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download Now';
        downloadBtn.onclick = null;
        return;
    }
    
    try {
        if (window.LicenseKeyManager) {
            const licenseManager = new window.LicenseKeyManager();
            await licenseManager.init();
            
            const status = await licenseManager.getUserLicenseStatus(user.uid);
            
            if (status.hasLicense && status.subscriptionStatus === 'active') {
                // Premium user - show premium download
                downloadBtn.innerHTML = `<i class="fas fa-crown"></i> Download Premium (${status.accountType})`;
                downloadBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            } else {
                // Regular user - encourage premium
                downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download Free Version';
                
                // Add click handler to show premium benefits
                downloadBtn.onclick = function(e) {
                    if (confirm('You\'re downloading the free version. Would you like to upgrade to Premium for additional features?')) {
                        window.location.href = 'account.html#premium';
                        e.preventDefault();
                    }
                };
            }
        }
    } catch (error) {
        console.error('Error updating download button:', error);
    }
}

// Removed - Premium styles handled in account dropdown 

// Enhanced Hero Title Effects
function initEnhancedTitle() {
    const titleContainer = document.querySelector('.title-container');
    const titleBrand = document.querySelector('.title-brand');
    const titleLetters = document.querySelectorAll('.title-letter');
    const titleParticles = document.querySelector('.title-particles');
    
    if (!titleContainer || !titleBrand) return;
    
    // Dynamic particle generation
    function createParticle(x, y) {
        const particle = document.createElement('div');
        particle.className = 'dynamic-particle';
        particle.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 3px;
            height: 3px;
            background: radial-gradient(circle, #3b82f6, transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 10;
            animation: particleExplosion 1.5s ease-out forwards;
        `;
        
        titleParticles.appendChild(particle);
        
        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 1500);
    }
    
    // Add particle explosion animation to CSS if not exists
    if (!document.querySelector('#dynamic-particle-styles')) {
        const style = document.createElement('style');
        style.id = 'dynamic-particle-styles';
        style.textContent = `
            @keyframes particleExplosion {
                0% {
                    transform: scale(0) rotate(0deg);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.5) rotate(180deg);
                    opacity: 0.8;
                }
                100% {
                    transform: scale(0) rotate(360deg) translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
                    opacity: 0;
                }
            }
            
            .title-letter.clicked {
                animation: letterClick 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }
            
            @keyframes letterClick {
                0% { transform: scale(1) rotateY(0deg); }
                50% { transform: scale(1.3) rotateY(180deg); }
                100% { transform: scale(1) rotateY(360deg); }
            }
            
            .title-brand.shaking {
                animation: titleShake 0.5s ease-in-out;
            }
            
            @keyframes titleShake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
                20%, 40%, 60%, 80% { transform: translateX(2px); }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Letter click effects
    titleLetters.forEach((letter, index) => {
        letter.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation class
            this.classList.add('clicked');
            
            // Generate particles at click position
            const rect = this.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2 - titleParticles.getBoundingClientRect().left;
            const centerY = rect.top + rect.height / 2 - titleParticles.getBoundingClientRect().top;
            
            // Create multiple particles
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    createParticle(
                        centerX + (Math.random() - 0.5) * 20,
                        centerY + (Math.random() - 0.5) * 20
                    );
                }, i * 50);
            }
            
            // Remove click class after animation
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 600);
            
            // Add shake effect to entire title
            titleBrand.classList.add('shaking');
            setTimeout(() => {
                titleBrand.classList.remove('shaking');
            }, 500);
        });
        
        // Add keyboard accessibility
        letter.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
        
        // Make letters focusable for accessibility
        letter.setAttribute('tabindex', '0');
        letter.setAttribute('role', 'button');
        letter.setAttribute('aria-label', `Letter ${letter.textContent}`);
    });
    
    // Hover effects removed as requested by user
    
    // Intersection Observer for scroll-triggered effects
    const titleObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Trigger entrance animation
                titleContainer.classList.add('title-entered');
                
                // Stagger letter entrance
                titleLetters.forEach((letter, index) => {
                    setTimeout(() => {
                        letter.style.opacity = '1';
                        letter.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }
        });
    }, { threshold: 0.5 });
    
    titleObserver.observe(titleContainer);
    
    // Initialize letters as hidden for entrance animation
    titleLetters.forEach(letter => {
        letter.style.opacity = '0';
        letter.style.transform = 'translateY(20px)';
        letter.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    });
    
    // Performance optimization: Reduce animations on mobile
    if (window.innerWidth <= 768) {
        titleLetters.forEach(letter => {
            letter.style.animation = 'letterGradient 3s ease infinite';
        });
    }
    
    // Add double-click easter egg
    let clickCount = 0;
    titleBrand.addEventListener('click', function() {
        clickCount++;
        setTimeout(() => {
            if (clickCount === 1) {
                // Single click - do nothing special
            } else if (clickCount >= 2) {
                // Double click or more - special effect
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = '';
                }, 100);
                
                // Create rainbow particle explosion
                for (let i = 0; i < 15; i++) {
                    setTimeout(() => {
                        const colors = ['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'];
                        const particle = document.createElement('div');
                        particle.style.cssText = `
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            width: 4px;
                            height: 4px;
                            background: ${colors[Math.floor(Math.random() * colors.length)]};
                            border-radius: 50%;
                            pointer-events: none;
                            z-index: 10;
                            animation: rainbowExplosion 2s ease-out forwards;
                        `;
                        titleParticles.appendChild(particle);
                        
                        setTimeout(() => {
                            if (particle.parentNode) {
                                particle.parentNode.removeChild(particle);
                            }
                        }, 2000);
                    }, i * 100);
                }
            }
            clickCount = 0;
        }, 300);
    });
    
    // Add rainbow explosion animation
    if (!document.querySelector('#rainbow-particle-styles')) {
        const style = document.createElement('style');
        style.id = 'rainbow-particle-styles';
        style.textContent = `
            @keyframes rainbowExplosion {
                0% {
                    transform: translate(-50%, -50%) scale(0) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(2) rotate(720deg) translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
} 