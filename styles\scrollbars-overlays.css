/* ===================================
   Scrollbars and Overlay Systems
   =================================== */

/* Global Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-card);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

::-webkit-scrollbar-corner {
    background: var(--background-card);
}

/* Thin Scrollbars */
.thin-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Recent Files Container Scrollbar */
.recent-files-container::-webkit-scrollbar {
    width: 6px;
}

.recent-files-container::-webkit-scrollbar-track {
    background: var(--background-hover);
    border-radius: 3px;
}

.recent-files-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.recent-files-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Modal Body Scrollbar */
.custom-modal-body::-webkit-scrollbar {
    width: 8px;
}

.custom-modal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.custom-modal-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.custom-modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Categories List Scrollbar */
.categories-list {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.categories-list::-webkit-scrollbar {
    width: 4px;
}

.categories-list::-webkit-scrollbar-track {
    background: transparent;
}

.categories-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

.categories-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Dropdown Options Scrollbar */
.dropdown-options::-webkit-scrollbar {
    width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
}

.dropdown-options::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Grid Overlay */
.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 180, 255, 0.1) 50%,
        transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.grid-item:hover .grid-overlay {
    opacity: 1;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Custom Modal Overlay */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    overflow: hidden;
}

.custom-modal-overlay.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.loading-overlay.hidden {
    display: none;
}

.loading-overlay .loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-overlay p {
    font-size: 1.1rem;
    color: var(--text-primary);
    text-align: center;
    margin: 0;
}

/* Game Overlay */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(2px);
    z-index: 5;
}

.steam-game-card:hover .game-overlay {
    opacity: 1;
}

/* Delete Mode Center Overlay */
.delete-mode-center-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(239, 68, 68, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 15;
    backdrop-filter: blur(2px);
}

.steam-game-card.delete-mode:hover .delete-mode-center-overlay {
    opacity: 1;
}

.delete-mode-center-overlay .delete-icon {
    font-size: 3rem;
    color: white;
    animation: deleteIconPulse 1s infinite;
}

@keyframes deleteIconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Backdrop Filter Utilities */
.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

.backdrop-blur {
    backdrop-filter: blur(8px);
}

.backdrop-blur-md {
    backdrop-filter: blur(12px);
}

.backdrop-blur-lg {
    backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
    backdrop-filter: blur(24px);
}

/* Overlay Animations */
.overlay-fade-in {
    animation: overlayFadeIn 0.3s ease;
}

@keyframes overlayFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.overlay-slide-up {
    animation: overlaySlideUp 0.3s ease;
}

@keyframes overlaySlideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.overlay-scale-in {
    animation: overlayScaleIn 0.3s ease;
}

@keyframes overlayScaleIn {
    from {
        transform: scale(0.9);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Specific Modal Overlays */
.download-categorization-modal .modal-overlay {
    pointer-events: auto;
}

.browser-loading-modal .modal-overlay {
    pointer-events: none;
}

/* HWID Modal Overlay Specific */
.custom-modal-overlay .custom-modal-container.hwid-info-modal {
    max-width: 95vw !important;
    width: 95vw !important;
    min-width: 1200px !important;
    backdrop-filter: blur(10px);
}

.custom-modal-overlay .hwid-info-modal.custom-modal-container {
    max-width: 95vw !important;
    width: 95vw !important;
    min-width: 1200px !important;
    backdrop-filter: blur(10px);
}

/* Enhanced Download Modal */
.custom-modal-overlay.active .enhanced-download-mods-modal {
    transform: scale(1) !important;
    backdrop-filter: blur(15px);
}

.custom-modal-overlay.active .add-mods-modal-wide {
    transform: scale(1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    
    .custom-modal-overlay .custom-modal-container.hwid-info-modal,
    .custom-modal-overlay .hwid-info-modal.custom-modal-container {
        min-width: 95vw !important;
        max-width: 95vw !important;
    }
    
    .loading-overlay .loading-spinner {
        width: 40px;
        height: 40px;
        border-width: 3px;
    }
    
    .loading-overlay p {
        font-size: 1rem;
    }
    
    .delete-mode-center-overlay .delete-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }
    
    .backdrop-blur-lg,
    .backdrop-blur-xl {
        backdrop-filter: blur(8px);
    }
    
    .loading-overlay .loading-spinner {
        width: 35px;
        height: 35px;
    }
    
    .delete-mode-center-overlay .delete-icon {
        font-size: 2rem;
    }
}
