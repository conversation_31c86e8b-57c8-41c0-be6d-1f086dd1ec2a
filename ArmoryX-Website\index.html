<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Armory X - Ultimate PC Tool Suite | Download Free</title>
    <meta name="description" content="Download Armory X - The ultimate PC optimization tool suite. Speed test, junk cleaner, gaming tools, desktop widget and more. Free download available.">
    <meta name="keywords" content="PC optimization, speed test, junk cleaner, gaming tools, desktop widget, Windows tools, system optimization">
    <meta name="author" content="Armory X">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://armoryx.software/">
    <meta property="og:title" content="Armory X - The Ultimate PC Tool Suite">
    <meta property="og:description" content="The complete toolkit for PC optimization, gaming enhancement, and system management. Download free.">
    <meta property="og:image" content="https://armoryx.software/assets/armory-x-preview.png">
    
    <!-- Twitter Card Meta Tags -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://armoryx.software/">
    <meta property="twitter:title" content="Armory X - Ultimate PC Tool Suite">
    <meta property="twitter:description" content="The complete toolkit for PC optimization, gaming enhancement, and system management. Download free.">
    <meta property="twitter:image" content="https://armoryx.software/assets/armory-x-preview.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase Configuration -->
    <script type="module">
        (async function() {
            try {
                // Prevent infinite refresh loops
                if (window.firebaseInitAttempted) {
                    console.log('🛑 Firebase initialization already attempted');
                    return;
                }
                window.firebaseInitAttempted = true;
                
                console.log('🔧 Starting Firebase initialization...');
                
                // Firebase configuration - REPLACE WITH YOUR OWN FIREBASE CONFIG
                const firebaseConfig = {
                    apiKey: "AIzaSyAto6FZKgKctMQCtD-wrP8GxvXXEsQ4Ads",
                    authDomain: "armoryx-website.firebaseapp.com",
                    projectId: "armoryx-website",
                    storageBucket: "armoryx-website.firebasestorage.app",
                    messagingSenderId: "671977508420",
                    appId: "1:671977508420:web:34b1978ad9011d3d5c3611"
                };

                console.log('📋 Firebase config:', {
                    projectId: firebaseConfig.projectId,
                    authDomain: firebaseConfig.authDomain
                });

                // Check if Firebase config is properly set up
                const isFirebaseConfigured = firebaseConfig.apiKey !== "YOUR_API_KEY_HERE" && 
                                            firebaseConfig.projectId !== "YOUR_PROJECT_ID";

                console.log('🔍 Firebase configured:', isFirebaseConfigured);

                if (isFirebaseConfigured) {
                    console.log('📦 Loading Firebase modules...');
                    
                    // Test network connectivity first
                    console.log('🌐 Testing network connectivity to Firebase...');
                    try {
                        const connectivityTest = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', { method: 'HEAD' });
                        console.log('✅ Network connectivity to Firebase CDN confirmed');
                    } catch (error) {
                        console.warn('⚠️ Network connectivity issue detected:', error.message);
                    }
                    
                    // Import Firebase modules only if configured
                    const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                    console.log('✅ Firebase app module loaded');
                    
                    const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                    console.log('✅ Firebase auth module loaded');
                    
                    const { getFirestore, doc, setDoc, getDoc, getDocs, collection, addDoc, deleteDoc, query, where, orderBy, limit, onSnapshot, serverTimestamp, updateDoc, increment, arrayUnion, arrayRemove } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                    console.log('✅ Firebase firestore module loaded');

                    // Initialize Firebase
                    console.log('🔥 Initializing Firebase app...');
                    const app = initializeApp(firebaseConfig);
                    console.log('✅ Firebase app initialized');
                    
                    console.log('🔐 Initializing Firebase auth...');
                    const auth = getAuth(app);
                    console.log('✅ Firebase auth initialized');
                    
                    console.log('🗄️ Initializing Firestore...');
                    const db = getFirestore(app);
                    console.log('✅ Firestore initialized');

                    // Test Firebase connection
                    console.log('🔗 Testing Firebase Auth connection...');
                    try {
                        // Test auth connection by checking current user (won't fail if not logged in)
                        const currentUser = auth.currentUser;
                        console.log('✅ Firebase Auth connection successful');
                    } catch (error) {
                        console.warn('⚠️ Firebase Auth connection test failed:', error);
                    }

                    console.log('🔗 Testing Firestore connection...');
                    try {
                        // Test Firestore connection (will help identify permission issues)
                        const testRef = doc(db, 'test', 'connection');
                        console.log('✅ Firestore connection test ready (permissions will be tested on first operation)');
                    } catch (error) {
                        console.warn('⚠️ Firestore connection test failed:', error);
                    }

                    // Make Firebase available globally
                    window.firebase = { 
                        auth, 
                        db, 
                        createUserWithEmailAndPassword, 
                        signInWithEmailAndPassword, 
                        signOut, 
                        onAuthStateChanged, 
                        doc, 
                        setDoc, 
                        getDoc, 
                        getDocs,
                        collection, 
                        addDoc, 
                        deleteDoc,
                        query, 
                        where,
                        orderBy, 
                        limit, 
                        onSnapshot, 
                        serverTimestamp, 
                        updateDoc, 
                        increment,
                        arrayUnion,
                        arrayRemove
                    };
                    
                    console.log('✅ Firebase initialized successfully - all services ready');
                    console.log('🔥 Firebase project:', firebaseConfig.projectId);
                    console.log('🔥 Auth domain:', firebaseConfig.authDomain);
                    
                    // Signal successful Firebase initialization immediately
                    window.firebaseInitialized = true;
                    console.log('🏁 Firebase initialization SUCCESS - services ready for use');
                } else {
                    console.log('🔧 Firebase not configured - running in local development mode');
                    window.firebase = null;
                    // Signal that Firebase initialization is complete (but not configured)
                    window.firebaseInitialized = true;
                }
            } catch (error) {
                console.error('❌ Firebase initialization failed:', error);
                console.error('Error details:', error.message);
                console.error('Error stack:', error.stack);
                window.firebase = null;
                // Signal that Firebase initialization is complete (but failed)
                window.firebaseInitialized = true;
            }
            
            console.log('🏁 Firebase initialization process complete');
        })();
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#home" class="nav-logo">
                <img src="assets/Armory_X.ico" alt="Armory X" class="nav-logo-icon">
                <span>Armory X</span>
            </a>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="#download" class="nav-link">Download</a></li>
                <li><a href="#updates" class="nav-link">Updates</a></li>
                <li><a href="forums.html" class="nav-link">Forums</a></li>
                <li class="nav-account-container">
                    <a href="account.html" class="nav-link">Account</a>
                    <div class="account-dropdown">
                        <div class="account-dropdown-header">
                            <div class="account-dropdown-user">
                                <div class="account-dropdown-avatar">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="account-dropdown-info">
                                    <h4 id="dropdown-user-name">Guest User</h4>
                                    <p id="dropdown-user-status">Not logged in</p>
                                </div>
                            </div>
                        </div>
                        <div class="account-dropdown-menu">
                            <a href="account.html#profile" class="account-dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="account.html#inbox" class="account-dropdown-item">
                                <i class="fas fa-inbox"></i> Inbox
                            </a>
                            <a href="account.html#friends" class="account-dropdown-item">
                                <i class="fas fa-users"></i> Friends
                            </a>
                            <a href="account.html#premium" class="account-dropdown-item" id="dropdown-premium-link">
                                <i class="fas fa-star"></i> Premium
                            </a>
                            <a href="account.html#settings" class="account-dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <div class="account-dropdown-divider"></div>
                            <a href="#" class="account-dropdown-item account-dropdown-logout" id="dropdown-logout">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </li>
                <li><a href="http://discord.gg/uq4Zs2G57g" class="nav-link discord-link" target="_blank">
                    <i class="fab fa-discord"></i> Discord
                </a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content reveal-on-scroll">
                <h1 class="hero-title">
                    <div class="title-container">
                        <div class="title-background-text" aria-hidden="true">ARMORY X</div>
                        <div class="title-main">
                            <span class="title-brand">
                                <span class="title-letter">A</span>
                                <span class="title-letter">R</span>
                                <span class="title-letter">M</span>
                                <span class="title-letter">O</span>
                                <span class="title-letter">R</span>
                                <span class="title-letter">Y</span>
                                <span class="title-space"></span>
                                <span class="title-letter">X</span>
                            </span>
                            <div class="title-particles" aria-hidden="true"></div>
                        </div>
                        <div class="title-subtitle">
                            <span class="subtitle-line-1">The Ultimate PC Tool Suite</span>
                            <span class="subtitle-line-2">for Gamers, by a Gamer</span>
                        </div>
                        <div class="title-glow" aria-hidden="true"></div>
                    </div>
                </h1>
                <p class="hero-description reveal-on-scroll">
                    The complete toolkit for PC optimization, gaming enhancement, and system management. 
                    Experience unmatched performance with our all-in-one solution.
                </p>
                <div class="hero-buttons reveal-on-scroll">
                    <a href="#download" class="btn btn-primary glow-effect">
                        <i class="fas fa-download"></i> Download Now
                    </a>
                    <a href="#features" class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i> Learn More
                    </a>
                </div>
                <div class="hero-stats reveal-on-scroll">
                    <div class="stat">
                        <span class="stat-number" id="active-users">10K+</span>
                        <span class="stat-label">Active Users</span>
                        <div class="live-indicator pulse-glow"></div>
                    </div>
                    <div class="stat">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Built-in Tools</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Support</span>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <div class="hero-widget">
                    <div class="widget-preview">
                        <img src="assets/dashboard-preview.png" alt="ArmoryX Dashboard Preview" class="dashboard-preview-image">
                        <div class="widget-pulse"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful Features</h2>
                <p class="section-description">Everything you need to optimize and enhance your PC experience</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3>Speed Test</h3>
                    <p>Advanced internet speed testing with detailed analytics and real-time monitoring</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-broom"></i>
                    </div>
                    <h3>Junk Cleaner</h3>
                    <p>Intelligent system cleanup to free up space and improve performance</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3>Gaming Tools</h3>
                    <p>Performance optimization and mod management for your favorite games</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <h3>Desktop Widget</h3>
                    <p>Customizable desktop widget for quick access to all your tools</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>Quick Launch</h3>
                    <p>Instant access to your applications and files with smart shortcuts</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>System Security</h3>
                    <p>Built-in security tools and Windows Defender management</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="download-content">
                <div class="download-info">
                    <h2 class="section-title">Download Armory X</h2>
                    <p class="section-description">Get the latest version with all the newest features and improvements</p>
                    
                    <div class="version-info">
                        <div class="version-badge">
                            <span class="version-label">Latest Version</span>
                            <span class="version-number" id="current-version">v0.2.4b</span>
                        </div>
                        <div class="version-details">
                            <p><i class="fas fa-calendar"></i> Released: <span id="release-date">Loading...</span></p>
                            <p><i class="fas fa-file-alt"></i> Size: <span id="file-size">Loading...</span></p>
                            <p><i class="fas fa-windows"></i> Compatible with Windows 10/11</p>
                        </div>
                    </div>

                    <div class="download-buttons">
                        <a href="#" class="btn btn-primary btn-large" id="download-btn">
                            <i class="fas fa-download"></i> Download Armory X
                        </a>
                        <p class="download-note">Free to use • Signup required</p>
                    </div>
                </div>
                <div class="download-preview">
                    <div class="download-icon-display">
                        <img src="assets/Armory_X.ico" alt="Armory X" class="download-main-icon">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Updates Section -->
    <section id="updates" class="updates">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Latest Updates</h2>
                <p class="section-description">Stay up to date with the latest features and improvements</p>
            </div>
            
            <div class="updates-container">
                <div class="updates-sidebar">
                    <h3>Planned Features</h3>
                    <div class="planned-features" id="planned-features">
                        <!-- Planned features will be loaded here -->
                    </div>
                </div>
                
                <div class="updates-timeline">
                    <div class="timeline" id="updates-timeline">
                        <!-- Updates will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/Armory_X.ico" alt="Armory X" class="footer-logo-icon">
                        <span>Armory X</span>
                    </div>
                    <p>The ultimate PC tool suite for optimization, gaming, and system management.</p>
                    <div class="social-links">
                        <a href="http://discord.gg/uq4Zs2G57g" target="_blank">
                            <i class="fab fa-discord"></i>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#download">Download</a></li>
                        <li><a href="#updates">Updates</a></li>
                        <li><a href="#account">Account</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Bug Reports</a></li>
                        <li><a href="#">Feature Requests</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Armory X. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Google Analytics (replace with your tracking ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        // Replace GA_TRACKING_ID with your actual Google Analytics tracking ID
        // gtag('config', 'GA_TRACKING_ID');
    </script>

    <script src="script.js"></script>
    <script src="animations.js"></script>
    <script src="notifications.js"></script>
    <!-- License System Scripts -->
    <script src="license-key-system.js"></script>
</body>
</html> 