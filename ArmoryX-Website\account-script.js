// Account Page Script
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Firebase initialization before setting up account
    waitForFirebaseInit().then(() => {
        initializeAccount();
    }).catch(error => {
        console.warn('Firebase initialization failed, continuing with fallback mode:', error);
        initializeAccount();
    });
});

// Wait for Firebase to be initialized (same function as other scripts)
function waitForFirebaseInit() {
    return new Promise((resolve) => {
        // Check if Firebase is already available
        if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
            console.log('🏎️ Firebase already available for account, proceeding immediately');
            resolve();
            return;
        }
        
        let attempts = 0;
        const maxAttempts = 150; // 15 seconds at 100ms intervals
        
        const checkInterval = setInterval(() => {
            attempts++;
            
            // Check for either firebaseInitialized flag OR firebase object
            if (typeof window.firebaseInitialized !== 'undefined' || window.firebase) {
                clearInterval(checkInterval);
                console.log(`✅ Firebase ready for account after ${attempts * 100}ms`);
                resolve();
                return;
            }
            
            // Timeout after max attempts
            if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                console.log('🕒 Firebase initialization timeout in account - proceeding without Firebase');
                console.log('⚠️ Account will use fallback authentication');
                window.firebase = null;
                resolve();
            }
        }, 100);
    });
}

function initializeAccount() {
    // Check authentication status
    if (window.firebase && window.firebase.auth && window.firebase.onAuthStateChanged) {
        console.log('🔥 Account using Firebase authentication');
        const { auth, onAuthStateChanged } = window.firebase;
        
        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log('User authenticated in account:', user.email);
                showAccountContent(user);
                loadUserAccountData(user);
            } else {
                console.log('User not authenticated');
                showLoginRequired();
            }
        });
    } else {
        console.log('🔧 Account using fallback authentication');
        // Fallback authentication check
        const isLoggedIn = checkLegacyAuth();
        if (isLoggedIn) {
            showAccountContent();
            loadLegacyAccountData();
        } else {
            showLoginRequired();
        }
    }
    
    setupAccountInteractions();
}

function checkLegacyAuth() {
    return localStorage.getItem('armoryX_user_token') !== null;
}

function showAccountContent(user = null) {
    const loginRequired = document.getElementById('login-required');
    const accountContent = document.getElementById('account-content');
    
    if (loginRequired) loginRequired.style.display = 'none';
    if (accountContent) accountContent.style.display = 'block';
}

function showLoginRequired() {
    const loginRequired = document.getElementById('login-required');
    const accountContent = document.getElementById('account-content');
    
    if (loginRequired) loginRequired.style.display = 'block';
    if (accountContent) accountContent.style.display = 'none';
}

async function loadUserAccountData(user) {
    if (!user) return;
    
    try {
        // Load user profile data
        const profileEmail = document.getElementById('profile-email');
        const profileJoinDate = document.getElementById('profile-join-date');
        const profileDisplayName = document.getElementById('profile-display-name');
        
        if (profileEmail) profileEmail.value = user.email;
        if (profileJoinDate) {
            const joinDate = user.metadata.creationTime ? 
                new Date(user.metadata.creationTime).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                }) : 'Unknown';
            profileJoinDate.value = joinDate;
        }
        
        // Check and display email verification status
        updateEmailVerificationStatus(user);
        
        // Load display name from Firestore
        if (window.firebase && window.firebase.db) {
            const { db, doc, getDoc } = window.firebase;
            
            try {
                const userDocRef = doc(db, 'users', user.uid);
                const userDoc = await getDoc(userDocRef);
                
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    if (profileDisplayName && userData.displayName) {
                        profileDisplayName.value = userData.displayName;
                    }
                    
                    // Load settings
                    loadUserSettings(userData);
                }
            } catch (error) {
                console.log('Error loading user data:', error);
            }
        }
        
    } catch (error) {
        console.error('Error loading account data:', error);
    }
}

function loadLegacyAccountData() {
    const userEmail = localStorage.getItem('armoryX_user_email');
    const profileEmail = document.getElementById('profile-email');
    const profileJoinDate = document.getElementById('profile-join-date');
    
    if (profileEmail && userEmail) profileEmail.value = userEmail;
    if (profileJoinDate) profileJoinDate.value = 'Unknown';
    
    // Load legacy settings
    loadLegacySettings();
}

function loadUserSettings(userData) {
    // Load toggle settings
    const cloudSyncToggle = document.getElementById('cloud-sync-toggle');
    const emailNotificationsToggle = document.getElementById('email-notifications-toggle');
    const privacyModeToggle = document.getElementById('privacy-mode-toggle');
    
    if (cloudSyncToggle && userData.settings) {
        cloudSyncToggle.checked = userData.settings.cloudSync || false;
    }
    if (emailNotificationsToggle && userData.settings) {
        emailNotificationsToggle.checked = userData.settings.emailNotifications !== false; // Default true
    }
    if (privacyModeToggle && userData.settings) {
        privacyModeToggle.checked = userData.settings.privacyMode || false;
    }
}

function loadLegacySettings() {
    // Load settings from localStorage
    const settings = JSON.parse(localStorage.getItem('armoryX_settings') || '{}');
    
    const cloudSyncToggle = document.getElementById('cloud-sync-toggle');
    const emailNotificationsToggle = document.getElementById('email-notifications-toggle');
    const privacyModeToggle = document.getElementById('privacy-mode-toggle');
    
    if (cloudSyncToggle) cloudSyncToggle.checked = settings.cloudSync || false;
    if (emailNotificationsToggle) emailNotificationsToggle.checked = settings.emailNotifications !== false;
    if (privacyModeToggle) privacyModeToggle.checked = settings.privacyMode || false;
}

function setupAccountInteractions() {
    // Tab switching
    document.addEventListener('click', function(e) {
        if (e.target.closest('.account-tab')) {
            const tab = e.target.closest('.account-tab');
            const tabId = tab.getAttribute('data-tab');
            switchAccountTab(tabId);
        }
        
        // Auth form tabs
        if (e.target.closest('.tab-btn')) {
            const tab = e.target.closest('.tab-btn');
            const tabId = tab.getAttribute('data-tab');
            switchAuthTab(tabId);
        }
    });
    
    // Form submissions
    const loginForm = document.getElementById('auth-login-form');
    const registerForm = document.getElementById('auth-register-form');
    const changePasswordForm = document.getElementById('change-password-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', handleChangePassword);
    }
    
    // Button clicks
    const saveProfileBtn = document.getElementById('save-profile-btn');
    const validateLicenseBtn = document.getElementById('validate-license-btn');
    const logoutBtn = document.getElementById('logout-btn');
    const resendVerificationBtn = document.getElementById('resend-verification-btn');
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    
    if (saveProfileBtn) {
        saveProfileBtn.addEventListener('click', handleSaveProfile);
    }
    if (validateLicenseBtn) {
        validateLicenseBtn.addEventListener('click', handleValidateLicense);
    }
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    if (resendVerificationBtn) {
        resendVerificationBtn.addEventListener('click', handleResendVerification);
    }
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }
    
    // Settings toggles
    const settingsToggles = document.querySelectorAll('.toggle-switch input');
    settingsToggles.forEach(toggle => {
        toggle.addEventListener('change', handleSettingChange);
    });
}

// Tab Management
function switchAccountTab(tabId) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.account-tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.account-tab-content').forEach(content => content.classList.remove('active'));
    
    // Add active class to clicked tab and corresponding content
    const activeTab = document.querySelector(`[data-tab="${tabId}"]`);
    const activeContent = document.getElementById(`${tabId}-tab`);
    
    if (activeTab) activeTab.classList.add('active');
    if (activeContent) activeContent.classList.add('active');
    
    // Load content for specific tabs
    if (tabId === 'inbox') {
        loadInboxContent();
    } else if (tabId === 'friends') {
        loadFriendsContent();
    }
}

function switchAuthTab(tabId) {
    // Remove active class from all auth tabs and panes
    document.querySelectorAll('.tab-btn').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
    
    // Add active class to clicked tab and corresponding pane
    const activeTab = document.querySelector(`[data-tab="${tabId}"]`);
    const activePane = document.getElementById(`${tabId}-form`);
    
    if (activeTab) activeTab.classList.add('active');
    if (activePane) activePane.classList.add('active');
}

// Authentication Handlers
async function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email').trim();
    const password = formData.get('password');
    
    if (!email || !password) {
        showAccountMessage('Please fill in all fields.', 'error');
        return;
    }
    
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
    submitBtn.disabled = true;
    
    try {
        if (window.firebase && window.firebase.auth) {
            const { auth, signInWithEmailAndPassword } = window.firebase;
            await signInWithEmailAndPassword(auth, email, password);
            showAccountMessage('Login successful!', 'success');
        } else {
            // Fallback authentication
            localStorage.setItem('armoryX_user_token', 'demo_token');
            localStorage.setItem('armoryX_user_email', email);
            showAccountMessage('Login successful! (Demo mode)', 'success');
            setTimeout(() => location.reload(), 1000);
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showAccountMessage(`Login failed: ${error.message}`, 'error');
        
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

async function handleRegister(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const displayName = formData.get('display-name').trim();
    const email = formData.get('email').trim();
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm-password');
    
    if (!displayName || !email || !password || !confirmPassword) {
        showAccountMessage('Please fill in all fields.', 'error');
        return;
    }
    
    if (displayName.length < 2 || displayName.length > 30) {
        showAccountMessage('Display name must be between 2 and 30 characters.', 'error');
        return;
    }
    
    // Check for inappropriate characters in display name
    if (!/^[a-zA-Z0-9_\-\s]+$/.test(displayName)) {
        showAccountMessage('Display name can only contain letters, numbers, spaces, hyphens, and underscores.', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showAccountMessage('Passwords do not match.', 'error');
        return;
    }
    
    if (password.length < 6) {
        showAccountMessage('Password must be at least 6 characters long.', 'error');
        return;
    }
    
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
    submitBtn.disabled = true;
    
    try {
        if (window.firebase && window.firebase.auth) {
            const { auth, createUserWithEmailAndPassword, db, doc, setDoc, serverTimestamp, sendEmailVerification } = window.firebase;
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            
            // Send email verification
            await sendEmailVerification(userCredential.user);
            
            // Create user document in Firestore
            if (db) {
                await setDoc(doc(db, 'users', userCredential.user.uid), {
                    email: email,
                    displayName: displayName,
                    createdAt: serverTimestamp(),
                    lastSeen: serverTimestamp(),
                    emailVerified: false,
                    reputation: 0,
                    role: 'user',
                    friends: [],
                    friendRequests: [],
                    settings: {
                        cloudSync: false,
                        emailNotifications: true,
                        privacyMode: false,
                        showOnlineStatus: true
                    }
                });
            }
            
            showAccountMessage('Account created successfully! Please check your email to verify your account.', 'success');
        } else {
            // Fallback registration
            localStorage.setItem('armoryX_user_token', 'demo_token');
            localStorage.setItem('armoryX_user_email', email);
            showAccountMessage('Account created successfully! (Demo mode)', 'success');
            setTimeout(() => location.reload(), 1000);
        }
        
    } catch (error) {
        console.error('Registration error:', error);
        showAccountMessage(`Registration failed: ${error.message}`, 'error');
        
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

async function handleChangePassword(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const currentPassword = formData.get('current-password');
    const newPassword = formData.get('new-password');
    const confirmNewPassword = formData.get('confirm-new-password');
    
    if (!currentPassword || !newPassword || !confirmNewPassword) {
        showAccountMessage('Please fill in all fields.', 'error');
        return;
    }
    
    if (newPassword !== confirmNewPassword) {
        showAccountMessage('New passwords do not match.', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showAccountMessage('New password must be at least 6 characters long.', 'error');
        return;
    }
    
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    submitBtn.disabled = true;
    
    try {
        // TODO: Implement password change with Firebase Auth
        showAccountMessage('Password change functionality coming soon!', 'info');
        
        // Clear form
        form.reset();
        
    } catch (error) {
        console.error('Password change error:', error);
        showAccountMessage(`Password change failed: ${error.message}`, 'error');
    } finally {
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

async function handleSaveProfile() {
    const displayName = document.getElementById('profile-display-name').value.trim();
    
    if (!displayName) {
        showAccountMessage('Please enter a display name.', 'error');
        return;
    }
    
    if (displayName.length < 2 || displayName.length > 30) {
        showAccountMessage('Display name must be between 2 and 30 characters.', 'error');
        return;
    }
    
    const saveBtn = document.getElementById('save-profile-btn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    saveBtn.disabled = true;
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, doc, updateDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                await updateDoc(doc(db, 'users', user.uid), {
                    displayName: displayName,
                    updatedAt: new Date()
                });
                
                showAccountMessage('Profile saved successfully!', 'success');
            }
        } else {
            // Fallback save
            localStorage.setItem('armoryX_display_name', displayName);
            showAccountMessage('Profile saved successfully! (Demo mode)', 'success');
        }
        
    } catch (error) {
        console.error('Profile save error:', error);
        showAccountMessage(`Failed to save profile: ${error.message}`, 'error');
    } finally {
        // Restore button
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    }
}

async function handleValidateLicense() {
    const licenseKey = document.getElementById('license-key-input').value.trim();
    
    if (!licenseKey) {
        showLicenseStatus('Please enter a license key.', 'error');
        return;
    }
    
    const validateBtn = document.getElementById('validate-license-btn');
    const originalText = validateBtn.innerHTML;
    validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';
    validateBtn.disabled = true;
    
    try {
        // Simulate license validation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Mock validation logic
        if (licenseKey.toLowerCase().includes('premium') || licenseKey.toLowerCase().includes('armory')) {
            showLicenseStatus('License key validated successfully! Premium features activated.', 'success');
        } else {
            showLicenseStatus('Invalid license key. Please check and try again.', 'error');
        }
        
    } catch (error) {
        console.error('License validation error:', error);
        showLicenseStatus(`Validation failed: ${error.message}`, 'error');
    } finally {
        // Restore button
        validateBtn.innerHTML = originalText;
        validateBtn.disabled = false;
    }
}

async function handleSettingChange(event) {
    const setting = event.target.id;
    const value = event.target.checked;
    
    console.log(`Setting changed: ${setting} = ${value}`);
    
    try {
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            const { auth, db, doc, updateDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                const settingKey = setting.replace('-toggle', '').replace('-', '');
                await updateDoc(doc(db, 'users', user.uid), {
                    [`settings.${settingKey}`]: value,
                    updatedAt: new Date()
                });
            }
        } else {
            // Fallback save to localStorage
            const settings = JSON.parse(localStorage.getItem('armoryX_settings') || '{}');
            const settingKey = setting.replace('-toggle', '').replace('-', '');
            settings[settingKey] = value;
            localStorage.setItem('armoryX_settings', JSON.stringify(settings));
        }
        
        // Show brief success message
        showAccountMessage(`${setting.replace('-toggle', '').replace('-', ' ')} setting updated.`, 'success', 2000);
        
    } catch (error) {
        console.error('Settings update error:', error);
        showAccountMessage(`Failed to update setting: ${error.message}`, 'error');
        
        // Revert toggle
        event.target.checked = !value;
    }
}

async function handleLogout() {
    try {
        if (window.firebase && window.firebase.auth) {
            const { auth, signOut } = window.firebase;
            await signOut(auth);
        } else {
            // Fallback logout
            localStorage.removeItem('armoryX_user_token');
            localStorage.removeItem('armoryX_user_email');
        }
        
        showAccountMessage('Logged out successfully!', 'success');
        setTimeout(() => location.reload(), 1000);
        
    } catch (error) {
        console.error('Logout error:', error);
        showAccountMessage(`Logout failed: ${error.message}`, 'error');
    }
}

// Utility Functions
function showAccountMessage(message, type = 'info', duration = 5000) {
    // Remove existing messages
    const existingMessage = document.querySelector('.account-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `account-message ${type}`;
    messageElement.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        <span>${message}</span>
        <button class="message-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles for the message
    messageElement.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        border-left: 4px solid;
        color: var(--text-primary);
        font-weight: 500;
        box-shadow: var(--shadow-lg);
        animation: slideIn 0.3s ease;
        max-width: 400px;
        background: ${type === 'success' ? 'rgba(16, 185, 129, 0.15)' : type === 'error' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(59, 130, 246, 0.15)'};
        border-left-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;
    
    // Add to page
    document.body.appendChild(messageElement);
    
    // Auto-remove after duration
    setTimeout(() => {
        if (messageElement.parentElement) {
            messageElement.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => messageElement.remove(), 300);
        }
    }, duration);
}

function showLicenseStatus(message, type) {
    const licenseStatus = document.getElementById('license-status');
    if (!licenseStatus) return;
    
    licenseStatus.textContent = message;
    licenseStatus.className = `license-status ${type}`;
    licenseStatus.style.display = 'block';
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        licenseStatus.style.display = 'none';
    }, 5000);
}

// Add required styles
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .message-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: background 0.3s ease;
    }
    
    .message-close:hover {
        background: rgba(255, 255, 255, 0.1);
    }
`;
document.head.appendChild(style);

// ============================================
// MODERATION SYSTEM FUNCTIONS
// ============================================

// Check user role and show/hide moderation tab
async function checkUserRoleAndShowModerationTab(user) {
    if (!window.firebase || !window.firebase.db || !user) {
        return;
    }
    
    try {
        const { db, doc, getDoc } = window.firebase;
        const userDocRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
            const userData = userDoc.data();
            const userRole = userData.role || 'user';
            
            // Show moderation tab if user is moderator or admin
            if (userRole === 'moderator' || userRole === 'admin') {
                const moderationTabButton = document.getElementById('moderation-tab-button');
                if (moderationTabButton) {
                    moderationTabButton.style.display = 'flex';
                }
                
                // Initialize moderation panel
                initializeModerationPanel(userRole, userData);
            }
        }
    } catch (error) {
        console.error('Error checking user role:', error);
    }
}

// Initialize moderation panel
function initializeModerationPanel(userRole, userData) {
    // Update role badge
    const roleDisplay = document.getElementById('user-role-display');
    const roleBadge = document.getElementById('moderation-role-badge');
    
    if (roleDisplay) {
        roleDisplay.textContent = userRole.charAt(0).toUpperCase() + userRole.slice(1);
    }
    
    if (roleBadge) {
        roleBadge.classList.add(userRole);
    }
    
    // Show/hide admin-only sections
    if (userRole === 'admin') {
        const roleManagementCard = document.getElementById('role-management-card');
        if (roleManagementCard) {
            roleManagementCard.style.display = 'block';
        }
    }
    
    // Load moderation data
    loadModerationData();
    
    // Setup moderation event listeners
    setupModerationEventListeners();
}

// Setup event listeners for moderation interface
function setupModerationEventListeners() {
    // User search
    const searchUsersBtn = document.getElementById('search-users-btn');
    const userSearchInput = document.getElementById('user-search-input');
    
    if (searchUsersBtn) {
        searchUsersBtn.addEventListener('click', searchUsers);
    }
    
    if (userSearchInput) {
        userSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
    }
    
    // Role assignment
    const assignRoleBtn = document.getElementById('assign-role-btn');
    if (assignRoleBtn) {
        assignRoleBtn.addEventListener('click', assignUserRole);
    }
    
    // Quick actions
    const refreshForumBtn = document.getElementById('refresh-forum-btn');
    const exportLogsBtn = document.getElementById('export-logs-btn');
    const viewForumBtn = document.getElementById('view-forum-btn');
    
    if (refreshForumBtn) {
        refreshForumBtn.addEventListener('click', refreshModerationData);
    }
    
    if (exportLogsBtn) {
        exportLogsBtn.addEventListener('click', exportModerationLogs);
    }
    
    if (viewForumBtn) {
        viewForumBtn.addEventListener('click', () => {
            window.open('forums.html', '_blank');
        });
    }
}

// Load moderation data
async function loadModerationData() {
    await Promise.all([
        loadModerationStats(),
        loadBannedUsers(),
        loadReportedContent()
    ]);
}

// Load moderation statistics
async function loadModerationStats() {
    if (!window.firebase || !window.firebase.db) {
        return;
    }
    
    try {
        const { db, collection, getDocs, query, where } = window.firebase;
        
        // Get total users
        const usersSnapshot = await getDocs(collection(db, 'users'));
        const totalUsers = usersSnapshot.size;
        
        // Get banned users count
        const bannedUsersQuery = query(collection(db, 'users'), where('banned', '==', true));
        const bannedUsersSnapshot = await getDocs(bannedUsersQuery);
        const bannedUsersCount = bannedUsersSnapshot.size;
        
        // Get total posts count
        const postsSnapshot = await getDocs(collection(db, 'forum_posts'));
        const totalPosts = postsSnapshot.size;
        
        // Get pending reports count (if reports collection exists)
        let pendingReports = 0;
        try {
            const reportsQuery = query(collection(db, 'reports'), where('status', '==', 'pending'));
            const reportsSnapshot = await getDocs(reportsQuery);
            pendingReports = reportsSnapshot.size;
        } catch (error) {
            // Reports collection might not exist yet
            console.log('Reports collection not found or empty:', error);
        }
        
        // Update UI
        updateStatElement('total-users', totalUsers);
        updateStatElement('banned-users-count', bannedUsersCount);
        updateStatElement('total-posts', totalPosts);
        updateStatElement('pending-reports', pendingReports);
        
    } catch (error) {
        console.error('Error loading moderation stats:', error);
        
        // Fallback to demo numbers
        updateStatElement('total-users', 0);
        updateStatElement('banned-users-count', 0);
        updateStatElement('total-posts', 0);
        updateStatElement('pending-reports', 0);
    }
}

// Update stat element
function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

// Search users
async function searchUsers() {
    const searchInput = document.getElementById('user-search-input');
    const searchResults = document.getElementById('user-search-results');
    const searchTerm = searchInput.value.trim();
    
    if (!searchTerm) {
        showModerationMessage('Please enter a search term', 'error');
        return;
    }
    
    if (!window.firebase || !window.firebase.db) {
        showModerationMessage('Firebase database not available', 'error');
        return;
    }
    
    searchResults.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Searching users...</div>';
    
    try {
        const { db, collection, getDocs } = window.firebase;
        const usersSnapshot = await getDocs(collection(db, 'users'));
        
        const matchingUsers = [];
        usersSnapshot.forEach((doc) => {
            const userData = doc.data();
            const userId = doc.id;
            
            // Search by email or display name
            if (userData.email && userData.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                userData.displayName && userData.displayName.toLowerCase().includes(searchTerm.toLowerCase())) {
                matchingUsers.push({ id: userId, ...userData });
            }
        });
        
        displayUserSearchResults(matchingUsers);
        
    } catch (error) {
        console.error('Error searching users:', error);
        searchResults.innerHTML = '<div class="loading-message">Error searching users</div>';
    }
}

// Display user search results
function displayUserSearchResults(users) {
    const searchResults = document.getElementById('user-search-results');
    
    if (users.length === 0) {
        searchResults.innerHTML = '<div class="loading-message">No users found</div>';
        return;
    }
    
    const resultsHTML = users.map(user => `
        <div class="user-result-item">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <h4>${user.displayName || 'No display name'}</h4>
                    <p>${user.email}</p>
                    <span class="user-role-badge ${user.role || 'user'}">${user.role || 'user'}</span>
                </div>
            </div>
            <div class="user-actions">
                ${user.banned ? 
                    `<button class="btn-unban" onclick="unbanUser('${user.id}', '${user.email}')">
                        <i class="fas fa-user-check"></i> Unban
                    </button>` :
                    `<button class="btn-ban" onclick="banUser('${user.id}', '${user.email}')">
                        <i class="fas fa-ban"></i> Ban
                    </button>`
                }
            </div>
        </div>
    `).join('');
    
    searchResults.innerHTML = resultsHTML;
}

// Ban user function
async function banUser(userId, userEmail) {
    const reason = prompt('Enter ban reason:');
    if (!reason) return;
    
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, doc, updateDoc, addDoc, collection, serverTimestamp } = window.firebase;
        
        // Update user document
        await updateDoc(doc(db, 'users', userId), {
            banned: true,
            bannedAt: serverTimestamp(),
            bannedReason: reason,
            bannedBy: window.firebase.auth.currentUser.uid
        });
        
        // Log moderation action
        await addDoc(collection(db, 'moderation_logs'), {
            action: 'ban',
            targetUserId: userId,
            targetUserEmail: userEmail,
            moderatorId: window.firebase.auth.currentUser.uid,
            moderatorEmail: window.firebase.auth.currentUser.email,
            reason: reason,
            timestamp: serverTimestamp()
        });
        
        showModerationMessage(`User ${userEmail} has been banned`, 'success');
        
        // Refresh data
        loadModerationData();
        searchUsers(); // Refresh search results
        
    } catch (error) {
        console.error('Error banning user:', error);
        showModerationMessage(`Failed to ban user: ${error.message}`, 'error');
    }
}

// Unban user function
async function unbanUser(userId, userEmail) {
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, doc, updateDoc, addDoc, collection, serverTimestamp } = window.firebase;
        
        // Update user document
        await updateDoc(doc(db, 'users', userId), {
            banned: false,
            bannedAt: null,
            bannedReason: null,
            bannedBy: null,
            unbannedAt: serverTimestamp(),
            unbannedBy: window.firebase.auth.currentUser.uid
        });
        
        // Log moderation action
        await addDoc(collection(db, 'moderation_logs'), {
            action: 'unban',
            targetUserId: userId,
            targetUserEmail: userEmail,
            moderatorId: window.firebase.auth.currentUser.uid,
            moderatorEmail: window.firebase.auth.currentUser.email,
            timestamp: serverTimestamp()
        });
        
        showModerationMessage(`User ${userEmail} has been unbanned`, 'success');
        
        // Refresh data
        loadModerationData();
        searchUsers(); // Refresh search results
        
    } catch (error) {
        console.error('Error unbanning user:', error);
        showModerationMessage(`Failed to unban user: ${error.message}`, 'error');
    }
}

// Assign user role (admin only)
async function assignUserRole() {
    const userEmail = document.getElementById('role-user-email').value.trim();
    const newRole = document.getElementById('new-role-select').value;
    
    if (!userEmail) {
        showRoleAssignmentStatus('Please enter a user email', 'error');
        return;
    }
    
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, collection, getDocs, query, where, doc, updateDoc, addDoc, serverTimestamp } = window.firebase;
        
        // Find user by email
        const usersQuery = query(collection(db, 'users'), where('email', '==', userEmail));
        const userSnapshot = await getDocs(usersQuery);
        
        if (userSnapshot.empty) {
            showRoleAssignmentStatus('User not found', 'error');
            return;
        }
        
        const userDoc = userSnapshot.docs[0];
        const userId = userDoc.id;
        
        // Update user role
        await updateDoc(doc(db, 'users', userId), {
            role: newRole,
            roleAssignedAt: serverTimestamp(),
            roleAssignedBy: window.firebase.auth.currentUser.uid
        });
        
        // Log moderation action
        await addDoc(collection(db, 'moderation_logs'), {
            action: 'role_assignment',
            targetUserId: userId,
            targetUserEmail: userEmail,
            newRole: newRole,
            moderatorId: window.firebase.auth.currentUser.uid,
            moderatorEmail: window.firebase.auth.currentUser.email,
            timestamp: serverTimestamp()
        });
        
        showRoleAssignmentStatus(`Role "${newRole}" assigned to ${userEmail}`, 'success');
        
        // Clear form
        document.getElementById('role-user-email').value = '';
        document.getElementById('new-role-select').value = 'user';
        
        // Refresh data
        loadModerationData();
        
    } catch (error) {
        console.error('Error assigning role:', error);
        showRoleAssignmentStatus(`Failed to assign role: ${error.message}`, 'error');
    }
}

// Load banned users
async function loadBannedUsers() {
    const bannedUsersList = document.getElementById('banned-users-list');
    
    if (!window.firebase || !window.firebase.db) {
        bannedUsersList.innerHTML = '<div class="loading-message">Firebase not available</div>';
        return;
    }
    
    try {
        const { db, collection, getDocs, query, where } = window.firebase;
        const bannedUsersQuery = query(collection(db, 'users'), where('banned', '==', true));
        const bannedUsersSnapshot = await getDocs(bannedUsersQuery);
        
        if (bannedUsersSnapshot.empty) {
            bannedUsersList.innerHTML = '<div class="loading-message">No banned users</div>';
            return;
        }
        
        const bannedUsersHTML = [];
        bannedUsersSnapshot.forEach((doc) => {
            const userData = doc.data();
            bannedUsersHTML.push(`
                <div class="banned-user-item">
                    <div class="ban-info">
                        <h4>${userData.displayName || userData.email}</h4>
                        <p>Banned: ${userData.bannedAt ? new Date(userData.bannedAt.toDate()).toLocaleString() : 'Unknown'}</p>
                        ${userData.bannedReason ? `<div class="ban-reason">${userData.bannedReason}</div>` : ''}
                    </div>
                    <button class="btn-unban" onclick="unbanUser('${doc.id}', '${userData.email}')">
                        <i class="fas fa-user-check"></i> Unban
                    </button>
                </div>
            `);
        });
        
        bannedUsersList.innerHTML = bannedUsersHTML.join('');
        
    } catch (error) {
        console.error('Error loading banned users:', error);
        bannedUsersList.innerHTML = '<div class="loading-message">Error loading banned users</div>';
    }
}

// Load reported content
async function loadReportedContent() {
    const reportedContentList = document.getElementById('reported-content-list');
    
    if (!window.firebase || !window.firebase.db) {
        reportedContentList.innerHTML = '<div class="loading-message">Firebase not available</div>';
        return;
    }
    
    try {
        const { db, collection, getDocs, query, where, orderBy } = window.firebase;
        const reportsQuery = query(
            collection(db, 'reports'), 
            where('status', '==', 'pending'),
            orderBy('createdAt', 'desc')
        );
        const reportsSnapshot = await getDocs(reportsQuery);
        
        if (reportsSnapshot.empty) {
            reportedContentList.innerHTML = '<div class="loading-message">No pending reports</div>';
            return;
        }
        
        const reportsHTML = [];
        reportsSnapshot.forEach((doc) => {
            const reportData = doc.data();
            reportsHTML.push(`
                <div class="report-item">
                    <div class="report-header">
                        <span class="report-type">${reportData.type || 'Content'}</span>
                        <span class="report-date">${reportData.createdAt ? new Date(reportData.createdAt.toDate()).toLocaleString() : 'Unknown'}</span>
                    </div>
                    <div class="report-content">
                        <strong>Reason:</strong> ${reportData.reason || 'No reason provided'}
                        ${reportData.description ? `<br><strong>Description:</strong> ${reportData.description}` : ''}
                    </div>
                    <div class="report-actions">
                        <button class="btn-resolve" onclick="resolveReport('${doc.id}')">
                            <i class="fas fa-check"></i> Resolve
                        </button>
                        <button class="btn-dismiss" onclick="dismissReport('${doc.id}')">
                            <i class="fas fa-times"></i> Dismiss
                        </button>
                    </div>
                </div>
            `);
        });
        
        reportedContentList.innerHTML = reportsHTML.join('');
        
    } catch (error) {
        console.error('Error loading reported content:', error);
        // Don't show error if reports collection doesn't exist yet
        reportedContentList.innerHTML = '<div class="loading-message">No pending reports</div>';
    }
}

// Resolve report
async function resolveReport(reportId) {
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, doc, updateDoc, serverTimestamp } = window.firebase;
        
        await updateDoc(doc(db, 'reports', reportId), {
            status: 'resolved',
            resolvedAt: serverTimestamp(),
            resolvedBy: window.firebase.auth.currentUser.uid
        });
        
        showModerationMessage('Report resolved successfully', 'success');
        loadReportedContent(); // Refresh reports
        loadModerationStats(); // Update stats
        
    } catch (error) {
        console.error('Error resolving report:', error);
        showModerationMessage(`Failed to resolve report: ${error.message}`, 'error');
    }
}

// Dismiss report
async function dismissReport(reportId) {
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, doc, updateDoc, serverTimestamp } = window.firebase;
        
        await updateDoc(doc(db, 'reports', reportId), {
            status: 'dismissed',
            dismissedAt: serverTimestamp(),
            dismissedBy: window.firebase.auth.currentUser.uid
        });
        
        showModerationMessage('Report dismissed successfully', 'success');
        loadReportedContent(); // Refresh reports
        loadModerationStats(); // Update stats
        
    } catch (error) {
        console.error('Error dismissing report:', error);
        showModerationMessage(`Failed to dismiss report: ${error.message}`, 'error');
    }
}

// Refresh moderation data
async function refreshModerationData() {
    const refreshBtn = document.getElementById('refresh-forum-btn');
    const originalText = refreshBtn.innerHTML;
    
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
    
    try {
        await loadModerationData();
        showModerationMessage('Data refreshed successfully', 'success');
    } catch (error) {
        showModerationMessage('Failed to refresh data', 'error');
    } finally {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }
}

// Export moderation logs
async function exportModerationLogs() {
    try {
        if (!window.firebase || !window.firebase.db) {
            throw new Error('Firebase not available');
        }
        
        const { db, collection, getDocs, orderBy, query } = window.firebase;
        const logsQuery = query(collection(db, 'moderation_logs'), orderBy('timestamp', 'desc'));
        const logsSnapshot = await getDocs(logsQuery);
        
        const logs = [];
        logsSnapshot.forEach((doc) => {
            const logData = doc.data();
            logs.push({
                id: doc.id,
                action: logData.action,
                moderator: logData.moderatorEmail,
                target: logData.targetUserEmail,
                reason: logData.reason || '',
                timestamp: logData.timestamp ? new Date(logData.timestamp.toDate()).toISOString() : ''
            });
        });
        
        // Convert to CSV
        const csvContent = [
            ['ID', 'Action', 'Moderator', 'Target User', 'Reason', 'Timestamp'].join(','),
            ...logs.map(log => [
                log.id,
                log.action,
                log.moderator,
                log.target,
                `"${log.reason.replace(/"/g, '""')}"`,
                log.timestamp
            ].join(','))
        ].join('\n');
        
        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `moderation_logs_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        
        showModerationMessage('Moderation logs exported successfully', 'success');
        
    } catch (error) {
        console.error('Error exporting logs:', error);
        showModerationMessage(`Failed to export logs: ${error.message}`, 'error');
    }
}

// Show moderation message
function showModerationMessage(message, type) {
    showAccountMessage(message, type);
}

// Show role assignment status
function showRoleAssignmentStatus(message, type) {
    const statusElement = document.getElementById('role-assignment-status');
    if (!statusElement) return;
    
    statusElement.className = `status-message ${type}`;
    statusElement.style.display = 'block';
    statusElement.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    if (type === 'success') {
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    }
}

// Function to make current user admin (for initial setup ONLY)
// This function is protected by Firebase security rules and will only work
// if the current user already has admin permissions or if no admins exist yet
window.assignAdminRole = async function() {
    if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
        console.error('Firebase not available');
        return;
    }
    
    try {
        const { auth, db, doc, updateDoc, serverTimestamp, collection, getDocs, query, where } = window.firebase;
        const user = auth.currentUser;
        
        if (!user) {
            console.error('No user logged in');
            return;
        }
        
        // Check if any admins already exist (additional security check)
        const adminQuery = query(collection(db, 'users'), where('role', '==', 'admin'));
        const adminSnapshot = await getDocs(adminQuery);
        
        if (!adminSnapshot.empty) {
            console.error('Admin users already exist. Use the proper role assignment system.');
            alert('❌ Security Error: Admin users already exist. Only existing admins can assign roles through the moderation panel.');
            return;
        }
        
        // Only proceed if no admins exist (initial setup)
        await updateDoc(doc(db, 'users', user.uid), {
            role: 'admin',
            roleAssignedAt: serverTimestamp(),
            roleAssignedBy: user.uid // Initial setup
        });
        
        console.log('✅ Initial admin role assigned successfully');
        alert('✅ You are now the first admin! The page will reload to show moderation features.');
        location.reload();
        
    } catch (error) {
        console.error('❌ Error assigning admin role:', error);
        if (error.code === 'permission-denied') {
            alert('❌ Permission Denied: You cannot assign admin roles. This function only works for initial setup when no admins exist, or the Firebase security rules are preventing unauthorized access (which is working correctly).');
        } else {
            alert('❌ Error: ' + error.message);
        }
    }
};

// ============================================
// EMAIL VERIFICATION & PASSWORD RESET FUNCTIONS
// ============================================

function updateEmailVerificationStatus(user) {
    const verificationStatus = document.getElementById('email-verification-status');
    const verificationBadge = document.getElementById('verification-badge');
    const resendBtn = document.getElementById('resend-verification-btn');
    
    if (!verificationStatus || !verificationBadge || !user) return;
    
    if (user.emailVerified) {
        // Email is verified
        verificationStatus.style.display = 'block';
        verificationStatus.classList.add('verified');
        verificationBadge.classList.add('verified');
        verificationBadge.innerHTML = '<i class="fas fa-check-circle"></i><span>Email verified</span>';
        if (resendBtn) resendBtn.style.display = 'none';
    } else {
        // Email not verified
        verificationStatus.style.display = 'block';
        verificationStatus.classList.remove('verified');
        verificationBadge.classList.remove('verified');
        verificationBadge.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>Email not verified</span>';
        if (resendBtn) resendBtn.style.display = 'inline-block';
    }
}

async function handleResendVerification(event) {
    event.preventDefault();
    
    if (!window.firebase || !window.firebase.auth) {
        showAccountMessage('Firebase authentication not available.', 'error');
        return;
    }
    
    const { auth, sendEmailVerification } = window.firebase;
    const user = auth.currentUser;
    
    if (!user) {
        showAccountMessage('No user logged in.', 'error');
        return;
    }
    
    if (user.emailVerified) {
        showAccountMessage('Your email is already verified!', 'info');
        return;
    }
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    btn.disabled = true;
    
    try {
        await sendEmailVerification(user);
        showAccountMessage('Verification email sent! Please check your inbox.', 'success');
    } catch (error) {
        console.error('Error sending verification email:', error);
        showAccountMessage(`Failed to send verification email: ${error.message}`, 'error');
    } finally {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

async function handleForgotPassword(event) {
    event.preventDefault();
    
    if (!window.firebase || !window.firebase.auth) {
        showAccountMessage('Firebase authentication not available.', 'error');
        return;
    }
    
    // Get email from login form
    const emailInput = document.getElementById('login-email');
    let email = emailInput ? emailInput.value.trim() : '';
    
    // If no email in form, prompt user
    if (!email) {
        email = prompt('Please enter your email address to reset your password:');
        if (!email) return;
    }
    
    if (!email.includes('@')) {
        showAccountMessage('Please enter a valid email address.', 'error');
        return;
    }
    
    const { sendPasswordResetEmail } = window.firebase;
    
    try {
        await sendPasswordResetEmail(window.firebase.auth, email);
        showAccountMessage(`Password reset email sent to ${email}. Please check your inbox.`, 'success');
    } catch (error) {
        console.error('Error sending password reset email:', error);
        let errorMessage = 'Failed to send password reset email.';
        
        if (error.code === 'auth/user-not-found') {
            errorMessage = 'No account found with this email address.';
        } else if (error.code === 'auth/invalid-email') {
            errorMessage = 'Invalid email address.';
        } else if (error.code === 'auth/too-many-requests') {
            errorMessage = 'Too many attempts. Please try again later.';
        }
        
        showAccountMessage(errorMessage, 'error');
    }
}

// Monitor auth state changes for email verification
function setupEmailVerificationMonitoring() {
    if (!window.firebase || !window.firebase.auth) return;
    
    const { auth, onAuthStateChanged } = window.firebase;
    
    onAuthStateChanged(auth, async (user) => {
        if (user) {
            // Reload user to get latest verification status
            await user.reload();
            updateEmailVerificationStatus(user);
            
            // Update Firestore if verification status changed
            if (user.emailVerified && window.firebase.db) {
                try {
                    const { db, doc, updateDoc } = window.firebase;
                    await updateDoc(doc(db, 'users', user.uid), {
                        emailVerified: true,
                        emailVerifiedAt: new Date()
                    });
                } catch (error) {
                    console.log('Error updating verification status in Firestore:', error);
                }
            }
        }
    });
}

// Initialize email verification monitoring
if (window.firebase) {
    setupEmailVerificationMonitoring();
} else {
    // Wait for Firebase to be initialized
    const checkFirebase = setInterval(() => {
        if (window.firebase) {
            setupEmailVerificationMonitoring();
            clearInterval(checkFirebase);
        }
    }, 100);
}

// Update the loadUserAccountData function to check for moderation tab
const originalLoadUserAccountData = loadUserAccountData;
loadUserAccountData = async function(user) {
    await originalLoadUserAccountData.call(this, user);
    await checkUserRoleAndShowModerationTab(user);
};

// ============================================
// INBOX & MESSAGING SYSTEM
// ============================================

let currentConversation = null;

async function loadInboxContent() {
    console.log('Loading inbox content...');
    // Initialize inbox functionality
    setupInboxEventListeners();
    loadConversations();
}

function setupInboxEventListeners() {
    // Start conversation button (always visible at bottom)
    const startConversationBtnBottom = document.getElementById('start-conversation-btn-bottom');
    if (startConversationBtnBottom) {
        startConversationBtnBottom.onclick = () => showNewConversationModal();
    }
    
    // Send message form
    const sendMessageForm = document.getElementById('send-message-form');
    if (sendMessageForm) {
        sendMessageForm.onsubmit = (e) => handleSendMessage(e);
    }
    
    // Modal conversation search - case-insensitive with debouncing
    const conversationRecipient = document.getElementById('conversation-recipient');
    if (conversationRecipient) {
        let searchTimeout;
        conversationRecipient.oninput = (e) => {
            clearTimeout(searchTimeout);
            const searchTerm = e.target.value.trim();
            
            if (searchTerm.length < 2) {
                hideConversationSearchResults();
                // Reset modal state when clearing search
                const startBtn = document.getElementById('start-conversation-modal-btn');
                if (startBtn) {
                    startBtn.disabled = true;
                }
                window.selectedConversationUser = null;
                return;
            }
            
            searchTimeout = setTimeout(() => {
                searchUsersForConversation(searchTerm);
            }, 300); // 300ms debounce for smooth typing
        };
        
        // Add escape key support
        conversationRecipient.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                conversationRecipient.value = '';
                hideConversationSearchResults();
                const startBtn = document.getElementById('start-conversation-modal-btn');
                if (startBtn) {
                    startBtn.disabled = true;
                }
                window.selectedConversationUser = null;
            }
        });
    }
    
    // Start conversation modal button
    const startConversationModalBtn = document.getElementById('start-conversation-modal-btn');
    if (startConversationModalBtn) {
        startConversationModalBtn.onclick = () => startConversationFromModal();
    }
}

async function loadConversations() {
    const conversationsList = document.getElementById('conversations-list');
    
    if (!conversationsList) return;
    
    try {
        let conversations = [];
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Load from Firebase
            const { auth, db, collection, query, where, getDocs } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                const conversationsRef = collection(db, 'conversations');
                const q = query(
                    conversationsRef,
                    where('participants', 'array-contains', user.uid)
                );
                
                const snapshot = await getDocs(q);
                conversations = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                }));
                
                // Sort by lastMessageTime (most recent first)
                conversations.sort((a, b) => {
                    const timeA = a.lastMessageTime?.toDate?.() || a.lastMessageTime || new Date(0);
                    const timeB = b.lastMessageTime?.toDate?.() || b.lastMessageTime || new Date(0);
                    return new Date(timeB) - new Date(timeA);
                });
            }
        } else {
            // Demo mode - create some sample conversations
            conversations = [
                {
                    id: 'demo1',
                    participants: ['demo_user', 'demo_friend1'],
                    participantNames: {
                        demo_user: 'You',
                        demo_friend1: 'Demo Friend'
                    },
                    lastMessage: 'Hey, how are you doing?',
                    lastMessageTime: new Date(),
                    unreadCount: 2
                }
            ];
        }
        
        displayConversations(conversations);
        
    } catch (error) {
        console.error('Error loading conversations:', error);
        
        // More specific error handling
        if (error.code === 'permission-denied') {
            conversationsList.innerHTML = `
                <div class="no-conversations">
                    <i class="fas fa-lock"></i>
                    <p>Permission denied loading conversations</p>
                    <p style="font-size: 0.9rem; color: var(--text-secondary); margin-top: 0.5rem;">
                        Please update your Firebase security rules to include conversations and messages collections.
                    </p>
                    <button class="btn btn-primary" onclick="loadConversations()">Retry</button>
                </div>
            `;
        } else {
            conversationsList.innerHTML = `
                <div class="no-conversations">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading conversations</p>
                    <p style="font-size: 0.9rem; color: var(--text-secondary);">${error.message}</p>
                    <button class="btn btn-primary" onclick="loadConversations()">Retry</button>
                </div>
            `;
        }
    }
}

function displayConversations(conversations) {
    const conversationsList = document.getElementById('conversations-list');
    
    if (!conversationsList) return;
    
    if (conversations.length === 0) {
        conversationsList.innerHTML = `
            <div class="no-conversations">
                <i class="fas fa-comments"></i>
                <p>No conversations yet</p>
                <p class="text-secondary">Use the button below to start your first conversation</p>
            </div>
        `;
    } else {
        const currentUserId = getCurrentUserId();
        
        const conversationsHTML = conversations.map(conversation => {
            // Find the other participant (not the current user)
            const otherParticipantId = conversation.participants.find(p => p !== currentUserId);
            const otherParticipantName = conversation.participantNames?.[otherParticipantId] || 'Unknown User';
            
            // Get first letter for avatar
            const avatarLetter = otherParticipantName.charAt(0).toUpperCase();
            
            // Format last message
            let lastMessage = conversation.lastMessage || 'No messages yet';
            if (lastMessage.length > 50) {
                lastMessage = lastMessage.substring(0, 50) + '...';
            }
            
            // Format last message time
            let timeDisplay = '';
            if (conversation.lastMessageTime) {
                const messageTime = conversation.lastMessageTime.toDate ? 
                    conversation.lastMessageTime.toDate() : 
                    new Date(conversation.lastMessageTime);
                timeDisplay = formatTime(messageTime);
            }
            
            const unreadCount = conversation.unreadCount || 0;
            
            return `
                <div class="conversation-item" onclick="selectConversation('${conversation.id}', event)">
                    <div class="conversation-avatar">
                        ${avatarLetter}
                    </div>
                    <div class="conversation-info">
                        <div class="conversation-name">${escapeHtml(otherParticipantName)}</div>
                        <div class="conversation-preview">${escapeHtml(lastMessage)}</div>
                        ${timeDisplay ? `<div class="conversation-time">${timeDisplay}</div>` : ''}
                    </div>
                    ${unreadCount > 0 ? `<div class="conversation-unread">${unreadCount}</div>` : ''}
                    <div class="conversation-actions">
                        <button class="btn btn-sm btn-danger conversation-delete-btn" onclick="event.stopPropagation(); deleteConversation('${conversation.id}', '${escapeHtml(otherParticipantName)}')" title="Delete conversation">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        conversationsList.innerHTML = conversationsHTML;
    }
    
    // Always setup inbox event listeners
    setupInboxEventListeners();
}

async function selectConversation(conversationId, clickEvent = null) {
    currentConversation = conversationId;
    
    // Update UI
    document.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Find the conversation item and participant name
    let conversationItem = null;
    let participantName = 'Unknown User';
    
    if (clickEvent && clickEvent.target) {
        // Called from click event
        conversationItem = clickEvent.target.closest('.conversation-item');
        conversationItem?.classList.add('active');
        participantName = conversationItem?.querySelector('.conversation-name')?.textContent || 'Unknown User';
    } else {
        // Called programmatically - find conversation by ID
        const allConversationItems = document.querySelectorAll('.conversation-item');
        for (const item of allConversationItems) {
            if (item.onclick && item.onclick.toString().includes(conversationId)) {
                conversationItem = item;
                item.classList.add('active');
                participantName = item.querySelector('.conversation-name')?.textContent || 'Unknown User';
                break;
            }
        }
    }
    
    // Update message header with participant info
    const messageHeader = document.getElementById('message-header');
    const recipientName = document.getElementById('recipient-name');
    const recipientStatus = document.getElementById('recipient-status');
    
    if (recipientName) {
        recipientName.textContent = participantName;
    }
    if (recipientStatus) {
        recipientStatus.textContent = 'Last seen recently';
    }
    
    // Show message interface
    messageHeader.style.display = 'block';
    document.getElementById('message-input').style.display = 'block';
    
    // Show loading state while messages load
    const messageContent = document.getElementById('message-content');
    messageContent.innerHTML = `
        <div class="loading-messages">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading messages...</p>
        </div>
    `;
    
    // Load messages
    await loadMessages(conversationId);
}

async function loadMessages(conversationId) {
    const messageContent = document.getElementById('message-content');
    
    try {
        let messages = [];
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Load from Firebase
            const { db, collection, query, where, orderBy, getDocs } = window.firebase;
            const messagesRef = collection(db, 'messages');
            const q = query(
                messagesRef,
                where('conversationId', '==', conversationId),
                orderBy('timestamp', 'asc')
            );
            
            const snapshot = await getDocs(q);
            messages = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } else {
            // Demo mode
            const allMessages = JSON.parse(localStorage.getItem('armoryX_messages') || '[]');
            messages = allMessages.filter(msg => msg.conversationId === conversationId);
        }
        
        displayMessages(messages);
        
    } catch (error) {
        console.error('Error loading messages:', error);
        
        // Show a more user-friendly error state
        messageContent.innerHTML = `
            <div class="messages-error">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Unable to load messages</h3>
                <p>There was a problem loading the conversation. Please try again.</p>
                <button class="btn btn-primary" onclick="loadMessages('${conversationId}')">
                    <i class="fas fa-sync"></i> Retry
                </button>
            </div>
        `;
    }
}

function displayMessages(messages) {
    const messageContent = document.getElementById('message-content');
    const currentUserId = getCurrentUserId();
    
    if (messages.length === 0) {
        messageContent.innerHTML = `
            <div class="no-conversation-selected">
                <i class="fas fa-comments"></i>
                <h3>No messages yet</h3>
                <p>Send the first message to start the conversation</p>
            </div>
        `;
        return;
    }
    
    const messagesHTML = messages.map(message => {
        const isSent = message.senderId === currentUserId;
        const time = formatMessageTime(message.timestamp);
        
        return `
            <div class="message-bubble ${isSent ? 'sent' : 'received'}">
                <div class="message-text">${escapeHtml(message.text)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
    }).join('');
    
    messageContent.innerHTML = messagesHTML;
    messageContent.scrollTop = messageContent.scrollHeight;
}

async function handleSendMessage(event) {
    event.preventDefault();
    
    const messageInput = document.getElementById('message-text');
    const messageText = messageInput.value.trim();
    
    if (!messageText || !currentConversation) return;
    
    const sendButton = event.target.querySelector('button[type="submit"]');
    sendButton.disabled = true;
    
    try {
        const currentUserId = getCurrentUserId();
        const messageData = {
            conversationId: currentConversation,
            senderId: currentUserId,
            text: messageText,
            timestamp: new Date(),
            read: false
        };
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Send via Firebase
            const { db, collection, addDoc, doc, updateDoc, serverTimestamp } = window.firebase;
            
            await addDoc(collection(db, 'messages'), {
                ...messageData,
                timestamp: serverTimestamp()
            });
            
            // Update conversation last message
            await updateDoc(doc(db, 'conversations', currentConversation), {
                lastMessage: messageText,
                lastMessageTime: serverTimestamp()
            });
        } else {
            // Demo mode
            messageData.id = Date.now().toString();
            const messages = JSON.parse(localStorage.getItem('armoryX_messages') || '[]');
            messages.push(messageData);
            localStorage.setItem('armoryX_messages', JSON.stringify(messages));
        }
        
        // Clear input and reload messages
        messageInput.value = '';
        await loadMessages(currentConversation);
        
    } catch (error) {
        console.error('Error sending message:', error);
        showAccountMessage('Failed to send message', 'error');
    } finally {
        sendButton.disabled = false;
    }
}

// Modal Functions for New Conversations
function showNewConversationModal() {
    const modal = document.getElementById('new-conversation-modal');
    const recipientInput = document.getElementById('conversation-recipient');
    const startBtn = document.getElementById('start-conversation-modal-btn');
    
    if (modal) {
        modal.style.display = 'flex';
        if (recipientInput) {
            recipientInput.value = '';
            recipientInput.focus();
        }
        if (startBtn) {
            startBtn.disabled = true;
        }
        hideConversationSearchResults();
        
        // Store selected user for later
        window.selectedConversationUser = null;
    }
}

function closeNewConversationModal() {
    const modal = document.getElementById('new-conversation-modal');
    if (modal) {
        modal.style.display = 'none';
        hideConversationSearchResults();
        window.selectedConversationUser = null;
    }
}

function hideConversationSearchResults() {
    const searchResults = document.getElementById('conversation-search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
        searchResults.innerHTML = '';
    }
}

async function searchUsersForConversation(searchTerm) {
    try {
        const searchResults = document.getElementById('conversation-search-results');
        if (!searchResults) return;
        
        searchResults.style.display = 'block';
        searchResults.innerHTML = '<p style="text-align: center; padding: 1rem; color: var(--text-secondary);"><i class="fas fa-spinner fa-spin"></i> Searching...</p>';
        
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            searchResults.innerHTML = '<p style="color: var(--error-color); text-align: center; padding: 1rem;">Firebase not available</p>';
            return;
        }

        const { auth, db, collection, query, where, getDocs, doc, getDoc } = window.firebase;
        const currentUser = auth.currentUser;
        
        if (!currentUser) {
            searchResults.innerHTML = '<p style="color: var(--error-color); text-align: center; padding: 1rem;">Please log in to search for users</p>';
            return;
        }

        // For messaging, NEVER show emails - this is user-facing functionality, not admin
        const usersRef = collection(db, 'users');
        
        // Get all users and filter client-side for case-insensitive search
        const snapshot = await getDocs(usersRef);
        const users = [];
        const searchTermLower = searchTerm.toLowerCase();
        
        snapshot.forEach(doc => {
            const userData = doc.data();
            if (doc.id !== currentUser.uid && userData.displayName) { // Exclude current user and check displayName exists
                const displayNameLower = userData.displayName.toLowerCase();
                // Case-insensitive search - check if display name contains search term
                if (displayNameLower.includes(searchTermLower)) {
                    users.push({
                        id: doc.id,
                        displayName: userData.displayName,
                        email: null, // Never show emails in messaging functionality
                        lastSeen: userData.lastSeen
                    });
                }
            }
        });

        displayConversationSearchResults(users, searchTerm);
        
    } catch (error) {
        console.error('Error searching users for conversation:', error);
        const searchResults = document.getElementById('conversation-search-results');
        if (searchResults) {
            searchResults.innerHTML = '<p style="color: var(--error-color); text-align: center; padding: 1rem;">Error searching users</p>';
        }
    }
}

function displayConversationSearchResults(users, searchTerm) {
    const searchResults = document.getElementById('conversation-search-results');
    if (!searchResults) return;
    
    if (users.length === 0) {
        searchResults.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                <i class="fas fa-user-slash"></i>
                <p>No users found matching "${escapeHtml(searchTerm)}"</p>
            </div>
        `;
        return;
    }
    
    const resultsHTML = users.map(user => {
        const initial = user.displayName ? user.displayName.charAt(0).toUpperCase() : 'U';
        const isOnline = user.lastSeen && isRecentlyActive(user.lastSeen);
        
        return `
            <div class="user-search-result" onclick="selectConversationUser('${user.id}', '${escapeHtml(user.displayName || 'Unknown User')}', event)">
                <div class="user-avatar">
                    ${initial}
                    ${isOnline ? '<div class="online-indicator"></div>' : ''}
                </div>
                <div class="user-info">
                    <div class="user-name">${escapeHtml(user.displayName || 'Unknown User')}</div>
                    ${user.email ? `<div class="user-email">${escapeHtml(user.email)}</div>` : ''}
                    <div class="user-status">${isOnline ? 'Online' : 'Last seen ' + formatTime(user.lastSeen)}</div>
                </div>
                <div class="user-action">
                    <i class="fas fa-comments"></i>
                </div>
            </div>
        `;
    }).join('');
    
    searchResults.innerHTML = resultsHTML;
}

function selectConversationUser(userId, displayName, clickEvent = null) {
    window.selectedConversationUser = { id: userId, displayName: displayName };
    
    // Update UI to show selection
    document.querySelectorAll('.user-search-result').forEach(item => {
        item.classList.remove('selected');
    });
    
    // Find and highlight the selected item
    if (clickEvent && clickEvent.target) {
        clickEvent.target.closest('.user-search-result')?.classList.add('selected');
    } else {
        // Fallback: find by userId
        const searchResults = document.querySelectorAll('.user-search-result');
        searchResults.forEach(item => {
            if (item.onclick && item.onclick.toString().includes(userId)) {
                item.classList.add('selected');
            }
        });
    }
    
    // Enable start conversation button
    const startBtn = document.getElementById('start-conversation-modal-btn');
    if (startBtn) {
        startBtn.disabled = false;
    }
}

async function startConversationFromModal() {
    if (!window.selectedConversationUser) {
        showAccountMessage('Please select a user to start a conversation with', 'error');
        return;
    }
    
    const targetUser = window.selectedConversationUser;
    
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Messaging requires Firebase connection', 'error');
            return;
        }

        const { auth, db, collection, addDoc, getDocs, query, where, serverTimestamp } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to start conversations', 'error');
            return;
        }

        // Check if conversation already exists
        const conversationsRef = collection(db, 'conversations');
        const existingQuery = query(
            conversationsRef,
            where('participants', 'array-contains', currentUser.uid)
        );
        const existingSnapshot = await getDocs(existingQuery);
        
        let existingConversation = null;
        existingSnapshot.forEach(doc => {
            const data = doc.data();
            if (data.participants.includes(targetUser.id)) {
                existingConversation = { id: doc.id, ...data };
            }
        });

        if (existingConversation) {
            showAccountMessage('Conversation already exists with this user.', 'info');
            closeNewConversationModal();
            selectConversation(existingConversation.id);
            return;
        }

        // Create new conversation
        const conversationData = {
            participants: [currentUser.uid, targetUser.id],
            participantNames: {
                [currentUser.uid]: getCurrentUserDisplayName(),
                [targetUser.id]: targetUser.displayName || 'Unknown User'
            },
            createdAt: serverTimestamp(),
            lastMessage: '',
            lastMessageTime: serverTimestamp()
        };

        const docRef = await addDoc(conversationsRef, conversationData);
        
        showAccountMessage(`Conversation started with ${targetUser.displayName}!`, 'success');
        closeNewConversationModal();
        
        // Reload conversations and select the new one
        setTimeout(() => {
            loadConversations();
            setTimeout(() => {
                selectConversation(docRef.id);
            }, 500);
        }, 500);

    } catch (error) {
        console.error('Error starting conversation:', error);
        showAccountMessage('Failed to start conversation: ' + error.message, 'error');
    }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modal = document.getElementById('new-conversation-modal');
    if (modal && e.target === modal) {
        closeNewConversationModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const newConversationModal = document.getElementById('new-conversation-modal');
        const confirmationModal = document.getElementById('confirmation-modal');
        
        if (newConversationModal && newConversationModal.style.display === 'flex') {
            closeNewConversationModal();
        } else if (confirmationModal && confirmationModal.style.display === 'flex') {
            closeConfirmationModal();
        }
    }
});

// Confirmation Modal Functions
let confirmationCallback = null;

function showConfirmationModal(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const titleElement = document.getElementById('confirmation-title');
    const messageElement = document.getElementById('confirmation-message');
    const confirmBtn = document.getElementById('confirmation-confirm-btn');
    
    if (!modal || !titleElement || !messageElement || !confirmBtn) {
        console.error('Confirmation modal elements not found');
        return;
    }
    
    // Set content
    titleElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${title}`;
    messageElement.textContent = message;
    
    // Store callback
    confirmationCallback = onConfirm;
    
    // Show modal
    modal.style.display = 'flex';
    
    // Add click outside to close
    modal.onclick = (e) => {
        if (e.target === modal) {
            closeConfirmationModal();
        }
    };
}

function closeConfirmationModal() {
    const modal = document.getElementById('confirmation-modal');
    if (modal) {
        modal.style.display = 'none';
        confirmationCallback = null;
    }
}

function confirmAction() {
    if (confirmationCallback) {
        confirmationCallback();
        closeConfirmationModal();
    }
}

// Add event listener for confirmation button
document.addEventListener('DOMContentLoaded', () => {
    const confirmBtn = document.getElementById('confirmation-confirm-btn');
    if (confirmBtn) {
        confirmBtn.onclick = confirmAction;
    }
});

// Delete conversation function
async function deleteConversation(conversationId, participantName) {
    // Show custom confirmation modal instead of default confirm
    showConfirmationModal(
        'Delete Conversation',
        `Are you sure you want to delete your conversation with ${participantName}? This action cannot be undone.`,
        () => {
            // On confirm, execute the deletion
            executeDeleteConversation(conversationId, participantName);
        }
    );
}

// Execute the actual deletion after confirmation
async function executeDeleteConversation(conversationId, participantName) {
    
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Firebase not available', 'error');
            return;
        }

        const { auth, db, doc, deleteDoc, collection, query, where, getDocs } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to delete conversations', 'error');
            return;
        }

        // Delete all messages in the conversation
        const messagesRef = collection(db, 'messages');
        const messagesQuery = query(messagesRef, where('conversationId', '==', conversationId));
        const messagesSnapshot = await getDocs(messagesQuery);
        
        const deletePromises = [];
        messagesSnapshot.forEach(doc => {
            deletePromises.push(deleteDoc(doc.ref));
        });
        
        // Delete the conversation document
        deletePromises.push(deleteDoc(doc(db, 'conversations', conversationId)));
        
        await Promise.all(deletePromises);
        
        showAccountMessage(`Conversation with ${participantName} deleted successfully`, 'success');
        
        // If this was the currently selected conversation, clear the UI
        if (currentConversation === conversationId) {
            currentConversation = null;
            document.getElementById('message-header').style.display = 'none';
            document.getElementById('message-input').style.display = 'none';
            document.getElementById('message-content').innerHTML = `
                <div class="no-conversation-selected">
                    <i class="fas fa-comments"></i>
                    <h3>Select a conversation</h3>
                    <p>Choose from your existing conversations or start a new one</p>
                </div>
            `;
        }
        
        // Reload conversations
        loadConversations();
        
    } catch (error) {
        console.error('Error deleting conversation:', error);
        showAccountMessage('Failed to delete conversation: ' + error.message, 'error');
    }
}

async function startNewConversation(recipient) {
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Messaging requires Firebase connection', 'error');
            return;
        }

        const { auth, db, collection, addDoc, getDocs, query, where, serverTimestamp } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to start conversations', 'error');
            return;
        }

        // Search for user by email or display name
        const usersRef = collection(db, 'users');
        const emailQuery = query(usersRef, where('email', '==', recipient.toLowerCase()));
        const nameQuery = query(usersRef, where('displayName', '==', recipient));

        let targetUser = null;

        // Try email search first
        const emailSnapshot = await getDocs(emailQuery);
        if (!emailSnapshot.empty) {
            targetUser = { id: emailSnapshot.docs[0].id, ...emailSnapshot.docs[0].data() };
        } else {
            // Try display name search
            const nameSnapshot = await getDocs(nameQuery);
            if (!nameSnapshot.empty) {
                targetUser = { id: nameSnapshot.docs[0].id, ...nameSnapshot.docs[0].data() };
            }
        }

        if (!targetUser) {
            showAccountMessage('User not found. Please check the email or display name.', 'error');
            return;
        }

        if (targetUser.id === currentUser.uid) {
            showAccountMessage('You cannot start a conversation with yourself.', 'error');
            return;
        }

        // Check if conversation already exists
        const conversationsRef = collection(db, 'conversations');
        const existingQuery = query(
            conversationsRef,
            where('participants', 'array-contains', currentUser.uid)
        );
        const existingSnapshot = await getDocs(existingQuery);
        
        let existingConversation = null;
        existingSnapshot.forEach(doc => {
            const data = doc.data();
            if (data.participants.includes(targetUser.id)) {
                existingConversation = { id: doc.id, ...data };
            }
        });

        if (existingConversation) {
            showAccountMessage('Conversation already exists with this user.', 'info');
            selectConversation(existingConversation.id);
            return;
        }

        // Create new conversation
        const conversationData = {
            participants: [currentUser.uid, targetUser.id],
            participantNames: {
                [currentUser.uid]: getCurrentUserDisplayName(),
                [targetUser.id]: targetUser.displayName || 'Unknown User'
            },
            createdAt: serverTimestamp(),
            lastMessage: '',
            lastMessageTime: serverTimestamp()
        };

        const docRef = await addDoc(conversationsRef, conversationData);
        
        showAccountMessage(`Conversation started with ${targetUser.displayName || targetUser.email}!`, 'success');
        
        // Reload conversations and select the new one
        setTimeout(() => {
            loadConversations();
            setTimeout(() => {
                selectConversation(docRef.id);
            }, 500);
        }, 500);

    } catch (error) {
        console.error('Error starting conversation:', error);
        showAccountMessage('Failed to start conversation: ' + error.message, 'error');
    }
}

function getCurrentUserDisplayName() {
    if (window.firebase && window.firebase.auth && window.firebase.auth.currentUser) {
        return window.firebase.auth.currentUser.displayName || 
               window.firebase.auth.currentUser.email?.split('@')[0] || 'You';
    }
    return localStorage.getItem('armoryX_display_name') || 'You';
}

// ============================================
// FRIENDS SYSTEM
// ============================================

async function loadFriendsContent() {
    console.log('Loading friends content...');
    setupFriendsEventListeners();
    loadFriendsList();
    loadFriendRequests();
}

let searchTimeout = null;

function setupFriendsEventListeners() {
    const friendSearch = document.getElementById('friend-search');
    const clearBtn = document.getElementById('search-clear-btn');
    
    if (friendSearch) {
        // Live search with debouncing
        friendSearch.oninput = (e) => {
            const searchTerm = e.target.value.trim();
            
            // Show/hide clear button
            toggleClearButton(e.target.value);
            
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Hide results if search is too short
            if (searchTerm.length < 2) {
                hideSearchResults();
                return;
            }
            
            // Show loading state
            showSearchLoading();
            
            // Debounce search for 300ms
            searchTimeout = setTimeout(() => {
                searchForUsers(searchTerm);
            }, 300);
        };
        
        // Clear search when input is empty
        friendSearch.onkeyup = (e) => {
            if (e.target.value.trim() === '') {
                hideSearchResults();
            }
        };
        
        // Handle keyboard shortcuts
        friendSearch.onkeydown = (e) => {
            if (e.key === 'Escape') {
                clearSearch();
            }
        };
    }
}

function toggleClearButton(inputValue) {
    const clearBtn = document.getElementById('search-clear-btn');
    if (clearBtn) {
        clearBtn.style.display = inputValue.length > 0 ? 'flex' : 'none';
    }
}

function clearSearch() {
    const friendSearch = document.getElementById('friend-search');
    const clearBtn = document.getElementById('search-clear-btn');
    
    if (friendSearch) {
        friendSearch.value = '';
        friendSearch.focus();
    }
    
    if (clearBtn) {
        clearBtn.style.display = 'none';
    }
    
    // Clear any pending search timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    hideSearchResults();
}

function showSearchLoading() {
    const searchStatus = document.getElementById('search-status');
    const searchSection = document.getElementById('search-results-section');
    
    if (searchStatus) {
        searchStatus.style.display = 'flex';
    }
    
    if (searchSection) {
        searchSection.style.display = 'block';
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.innerHTML = '<p style="text-align: center; padding: 2rem; color: var(--text-secondary);"><i class="fas fa-spinner fa-spin"></i> Searching for users...</p>';
        }
    }
}

function hideSearchResults() {
    const searchStatus = document.getElementById('search-status');
    const searchSection = document.getElementById('search-results-section');
    
    if (searchStatus) {
        searchStatus.style.display = 'none';
    }
    
    if (searchSection) {
        searchSection.style.display = 'none';
    }
}

async function loadFriendsList() {
    const friendsList = document.getElementById('friends-list');
    
    try {
        let friends = [];
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Load from Firebase
            const { auth, db, doc, getDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                const userDoc = await getDoc(doc(db, 'users', user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const friendIds = userData.friends || [];
                    
                    // Load friend details
                    friends = await Promise.all(friendIds.map(async (friendId) => {
                        const friendDoc = await getDoc(doc(db, 'users', friendId));
                        return friendDoc.exists() ? { id: friendId, ...friendDoc.data() } : null;
                    }));
                    friends = friends.filter(Boolean);
                }
            }
        } else {
            // Demo mode
            friends = JSON.parse(localStorage.getItem('armoryX_friends') || '[]');
        }
        
        displayFriendsList(friends);
        
    } catch (error) {
        console.error('Error loading friends:', error);
        friendsList.innerHTML = '<p class="error">Error loading friends</p>';
    }
}

function displayFriendsList(friends) {
    const friendsList = document.getElementById('friends-list');
    
    if (friends.length === 0) {
        friendsList.innerHTML = `
            <div class="no-friends">
                <i class="fas fa-users"></i>
                <p>You haven't added any friends yet</p>
                <p class="text-secondary">Use the search above to find and connect with other users</p>
            </div>
        `;
        return;
    }
    
    const friendsHTML = friends.map(friend => {
        const initial = friend.displayName ? friend.displayName.charAt(0).toUpperCase() : 'U';
        const isOnline = friend.lastSeen && isRecentlyActive(friend.lastSeen);
        
        return `
            <div class="friend-item">
                <div class="friend-avatar">
                    ${initial}
                    ${isOnline ? '<div class="online-indicator"></div>' : ''}
                </div>
                <div class="friend-info">
                    <div class="friend-name">${escapeHtml(friend.displayName || 'Unknown User')}</div>
                    <div class="friend-status">${isOnline ? 'Online' : 'Last seen ' + formatTime(friend.lastSeen)}</div>
                </div>
                <div class="friend-actions">
                    <button class="btn btn-sm btn-primary" onclick="startDirectMessage('${friend.id}')">
                        <i class="fas fa-comment"></i> Message
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="removeFriend('${friend.id}')">
                        <i class="fas fa-user-minus"></i> Remove
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    friendsList.innerHTML = friendsHTML;
}

async function loadFriendRequests() {
    const requestsList = document.getElementById('friend-requests-list');
    
    try {
        let requests = [];
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Load from Firebase
            const { auth, db, doc, getDoc } = window.firebase;
            const user = auth.currentUser;
            
            if (user) {
                const userDoc = await getDoc(doc(db, 'users', user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    const requestIds = userData.friendRequests || [];
                    
                    // Load request details
                    requests = await Promise.all(requestIds.map(async (requestId) => {
                        const requestDoc = await getDoc(doc(db, 'users', requestId));
                        return requestDoc.exists() ? { id: requestId, ...requestDoc.data() } : null;
                    }));
                    requests = requests.filter(Boolean);
                }
            }
        } else {
            // Demo mode
            requests = JSON.parse(localStorage.getItem('armoryX_friend_requests') || '[]');
        }
        
        displayFriendRequests(requests);
        
    } catch (error) {
        console.error('Error loading friend requests:', error);
        requestsList.innerHTML = '<p class="error">Error loading friend requests</p>';
    }
}

function displayFriendRequests(requests) {
    const requestsList = document.getElementById('friend-requests-list');
    
    if (requests.length === 0) {
        requestsList.innerHTML = `
            <div class="no-requests">
                <p>No pending friend requests</p>
            </div>
        `;
        return;
    }
    
    const requestsHTML = requests.map(request => {
        const initial = request.displayName ? request.displayName.charAt(0).toUpperCase() : 'U';
        
        return `
            <div class="friend-item">
                <div class="friend-avatar">${initial}</div>
                <div class="friend-info">
                    <div class="friend-name">${escapeHtml(request.displayName || 'Unknown User')}</div>
                    <div class="friend-status">${escapeHtml(request.email)}</div>
                </div>
                <div class="friend-actions">
                    <button class="btn btn-sm btn-primary" onclick="acceptFriendRequest('${request.id}')">
                        <i class="fas fa-check"></i> Accept
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="declineFriendRequest('${request.id}')">
                        <i class="fas fa-times"></i> Decline
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    requestsList.innerHTML = requestsHTML;
}

async function searchForUsers(searchTerm = null) {
    const searchInput = document.getElementById('friend-search');
    const searchResults = document.getElementById('search-results');
    const searchSection = document.getElementById('search-results-section');
    const searchStatus = document.getElementById('search-status');
    
    // Use provided searchTerm or get from input
    const term = searchTerm || searchInput.value.trim();
    
    if (!term || term.length < 2) {
        hideSearchResults();
        return;
    }
    
    try {
        let users = [];
        
        if (window.firebase && window.firebase.auth && window.firebase.db) {
            // Search in Firebase
            const { auth, db, collection, getDocs } = window.firebase;
            const currentUser = auth.currentUser;
            
            const usersRef = collection(db, 'users');
            const snapshot = await getDocs(usersRef);
            
            users = snapshot.docs
                .map(doc => {
                    const userData = doc.data();
                    return {
                        id: doc.id,
                        displayName: userData.displayName,
                        email: null, // NEVER show emails in friends search - user-facing functionality
                        lastSeen: userData.lastSeen
                    };
                })
                .filter(user => 
                    user.displayName?.toLowerCase().includes(term.toLowerCase())
                )
                .slice(0, 10); // Limit results
        } else {
            // Demo mode - mock users (emails always hidden in user-facing search)
            const mockUsers = [
                { id: 'demo1', displayName: 'Demo User 1', email: null },
                { id: 'demo2', displayName: 'Demo User 2', email: null },
                { id: 'demo3', displayName: 'Test User', email: null },
                { id: 'demo4', displayName: 'StickyPanda420', email: null },
                { id: 'demo5', displayName: 'GameMaster', email: null },
                { id: 'demo6', displayName: 'TechNinja', email: null }
            ];
            
            users = mockUsers
                .filter(user => 
                    user.displayName.toLowerCase().includes(term.toLowerCase())
                );
        }
        
        // Hide loading status
        if (searchStatus) {
            searchStatus.style.display = 'none';
        }
        
        // Show results section
        searchSection.style.display = 'block';
        
        displaySearchResults(users, term);
        
    } catch (error) {
        console.error('Error searching users:', error);
        
        // Hide loading status
        if (searchStatus) {
            searchStatus.style.display = 'none';
        }
        
        searchResults.innerHTML = '<p class="error" style="text-align: center; padding: 2rem; color: var(--text-secondary);">Error searching users. Please try again.</p>';
    }
}

function displaySearchResults(users, searchTerm) {
    const searchResults = document.getElementById('search-results');
    
    if (users.length === 0) {
        searchResults.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No users found matching "${escapeHtml(searchTerm)}"</p>
                <p style="font-size: 0.9rem; margin-top: 0.5rem;">Try searching for display names</p>
            </div>
        `;
        return;
    }
    
    const currentUserId = getCurrentUserId();
    console.log('🔍 Current user ID for filtering:', currentUserId);
    console.log('📋 All search results before filtering:', users.map(u => ({ id: u.id, displayName: u.displayName, email: u.email })));
    
    const filteredUsers = users.filter(user => {
        console.log(`🔍 Checking user: ${user.id} !== ${currentUserId} = ${user.id !== currentUserId}`);
        return user.id !== currentUserId;
    });
    
    console.log('✅ Filtered users (excluding current user):', filteredUsers.map(u => ({ id: u.id, displayName: u.displayName, email: u.email })));
    
    if (filteredUsers.length === 0) {
        searchResults.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                <i class="fas fa-user" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>Found users, but they're already you!</p>
                <p style="font-size: 0.9rem; margin-top: 0.5rem;">Try searching for other users</p>
            </div>
        `;
        return;
    }
    
    const resultsHTML = filteredUsers.map(user => {
        const initial = user.displayName ? user.displayName.charAt(0).toUpperCase() : 'U';
        const isOnline = user.lastSeen && isRecentlyActive(user.lastSeen);
        
        return `
            <div class="user-search-item">
                <div class="user-avatar">
                    ${initial}
                    ${isOnline ? '<div class="online-indicator"></div>' : ''}
                </div>
                <div class="user-info">
                    <div class="user-name">${escapeHtml(user.displayName || 'Unknown User')}</div>
                    <div class="user-status">${isOnline ? 'Online' : (user.lastSeen ? 'Last seen ' + formatTime(user.lastSeen) : 'Offline')}</div>
                </div>
                <div class="user-actions">
                    <button class="btn btn-sm btn-primary" onclick="sendFriendRequest('${user.id}')" data-user-id="${user.id}">
                        <i class="fas fa-user-plus"></i> Add Friend
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    // Add results count header
    const resultsHeader = `
        <div style="padding: 0.5rem 0; margin-bottom: 1rem; border-bottom: 1px solid var(--border-color);">
            <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                <i class="fas fa-search"></i> Found ${filteredUsers.length} user${filteredUsers.length === 1 ? '' : 's'} matching "${escapeHtml(searchTerm)}"
            </p>
        </div>
    `;
    
    searchResults.innerHTML = resultsHeader + resultsHTML;
}

async function sendFriendRequest(userId) {
    console.log('🤝 Sending friend request to userId:', userId);
    
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Friend requests require Firebase connection', 'error');
            return;
        }

        // Debug: Check if arrayUnion is available
        if (!window.firebase.arrayUnion) {
            console.error('arrayUnion is not available in window.firebase');
            showAccountMessage('Firebase functions not properly loaded. Please refresh the page.', 'error');
            return;
        }

        const { auth, db, collection, addDoc, doc, getDoc, updateDoc, arrayUnion, serverTimestamp } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to send friend requests', 'error');
            return;
        }

        console.log('👤 Current user:', currentUser.uid, 'sending request to:', userId);

        // Validate that we're not sending request to ourselves
        if (currentUser.uid === userId) {
            showAccountMessage('You cannot send a friend request to yourself', 'error');
            return;
        }

        // Check if users are already friends
        console.log('📝 Checking if users are already friends...');
        const currentUserDoc = await getDoc(doc(db, 'users', currentUser.uid));
        if (currentUserDoc.exists()) {
            const userData = currentUserDoc.data();
            console.log('👤 Current user data:', { friends: userData.friends });
            if (userData.friends && userData.friends.includes(userId)) {
                showAccountMessage('You are already friends with this user', 'info');
                return;
            }
        } else {
            console.log('⚠️ Current user document not found in database');
        }

        // Check if friend request already exists
        console.log('📝 Checking if friend request already exists...');
        const targetUserDoc = await getDoc(doc(db, 'users', userId));
        if (targetUserDoc.exists()) {
            const targetData = targetUserDoc.data();
            console.log('🎯 Target user data:', { 
                displayName: targetData.displayName, 
                friendRequests: targetData.friendRequests 
            });
            if (targetData.friendRequests && targetData.friendRequests.includes(currentUser.uid)) {
                showAccountMessage('Friend request already sent to this user', 'info');
                return;
            }
        } else {
            console.log('❌ Target user document not found in database');
            showAccountMessage('User not found in database', 'error');
            return;
        }

        // Create friend request record
        console.log('📝 Creating friend request record...');
        await addDoc(collection(db, 'friend_requests'), {
            fromUserId: currentUser.uid,
            toUserId: userId,
            status: 'pending',
            createdAt: serverTimestamp()
        });
        console.log('✅ Friend request record created');

        // Add to target user's friend requests array
        console.log('📤 Adding friend request to target user...');
        await updateDoc(doc(db, 'users', userId), {
            friendRequests: arrayUnion(currentUser.uid)
        });
        console.log('✅ Friend request added to user document');

        // Create notification for target user (not current user)
        try {
            console.log('📧 Creating notification...');
            console.log('📧 Current user (sender):', currentUser.uid, currentUser.email);
            console.log('📧 Target user (recipient):', userId);
            console.log('📧 Notification will be created for userId:', userId);
            
            const currentUserData = currentUserDoc.data();
            const displayName = currentUserData?.displayName || currentUser.email?.split('@')[0] || 'Someone';
            
            const notificationData = {
                userId: userId, // Target user, not current user
                type: 'friend_request',
                title: 'New Friend Request',
                message: `${displayName} sent you a friend request`,
                data: { fromUserId: currentUser.uid },
                isRead: false,
                createdAt: serverTimestamp()
            };
            
            console.log('📧 Notification data:', notificationData);
            
            // Create notification document directly for target user
            const notificationRef = await addDoc(collection(db, 'notifications'), notificationData);
            console.log('✅ Notification created with ID:', notificationRef.id);
            
        } catch (error) {
            console.log('❌ Error creating friend request notification:', error);
        }

        showAccountMessage('Friend request sent successfully!', 'success');
        console.log('🎉 Friend request process completed successfully');
        
        // Update the button to show "Requested" - find button by data attribute
        console.log('🔄 Updating button state...');
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            const button = searchResults.querySelector(`button[data-user-id="${userId}"]`);
            if (button) {
                console.log('🎯 Button found, updating...');
                button.innerHTML = '<i class="fas fa-clock"></i> Requested';
                button.disabled = true;
                button.classList.remove('btn-primary');
                button.classList.add('btn-secondary');
                button.onclick = null; // Remove click handler
            } else {
                console.log('⚠️ Button not found for userId:', userId);
            }
        } else {
            console.log('⚠️ Search results container not found');
        }

    } catch (error) {
        console.error('Error sending friend request:', error);
        showAccountMessage('Failed to send friend request: ' + error.message, 'error');
    }
}

async function acceptFriendRequest(userId) {
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Friend requests require Firebase connection', 'error');
            return;
        }

        const { auth, db, doc, updateDoc, arrayUnion, arrayRemove, collection, query, where, getDocs, deleteDoc } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to accept friend requests', 'error');
            return;
        }

        // Add both users to each other's friends list
        await updateDoc(doc(db, 'users', currentUser.uid), {
            friends: arrayUnion(userId),
            friendRequests: arrayRemove(userId)
        });

        await updateDoc(doc(db, 'users', userId), {
            friends: arrayUnion(currentUser.uid)
        });

        // Delete the friend request record
        const requestsQuery = query(
            collection(db, 'friend_requests'),
            where('fromUserId', '==', userId),
            where('toUserId', '==', currentUser.uid)
        );
        const requestSnapshot = await getDocs(requestsQuery);
        requestSnapshot.forEach(async (doc) => {
            await deleteDoc(doc.ref);
        });

        showAccountMessage('Friend request accepted! You are now friends.', 'success');
        loadFriendRequests();
        loadFriendsList();

    } catch (error) {
        console.error('Error accepting friend request:', error);
        showAccountMessage('Failed to accept friend request: ' + error.message, 'error');
    }
}

async function declineFriendRequest(userId) {
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Friend requests require Firebase connection', 'error');
            return;
        }

        const { auth, db, doc, updateDoc, arrayRemove, collection, query, where, getDocs, deleteDoc } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to decline friend requests', 'error');
            return;
        }

        // Remove from friend requests list
        await updateDoc(doc(db, 'users', currentUser.uid), {
            friendRequests: arrayRemove(userId)
        });

        // Delete the friend request record
        const requestsQuery = query(
            collection(db, 'friend_requests'),
            where('fromUserId', '==', userId),
            where('toUserId', '==', currentUser.uid)
        );
        const requestSnapshot = await getDocs(requestsQuery);
        requestSnapshot.forEach(async (doc) => {
            await deleteDoc(doc.ref);
        });

        showAccountMessage('Friend request declined', 'info');
        loadFriendRequests();

    } catch (error) {
        console.error('Error declining friend request:', error);
        showAccountMessage('Failed to decline friend request: ' + error.message, 'error');
    }
}

async function removeFriend(userId) {
    if (!confirm('Are you sure you want to remove this friend? This action cannot be undone.')) return;
    
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Friend management requires Firebase connection', 'error');
            return;
        }

        const { auth, db, doc, updateDoc, arrayRemove } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to remove friends', 'error');
            return;
        }

        // Remove from both users' friends lists
        await updateDoc(doc(db, 'users', currentUser.uid), {
            friends: arrayRemove(userId)
        });

        await updateDoc(doc(db, 'users', userId), {
            friends: arrayRemove(currentUser.uid)
        });

        showAccountMessage('Friend removed successfully', 'info');
        loadFriendsList();

    } catch (error) {
        console.error('Error removing friend:', error);
        showAccountMessage('Failed to remove friend: ' + error.message, 'error');
    }
}

async function startDirectMessage(userId) {
    try {
        if (!window.firebase || !window.firebase.auth || !window.firebase.db) {
            showAccountMessage('Messaging requires Firebase connection', 'error');
            return;
        }

        const { auth, db, collection, addDoc, getDocs, query, where, serverTimestamp, doc, getDoc } = window.firebase;
        const currentUser = auth.currentUser;

        if (!currentUser) {
            showAccountMessage('You must be logged in to send messages', 'error');
            return;
        }

        // Get target user data
        const targetUserDoc = await getDoc(doc(db, 'users', userId));
        if (!targetUserDoc.exists()) {
            showAccountMessage('User not found', 'error');
            return;
        }

        const targetUserData = targetUserDoc.data();

        // Check if conversation already exists
        const conversationsRef = collection(db, 'conversations');
        const existingQuery = query(
            conversationsRef,
            where('participants', 'array-contains', currentUser.uid)
        );
        const existingSnapshot = await getDocs(existingQuery);
        
        let existingConversation = null;
        existingSnapshot.forEach(docSnap => {
            const data = docSnap.data();
            if (data.participants.includes(userId)) {
                existingConversation = { id: docSnap.id, ...data };
            }
        });

        if (existingConversation) {
            // Switch to inbox and select existing conversation
            switchAccountTab('inbox');
            setTimeout(() => {
                selectConversation(existingConversation.id);
                showAccountMessage(`Opened conversation with ${targetUserData.displayName}`, 'success');
            }, 500);
            return;
        }

        // Create new conversation
        const conversationData = {
            participants: [currentUser.uid, userId],
            participantNames: {
                [currentUser.uid]: getCurrentUserDisplayName(),
                [userId]: targetUserData.displayName || 'Unknown User'
            },
            createdAt: serverTimestamp(),
            lastMessage: '',
            lastMessageTime: serverTimestamp()
        };

        const docRef = await addDoc(conversationsRef, conversationData);
        
        // Switch to inbox tab and select the new conversation
        switchAccountTab('inbox');
        
        setTimeout(() => {
            loadConversations();
            setTimeout(() => {
                selectConversation(docRef.id);
                showAccountMessage(`Started conversation with ${targetUserData.displayName}!`, 'success');
            }, 500);
        }, 500);

    } catch (error) {
        console.error('Error starting direct message:', error);
        showAccountMessage('Failed to start conversation: ' + error.message, 'error');
    }
}

// ============================================
// UTILITY FUNCTIONS
// ============================================

function getCurrentUserId() {
    if (window.firebase && window.firebase.auth && window.firebase.auth.currentUser) {
        const uid = window.firebase.auth.currentUser.uid;
        console.log('🔍 Firebase current user ID:', uid);
        return uid;
    }
    console.log('🔍 Using demo user ID: demo_user');
    return 'demo_user'; // Fallback for demo mode
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatTime(timestamp) {
    if (!timestamp) return 'Unknown';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return date.toLocaleDateString();
}

function formatMessageTime(timestamp) {
    if (!timestamp) return '';
    
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function isRecentlyActive(lastSeen) {
    if (!lastSeen) return false;
    
    const date = lastSeen.toDate ? lastSeen.toDate() : new Date(lastSeen);
    const now = new Date();
    const diff = now - date;
    
    return diff < 300000; // 5 minutes
}

// Handle URL hash navigation for direct tab access
window.addEventListener('load', function() {
    const hash = window.location.hash.substring(1);
    if (hash && ['profile', 'inbox', 'friends', 'settings', 'premium', 'security'].includes(hash)) {
        setTimeout(() => {
            switchAccountTab(hash);
        }, 500);
    }
});

// Make clear search function globally available
window.clearSearch = clearSearch;

// Debug helper function
window.debugFirebase = function() {
    console.log('🔧 Firebase Debug Information:');
    console.log('Firebase available:', !!window.firebase);
    console.log('Firebase initialized:', !!window.firebaseInitialized);
    
    if (window.firebase) {
        console.log('Available functions:', Object.keys(window.firebase));
        console.log('arrayUnion available:', !!window.firebase.arrayUnion);
        console.log('arrayRemove available:', !!window.firebase.arrayRemove);
        console.log('Current user:', window.firebase.auth?.currentUser?.email || 'Not logged in');
        console.log('Current user UID:', window.firebase.auth?.currentUser?.uid || 'Not logged in');
    } else {
        console.log('Firebase not available - check console for initialization errors');
    }
};

// Debug helper to check user search results
window.debugUserSearch = function() {
    const currentUser = window.firebase?.auth?.currentUser;
    console.log('🔧 Current User Debug:');
    console.log('Email:', currentUser?.email);
    console.log('UID:', currentUser?.uid);
    console.log('Display Name:', currentUser?.displayName);
    
    // Test getCurrentUserId
    const getCurrentUserId = () => {
        if (window.firebase && window.firebase.auth && window.firebase.auth.currentUser) {
            const uid = window.firebase.auth.currentUser.uid;
            console.log('🔍 Firebase current user ID:', uid);
            return uid;
        }
        console.log('🔍 Using demo user ID: demo_user');
        return 'demo_user'; // Fallback for demo mode
    };
    
    console.log('getCurrentUserId() returns:', getCurrentUserId());
}; 