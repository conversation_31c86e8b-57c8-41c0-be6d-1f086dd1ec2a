/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables */
:root {
    --primary-color: #00b4ff;
    --primary-hover: #0099e6;
    --secondary-color: #1a1a1a;
    --background-dark: #0a0a0a;
    --background-card: #111111;
    --background-hover: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --border-color: #333333;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --error-color: #ff4444;
    --gradient-primary: linear-gradient(135deg, #00b4ff, #0099e6);
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #0a0a0a);
    --shadow-glow: 0 0 20px rgba(0, 180, 255, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
    --border-radius: 12px;
    --transition-fast: 0.2s ease;
    --transition-smooth: 0.3s ease;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    background: transparent !important;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: transparent;
    color: #fff;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
}

/* Hide scrollbars globally */
::-webkit-scrollbar {
    display: none;
}

/* Ensure no scrollbars on any element */
* {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* Ensure panel doesn't create scrollbars */
.login-panel,
.panel-content {
    overflow: hidden !important;
}

/* Minimal Animated Background */
.bg-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    opacity: 0.3;
}

.bg-gradient {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(0, 180, 255, 0.02) 0%, transparent 60%);
    animation: gradientShift 8s ease-in-out infinite;
}

.bg-particles {
    display: none; /* Hide particles for cleaner look */
}

.bg-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 180, 255, 0.01) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 180, 255, 0.01) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: gridMove 40s linear infinite;
}

.bg-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    animation: orbFloat 15s ease-in-out infinite;
}

.orb-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(0, 180, 255, 0.03) 0%, transparent 70%);
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.orb-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(0, 180, 255, 0.02) 0%, transparent 70%);
    top: 70%;
    right: 30%;
    animation-delay: -5s;
}

.orb-3 {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(0, 180, 255, 0.02) 0%, transparent 70%);
    bottom: 30%;
    left: 70%;
    animation-delay: -10s;
}

/* Main Login Panel */
.login-panel {
    width: 440px;
    background: rgba(17, 17, 17, 0.95);
    backdrop-filter: none; /* Remove backdrop filter to eliminate edge artifacts */
    border-radius: 20px;
    border: none; /* Remove border to eliminate faint box */
    box-shadow: none; /* Remove shadow for clean edges */
    outline: none; /* Ensure no outline */
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
    margin: 0; /* No margin to ensure window fits exactly */
    -webkit-backface-visibility: hidden; /* Prevent rendering artifacts */
    transform: translateZ(0); /* Force hardware acceleration for cleaner edges */
}

/* Custom Title Bar */
.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 1.5rem;
    background: rgba(0, 180, 255, 0.15);
    border-bottom: none; /* Remove border for cleaner look */
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    -webkit-app-region: drag;
}

.title-bar-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.title-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;
}

.title-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.title-bar-controls {
    display: flex;
    gap: 0.5rem;
    -webkit-app-region: no-drag;
}

.title-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.title-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.close-btn:hover {
    background: #ff4444;
    color: white;
}

/* Panel Content */
.panel-content {
    padding: 1.5rem 3rem 2rem 3rem;
}

/* Header Section */
.panel-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-container {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem auto;
}

.logo-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 2;
    position: relative;
    animation: none; /* Disable glow animation that might cause edge artifacts */
    filter: none; /* Remove any filter effects */
}

.logo-glow {
    display: none; /* Hide glow effect that might cause edge artifacts */
}

.app-title {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.3rem;
    animation: textGlow 3s ease-in-out infinite;
}

.app-subtitle {
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 400;
    opacity: 0.8;
    margin-bottom: 0.8rem;
}

.version-info {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    align-items: center;
}

.version-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge {
    background: var(--background-card);
    color: var(--success-color);
    padding: 0.25rem 0.6rem;
    border-radius: 16px;
    font-size: 0.75rem;
    border: none; /* Remove border */
    animation: none; /* Disable animation that might cause artifacts */
}

/* Login Section */
.login-section {
    margin-bottom: 1rem;
}

.login-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.login-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    color: var(--text-primary);
}

.login-header p {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.form-group label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 0.8rem;
    font-size: 1rem;
    color: var(--text-muted);
    z-index: 2;
}

.input-container input {
    width: 100%;
    padding: 0.8rem 0.8rem 0.8rem 2.4rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition-smooth);
    font-family: inherit;
}

.input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: none; /* Remove focus shadow that might extend beyond panel */
}

.input-container input::placeholder {
    color: var(--text-muted);
}

.input-container input.error {
    border-color: #ff4444;
    box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
}

.input-container input.success {
    border-color: #00e676;
    box-shadow: 0 0 0 2px rgba(0, 230, 118, 0.2);
}

.toggle-password {
    position: absolute;
    right: 0.8rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.2rem;
    border-radius: 4px;
    transition: var(--transition-fast);
}

.toggle-password:hover {
    color: var(--text-secondary);
    background: var(--background-hover);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.3rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    cursor: pointer;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 3px;
    position: relative;
    transition: var(--transition-fast);
}

.remember-me input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.remember-me input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-hover);
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--gradient-primary);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    margin-top: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 180, 255, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn.loading {
    pointer-events: none;
}

.login-btn.error {
    background: linear-gradient(90deg, #ff4444 60%, #ff6b6b 100%);
    animation: shakeError 0.5s ease-in-out;
}

.login-btn.success {
    background: linear-gradient(90deg, #00e676 60%, #4caf50 100%);
}

.btn-text {
    transition: var(--transition-fast);
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: var(--transition-fast);
}

.login-btn.loading .btn-text {
    opacity: 0;
}

.login-btn.loading .btn-loader {
    opacity: 1;
}

.spinner {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Demo Credentials */
.demo-credentials {
    background: rgba(0, 180, 255, 0.1);
    border: 1px solid rgba(0, 180, 255, 0.3);
    border-radius: 8px;
    padding: 0.8rem;
    margin-top: 0.8rem;
    text-align: center;
}

.demo-credentials p {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-bottom: 0.4rem;
}

.demo-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--text-primary);
}

.demo-info span {
    background: rgba(0, 180, 255, 0.2);
    padding: 0.15rem 0.4rem;
    border-radius: 4px;
}

/* Login Footer */
.login-footer {
    text-align: center;
    margin-top: 1rem;
}

.login-footer p {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.register-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.register-link:hover {
    color: var(--primary-hover);
}

/* Panel Footer */
.panel-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    text-align: center;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.8rem;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Modern Error Message */
.error-message {
    background: rgba(255, 68, 68, 0.15);
    border: 1px solid rgba(255, 68, 68, 0.3);
    border-radius: 8px;
    padding: 0.6rem 0.8rem;
    margin-top: 0.5rem;
    color: #ff6b6b;
    font-size: 0.85rem;
    text-align: center;
    display: none;
    animation: slideIn 0.3s ease-out;
}

.error-message.show {
    display: block;
}

.success-message {
    background: rgba(0, 230, 118, 0.15);
    border: 1px solid rgba(0, 230, 118, 0.3);
    border-radius: 8px;
    padding: 0.6rem 0.8rem;
    margin-top: 0.5rem;
    color: #4caf50;
    font-size: 0.85rem;
    text-align: center;
    display: none;
    animation: slideIn 0.3s ease-out;
}

.success-message.show {
    display: block;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes particleFloat {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-10px) translateX(10px); }
    66% { transform: translateY(5px) translateX(-5px); }
    100% { transform: translateY(0px) translateX(0px); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes orbFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-20px) scale(1.05); }
}

@keyframes logoGlow {
    0%, 100% { filter: drop-shadow(0 0 5px rgba(0, 180, 255, 0.5)); }
    50% { filter: drop-shadow(0 0 15px rgba(0, 180, 255, 0.8)); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes textGlow {
    0%, 100% { text-shadow: 0 0 10px rgba(0, 180, 255, 0.3); }
    50% { text-shadow: 0 0 20px rgba(0, 180, 255, 0.6); }
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shakeError {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 680px) {
    .login-panel {
        width: 95%;
        max-width: 680px;
    }
    
    .panel-content {
        padding: 1.2rem 2rem 1.5rem 2rem;
    }
    
    .app-title {
        font-size: 1.8rem;
    }
    
    .footer-links {
        gap: 0.8rem;
    }
}

@media (max-width: 500px) {
    .login-panel {
        width: 98%;
        max-width: 450px;
    }
    
    .panel-content {
        padding: 1rem 1.5rem 1.2rem 1.5rem;
    }
    
    .app-title {
        font-size: 1.6rem;
    }
}

/* Dark theme specific enhancements */
@media (prefers-color-scheme: dark) {
    .login-panel {
        background: rgba(17, 17, 17, 0.95);
    }
    
    .input-container input {
        background: rgba(26, 26, 26, 0.8);
    }
    
    .notification {
        background: rgba(17, 17, 17, 0.95);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.login-btn:focus,
.toggle-password:focus,
.remember-me:focus,
.forgot-password:focus,
.register-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading states and transitions */
.fade-enter {
    opacity: 0;
}

.fade-enter-active {
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}

.fade-exit {
    opacity: 1;
}

.fade-exit-active {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
} 

/* Notification System */
.notification-container {
    position: fixed;
    top: 120px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
}

.notification {
    background: rgba(20, 20, 20, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    animation: slideInRight 0.3s ease-out;
    position: relative;
    overflow: hidden;
    min-width: 300px;
}

.notification.success {
    border-left: 4px solid #4CAF50;
    background: rgba(20, 20, 20, 0.95);
}

.notification.error {
    border-left: 4px solid #f44336;
    background: rgba(20, 20, 20, 0.95);
}

.notification.warning {
    border-left: 4px solid #ff9800;
    background: rgba(20, 20, 20, 0.95);
}

.notification.info {
    border-left: 4px solid #2196F3;
    background: rgba(20, 20, 20, 0.95);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
    color: #fff;
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #aaa;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.notification-actions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #fff;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

.notification-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Error and Success states for form elements */
.form-group input.error {
    border-color: #f44336;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.form-group input.success {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.login-btn.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.login-btn.success {
    background: linear-gradient(135deg, #4CAF50, #388e3c);
} 

/* Email Verification Section */
.verification-section {
    text-align: center;
    padding: 40px 20px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.verification-header {
    margin-bottom: 30px;
}

.verification-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: inline-block;
    animation: pulse 2s ease-in-out infinite;
}

.verification-header h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #fff;
}

.verification-header p {
    color: #aaa;
    margin-bottom: 5px;
}

.verification-email {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 16px;
}

.verification-content {
    max-width: 400px;
    margin: 0 auto;
}

.verification-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.loading-spinner {
    width: 24px;
    height: 24px;
}

.verification-info {
    margin-bottom: 30px;
}

.verification-info p {
    color: #ccc;
    margin-bottom: 10px;
    line-height: 1.5;
}

.verification-note {
    font-size: 13px;
    color: #888;
}

.verification-actions {
    margin-bottom: 30px;
}

.resend-btn {
    width: 100%;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.resend-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.resend-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.resend-timer {
    color: var(--primary-color);
    font-weight: 600;
}

.resend-attempts {
    font-size: 13px;
    color: #888;
}

.verification-footer {
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.verification-footer a:hover {
    text-decoration: underline;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
} 

/* Discord-style Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 435px;
    height: 740px;
    z-index: 1000;
    overflow: hidden;
    background: rgba(17, 17, 17, 0.95);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
}

.loading-backdrop {
    display: none; /* Not needed anymore */
}

.loading-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: fadeInUpScale 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards;
}

.loading-logo-container {
    margin: 0 auto 30px;
}

.loading-logo {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.loading-logo-img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    filter: none; /* Remove filter to prevent edge artifacts */
}

.loading-logo-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(0, 174, 239, 0.3);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0.8);
    animation: fadeInScale 0.6s ease-out 0.8s forwards, rotate 3s linear 1.4s infinite;
}

.loading-logo-ring::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: #00aeef;
    animation: rotate 1.5s linear 1.4s infinite;
}

.loading-logo-glow {
    display: none; /* Disable glow to maintain clean edges */
}

.loading-title {
    font-size: 28px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 40px;
    letter-spacing: 3px;
    text-transform: uppercase;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease-out 0.9s forwards;
}

.loading-progress {
    margin-bottom: 30px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease-out 1.1s forwards;
}

.loading-progress-bar {
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 16px;
    position: relative;
}

.loading-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0, 174, 239, 0.3), 
        transparent
    );
    animation: shimmer 2s linear 1.5s infinite;
}

.loading-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00aeef, #00ccff);
    border-radius: 3px;
    width: 0%;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: none; /* Remove shadow to prevent edge artifacts */
    position: relative;
    z-index: 1;
}

.loading-status {
    font-size: 13px;
    color: #888;
    margin: 0;
    font-weight: 500;
    transition: opacity 0.2s ease;
}

.loading-tip {
    padding: 14px 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.06);
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease-out 1.3s forwards;
}

.loading-tip p {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
    transition: opacity 0.3s ease;
    font-style: italic;
}

/* Exit animation class */
.loading-screen.exiting {
    animation: fadeOut 0.6s ease-out forwards;
}

.loading-screen.exiting .loading-backdrop {
    opacity: 0;
    transition: opacity 0.6s ease-out;
}

.loading-screen.exiting .loading-content {
    animation: fadeOutScale 0.5s ease-out forwards;
}

/* Backdrop animations removed - not needed */

@keyframes fadeInUpScale {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeOutScale {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-30px) scale(1.05);
    }
}

@keyframes fadeInScale {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    to {
        opacity: 0;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.2;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes shimmer {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(100%);
    }
} 

/* Section transitions */
.login-section, .register-section, .verification-section {
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.login-section.hiding, .register-section.hiding, .verification-section.hiding {
    opacity: 0;
    transform: scale(0.95);
}

/* Login panel fade effect */
.login-panel {
    transition: opacity 0.4s ease-out;
}

/* Smooth page transition */
body {
    transition: opacity 0.3s ease-out;
}

body.exiting {
    opacity: 0;
}

/* Ensure body takes minimal space */
body {
    width: 440px;
    height: auto;
    min-height: auto;
    padding: 0;
    margin: 0;
    position: relative;
}

/* Hide section backgrounds */
.login-section, .register-section, .verification-section {
    background: transparent;
}

/* Removed duplicate - see main loading screen styles below */

/* Panel shrink animation when transitioning to loading */
.login-panel.loading-transition {
    animation: panelFadeOut 0.3s ease-out forwards;
}

@keyframes panelFadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
} 