# RGB Engine Documentation

## Overview

The RGB Engine is a fully standalone RGB control system for Armory X that provides SignalRGB-like functionality without requiring external programs. It features a 60 FPS render loop, built-in effects, and native protocol implementations for major RGB vendors.

## Architecture

### Core Components

1. **RGB Engine (`modules/rgb-engine.js`)**
   - 60 FPS continuous render loop
   - Event-driven architecture
   - Device management
   - Effect system
   - Brightness control

2. **Protocol Modules (`modules/protocols/`)**
   - `corsair-protocol.js` - Corsair devices (iCUE)
   - `msi-protocol.js` - MSI Mystic Light devices
   - `steelseries-protocol.js` - SteelSeries devices

3. **RGB Manager (`modules/rgb-manager.js`)**
   - High-level API wrapper
   - IPC integration
   - Legacy compatibility

## Features

### Built-in Effects
- **Static Color** - Solid color across all LEDs
- **Rainbow Wave** - Animated rainbow gradient
- **Breathing** - Pulsing color effect
- **Color Cycle** - Cycles through multiple colors

### Device Support
- **Corsair**
  - COMMANDER CORE XT (204 LEDs)
  - Lighting Node CORE (60 LEDs)
  - iCUE compatible devices

- **MSI**
  - MYSTIC LIGHT motherboards
  - MSI graphics cards
  - MSI RAM modules

- **SteelSeries**
  - Apex Pro Gen 3 keyboards
  - Other SteelSeries RGB devices

## API Usage

### Starting the Engine
```javascript
const RGBManager = require('./modules/rgb-manager');
const rgbManager = new RGBManager();

// Start the engine
rgbManager.start();

// List devices
const devices = rgbManager.listDevices();
```

### Setting Colors
```javascript
// Set static color (RGB array)
rgbManager.setDeviceColor(deviceId, [255, 0, 0]); // Red

// Set effect
rgbManager.setDeviceEffect(deviceId, 'rainbow', { speed: 1 });

// Set brightness (0-100)
rgbManager.setDeviceBrightness(deviceId, 50);

// Enable/disable device
rgbManager.setDeviceEnabled(deviceId, false);
```

### IPC Handlers

The following IPC handlers are available:

- `rgb-get-devices` - List all RGB devices
- `rgb-set-color` - Set device color
- `rgb-apply-preset` - Apply effect preset
- `rgb-refresh-devices` - Refresh device list
- `rgb-get-effects` - Get available effects
- `rgb-set-effect` - Set device effect
- `rgb-set-brightness` - Set device brightness
- `rgb-set-enabled` - Enable/disable device
- `rgb-start-engine` - Start RGB engine
- `rgb-stop-engine` - Stop RGB engine
- `rgb-apply-color-all` - Apply color to all devices
- `rgb-apply-color-selected` - Apply color to selected devices

## Technical Details

### Rendering System
- Runs at 60 FPS using `setImmediate` for optimal performance
- Each frame calculates colors based on current effect and time
- Applies brightness adjustments before sending to devices
- Handles device disconnections gracefully

### Protocol Implementation
- Uses node-hid for direct USB communication
- No external dependencies on vendor software
- Implements vendor-specific packet structures
- Supports both legacy and modern protocols

### Memory Management
- Efficient buffer reuse
- Lazy device initialization
- Automatic cleanup on shutdown

## Troubleshooting

### No Devices Detected
1. Close all RGB software (iCUE, SignalRGB, etc.)
2. Check USB connections
3. Verify device power
4. Run as administrator

### Colors Not Changing
1. Ensure RGB Engine is started
2. Check device is enabled
3. Verify correct protocol implementation
4. Check for USB write permissions

### Performance Issues
1. Reduce effect complexity
2. Disable unused devices
3. Check CPU usage
4. Verify USB bandwidth

## Future Enhancements

1. **Additional Effects**
   - Wave patterns
   - Audio reactive
   - Game integration

2. **More Device Support**
   - Razer Chroma
   - ASUS Aura
   - Gigabyte RGB Fusion

3. **Advanced Features**
   - Per-LED control
   - Custom effect scripting
   - Profile system
   - Hardware sync

## Contributing

To add support for new devices:

1. Create a new protocol module in `modules/protocols/`
2. Implement `initialize()`, `sendColors()`, and `close()` methods
3. Add device detection in `rgb-engine.js`
4. Test with actual hardware

The RGB Engine is designed to be fully standalone as per user requirements, providing professional-grade RGB control without external dependencies. 