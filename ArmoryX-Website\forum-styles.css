/* Forum-specific styles */

/* Forum Header */
.forum-header {
    background: var(--bg-secondary);
    padding: 6rem 0 4rem;
    margin-top: 70px;
    border-bottom: 1px solid var(--border-color);
}

.forum-header-content {
    text-align: center;
}

.forum-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.forum-title i {
    color: var(--primary-color);
}

.forum-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.forum-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
}

.forum-stats .stat {
    text-align: center;
}

.forum-stats .stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.forum-stats .stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* <PERSON>gin Required Section */
.login-required {
    background: var(--bg-primary);
    padding: 6rem 0;
    text-align: center;
}

.login-message {
    max-width: 600px;
    margin: 0 auto;
    background: var(--bg-secondary);
    padding: 3rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.login-message i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.login-message h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.login-message p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.login-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Forum Content */
.forum-content {
    background: var(--bg-primary);
    padding: 4rem 0;
}

/* Forum Categories */
.forum-categories {
    margin-bottom: 3rem;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.category-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
}

.new-post-btn {
    padding: 0.75rem 1.5rem;
}

.forum-category {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.forum-category:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.category-icon i {
    font-size: 1.3rem;
    color: white;
}

.category-info {
    flex: 1;
}

.category-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.category-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
}

.posts-count {
    color: var(--primary-color);
    font-weight: 500;
}

.last-post {
    color: var(--text-muted);
}

.category-arrow {
    color: var(--text-muted);
    font-size: 1.2rem;
}

/* Recent Posts */
.recent-posts h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.post-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.post-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.post-avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-primary);
    border-radius: 50%;
    flex-shrink: 0;
}

.post-avatar i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.post-content {
    flex: 1;
}

.post-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
}

.post-author {
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    min-width: 0;
    flex-shrink: 0;
}

.post-author i {
    margin-top: -1px;
    flex-shrink: 0;
}

.post-author .author-name {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.post-time {
    color: var(--text-muted);
}

.post-category {
    color: var(--accent-color);
    background: rgba(6, 182, 212, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.post-preview {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.post-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
    font-size: 0.8rem;
}

.replies {
    color: var(--primary-color);
    font-weight: 500;
}

.views {
    color: var(--text-muted);
}

/* Active nav link */
.nav-link.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .forum-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .forum-stats {
        gap: 2rem;
    }

    .category-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .forum-category {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .category-stats {
        justify-content: center;
    }

    .post-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .post-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .post-stats {
        flex-direction: row;
        justify-content: center;
        gap: 1rem;
    }

    .login-buttons {
        flex-direction: column;
    }
}

/* Forum Tabs */
.forum-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.forum-tab {
    background: transparent;
    border: none;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.forum-tab:hover {
    color: var(--text-primary);
    background: rgba(45, 114, 255, 0.05);
    border-radius: 8px 8px 0 0;
}

.forum-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(45, 114, 255, 0.1);
    border-radius: 8px 8px 0 0;
}

.forum-tab i {
    font-size: 0.9rem;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Users Tab */
.forum-users {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.users-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
}

.users-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
}

.users-stats span {
    color: var(--text-secondary);
}

.users-stats strong {
    color: var(--primary-color);
}

.users-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.user-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.user-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    position: relative;
}

.user-avatar i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.user-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: 0.25rem;
}

.user-email {
    font-size: 0.85rem;
    color: var(--text-muted);
    text-align: center;
    margin-bottom: 1rem;
}

.user-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.user-stats span {
    color: var(--text-secondary);
}

.user-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
}

.status-indicator.online {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.recent {
    background: #f59e0b;
}

.user-join-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: center;
}

/* Forum Message Styles */
.forum-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
    color: var(--text-primary);
    position: fixed;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3000;
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.forum-message.info {
    border-left-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.forum-message.success {
    border-left-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.forum-message.warning {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.forum-message.error {
    border-left-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.forum-message i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.forum-message span {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.message-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* Category Post View */
.category-posts {
    display: none;
}

.category-posts.active {
    display: block;
}

.category-posts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.category-posts-header .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.category-posts-header .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.category-posts-header .header-right {
    display: flex;
    align-items: center;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--bg-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.sort-controls label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

.sort-controls label i {
    margin-right: 0.25rem;
    color: var(--primary-color);
}

.sort-controls select {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sort-controls select:hover {
    border-color: var(--primary-color);
}

.sort-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(45, 114, 255, 0.1);
}

.reply-sort-controls {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--bg-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.reply-sort-controls label {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.reply-sort-controls label i {
    margin-right: 0.25rem;
    color: var(--primary-color);
}

.reply-sort-controls select {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reply-sort-controls select:hover {
    border-color: var(--primary-color);
}

.reply-sort-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(45, 114, 255, 0.1);
}

.back-to-categories {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-to-categories:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* New Post Form */
.new-post-form {
    display: none;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.new-post-form.active {
    display: block;
}

.new-post-form h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 200px;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    margin-bottom: 0;
}

select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
}

textarea {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Error Message Styles */
.error-message {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.error-message i {
    font-size: 2rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.error-details {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
}

.loading-message {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* No Posts Message */
.no-posts-message {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.no-posts-message i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.no-posts-message h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.no-posts-message p {
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

/* Sticky Posts */
.post-item.sticky {
    border-color: var(--accent-color);
    background: rgba(6, 182, 212, 0.05);
}

.sticky-icon {
    color: var(--accent-color);
    margin-right: 0.5rem;
}

/* Post Detail Modal */
.post-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.post-modal.active {
    opacity: 1;
    visibility: visible;
}

.post-modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.post-modal-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background: var(--bg-secondary);
    z-index: 1;
}

.post-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.post-modal-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.post-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-right: 3rem;
}

.post-author-details {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.author-avatar i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.author-info {
    flex: 1;
}

.author-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.author-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.reputation-badge.negative {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.post-modal-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.post-modal-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 500;
}

.post-modal-body {
    padding: 2rem;
}

.post-content-full {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: 2rem;
    white-space: pre-wrap;
}

.post-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.like-button, .dislike-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.like-button:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(45, 114, 255, 0.05);
}

.dislike-button:hover {
    border-color: #ef4444;
    color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

.like-button.liked {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.like-button.liked i {
    animation: heartPulse 0.3s ease;
}

.dislike-button.disliked {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: scale(1.05);
}

.dislike-button.disliked i {
    animation: heartBreak 0.3s ease;
}

.like-button i, .dislike-button i {
    font-size: 1rem;
}

@keyframes heartPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes heartBreak {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(-5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.reply-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.reply-button:hover {
    background: var(--primary-dark);
}

/* Replies Section */
.replies-section {
    padding: 2rem;
}

.replies-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.replies-header h3 {
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.replies-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    transition: opacity 0.3s ease;
}

.reply-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    position: relative;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.reply-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.reply-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reply-time {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.reply-actions-dropdown {
    position: relative;
}

.reply-options-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.reply-options-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.reply-options-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 120px;
}

.reply-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.5rem 0.75rem;
    background: none;
    border: none;
    color: var(--text-primary);
    text-align: left;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.reply-option:hover {
    background: var(--bg-primary);
}

.reply-option.delete-option {
    color: #ef4444;
}

.reply-option.delete-option:hover {
    background: rgba(239, 68, 68, 0.1);
}

.mod-indicator {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-style: italic;
}

.reply-edit-form {
    margin: 0.5rem 0;
}

.reply-edit-form textarea {
    width: 100%;
    min-height: 80px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 0.5rem;
    font-size: 0.9rem;
    resize: vertical;
    font-family: inherit;
}

.reply-edit-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(45, 114, 255, 0.1);
}

.reply-edit-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 0.5rem;
}

.reply-edit-actions .btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.edited-indicator {
    font-style: italic;
    color: var(--text-muted);
    font-size: 0.8rem;
}

.timestamp-updated {
    animation: timestampUpdate 0.3s ease;
}

@keyframes timestampUpdate {
    0% { color: var(--primary-color); }
    100% { color: var(--text-secondary); }
}

.reply-content {
    color: var(--text-primary);
    line-height: 1.5;
    margin-bottom: 0.5rem;
    white-space: pre-wrap;
}

.reply-actions {
    display: flex;
    gap: 0.5rem;
}

.reply-like-button, .reply-dislike-button {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.reply-like-button:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

.reply-dislike-button:hover {
    background: var(--bg-secondary);
    color: #ef4444;
}

.reply-like-button.liked {
    color: var(--primary-color);
    background: rgba(45, 114, 255, 0.1);
    font-weight: 600;
}

.reply-like-button.liked i {
    animation: heartPulse 0.2s ease;
}

.reply-dislike-button.disliked {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    font-weight: 600;
}

.reply-dislike-button.disliked i {
    animation: heartBreak 0.2s ease;
}

/* Reply Form */
.reply-form {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
}

.reply-form h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reply-form textarea {
    width: 100%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    padding: 0.75rem;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    margin-bottom: 1rem;
}

.reply-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.reply-form-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.cancel-reply {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-reply:hover {
    border-color: var(--text-secondary);
    color: var(--text-primary);
}

/* No Replies Message */
.no-replies {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Reputation Badge */
.reputation-badge {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.reputation-badge i {
    font-size: 0.7rem;
}

/* Mobile Responsive for Post Modal */
@media (max-width: 768px) {
    .post-modal-content {
        width: 95%;
        max-height: 95vh;
        margin: 1rem;
    }
    
    .post-modal-header {
        padding: 1.5rem;
    }
    
    .post-modal-title {
        font-size: 1.25rem;
        padding-right: 2.5rem;
    }
    
    .post-modal-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .post-modal-body {
        padding: 1.5rem;
    }
    
    .post-actions {
        padding: 1rem 1.5rem;
        flex-wrap: wrap;
    }
    
    .replies-section {
        padding: 1.5rem;
    }
    
    .reply-form {
        padding: 1rem;
    }
    
    .reply-form textarea {
        min-height: 80px;
    }
    
    .reply-form-actions {
        flex-direction: column;
    }
    
    .reply-form-actions .btn {
        width: 100%;
    }
}

/* OP Badge and Controls */
.op-badge {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    text-shadow: none;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.op-badge i {
    font-size: 0.7rem;
}

.op-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-left: auto;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    min-width: unset;
    border-radius: 4px;
}

.btn-danger {
    background: #ef4444;
    color: white;
    border: 1px solid #ef4444;
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
}

/* Post Edit Form */
.post-edit-form {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid var(--border-color);
}

.post-edit-form h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.post-edit-form .form-group {
    margin-bottom: 1rem;
}

.post-edit-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.post-edit-form input,
.post-edit-form textarea,
.post-edit-form select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.post-edit-form input:focus,
.post-edit-form textarea:focus,
.post-edit-form select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(45, 114, 255, 0.1);
}

.post-edit-form textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.5;
}

.edit-form-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Enhanced Post Author Details */
.post-author-details {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    position: relative;
}

.post-author-details .op-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Moderation Styles */
.mod-badge {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

/* Confirmation Dialog Styles */
.confirm-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: confirmOverlayFadeIn 0.3s ease-out;
}

.confirm-dialog {
    background: var(--bg-secondary);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    border: 1px solid var(--border-color);
    max-width: 450px;
    width: 90%;
    position: relative;
    animation: confirmDialogSlideIn 0.3s ease-out;
    overflow: hidden;
}

.confirm-dialog-header {
    padding: 2rem;
    text-align: center;
    position: relative;
}

.confirm-dialog-header.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.confirm-dialog-header.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.confirm-dialog-header.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.confirm-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    animation: confirmIconPulse 2s infinite;
}

.confirm-icon i {
    font-size: 2.5rem;
    color: white;
}

.confirm-dialog-header h3 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.confirm-dialog-content {
    padding: 2rem;
    text-align: center;
}

.confirm-dialog-content p {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.confirm-dialog-actions {
    display: flex;
    gap: 1rem;
    padding: 0 2rem 2rem;
    justify-content: center;
}

.confirm-dialog-actions .btn {
    flex: 1;
    max-width: 140px;
    justify-content: center;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.confirm-dialog-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.confirm-dialog-actions .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    color: white;
}

.confirm-dialog-actions .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.confirm-dialog-actions .btn-secondary {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.confirm-dialog-actions .btn-secondary:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--text-secondary);
}

/* Dialog Animations */
@keyframes confirmOverlayFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes confirmDialogSlideIn {
    from { 
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to { 
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes confirmIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.dialog-closing {
    animation: confirmOverlayFadeOut 0.3s ease-out forwards;
}

.dialog-closing .confirm-dialog {
    animation: confirmDialogSlideOut 0.3s ease-out forwards;
}

@keyframes confirmOverlayFadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes confirmDialogSlideOut {
    from { 
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    to { 
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
}

/* Mobile responsiveness for confirmation dialog */
@media (max-width: 768px) {
    .confirm-dialog {
        width: 95%;
        margin: 1rem;
    }
    
    .confirm-dialog-header {
        padding: 1.5rem;
    }
    
    .confirm-icon {
        width: 60px;
        height: 60px;
    }
    
    .confirm-icon i {
        font-size: 2rem;
    }
    
    .confirm-dialog-header h3 {
        font-size: 1.25rem;
    }
    
    .confirm-dialog-content {
        padding: 1.5rem;
    }
    
    .confirm-dialog-content p {
        font-size: 1rem;
    }
    
    .confirm-dialog-actions {
        flex-direction: column;
        padding: 0 1.5rem 1.5rem;
    }
    
    .confirm-dialog-actions .btn {
        max-width: none;
    }
}

.moderation-controls {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.mod-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-mod-edit,
.btn-mod-delete,
.btn-mod-ban {
    background: none;
    border: 1px solid;
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-mod-edit {
    border-color: #3b82f6;
    color: #3b82f6;
}

.btn-mod-edit:hover {
    background: #3b82f6;
    color: white;
}

.btn-mod-delete {
    border-color: #ef4444;
    color: #ef4444;
}

.btn-mod-delete:hover {
    background: #ef4444;
    color: white;
}

.btn-mod-ban {
    border-color: #f59e0b;
    color: #f59e0b;
}

.btn-mod-ban:hover {
    background: #f59e0b;
    color: white;
}

.btn-report {
    background: none;
    border: 1px solid #6b7280;
    color: #6b7280;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-report:hover {
    background: #6b7280;
    color: white;
}

/* Admin/Moderator Indicators */
.user-role-admin::after {
    content: "👑";
    margin-left: 0.5rem;
    font-size: 0.8rem;
}

.user-role-moderator::after {
    content: "🛡️";
    margin-left: 0.5rem;
    font-size: 0.8rem;
}

.user-email-unverified::after {
    content: "unverified";
    margin-left: 0.5rem;
    font-size: 0.7rem;
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.2);
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Enhanced OP Controls with Moderation */
.op-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.op-controls .mod-badge {
    margin-left: 0;
}

/* Banned User Indicator */
.user-banned {
    opacity: 0.6;
    position: relative;
}

.user-banned::before {
    content: "🚫 BANNED";
    position: absolute;
    top: 0;
    right: 0;
    background: #ef4444;
    color: white;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
}

/* Report Status Indicators */
.content-reported {
    border-left: 3px solid #f59e0b;
    background: rgba(245, 158, 11, 0.05);
}

.content-reported::before {
    content: "📝 REPORTED";
    display: block;
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    font-weight: bold;
}

/* Moderation Log Entries */
.mod-log-entry {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.mod-log-action {
    font-weight: bold;
    color: var(--primary-color);
    text-transform: uppercase;
}

.mod-log-reason {
    font-style: italic;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Responsive Design for New Elements */
@media (max-width: 768px) {
    .forum-tabs {
        flex-wrap: wrap;
    }
    
    .forum-tab {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
    
    .users-header {
        flex-direction: column;
        text-align: center;
    }
    
    .users-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .users-list {
        grid-template-columns: 1fr;
    }
    
    /* Mobile responsive for OP controls */
    .post-author-details {
        flex-direction: column;
        gap: 0.75rem;
        padding-right: 1rem;
    }
    
    .post-author-details .op-controls {
        position: static;
        margin-left: 0;
        justify-content: flex-start;
        width: 100%;
    }
    
    .op-controls {
        flex-wrap: wrap;
    }
    
    .edit-form-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .edit-form-actions .btn {
        width: 100%;
    }
    
    .post-edit-form {
        padding: 1rem;
    }
    
    /* Mobile responsive for moderation */
    .mod-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-mod-edit,
    .btn-mod-delete,
    .btn-mod-ban,
    .btn-report {
        width: 100%;
        justify-content: center;
    }
    
    .moderation-controls {
        padding: 0.75rem;
    }
    
    .mod-badge {
        font-size: 0.65rem;
        padding: 0.2rem 0.5rem;
    }
} 