<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Armory X - Animation Demo</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-section {
            padding: 4rem 0;
            margin: 2rem 0;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .demo-card {
            background: var(--bg-secondary);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            text-align: center;
        }
        .demo-title {
            font-size: 2rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .controls-section {
            background: var(--bg-secondary);
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            text-align: center;
        }
        .control-btn {
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Demo Content -->
    <div class="container" style="padding-top: 2rem;">
        <h1 class="demo-title gradient-text-animated">🎨 Armory X Animation Demo</h1>
        <p style="text-align: center; color: var(--text-secondary); margin-bottom: 3rem;">
            Experience all the visual effects and animations in action!
        </p>

        <!-- Animation Controls -->
        <div class="controls-section">
            <h2>Animation Controls</h2>
            <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                Use these controls to test the animation system:
            </p>
            <button class="control-btn" onclick="window.toggleAnimations?.()">
                <i class="fas fa-play-pause"></i> Toggle All Animations
            </button>
            <button class="control-btn" onclick="window.pauseAnimations?.()">
                <i class="fas fa-pause"></i> Pause Animations
            </button>
            <button class="control-btn" onclick="window.resumeAnimations?.()">
                <i class="fas fa-play"></i> Resume Animations
            </button>
            <button class="control-btn glow-effect" onclick="createTestShootingStar()">
                <i class="fas fa-star"></i> Create Shooting Star
            </button>
        </div>

        <!-- Starfield Demo -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">✨ Starfield Background</h2>
            <p style="text-align: center; color: var(--text-secondary);">
                The animated starfield with twinkling stars and shooting stars is always active in the background!
            </p>
        </div>

        <!-- Button Effects Demo -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">🔥 Enhanced Button Effects</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>Primary Button</h3>
                    <button class="btn btn-primary glow-effect">
                        <i class="fas fa-rocket"></i> Hover Me!
                    </button>
                </div>
                <div class="demo-card">
                    <h3>Secondary Button</h3>
                    <button class="btn btn-secondary">
                        <i class="fas fa-info"></i> Info Button
                    </button>
                </div>
                <div class="demo-card">
                    <h3>Pulse Effect</h3>
                                         <div class="live-indicator pulse-glow"></div>
                     <p style="margin-top: 0.5rem; font-size: 0.8rem;">Live Indicator</p>
                </div>
            </div>
        </div>

        <!-- Card Hover Effects -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">🎯 Card Hover Effects</h2>
            <p style="text-align: center; color: var(--text-secondary); margin-bottom: 2rem;">
                Hover over these cards to see particle effects and animations!
            </p>
            <div class="demo-grid">
                <div class="feature-card reveal-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h3>Hover Effect 1</h3>
                    <p>This card will lift up and show particles when you hover over it!</p>
                </div>
                <div class="feature-card reveal-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-sparkles"></i>
                    </div>
                    <h3>Hover Effect 2</h3>
                    <p>Watch for the floating particles and smooth transformations!</p>
                </div>
                <div class="feature-card reveal-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Hover Effect 3</h3>
                    <p>Each hover creates unique particle animations!</p>
                </div>
            </div>
        </div>

        <!-- Forum-style Interactions -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">💬 Forum Interaction Effects</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>Like Button</h3>
                    <button class="like-button" onclick="this.classList.toggle('liked'); addSparkleEffect?.(this)">
                        <i class="fas fa-heart"></i> <span>42</span>
                    </button>
                </div>
                <div class="demo-card">
                    <h3>Dislike Button</h3>
                    <button class="dislike-button" onclick="this.classList.toggle('disliked')">
                        <i class="fas fa-heart-broken"></i> <span>3</span>
                    </button>
                </div>
                <div class="demo-card">
                    <h3>Success Ripple</h3>
                    <button class="btn btn-primary" onclick="addSuccessRipple?.(this)">
                        <i class="fas fa-check"></i> Click for Ripple
                    </button>
                </div>
            </div>
        </div>

        <!-- Gradient Text Demo -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title gradient-text-animated">🌈 Animated Gradient Text</h2>
            <p style="text-align: center;">
                <span class="gradient-text-animated" style="font-size: 1.5rem;">
                    This text has an animated gradient that shifts colors!
                </span>
            </p>
        </div>

        <!-- Loading Animation Demo -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">⏳ Loading Animations</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>Loading Dots</h3>
                    <div class="loading-dots"></div>
                </div>
                <div class="demo-card">
                    <h3>Spinning Loader</h3>
                    <div style="display: inline-block; width: 30px; height: 30px; border: 3px solid rgba(59, 130, 246, 0.3); border-left-color: #3b82f6; border-radius: 50%; animation: loadingSpin 1s linear infinite;"></div>
                </div>
            </div>
        </div>

        <!-- Scroll Reveal Demo -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">📜 Scroll Reveal Effects</h2>
            <p style="text-align: center; color: var(--text-secondary);">
                Elements fade in and slide up as you scroll down the page!
            </p>
            <div class="demo-grid">
                <div class="demo-card reveal-on-scroll">Reveal 1</div>
                <div class="demo-card reveal-on-scroll">Reveal 2</div>
                <div class="demo-card reveal-on-scroll">Reveal 3</div>
                <div class="demo-card reveal-on-scroll">Reveal 4</div>
            </div>
        </div>

        <!-- Performance Info -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">⚡ Performance Features</h2>
            <div class="demo-card">
                <h3>Optimizations Built-in</h3>
                <ul style="text-align: left; color: var(--text-secondary);">
                    <li>✅ Automatic cleanup of particles and shooting stars</li>
                    <li>✅ Reduced animations on mobile devices</li>
                    <li>✅ Respects user's "prefers-reduced-motion" setting</li>
                    <li>✅ Animations pause when tab is not active</li>
                    <li>✅ Throttled to ~60fps for smooth performance</li>
                </ul>
            </div>
        </div>

        <!-- Console Commands -->
        <div class="demo-section reveal-on-scroll">
            <h2 class="demo-title">🛠️ Developer Tools</h2>
            <div class="demo-card">
                <h3>Console Commands</h3>
                <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                    Open browser console (F12) and try these commands:
                </p>
                <code style="display: block; background: var(--bg-primary); padding: 1rem; border-radius: 8px; margin: 0.5rem 0;">
                    toggleAnimations() // Toggle all animations<br>
                    pauseAnimations()  // Pause all animations<br>
                    resumeAnimations() // Resume all animations
                </code>
            </div>
        </div>
    </div>

    <script>
        // Test function to create shooting stars on demand
        function createTestShootingStar() {
            if (window.armoryXAnimations) {
                const shootingStar = document.createElement('div');
                shootingStar.className = 'shooting-star';
                
                const startX = Math.random() * window.innerWidth;
                const startY = Math.random() * (window.innerHeight * 0.3);
                
                shootingStar.style.left = startX + 'px';
                shootingStar.style.top = startY + 'px';
                
                document.body.appendChild(shootingStar);
                
                setTimeout(() => {
                    if (shootingStar.parentNode) {
                        shootingStar.parentNode.removeChild(shootingStar);
                    }
                }, 3000);
            }
        }

        // Add demo-specific interactions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 Animation Demo loaded!');
            console.log('Try the console commands: toggleAnimations(), pauseAnimations(), resumeAnimations()');
        });
    </script>
    <script src="animations.js"></script>
</body>
</html> 