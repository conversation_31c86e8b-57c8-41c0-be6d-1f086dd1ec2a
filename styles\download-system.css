/* ===================================
   Download System Interface
   =================================== */

/* Add Mods Modal Styling */
.add-mods-modal-wide {
    max-width: 700px !important;
    width: 85vw !important;
    min-width: 500px !important;
    transform: scale(0.8) !important;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

.custom-modal-overlay.active .add-mods-modal-wide {
    transform: scale(1) !important;
}

.add-mods-modal-wide .custom-modal-body {
    max-height: 60vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 1.5rem !important;
}

/* Enhanced Download Mods Modal */
.enhanced-download-mods-modal {
    max-width: 650px !important;
    width: 90vw !important;
    min-width: 500px !important;
    transform: scale(0.8) !important;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

.custom-modal-overlay.active .enhanced-download-mods-modal {
    transform: scale(1) !important;
}

.enhanced-download-mods-modal .custom-modal-body {
    max-height: 60vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 1.5rem !important;
}

.enhanced-download-mods-modal .download-mods-container {
    max-height: none !important;
    overflow: visible !important;
    padding-right: 0 !important;
}

/* Download Categorization Modal */
.download-categorization-modal .modal-content {
    max-width: 600px;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
}

.download-categorization-modal .modal-overlay {
    pointer-events: auto;
}

/* Browser Loading Modal */
.browser-loading-modal .modal-content {
    max-width: 550px;
    text-align: center;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 16px;
}

.browser-loading-modal .modal-overlay {
    pointer-events: none;
}

.browser-loading-modal .modal-content {
    pointer-events: auto;
}

/* Download Progress Indicators */
.download-progress-container {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.download-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.download-filename {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
}

.download-status {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
}

.download-progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.download-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #2563eb);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.download-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    animation: downloadShimmer 2s infinite;
}

.download-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.download-speed {
    color: var(--primary-color);
    font-weight: 500;
}

.download-eta {
    color: var(--text-secondary);
}

/* Download Queue */
.download-queue {
    max-height: 300px;
    overflow-y: auto;
    margin: 1rem 0;
}

.download-queue-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.download-queue-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(59, 130, 246, 0.3);
}

.download-queue-item.downloading {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.download-queue-item.completed {
    border-color: var(--success-color);
    background: rgba(34, 197, 94, 0.05);
}

.download-queue-item.failed {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.05);
}

.download-queue-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.download-queue-icon.pending {
    color: var(--text-muted);
}

.download-queue-icon.downloading {
    color: var(--primary-color);
    animation: downloadSpin 1s linear infinite;
}

.download-queue-icon.completed {
    color: var(--success-color);
}

.download-queue-icon.failed {
    color: var(--error-color);
}

.download-queue-info {
    flex: 1;
    min-width: 0;
}

.download-queue-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
}

.download-queue-size {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.download-queue-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.download-queue-btn {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-queue-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

.download-queue-btn.danger:hover {
    border-color: var(--error-color);
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

/* Download Statistics */
.download-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.download-stat {
    text-align: center;
}

.download-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.download-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* File Browser Integration */
.file-browser-download {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 6px;
    padding: 0.5rem;
    margin: 0.5rem 0;
}

.file-browser-download-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.file-browser-download-icon {
    color: var(--primary-color);
    font-size: 1rem;
}

.file-browser-download-text {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.file-browser-download-path {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-family: monospace;
    word-break: break-all;
}

/* Animations */
@keyframes downloadShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes downloadSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .add-mods-modal-wide,
    .enhanced-download-mods-modal {
        width: 95vw !important;
        min-width: 300px !important;
        max-width: none !important;
    }
    
    .download-progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .download-filename {
        max-width: 100%;
    }
    
    .download-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .download-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 0.75rem;
    }
    
    .download-queue-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .download-queue-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
