// Use existing Electron setup from app.js
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

// Simple Modern Dashboard Manager
class ModernDashboard {
    constructor() {
        this.apps = [];
        this.quickAccess = [];
        this.currentPath = '';
        this.navigationHistory = [];
        this.selectedFile = null;
        this.currentCalendarDate = null;
        this.clockInterval = null;
        this.welcomeInterval = null;
        this.clockSettings = {
            timeFormat: '12', // '12' or '24'
            showSeconds: true,
            showCalendar: true,
            showWeekdays: true,
            clockAnimation: true
        };
        this.init();
    }

    init() {
        console.log('🎯 Initializing Modern Dashboard...');
        
        // Track initialization attempts
        this.initAttempts = (this.initAttempts || 0) + 1;
        
        // Wait for Electron to be ready
        if (window.ipcRenderer && window.electronAPI) {
            this.setup();
        } else if (this.initAttempts < 50) { // Max 50 attempts (5 seconds)
            setTimeout(() => this.init(), 100);
        } else {
            console.warn('⚠️ Dashboard initialization timeout - proceeding without Electron API');
            this.setup();
        }
    }

    setup() {
        try {
            // Ensure modals are hidden on startup
            this.ensureModalsHidden();
            
            this.loadQuickLaunchApps();
            this.loadQuickAccess();
            this.initializeClockCalendar();
            this.loadClockSettings(); // Load after clock is initialized
            this.setupEventListeners();
            console.log('✅ Modern Dashboard initialized');
        } catch (error) {
            console.error('❌ Dashboard setup failed:', error);
        }
    }

    setupEventListeners() {
        // Global functions for HTML onclick handlers
        window.addQuickLaunchApp = () => this.showFileBrowser();
        window.addQuickAccessItem = () => this.showQuickAccessModal();
        window.changeMonth = (direction) => this.changeMonth(direction);
        window.openTaskManager = () => this.openTaskManager();
        
        // Clock Settings functions
        window.openClockSettings = () => this.openClockSettings();
        window.closeClockSettings = () => this.closeClockSettings();
        window.changeTimeFormat = () => this.changeTimeFormat();

        window.toggleSeconds = () => this.toggleSeconds();
        window.toggleCalendar = () => this.toggleCalendar();
        window.toggleWeekdays = () => this.toggleWeekdays();
        window.toggleClockAnimation = () => this.toggleClockAnimation();
        window.resetClockSettings = () => this.resetClockSettings();
        
        // Quick Access Modal functions
        window.closeQuickAccessModal = () => this.closeQuickAccessModal();
        window.addQuickAccessPage = (pageId, icon, name) => this.addQuickAccessPage(pageId, icon, name);
        window.addQuickAccessTool = (command, icon, name) => this.addQuickAccessTool(command, icon, name);
        window.openFileBrowser = () => this.showFileBrowser();
        
        // File Browser functions
        window.closeFileBrowser = () => this.closeFileBrowser();
        window.fileBrowserBack = () => this.fileBrowserBack();
        window.fileBrowserUp = () => this.fileBrowserUp();
        window.refreshFileBrowser = () => this.refreshFileBrowser();
        window.navigateToPath = (path) => this.navigateToPath(path);
        window.confirmFileSelection = () => this.confirmFileSelection();
        window.getUserHome = () => os.homedir();
        window.getUserDesktop = () => path.join(os.homedir(), 'Desktop');
        window.getUserDocuments = () => path.join(os.homedir(), 'Documents');
        window.getUserDownloads = () => path.join(os.homedir(), 'Downloads');
    }

    // Quick Launch Section
    loadQuickLaunchApps() {
        const savedApps = localStorage.getItem('dashboard-quick-apps');
        this.apps = savedApps ? JSON.parse(savedApps) : this.getDefaultApps();
        this.renderQuickLaunch();
    }

    getDefaultApps() {
        return []; // Empty by default - users can add their own apps
    }

    renderQuickLaunch() {
        const container = document.getElementById('launch-grid');
        if (!container) return;

        if (this.apps.length === 0) {
            container.innerHTML = `
                <div class="empty-apps">
                    <div class="add-app-icon">📱</div>
                    <p>No apps added yet. Click + Add App to get started!</p>
                </div>
            `;
        } else {
            container.innerHTML = this.apps.map(app => `
                <div class="launch-app" onclick="modernDashboard.launchApp('${app.path}')">
                    <div class="app-icon">${app.icon}</div>
                    <div class="app-name">${app.name}</div>
                </div>
            `).join('') + `
                <div class="launch-app add-app-card" onclick="modernDashboard.addQuickLaunchApp()">
                    <div class="app-icon add-app-icon">+</div>
                    <div class="app-name">Add App</div>
                </div>
            `;
        }
    }

    // Add methods that match the onclick handlers in HTML
    addQuickLaunchApp() {
        this.showFileBrowser();
    }

    getAppIcon(name) {
        const iconMap = {
            'chrome': '🌐', 'firefox': '🦊', 'edge': '🔷',
            'discord': '💬', 'steam': '🎮', 'spotify': '🎵',
            'vscode': '📝', 'notepad': '📄', 'explorer': '📁',
            'calculator': '🔢', 'settings': '⚙️', 'paint': '🎨',
            'word': '📄', 'excel': '📊', 'powerpoint': '📽️'
        };

        const key = name.toLowerCase();
        for (const [app, icon] of Object.entries(iconMap)) {
            if (key.includes(app)) return icon;
        }
        return '📱';
    }

    async launchApp(appPath) {
        try {
            const { shell } = require('electron');
            if (appPath.startsWith('http')) {
                await shell.openExternal(appPath);
            } else {
                await shell.openPath(appPath);
            }
        } catch (error) {
            console.error('Failed to launch app:', error);
        }
    }

    // Quick Access Section
    loadQuickAccess() {
        const savedQuickAccess = localStorage.getItem('dashboard-quick-access');
        this.quickAccess = savedQuickAccess ? JSON.parse(savedQuickAccess) : [];
        this.renderQuickAccess();
    }

    renderQuickAccess() {
        const container = document.getElementById('quick-access-grid');
        if (!container) return;

        if (this.quickAccess.length === 0) {
            container.innerHTML = `
                <div class="favorite-card add-favorite-card" onclick="modernDashboard.addQuickAccessItem()">
                    <div class="add-favorite-icon">+</div>
                    <div class="favorite-name">Add Item</div>
                </div>
            `;
        } else {
            container.innerHTML = this.quickAccess.map(item => `
                <div class="favorite-card" onclick="modernDashboard.openQuickAccessItem('${item.path}', '${item.type}')">
                    <div class="favorite-icon">${item.icon}</div>
                    <div class="favorite-name">${item.name}</div>
                </div>
            `).join('') + `
                <div class="favorite-card add-favorite-card" onclick="modernDashboard.addQuickAccessItem()">
                    <div class="add-favorite-icon">+</div>
                    <div class="favorite-name">Add Item</div>
                </div>
            `;
        }
    }

    // Add method that matches the onclick handler in HTML
    addQuickAccessItem() {
        this.showQuickAccessModal();
    }

    // Quick Access Modal Functions
    showQuickAccessModal() {
        const modal = document.getElementById('quick-access-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('active');
        }
    }

    closeQuickAccessModal() {
        const modal = document.getElementById('quick-access-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('active');
        }
    }

    addQuickAccessPage(pageId, icon, name) {
        const item = {
            name: name,
            path: pageId,
            type: 'page',
            icon: icon
        };

        this.quickAccess.push(item);
        localStorage.setItem('dashboard-quick-access', JSON.stringify(this.quickAccess));
        this.renderQuickAccess();
        this.closeQuickAccessModal();
    }

    addQuickAccessTool(command, icon, name) {
        const item = {
            name: name,
            path: command,
            type: 'tool',
            icon: icon
        };

        this.quickAccess.push(item);
        localStorage.setItem('dashboard-quick-access', JSON.stringify(this.quickAccess));
        this.renderQuickAccess();
        this.closeQuickAccessModal();
    }

    getQuickAccessIcon(name, type) {
        const typeIcons = {
            'app': '🚀',
            'website': '🌐',
            'file': '📄',
            'folder': '📁',
            'tool': '🔧'
        };

        // Try to get specific icon based on name
        const nameIcon = this.getAppIcon(name);
        if (nameIcon !== '📱') return nameIcon;

        // Fall back to type icon
        return typeIcons[type] || '⭐';
    }

    async openQuickAccessItem(itemPath, itemType) {
        try {
            if (itemType === 'page') {
                // Switch to internal page
                if (typeof switchTab === 'function') {
                    switchTab(itemPath);
                } else {
                    console.log('Switching to page:', itemPath);
                }
            } else if (itemType === 'website' || itemPath.startsWith('http')) {
                const { shell } = require('electron');
                await shell.openExternal(itemPath);
            } else {
                const { shell } = require('electron');
                await shell.openPath(itemPath);
            }
        } catch (error) {
            console.error('Failed to open quick access item:', error);
        }
    }

    // File Browser Functions
    showFileBrowser() {
        this.currentPath = os.homedir();
        this.navigationHistory = [];
        this.selectedFile = null;
        
        const modal = document.getElementById('file-browser-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('active');
            this.loadDirectory(this.currentPath);
        }
    }

    closeFileBrowser() {
        const modal = document.getElementById('file-browser-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('active');
        }
        this.selectedFile = null;
    }

    async loadDirectory(dirPath) {
        try {
            this.currentPath = dirPath;
            
            // Update breadcrumb and path
            this.updateBreadcrumb(dirPath);
            document.getElementById('current-path').value = dirPath;
            
            // Load directory contents
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: var(--text-secondary);">Loading...</div>';
            
            const files = await fs.readdir(dirPath);
            const fileItems = [];
            
            for (const file of files) {
                try {
                    const fullPath = path.join(dirPath, file);
                    const stats = await fs.stat(fullPath);
                    
                    fileItems.push({
                        name: file,
                        path: fullPath,
                        isDirectory: stats.isDirectory(),
                        isExecutable: path.extname(file).toLowerCase() === '.exe',
                        size: stats.size,
                        modified: stats.mtime
                    });
                } catch (e) {
                    // Skip files we can't access
                }
            }
            
            // Sort: directories first, then files
            fileItems.sort((a, b) => {
                if (a.isDirectory && !b.isDirectory) return -1;
                if (!a.isDirectory && b.isDirectory) return 1;
                return a.name.localeCompare(b.name);
            });
            
            this.renderFileList(fileItems);
            
        } catch (error) {
            console.error('Failed to load directory:', error);
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: var(--text-danger);">Error loading directory</div>';
        }
    }

    renderFileList(files) {
        const fileList = document.getElementById('file-list');
        
        if (files.length === 0) {
            fileList.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: var(--text-secondary);">This folder is empty</div>';
            return;
        }
        
        fileList.innerHTML = files.map(file => `
            <div class="file-item" onclick="modernDashboard.selectFile('${file.path.replace(/\\/g, '\\\\')}', '${file.name}', ${file.isDirectory}, ${file.isExecutable})" 
                 ondblclick="modernDashboard.handleFileDoubleClick('${file.path.replace(/\\/g, '\\\\')}', ${file.isDirectory}, ${file.isExecutable})">
                <div class="file-item-icon">${this.getFileItemIcon(file)}</div>
                <div class="file-item-name">${file.name}</div>
            </div>
        `).join('');
    }

    getFileItemIcon(file) {
        if (file.isDirectory) return '📁';
        
        const ext = path.extname(file.name).toLowerCase();
        const iconMap = {
            '.exe': '⚙️',
            '.msi': '📦',
            '.bat': '📜',
            '.cmd': '📜',
            '.txt': '📄',
            '.pdf': '📕',
            '.doc': '📝',
            '.docx': '📝',
            '.jpg': '🖼️',
            '.png': '🖼️',
            '.gif': '🖼️',
            '.jpeg': '🖼️',
            '.mp4': '🎬',
            '.avi': '🎬',
            '.mp3': '🎵',
            '.wav': '🎵',
            '.zip': '🗜️',
            '.rar': '🗜️'
        };
        
        return iconMap[ext] || '📄';
    }

    selectFile(filePath, fileName, isDirectory, isExecutable) {
        // Remove previous selection
        document.querySelectorAll('.file-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to clicked item
        event.target.closest('.file-item').classList.add('selected');
        
        this.selectedFile = { path: filePath, name: fileName, isDirectory, isExecutable };
        
        // Update selected file display
        document.getElementById('selected-file-name').textContent = fileName;
        
        // Enable/disable select button
        const selectBtn = document.getElementById('select-file-btn');
        selectBtn.disabled = isDirectory; // Can only select files, not directories
    }

    handleFileDoubleClick(filePath, isDirectory, isExecutable) {
        if (isDirectory) {
            this.navigationHistory.push(this.currentPath);
            this.loadDirectory(filePath);
            this.updateNavigationButtons();
        } else if (isExecutable) {
            // Auto-select executable files on double-click
            this.confirmFileSelection();
        }
    }

    updateBreadcrumb(currentPath) {
        const breadcrumb = document.getElementById('file-breadcrumb');
        const pathParts = currentPath.split(path.sep).filter(part => part);
        
        let breadcrumbHTML = '<span class="breadcrumb-item" onclick="modernDashboard.navigateToPath(\'C:\\\')">This PC</span>';
        
        let buildPath = '';
        pathParts.forEach((part, index) => {
            buildPath += part + path.sep;
            breadcrumbHTML += ` > <span class="breadcrumb-item" onclick="modernDashboard.navigateToPath('${buildPath.replace(/\\/g, '\\\\')}')">${part}</span>`;
        });
        
        breadcrumb.innerHTML = breadcrumbHTML;
    }

    navigateToPath(targetPath) {
        this.navigationHistory.push(this.currentPath);
        this.loadDirectory(targetPath);
        this.updateNavigationButtons();
    }

    fileBrowserBack() {
        if (this.navigationHistory.length > 0) {
            const previousPath = this.navigationHistory.pop();
            this.loadDirectory(previousPath);
            this.updateNavigationButtons();
        }
    }

    fileBrowserUp() {
        const parentPath = path.dirname(this.currentPath);
        if (parentPath !== this.currentPath) {
            this.navigationHistory.push(this.currentPath);
            this.loadDirectory(parentPath);
            this.updateNavigationButtons();
        }
    }

    refreshFileBrowser() {
        this.loadDirectory(this.currentPath);
    }

    updateNavigationButtons() {
        const backBtn = document.getElementById('back-btn');
        const upBtn = document.getElementById('up-btn');
        
        backBtn.disabled = this.navigationHistory.length === 0;
        upBtn.disabled = path.dirname(this.currentPath) === this.currentPath;
    }

    confirmFileSelection() {
        if (!this.selectedFile || this.selectedFile.isDirectory) return;
        
        // Add to Quick Launch apps
        const fileName = path.basename(this.selectedFile.name, path.extname(this.selectedFile.name));
        const app = {
            name: fileName,
            path: this.selectedFile.path,
            icon: this.getAppIcon(fileName)
        };

        this.apps.push(app);
        localStorage.setItem('dashboard-quick-apps', JSON.stringify(this.apps));
        this.renderQuickLaunch();
        this.closeFileBrowser();
    }

    // Clock & Calendar Section
    initializeClockCalendar() {
        this.currentCalendarDate = new Date();
        this.updateClock();
        this.updateCalendar();
        this.updateWelcomeMessage();
        
        // Update clock every second
        this.clockInterval = setInterval(() => {
            this.updateClock();
        }, 1000);
        
        // Update welcome message every hour
        this.welcomeInterval = setInterval(() => {
            this.updateWelcomeMessage();
        }, 3600000); // 1 hour
    }

    updateClock() {
        const now = new Date();
        
        this.updateDigitalClock(now);
        
        // Always update date info
        this.updateDateInfo(now);
    }

    updateDigitalClock(now) {
        const timeElement = document.getElementById('current-time');
        const periodElement = document.getElementById('period');
        
        if (timeElement && periodElement) {
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            if (this.clockSettings.timeFormat === '12') {
                // 12-hour format
                let hours = now.getHours();
                const period = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12;
                if (hours === 0) hours = 12;
                
                const timeString = this.clockSettings.showSeconds ? 
                    `${hours}:${minutes}:${seconds}` : 
                    `${hours}:${minutes}`;
                
                timeElement.textContent = timeString;
                periodElement.textContent = period;
                periodElement.style.display = 'inline';
            } else {
                // 24-hour format
                const hours = String(now.getHours()).padStart(2, '0');
                const timeString = this.clockSettings.showSeconds ? 
                    `${hours}:${minutes}:${seconds}` : 
                    `${hours}:${minutes}`;
                
                timeElement.textContent = timeString;
                periodElement.style.display = 'none';
            }
        }
    }



    updateDateInfo(now) {
        const dayName = now.toLocaleDateString('en-US', { weekday: 'long' });
        const dateDetail = now.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        // Update date elements
        const dayNameElement = document.getElementById('day-name');
        const dateDetailElement = document.getElementById('date-detail');
        
        if (dayNameElement && dateDetailElement) {
            dayNameElement.textContent = dayName;
            dateDetailElement.textContent = dateDetail;
        }
    }

    updateWelcomeMessage() {
        const welcomeMessage = document.getElementById('welcome-message');
        const welcomeSubtitle = document.getElementById('welcome-subtitle');
        
        if (welcomeMessage && welcomeSubtitle) {
            const hour = new Date().getHours();
            let greeting, subtitle;
            
            if (hour < 12) {
                greeting = '🌅 Good morning!';
                subtitle = 'Ready to start your productive day?';
            } else if (hour < 20) {
                greeting = '☀️ Good afternoon!';
                subtitle = 'Hope your day is going well!';
            } else {
                greeting = '🌙 Good evening!';
                subtitle = 'Time to wind down and relax.';
            }
            
            // In the future, this can be replaced with: `Good ${timeOfDay}, ${username}!`
            welcomeMessage.textContent = greeting;
            welcomeSubtitle.textContent = subtitle;
        }
    }

    updateCalendar() {
        const monthElement = document.getElementById('calendar-month');
        const gridElement = document.getElementById('calendar-grid');
        
        if (!monthElement || !gridElement) return;
        
        const monthName = this.currentCalendarDate.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short' 
        });
        monthElement.textContent = monthName;
        
        // Generate calendar days
        const year = this.currentCalendarDate.getFullYear();
        const month = this.currentCalendarDate.getMonth();
        
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = firstDay.getDay();
        
        const today = new Date();
        const isCurrentMonth = year === today.getFullYear() && month === today.getMonth();
        
        let calendarHTML = '';
        
        // Add weekday headers if enabled
        if (this.clockSettings.showWeekdays) {
            const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
            weekdays.forEach(day => {
                calendarHTML += `<div class="calendar-weekday-compact">${day}</div>`;
            });
        }
        
        // Add empty cells for days before the month starts
        for (let i = 0; i < startingDayOfWeek; i++) {
            const prevMonthDay = new Date(year, month, 0 - (startingDayOfWeek - 1 - i));
            calendarHTML += `<div class="calendar-day-compact other-month">${prevMonthDay.getDate()}</div>`;
        }
        
        // Add days of the current month
        for (let day = 1; day <= daysInMonth; day++) {
            const isToday = isCurrentMonth && day === today.getDate();
            const classes = ['calendar-day-compact'];
            if (isToday) classes.push('today');
            
            calendarHTML += `<div class="${classes.join(' ')}">${day}</div>`;
        }
        
        // Fill remaining cells with next month's days
        const totalCells = Math.ceil((startingDayOfWeek + daysInMonth + 7) / 7) * 7;
        const remainingCells = totalCells - (startingDayOfWeek + daysInMonth + 7);
        
        for (let day = 1; day <= remainingCells; day++) {
            calendarHTML += `<div class="calendar-day-compact other-month">${day}</div>`;
        }
        
        gridElement.innerHTML = calendarHTML;
    }

    changeMonth(direction) {
        this.currentCalendarDate.setMonth(this.currentCalendarDate.getMonth() + direction);
        this.updateCalendar();
    }

    // Modal Management - ensure all modals are hidden on startup
    ensureModalsHidden() {
        const modals = ['quick-access-modal', 'file-browser-modal', 'clock-settings-modal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('active');
            }
        });
    }

    // Utility Functions
    async openTaskManager() {
        try {
            const { shell } = require('electron');
            await shell.openPath('taskmgr.exe');
        } catch (error) {
            console.error('Failed to open Task Manager:', error);
        }
    }

    // Clock Settings Functions
    loadClockSettings() {
        const saved = localStorage.getItem('dashboard-clock-settings');
        if (saved) {
            this.clockSettings = { ...this.clockSettings, ...JSON.parse(saved) };
        }
        this.applyClockSettings();
    }

    saveClockSettings() {
        localStorage.setItem('dashboard-clock-settings', JSON.stringify(this.clockSettings));
    }

    applyClockSettings() {
        // Only apply settings if DOM is ready
        if (!document.getElementById('digital-clock-display')) {
            console.log('DOM not ready, skipping clock settings application');
            return;
        }

        // Apply calendar visibility
        const calendarDisplay = document.getElementById('calendar-display');
        if (calendarDisplay) {
            calendarDisplay.style.display = this.clockSettings.showCalendar ? 'block' : 'none';
        }

        // Apply clock animation
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            if (this.clockSettings.clockAnimation) {
                timeElement.classList.add('gentleGlow');
            } else {
                timeElement.classList.remove('gentleGlow');
            }
        }

        // Update calendar weekday headers
        this.updateCalendar();
        // Force clock update
        this.updateClock();
    }





    openClockSettings() {
        // Load current settings into modal
        document.getElementById('time-format-select').value = this.clockSettings.timeFormat;
        document.getElementById('show-seconds').checked = this.clockSettings.showSeconds;
        document.getElementById('show-calendar').checked = this.clockSettings.showCalendar;
        document.getElementById('show-weekdays').checked = this.clockSettings.showWeekdays;
        document.getElementById('clock-animation').checked = this.clockSettings.clockAnimation;

        const modal = document.getElementById('clock-settings-modal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('active');
        }
    }

    closeClockSettings() {
        const modal = document.getElementById('clock-settings-modal');
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('active');
        }
    }

    changeTimeFormat() {
        const select = document.getElementById('time-format-select');
        this.clockSettings.timeFormat = select.value;
        this.saveClockSettings();
        this.updateClock();
    }



    toggleSeconds() {
        const checkbox = document.getElementById('show-seconds');
        this.clockSettings.showSeconds = checkbox.checked;
        this.saveClockSettings();
        this.updateClock();
    }

    toggleCalendar() {
        const checkbox = document.getElementById('show-calendar');
        this.clockSettings.showCalendar = checkbox.checked;
        this.saveClockSettings();
        this.applyClockSettings();
    }

    toggleWeekdays() {
        const checkbox = document.getElementById('show-weekdays');
        this.clockSettings.showWeekdays = checkbox.checked;
        this.saveClockSettings();
        this.updateCalendar();
    }

    toggleClockAnimation() {
        const checkbox = document.getElementById('clock-animation');
        this.clockSettings.clockAnimation = checkbox.checked;
        this.saveClockSettings();
        this.applyClockSettings();
    }

    resetClockSettings() {
        this.clockSettings = {
            timeFormat: '12',
            showSeconds: true,
            showCalendar: true,
            showWeekdays: true,
            clockAnimation: true
        };
        this.saveClockSettings();
        this.applyClockSettings();
        
        // Update modal controls
        document.getElementById('time-format-select').value = this.clockSettings.timeFormat;
        document.getElementById('show-seconds').checked = this.clockSettings.showSeconds;
        document.getElementById('show-calendar').checked = this.clockSettings.showCalendar;
        document.getElementById('show-weekdays').checked = this.clockSettings.showWeekdays;
        document.getElementById('clock-animation').checked = this.clockSettings.clockAnimation;
    }

    // Cleanup method for intervals
    destroy() {
        if (this.clockInterval) {
            clearInterval(this.clockInterval);
            this.clockInterval = null;
        }
        if (this.welcomeInterval) {
            clearInterval(this.welcomeInterval);
            this.welcomeInterval = null;
        }
    }
}

// Initialize Modern Dashboard
let modernDashboard;

console.log('🎯 Modern Dashboard script loaded');

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        modernDashboard = new ModernDashboard();
        window.modernDashboard = modernDashboard;
    });
} else {
    modernDashboard = new ModernDashboard();
    window.modernDashboard = modernDashboard;
} 