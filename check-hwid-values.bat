@echo off
echo.
echo ========================================
echo    HWID Values Check - Armory X
echo ========================================
echo.

echo [1] System Machine GUID:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography" /v "MachineGuid" 2>nul
if %errorlevel% neq 0 echo   ERROR: Could not read Machine GUID
echo.

echo [2] Hardware Profile GUID:
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001" /v "HwProfileGuid" 2>nul
if %errorlevel% neq 0 echo   ERROR: Could not read Hardware Profile GUID
echo.

echo [3] Computer Hardware ID:
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation" /v "ComputerHardwareId" 2>nul
if %errorlevel% neq 0 echo   ERROR: Could not read Computer Hardware ID
echo.

echo [4] Build GUID:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v "BuildGUID" 2>nul
if %errorlevel% neq 0 echo   ERROR: Could not read Build GUID
echo.

echo [5] Computer Hardware IDs:
reg query "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SystemInformation" /v "ComputerHardwareIds" 2>nul
if %errorlevel% neq 0 echo   ERROR: Could not read Computer Hardware IDs
echo.

echo [6] Additional System Info:
echo   System UUID (from WMI):
wmic csproduct get UUID 2>nul
echo.
echo   Machine ID (if available):
wmic computersystem get TotalPhysicalMemory,Manufacturer,Model 2>nul
echo.

echo ========================================
echo    Check complete!
echo ========================================
echo.
echo Instructions:
echo 1. Run this script to see CURRENT values
echo 2. Enable HWID Spoofer in Armory X
echo 3. Run this script again to see CHANGED values
echo 4. Values should be completely different!
echo.
pause 