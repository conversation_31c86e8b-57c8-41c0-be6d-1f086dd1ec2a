/* ===================================
   Mod Manager Interface
   =================================== */

/* Mod List Enhancements */
.mod-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--background-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.mod-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.mod-header-actions {
    display: flex;
    gap: 0.5rem;
}

.mod-category-header {
    margin: 1.5rem 0 1rem 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
}

.mod-category-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.mod-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.mod-item:hover {
    border-color: var(--primary-color);
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.1);
}

.mod-info {
    flex: 1;
    min-width: 0;
}

.mod-info h4 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mod-info p {
    margin: 0 0 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.mod-path {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-family: monospace;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

.mod-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    margin-left: 1rem;
}

.mod-list-content {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Mod Tabs Interface */
.mod-tabs-container {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.mod-tabs {
    display: flex;
    background: var(--background-hover);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.mod-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    font-size: 0.9rem;
    font-weight: 500;
}

.mod-tab:hover {
    background: var(--background-card);
    color: var(--text-primary);
}

.mod-tab.active {
    background: var(--background-card);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

.mod-tab.active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.tab-icon {
    font-size: 1rem;
    opacity: 0.7;
}

.mod-tab.active .tab-icon {
    opacity: 1;
}

.tab-count {
    background: var(--background-hover);
    color: var(--text-secondary);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.mod-tab.active .tab-count {
    background: var(--primary-color);
    color: white;
}

.mod-tab-contents {
    position: relative;
    min-height: 300px;
}

.mod-tab-content {
    display: none;
    padding: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.mod-tab-content.active {
    display: block;
}

.mod-category-info {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background: var(--background-hover);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

.mod-category-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Remove old category header styles since we're using tabs now */
.mod-category-header {
    display: none;
}

/* Responsive design for mod tabs */
@media (max-width: 768px) {
    .mod-tabs {
        flex-direction: column;
    }
    
    .mod-tab {
        justify-content: space-between;
        padding: 0.75rem 1rem;
    }
    
    .mod-tab.active::before {
        display: none;
    }
    
    .mod-tab.active {
        border-bottom: none;
        border-left: 3px solid var(--primary-color);
    }
    
    .mod-tab-content {
        padding: 1rem;
    }
    
    .mod-category-info {
        padding: 0.5rem 0.75rem;
    }
}

/* Professional Mod Manager Styling */

/* Main Header Section */
.mod-header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(37, 99, 235, 0.02));
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
}

.mod-header-info h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mod-count {
    color: #94a3b8;
    font-size: 1rem;
    font-weight: 500;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.mod-header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* No Mods Message Styling */
.no-mods-message {
    text-align: center;
    padding: 4rem 2rem;
    color: #94a3b8;
    background: var(--background-card);
}

.no-mods-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.7;
    color: var(--text-muted);
}

.no-mods-message h3 {
    color: #f1f5f9;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.no-mods-message p {
    color: #94a3b8;
    font-size: 1rem;
    margin: 0 0 2rem 0;
    line-height: 1.6;
}

.no-mods-message .btn {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.no-mods-message .btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Mod Manager Options Interface */
.mod-manager-options {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 2rem;
}

.mod-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    width: 100%;
}

.mod-option-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.mod-option-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(59, 130, 246, 0.2);
}

.mod-option-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.mod-option-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.mod-option-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* ===================================
   Minecraft Mod Sets Management
   =================================== */

.minecraft-modsets-section {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.08) 0%, rgba(29, 78, 216, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    transition: all 0.3s ease;
}

.minecraft-modsets-section:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
}

.modsets-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.modsets-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.modsets-info h4 {
    color: #3b82f6;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modsets-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.modsets-dropdown {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    color: #f1f5f9;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.modsets-dropdown:hover {
    border-color: rgba(59, 130, 246, 0.5);
    background: linear-gradient(135deg, rgba(15, 23, 42, 1) 0%, rgba(30, 41, 59, 0.9) 100%);
}

.modsets-dropdown:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.modsets-dropdown option {
    background: #1e293b;
    color: #f1f5f9;
}

.modsets-help-text {
    margin-bottom: 0.75rem;
}

.modsets-help-text small {
    color: #94a3b8;
    font-size: 0.85rem;
    line-height: 1.4;
    display: block;
}

.modsets-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.modsets-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
    flex-shrink: 0;
}

.modsets-status .status-text {
    color: #9ca3af;
}

/* Active mod set styling */
.modsets-status.active {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.modsets-status.active .status-indicator {
    color: #22c55e;
    background: #22c55e;
}

.modsets-status.active .status-text {
    color: #86efac;
}

/* Switching mod set styling */
.modsets-status.switching {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.modsets-status.switching .status-indicator {
    color: #fbbf24;
    background: #fbbf24;
    animation: pulse 1.5s ease-in-out infinite;
}

.modsets-status.switching .status-text {
    color: #fde68a;
}
