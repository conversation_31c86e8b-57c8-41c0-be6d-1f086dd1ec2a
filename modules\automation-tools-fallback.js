const { exec } = require('child_process');
const { globalShortcut } = require('electron');
const util = require('util');
const execAsync = util.promisify(exec);

class AutomationToolsFallback {
    constructor() {
        this.toolStates = {
            autoClicker: {
                active: false,
                interval: null,
                settings: {
                    button: 'left',
                    clickType: 'single',
                    interval: 100,
                    ludicrousMode: false,
                    hotkey: 'F6'
                }
            },
            antiRecoil: {
                active: false,
                interval: null,
                settings: {
                    strength: 5,
                    hotkey: 'F7'
                }
            },
            keySequence: {
                active: false,
                interval: null,
                currentIndex: 0,
                settings: {
                    keys: 'A,S,D,W',
                    delay: 100,
                    hotkey: 'F8'
                }
            }
        };

        this.hotkeysRegistered = false;
        this.mainWindow = null;
        this.isWindows = process.platform === 'win32';
    }

    setMainWindow(window) {
        this.mainWindow = window;
    }

    registerHotkeys() {
        if (this.hotkeysRegistered) return;

        try {
            Object.entries(this.toolStates).forEach(([toolName, tool]) => {
                const success = globalShortcut.register(tool.settings.hotkey, () => {
                    this.toggleTool(toolName);
                });

                if (success) {
                    console.log(`✅ Registered hotkey ${tool.settings.hotkey} for ${toolName}`);
                } else {
                    console.warn(`❌ Failed to register hotkey ${tool.settings.hotkey} for ${toolName}`);
                }
            });

            this.hotkeysRegistered = true;
        } catch (error) {
            console.error('❌ Error registering automation tool hotkeys:', error);
        }
    }

    unregisterHotkeys() {
        if (!this.hotkeysRegistered) return;

        try {
            Object.values(this.toolStates).forEach(tool => {
                globalShortcut.unregister(tool.settings.hotkey);
            });
            this.hotkeysRegistered = false;
            console.log('✅ Automation tool hotkeys unregistered');
        } catch (error) {
            console.error('❌ Error unregistering automation tool hotkeys:', error);
        }
    }

    // Windows PowerShell automation functions
    async executeMouseClick(button = 'left', doubleClick = false) {
        if (!this.isWindows) {
            throw new Error('PowerShell automation only supported on Windows');
        }

        const buttonMap = {
            'left': '[System.Windows.Forms.MouseButtons]::Left',
            'right': '[System.Windows.Forms.MouseButtons]::Right',
            'middle': '[System.Windows.Forms.MouseButtons]::Middle'
        };

        const psButton = buttonMap[button] || buttonMap['left'];
        const clickCount = doubleClick ? 2 : 1;

        const script = `
Add-Type -AssemblyName System.Windows.Forms;
$pos = [System.Windows.Forms.Cursor]::Position;
[System.Windows.Forms.SendKeys]::SendWait('%{F4}');
Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport("user32.dll")] public static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint dwData, UIntPtr dwExtraInfo); }';
for($i = 0; $i -lt ${clickCount}; $i++) {
    if('${button}' -eq 'left') { [Win32]::mouse_event(0x02, 0, 0, 0, 0); [Win32]::mouse_event(0x04, 0, 0, 0, 0); }
    if('${button}' -eq 'right') { [Win32]::mouse_event(0x08, 0, 0, 0, 0); [Win32]::mouse_event(0x10, 0, 0, 0, 0); }
    if($i -lt ${clickCount - 1}) { Start-Sleep -Milliseconds 50; }
}`;

        try {
            await execAsync(`powershell -Command "${script.replace(/\n/g, ' ')}"`);
            return true;
        } catch (error) {
            console.error('Mouse click error:', error);
            return false;
        }
    }

    async executeMouseMove(deltaX = 0, deltaY = 0) {
        if (!this.isWindows) {
            throw new Error('PowerShell automation only supported on Windows');
        }

        const script = `
Add-Type -TypeDefinition 'using System; using System.Runtime.InteropServices; public class Win32 { [DllImport("user32.dll")] public static extern bool SetCursorPos(int X, int Y); [DllImport("user32.dll")] public static extern bool GetCursorPos(out System.Drawing.Point lpPoint); }';
Add-Type -AssemblyName System.Drawing;
$pos = New-Object System.Drawing.Point;
[Win32]::GetCursorPos([ref]$pos);
[Win32]::SetCursorPos($pos.X + ${deltaX}, $pos.Y + ${deltaY});`;

        try {
            await execAsync(`powershell -Command "${script.replace(/\n/g, ' ')}"`);
            return true;
        } catch (error) {
            console.error('Mouse move error:', error);
            return false;
        }
    }

    async executeKeyPress(key) {
        if (!this.isWindows) {
            throw new Error('PowerShell automation only supported on Windows');
        }

        const script = `
Add-Type -AssemblyName System.Windows.Forms;
[System.Windows.Forms.SendKeys]::SendWait('${key}');`;

        try {
            await execAsync(`powershell -Command "${script.replace(/\n/g, ' ')}"`);
            return true;
        } catch (error) {
            console.error('Key press error:', error);
            return false;
        }
    }

    // Auto-Clicker functionality
    async startAutoClicker(settings) {
        const tool = this.toolStates.autoClicker;
        
        if (tool.active) {
            this.stopAutoClicker();
        }

        if (settings) {
            Object.assign(tool.settings, settings);
        }

        tool.active = true;

        const clickFunction = async () => {
            if (!tool.active) return;

            try {
                await this.executeMouseClick(tool.settings.button, tool.settings.clickType === 'double');
                console.log(`🖱️ Auto-click performed: ${tool.settings.button} ${tool.settings.clickType}`);
            } catch (error) {
                console.error('Auto-clicker error:', error);
            }
        };

        const interval = tool.settings.ludicrousMode ? 1 : tool.settings.interval;
        tool.interval = setInterval(clickFunction, interval);

        console.log(`✅ Auto-clicker started with ${interval}ms interval`);
        this.notifyRenderer('auto-clicker-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Auto-clicker started' };
    }

    stopAutoClicker() {
        const tool = this.toolStates.autoClicker;
        
        if (tool.interval) {
            clearInterval(tool.interval);
            tool.interval = null;
        }
        
        tool.active = false;
        
        console.log('✅ Auto-clicker stopped');
        this.notifyRenderer('auto-clicker-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Auto-clicker stopped' };
    }

    // Anti-Recoil functionality
    async startAntiRecoil(settings) {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.active) {
            this.stopAntiRecoil();
        }

        if (settings) {
            Object.assign(tool.settings, settings);
        }

        tool.active = true;

        const compensationFunction = async () => {
            if (!tool.active) return;

            try {
                await this.executeMouseMove(0, -tool.settings.strength);
            } catch (error) {
                console.error('Anti-recoil error:', error);
            }
        };

        tool.interval = setInterval(compensationFunction, 10);

        console.log(`✅ Anti-recoil started with strength ${tool.settings.strength}`);
        this.notifyRenderer('anti-recoil-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Anti-recoil started' };
    }

    stopAntiRecoil() {
        const tool = this.toolStates.antiRecoil;
        
        if (tool.interval) {
            clearInterval(tool.interval);
            tool.interval = null;
        }
        
        tool.active = false;
        
        console.log('✅ Anti-recoil stopped');
        this.notifyRenderer('anti-recoil-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Anti-recoil stopped' };
    }

    // Key Sequence functionality
    async startKeySequence(settings) {
        const tool = this.toolStates.keySequence;
        
        if (tool.active) {
            this.stopKeySequence();
        }

        if (settings) {
            Object.assign(tool.settings, settings);
        }

        const keys = tool.settings.keys.split(',').map(key => key.trim());
        
        if (keys.length === 0) {
            return { success: false, message: 'No keys specified' };
        }

        tool.active = true;
        tool.currentIndex = 0;

        const sequenceFunction = async () => {
            if (!tool.active || keys.length === 0) return;

            try {
                const key = keys[tool.currentIndex];
                await this.executeKeyPress(key);
                
                console.log(`⌨️ Key pressed: ${key}`);
                
                tool.currentIndex = (tool.currentIndex + 1) % keys.length;
            } catch (error) {
                console.error('Key sequence error:', error);
            }
        };

        tool.interval = setInterval(sequenceFunction, tool.settings.delay);

        console.log(`✅ Key sequence started: ${keys.join(', ')} with ${tool.settings.delay}ms delay`);
        this.notifyRenderer('key-sequence-status-changed', { active: true, settings: tool.settings });
        
        return { success: true, message: 'Key sequence started' };
    }

    stopKeySequence() {
        const tool = this.toolStates.keySequence;
        
        if (tool.interval) {
            clearInterval(tool.interval);
            tool.interval = null;
        }
        
        tool.active = false;
        tool.currentIndex = 0;
        
        console.log('✅ Key sequence stopped');
        this.notifyRenderer('key-sequence-status-changed', { active: false, settings: tool.settings });
        
        return { success: true, message: 'Key sequence stopped' };
    }

    // Generic tool toggle
    toggleTool(toolName) {
        const tool = this.toolStates[toolName];
        if (!tool) return { success: false, message: 'Unknown tool' };

        switch (toolName) {
            case 'autoClicker':
                return tool.active ? this.stopAutoClicker() : this.startAutoClicker();
            case 'antiRecoil':
                return tool.active ? this.stopAntiRecoil() : this.startAntiRecoil();
            case 'keySequence':
                return tool.active ? this.stopKeySequence() : this.startKeySequence();
            default:
                return { success: false, message: 'Unknown tool' };
        }
    }

    // Update tool settings
    updateToolSettings(toolName, settings) {
        const tool = this.toolStates[toolName];
        if (!tool) return { success: false, message: 'Unknown tool' };

        Object.assign(tool.settings, settings);
        
        if (tool.active) {
            this.toggleTool(toolName);  // Stop
            this.toggleTool(toolName);  // Start with new settings
        }

        console.log(`✅ Updated ${toolName} settings:`, settings);
        return { success: true, message: 'Settings updated' };
    }

    // Get tool states
    getToolStates() {
        return JSON.parse(JSON.stringify(this.toolStates));
    }

    // Notify renderer process of status changes
    notifyRenderer(event, data) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send(event, data);
        }
    }

    // Cleanup function
    cleanup() {
        Object.keys(this.toolStates).forEach(toolName => {
            const tool = this.toolStates[toolName];
            if (tool.active) {
                this.toggleTool(toolName);
            }
        });

        this.unregisterHotkeys();
        console.log('✅ Automation tools cleaned up');
    }
}

module.exports = { AutomationToolsFallback }; 