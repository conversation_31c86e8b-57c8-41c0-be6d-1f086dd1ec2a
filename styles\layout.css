/* ===================================
   Layout and Structure Styles
   =================================== */

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: var(--background-card);
    border-bottom: 1px solid var(--border-color);
    -webkit-app-region: drag;
    height: 60px;
    position: relative;
    z-index: 1000;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    -webkit-app-region: no-drag;
}

.header-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    -webkit-app-region: no-drag;
}

.current-tab-indicator {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    background: var(--background-hover);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Logo Section */
.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.app-logo {
    width: 28px;
    height: 28px;
}

.app-title {
    font-size: 1.3rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Window Controls */
.window-controls {
    display: flex;
    gap: 6px;
    -webkit-app-region: no-drag;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: var(--background-hover);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.control-btn.close:hover {
    background: var(--error-color);
    color: white;
}

.control-btn.account {
    margin-right: 8px;
    position: relative;
}

.control-btn.account::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 25%;
    height: 50%;
    width: 1px;
    background: var(--border-color);
}

.control-btn.account:hover {
    background: var(--primary-color);
    color: white;
}

/* Main Layout Structure */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: row;
    min-height: 0;
    overflow: hidden;
}

/* Main Content Area */
.main-content {
    flex: 1;
    background: var(--background-dark);
    padding: 2rem;
    overflow-y: auto;
    transition: var(--transition-smooth);
}

.tab-content {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

.content-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.content-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 60px; /* Position below header */
    left: 0;
    height: calc(100vh - 60px); /* Full remaining height */
    z-index: 900;
    width: 280px;
    background: var(--background-card);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    transition: transform var(--transition-smooth), opacity var(--transition-smooth);
    display: flex;
    flex-direction: column;
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
}

.sidebar:not(.collapsed) {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
    box-shadow: 0 10px 50px rgba(0,0,0,0.3);
}

/* Navigation Items */
.nav-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 12px;
    background: transparent;
    border: none;
    border-radius: 10px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: 0.95rem;
    font-weight: 500;
    text-align: left;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
}

.nav-item:hover {
    background: var(--background-hover);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.nav-item .icon {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    transition: var(--transition-smooth);
    overflow: hidden;
    white-space: nowrap;
}

/* Sidebar Footer */
.sidebar-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-top: auto;
}

.info-item {
    cursor: default;
    padding: 8px 12px;
    opacity: 0.7;
}

.info-item:hover {
    background: transparent;
    border: none;
    opacity: 1;
}

.info-item .nav-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-item small {
    font-size: 0.75rem;
    color: var(--text-muted);
}

#memory-usage, #cpu-usage {
    color: var(--primary-color);
    font-weight: 600;
}

/* Menu Toggle */
.menu-toggle {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: var(--transition-smooth);
    -webkit-app-region: no-drag;
}

.menu-toggle:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
}

.hamburger {
    width: 18px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: var(--transition-smooth);
}

.menu-toggle.open .hamburger:nth-child(1) {
    transform: translateY(6px) rotate(45deg);
}

.menu-toggle.open .hamburger:nth-child(2) {
    opacity: 0;
}

.menu-toggle.open .hamburger:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
}
