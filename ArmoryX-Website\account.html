<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account - Armory X</title>
    <meta name="description" content="Manage your Armory X account, profile settings, and access premium features.">
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://armoryx.software/account">
    <meta property="og:title" content="Armory X Account Management">
    <meta property="og:description" content="Manage your Armory X account settings and access premium features.">
    <meta property="og:image" content="https://armoryx.software/assets/armory-x-preview.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="account-styles.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="notifications.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Firebase Configuration -->
    <script type="module">
        (async function() {
            try {
                // Prevent infinite refresh loops
                if (window.firebaseInitAttempted) {
                    console.log('🛑 Firebase initialization already attempted');
                    return;
                }
                window.firebaseInitAttempted = true;
                
                console.log('🔧 Starting Firebase initialization...');
                
                // Firebase configuration - REPLACE WITH YOUR OWN FIREBASE CONFIG
                const firebaseConfig = {
                    apiKey: "AIzaSyAto6FZKgKctMQCtD-wrP8GxvXXEsQ4Ads",
                    authDomain: "armoryx-website.firebaseapp.com",
                    projectId: "armoryx-website",
                    storageBucket: "armoryx-website.firebasestorage.app",
                    messagingSenderId: "671977508420",
                    appId: "1:671977508420:web:34b1978ad9011d3d5c3611"
                };

                console.log('📋 Firebase config:', {
                    projectId: firebaseConfig.projectId,
                    authDomain: firebaseConfig.authDomain
                });

                // Check if Firebase config is properly set up
                const isFirebaseConfigured = firebaseConfig.apiKey !== "YOUR_API_KEY_HERE" && 
                                            firebaseConfig.projectId !== "YOUR_PROJECT_ID";

                console.log('🔍 Firebase configured:', isFirebaseConfigured);

                if (isFirebaseConfigured) {
                    console.log('📦 Loading Firebase modules...');
                    
                    // Test network connectivity first
                    console.log('🌐 Testing network connectivity to Firebase...');
                    try {
                        const connectivityTest = await fetch('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js', { method: 'HEAD' });
                        console.log('✅ Network connectivity to Firebase CDN confirmed');
                    } catch (error) {
                        console.warn('⚠️ Network connectivity issue detected:', error.message);
                    }
                    
                    // Import Firebase modules only if configured
                    const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                    console.log('✅ Firebase app module loaded');
                    
                    const { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged, sendEmailVerification, sendPasswordResetEmail } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
                    console.log('✅ Firebase auth module loaded');
                    
                    const { getFirestore, doc, setDoc, getDoc, getDocs, collection, addDoc, deleteDoc, query, where, orderBy, limit, onSnapshot, serverTimestamp, updateDoc, increment, arrayUnion, arrayRemove } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
                    console.log('✅ Firebase firestore module loaded');

                    // Initialize Firebase
                    console.log('🔥 Initializing Firebase app...');
                    const app = initializeApp(firebaseConfig);
                    console.log('✅ Firebase app initialized');
                    
                    console.log('🔐 Initializing Firebase auth...');
                    const auth = getAuth(app);
                    console.log('✅ Firebase auth initialized');
                    
                    console.log('🗄️ Initializing Firestore...');
                    const db = getFirestore(app);
                    console.log('✅ Firestore initialized');

                    // Test Firebase connection
                    console.log('🔗 Testing Firebase Auth connection...');
                    try {
                        // Test auth connection by checking current user (won't fail if not logged in)
                        const currentUser = auth.currentUser;
                        console.log('✅ Firebase Auth connection successful');
                    } catch (error) {
                        console.warn('⚠️ Firebase Auth connection test failed:', error);
                    }

                    console.log('🔗 Testing Firestore connection...');
                    try {
                        // Test Firestore connection (will help identify permission issues)
                        const testRef = doc(db, 'test', 'connection');
                        console.log('✅ Firestore connection test ready (permissions will be tested on first operation)');
                    } catch (error) {
                        console.warn('⚠️ Firestore connection test failed:', error);
                    }

                    // Make Firebase available globally
                    window.firebase = { 
                        auth, 
                        db, 
                        createUserWithEmailAndPassword, 
                        signInWithEmailAndPassword, 
                        signOut, 
                        onAuthStateChanged,
                        sendEmailVerification,
                        sendPasswordResetEmail,
                        doc, 
                        setDoc, 
                        getDoc, 
                        getDocs,
                        collection, 
                        addDoc, 
                        deleteDoc,
                        query, 
                        where,
                        orderBy, 
                        limit, 
                        onSnapshot, 
                        serverTimestamp, 
                        updateDoc, 
                        increment,
                        arrayUnion,
                        arrayRemove
                    };
                    
                    console.log('✅ Firebase initialized successfully - all services ready');
                    console.log('🔥 Firebase project:', firebaseConfig.projectId);
                    console.log('🔥 Auth domain:', firebaseConfig.authDomain);
                    console.log('🔧 Available Firebase functions:', Object.keys(window.firebase));
                    
                    // Signal successful Firebase initialization immediately
                    window.firebaseInitialized = true;
                    console.log('🏁 Firebase initialization SUCCESS - services ready for use');
                } else {
                    console.log('🔧 Firebase not configured - running in local development mode');
                    window.firebase = null;
                    // Signal that Firebase initialization is complete (but not configured)
                    window.firebaseInitialized = true;
                }
            } catch (error) {
                console.error('❌ Firebase initialization failed:', error);
                console.error('Error details:', error.message);
                console.error('Error stack:', error.stack);
                window.firebase = null;
                // Signal that Firebase initialization is complete (but failed)
                window.firebaseInitialized = true;
            }
            
            console.log('🏁 Firebase initialization process complete');
        })();
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.html" class="nav-logo">
                <img src="assets/Armory_X.ico" alt="Armory X" class="nav-logo-icon">
                <span>Armory X</span>
            </a>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="index.html#features" class="nav-link">Features</a></li>
                <li><a href="index.html#download" class="nav-link">Download</a></li>
                <li><a href="index.html#updates" class="nav-link">Updates</a></li>
                <li><a href="forums.html" class="nav-link">Forums</a></li>
                <li class="nav-account-container">
                    <a href="account.html" class="nav-link active">Account</a>
                    <div class="account-dropdown">
                        <div class="account-dropdown-header">
                            <div class="account-dropdown-user">
                                <div class="account-dropdown-avatar">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="account-dropdown-info">
                                    <h4 id="dropdown-user-name">Guest User</h4>
                                    <p id="dropdown-user-status">Not logged in</p>
                                </div>
                            </div>
                        </div>
                        <div class="account-dropdown-menu">
                            <a href="account.html#profile" class="account-dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="account.html#inbox" class="account-dropdown-item">
                                <i class="fas fa-inbox"></i> Inbox
                            </a>
                            <a href="account.html#friends" class="account-dropdown-item">
                                <i class="fas fa-users"></i> Friends
                            </a>
                            <a href="account.html#premium" class="account-dropdown-item" id="dropdown-premium-link">
                                <i class="fas fa-star"></i> Premium
                            </a>
                            <a href="account.html#settings" class="account-dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <div class="account-dropdown-divider"></div>
                            <a href="#" class="account-dropdown-item account-dropdown-logout" id="dropdown-logout">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </li>
                <li><a href="http://discord.gg/uq4Zs2G57g" class="nav-link discord-link" target="_blank">
                    <i class="fab fa-discord"></i> Discord
                </a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Account Header -->
    <section class="account-header">
        <div class="container">
            <div class="account-header-content">
                <h1 class="account-title">
                    <i class="fas fa-user-circle"></i>
                    Account Management
                </h1>
                <p class="account-description">
                    Manage your Armory X account, sync settings, and access premium features
                </p>
            </div>
        </div>
    </section>

    <!-- Login Required Message (shown to non-logged-in users) -->
    <section id="login-required" class="login-required">
        <div class="container">
            <div class="login-message">
                <i class="fas fa-lock"></i>
                <h2>Account Required</h2>
                <p>Create an account or log in to access your account management panel and premium features.</p>
                
                <!-- Login/Register Forms -->
                <div class="auth-forms">
                    <div class="form-container">
                        <div class="form-tabs">
                            <button class="tab-btn active" data-tab="login" id="login-tab-btn">Login</button>
                            <button class="tab-btn" data-tab="register" id="register-tab-btn">Register</button>
                        </div>
                        
                        <div class="form-content">
                            <div class="tab-pane active" id="login-form">
                                <form id="auth-login-form">
                                    <div class="form-group">
                                        <label for="login-email">Email</label>
                                        <input type="email" id="login-email" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="login-password">Password</label>
                                        <input type="password" id="login-password" name="password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-full">Login</button>
                                    <a href="#" class="forgot-password" id="forgot-password-link">Forgot Password?</a>
                                </form>
                            </div>
                            
                            <div class="tab-pane" id="register-form">
                                <form id="auth-register-form">
                                    <div class="form-group">
                                        <label for="register-display-name">Display Name *</label>
                                        <input type="text" id="register-display-name" name="display-name" required maxlength="30" minlength="2" placeholder="Enter your display name">
                                        <small class="form-hint">This will be shown in forums and community features</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="register-email">Email</label>
                                        <input type="email" id="register-email" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="register-password">Password</label>
                                        <input type="password" id="register-password" name="password" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="register-confirm">Confirm Password</label>
                                        <input type="password" id="register-confirm" name="confirm-password" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-full">Create Account</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="login-buttons">
                    <a href="index.html" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Account Content (shown to logged-in users) -->
    <section id="account-content" class="account-content" style="display: none;">
        <div class="container">
            <!-- Account Navigation Tabs -->
            <div class="account-tabs">
                <button class="account-tab active" data-tab="profile">
                    <i class="fas fa-user"></i> Profile
                </button>
                <button class="account-tab" data-tab="inbox">
                    <i class="fas fa-inbox"></i> Inbox
                </button>
                <button class="account-tab" data-tab="friends">
                    <i class="fas fa-users"></i> Friends
                </button>
                <button class="account-tab" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </button>
                <button class="account-tab" data-tab="premium">
                    <i class="fas fa-star"></i> Premium
                </button>
                <button class="account-tab" data-tab="security">
                    <i class="fas fa-shield-alt"></i> Security
                </button>
                <button class="account-tab" data-tab="moderation" id="moderation-tab-button" style="display: none;">
                    <i class="fas fa-gavel"></i> Moderation
                </button>
            </div>

            <!-- Profile Tab -->
            <div class="account-tab-content active" id="profile-tab">
                <div class="profile-section">
                    <h2>Profile Information</h2>
                    <div class="profile-card">
                        <div class="profile-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="profile-info">
                            <div class="form-group">
                                <label for="profile-email">Email Address</label>
                                <input type="email" id="profile-email" readonly>
                                <div class="email-verification-status" id="email-verification-status" style="display: none;">
                                    <div class="verification-badge" id="verification-badge">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Email not verified</span>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-secondary" id="resend-verification-btn">
                                        <i class="fas fa-envelope"></i> Resend Verification Email
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="profile-display-name">Display Name</label>
                                <input type="text" id="profile-display-name" placeholder="Enter your display name">
                                <small class="form-hint">This name will be shown in forums and community features</small>
                            </div>
                            <div class="form-group">
                                <label for="profile-join-date">Member Since</label>
                                <input type="text" id="profile-join-date" readonly>
                            </div>
                            <button type="button" class="btn btn-primary" id="save-profile-btn">
                                <i class="fas fa-save"></i> Save Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inbox Tab -->
            <div class="account-tab-content" id="inbox-tab">
                <div class="inbox-section">
                    <h2><i class="fas fa-inbox"></i> Direct Messages</h2>
                    
                    <div class="inbox-container">
                        <div class="conversations-list">
                            <div class="conversations-header">
                                <h3>Conversations</h3>
                            </div>
                            <div class="conversations-content" id="conversations-list">
                                <div class="no-conversations">
                                    <i class="fas fa-comments"></i>
                                    <p>No conversations yet</p>
                                    <button class="btn btn-primary" id="start-conversation-btn">Start a Conversation</button>
                                </div>
                            </div>
                            <div class="start-conversation-section">
                                <button class="btn btn-primary start-conversation-btn" id="start-conversation-btn-bottom">
                                    <i class="fas fa-plus"></i> Start a Conversation
                                </button>
                            </div>
                        </div>
                        
                        <div class="message-view">
                            <div class="message-header" id="message-header" style="display: none;">
                                <div class="recipient-info">
                                    <div class="recipient-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div class="recipient-details">
                                        <h4 id="recipient-name">Select a conversation</h4>
                                        <p id="recipient-status">Last seen recently</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="message-content" id="message-content">
                                <div class="no-conversation-selected">
                                    <i class="fas fa-comments"></i>
                                    <h3>Select a conversation</h3>
                                    <p>Choose from your existing conversations or start a new one</p>
                                </div>
                            </div>
                            
                            <div class="message-input" id="message-input" style="display: none;">
                                <form id="send-message-form">
                                    <div class="input-group">
                                        <input type="text" id="message-text" placeholder="Type a message..." maxlength="1000">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Conversation Modal -->
            <div class="modal-overlay" id="new-conversation-modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-comments"></i> Start a New Conversation</h3>
                        <button class="modal-close" onclick="closeNewConversationModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="conversation-recipient">Find User</label>
                            <input type="text" id="conversation-recipient" placeholder="Enter display name..." maxlength="100">
                            <small class="form-hint">Search by display name to find users</small>
                        </div>
                        <div class="search-results-modal" id="conversation-search-results" style="display: none;">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeNewConversationModal()">
                            Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="start-conversation-modal-btn" disabled>
                            <i class="fas fa-comments"></i> Start Conversation
                        </button>
                    </div>
                </div>
            </div>

            <!-- Confirmation Modal -->
            <div class="modal-overlay" id="confirmation-modal" style="display: none;">
                <div class="modal-content confirmation-modal">
                    <div class="modal-header">
                        <h3 id="confirmation-title"><i class="fas fa-exclamation-triangle"></i> Confirm Action</h3>
                        <button class="modal-close" onclick="closeConfirmationModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p id="confirmation-message">Are you sure you want to proceed?</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeConfirmationModal()">Cancel</button>
                        <button class="btn btn-danger" id="confirmation-confirm-btn">
                            <i class="fas fa-check"></i> Confirm
                        </button>
                    </div>
                </div>
            </div>

            <!-- Friends Tab -->
            <div class="account-tab-content" id="friends-tab">
                <div class="friends-section">
                    <h2><i class="fas fa-users"></i> Friends & Community</h2>
                    
                    <div class="friends-container">
                        <!-- Friends Search -->
                        <div class="friends-search">
                            <div class="search-group">
                                <div class="search-input-container">
                                    <input type="text" id="friend-search" placeholder="Search users by display name...">
                                    <button class="search-clear-btn" id="search-clear-btn" style="display: none;" onclick="clearSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-status" id="search-status" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i> Searching...
                                </div>
                            </div>
                        </div>
                        
                        <!-- Search Results -->
                        <div class="search-results-section" id="search-results-section" style="display: none;">
                            <h3><i class="fas fa-search"></i> Search Results</h3>
                            <div class="search-results" id="search-results">
                                <!-- Search results will be populated here -->
                            </div>
                        </div>
                        
                        <!-- Friend Requests -->
                        <div class="friend-requests-section">
                            <h3><i class="fas fa-user-plus"></i> Friend Requests</h3>
                            <div class="friend-requests-list" id="friend-requests-list">
                                <div class="no-requests">
                                    <p>No pending friend requests</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Friends List -->
                        <div class="friends-list-section">
                            <h3><i class="fas fa-users"></i> Your Friends</h3>
                            <div class="friends-list" id="friends-list">
                                <div class="no-friends">
                                    <i class="fas fa-users"></i>
                                    <p>You haven't added any friends yet</p>
                                    <p class="text-secondary">Use the search above to find and connect with other users</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="account-tab-content" id="settings-tab">
                <div class="settings-section">
                    <h2>Account Settings</h2>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <h4><i class="fas fa-sync"></i> Cloud Sync</h4>
                            <p>Sync your Armory X settings across devices</p>
                            <label class="toggle-switch">
                                <input type="checkbox" id="cloud-sync-toggle">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <h4><i class="fas fa-bell"></i> Email Notifications</h4>
                            <p>Receive updates about new features and forum activity</p>
                            <label class="toggle-switch">
                                <input type="checkbox" id="email-notifications-toggle">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="setting-item">
                            <h4><i class="fas fa-shield-alt"></i> Privacy Mode</h4>
                            <p>Hide your online status in forums</p>
                            <label class="toggle-switch">
                                <input type="checkbox" id="privacy-mode-toggle">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Premium Tab -->
            <div class="account-tab-content" id="premium-tab">
                <div class="premium-section">
                    <h2>Premium Features</h2>
                    <div class="premium-status">
                        <div class="status-card">
                            <i class="fas fa-user"></i>
                            <h3>Free Plan</h3>
                            <p>You're currently using the free version of Armory X</p>
                        </div>
                    </div>
                    
                    <div class="license-section">
                        <h3><i class="fas fa-key"></i> License Key</h3>
                        <div class="license-input-group">
                            <input type="text" id="license-key-input" placeholder="Enter your license key (XXXX-XXXX-XXXX-XXXX)">
                            <button class="btn btn-primary" id="validate-license-btn">
                                <i class="fas fa-check"></i> Validate
                            </button>
                        </div>
                        <div id="license-status" class="license-status"></div>
                    </div>
                    
                    <div class="premium-features">
                        <h3>Premium Benefits</h3>
                        <div class="features-grid">
                            <div class="feature-card">
                                <i class="fas fa-cloud"></i>
                                <h4>Cloud Sync</h4>
                                <p>Sync settings across all your devices</p>
                            </div>
                            <div class="feature-card">
                                <i class="fas fa-chart-line"></i>
                                <h4>Advanced Analytics</h4>
                                <p>Detailed performance reports and insights</p>
                            </div>
                            <div class="feature-card">
                                <i class="fas fa-tools"></i>
                                <h4>Premium Tools</h4>
                                <p>Access to exclusive optimization tools</p>
                            </div>
                            <div class="feature-card">
                                <i class="fas fa-headset"></i>
                                <h4>Priority Support</h4>
                                <p>Get help faster with premium support</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab -->
            <div class="account-tab-content" id="security-tab">
                <div class="security-section">
                    <h2>Security Settings</h2>
                    <div class="security-card">
                        <h3><i class="fas fa-key"></i> Change Password</h3>
                        <form id="change-password-form">
                            <div class="form-group">
                                <label for="current-password">Current Password</label>
                                <input type="password" id="current-password" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">New Password</label>
                                <input type="password" id="new-password" required>
                            </div>
                            <div class="form-group">
                                <label for="confirm-new-password">Confirm New Password</label>
                                <input type="password" id="confirm-new-password" required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-lock"></i> Update Password
                            </button>
                        </form>
                    </div>
                    
                    <div class="security-card">
                        <h3><i class="fas fa-sign-out-alt"></i> Account Actions</h3>
                        <div class="action-buttons">
                            <button class="btn btn-secondary" id="logout-btn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Moderation Tab -->
            <div class="account-tab-content" id="moderation-tab">
                <div class="moderation-section">
                    <h2><i class="fas fa-gavel"></i> Moderation Panel</h2>
                    <div class="moderation-role-badge" id="moderation-role-badge">
                        <i class="fas fa-shield-alt"></i>
                        <span id="user-role-display">Loading...</span>
                    </div>

                    <!-- User Management -->
                    <div class="moderation-card">
                        <h3><i class="fas fa-users"></i> User Management</h3>
                        <div class="user-search">
                            <input type="text" id="user-search-input" placeholder="Search users by email or display name...">
                            <button class="btn btn-primary" id="search-users-btn">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                        <div id="user-search-results" class="user-search-results"></div>
                    </div>

                    <!-- Role Management (Admin Only) -->
                    <div class="moderation-card" id="role-management-card" style="display: none;">
                        <h3><i class="fas fa-crown"></i> Role Management</h3>
                        <p class="admin-note">As an admin, you can assign moderator roles to other users.</p>
                        <div class="role-actions">
                            <div class="form-group">
                                <label for="role-user-email">User Email</label>
                                <input type="email" id="role-user-email" placeholder="Enter user email">
                            </div>
                            <div class="form-group">
                                <label for="new-role-select">Role</label>
                                <select id="new-role-select">
                                    <option value="user">User</option>
                                    <option value="moderator">Moderator</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" id="assign-role-btn">
                                <i class="fas fa-user-cog"></i> Assign Role
                            </button>
                        </div>
                        <div id="role-assignment-status" class="status-message"></div>
                    </div>

                    <!-- Banned Users -->
                    <div class="moderation-card">
                        <h3><i class="fas fa-ban"></i> Banned Users</h3>
                        <div id="banned-users-list" class="banned-users-list">
                            <div class="loading-message">
                                <i class="fas fa-spinner fa-spin"></i> Loading banned users...
                            </div>
                        </div>
                    </div>

                    <!-- Reported Content -->
                    <div class="moderation-card">
                        <h3><i class="fas fa-flag"></i> Reported Content</h3>
                        <div id="reported-content-list" class="reported-content-list">
                            <div class="loading-message">
                                <i class="fas fa-spinner fa-spin"></i> Loading reports...
                            </div>
                        </div>
                    </div>

                    <!-- Moderation Statistics -->
                    <div class="moderation-card">
                        <h3><i class="fas fa-chart-bar"></i> Moderation Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <div class="stat-info">
                                    <span class="stat-number" id="total-users">0</span>
                                    <span class="stat-label">Total Users</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-ban"></i>
                                <div class="stat-info">
                                    <span class="stat-number" id="banned-users-count">0</span>
                                    <span class="stat-label">Banned Users</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-flag"></i>
                                <div class="stat-info">
                                    <span class="stat-number" id="pending-reports">0</span>
                                    <span class="stat-label">Pending Reports</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comments"></i>
                                <div class="stat-info">
                                    <span class="stat-number" id="total-posts">0</span>
                                    <span class="stat-label">Forum Posts</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="moderation-card">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                        <div class="quick-actions">
                            <button class="btn btn-secondary" id="refresh-forum-btn">
                                <i class="fas fa-sync"></i> Refresh Forum Data
                            </button>
                            <button class="btn btn-secondary" id="export-logs-btn">
                                <i class="fas fa-download"></i> Export Mod Logs
                            </button>
                            <button class="btn btn-secondary" id="view-forum-btn">
                                <i class="fas fa-external-link-alt"></i> Go to Forums
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/Armory_X.ico" alt="Armory X" class="footer-logo-icon">
                        <span>Armory X</span>
                    </div>
                    <p>The ultimate PC tool suite for optimization, gaming, and system management.</p>
                    <div class="social-links">
                        <a href="http://discord.gg/uq4Zs2G57g" target="_blank">
                            <i class="fab fa-discord"></i>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html#features">Features</a></li>
                        <li><a href="index.html#download">Download</a></li>
                        <li><a href="index.html#updates">Updates</a></li>
                        <li><a href="account.html">Account</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Community</h4>
                    <ul>
                        <li><a href="forums.html">Forums</a></li>
                        <li><a href="http://discord.gg/uq4Zs2G57g">Discord</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">License</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Armory X. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="account-script.js"></script>
    <script src="animations.js"></script>
    <script src="notifications.js"></script>
    <!-- License System Scripts -->
    <script src="license-key-system.js"></script>
    <script src="license-integration.js"></script>
    <script src="debug-license-system.js"></script>
    
    <!-- Emergency Database Clear (Console Command) -->
    <script src="emergency-database-clear.js"></script>
</body>
</html> 