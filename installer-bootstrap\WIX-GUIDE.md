# Armory X WiX Installer Guide

## What is WiX?
WiX (Windows Installer XML) is Microsoft's official toolset for building Windows installation packages. It creates professional `.msi` files that are the standard for Windows software distribution.

## Key Benefits
- ✅ **Professional MSI installers** - The same technology used by Microsoft Office, Visual Studio, etc.
- ✅ **Full Windows integration** - Proper registry entries, Add/Remove Programs, etc.
- ✅ **Reliable uninstall** - Cleanly removes all files and registry entries
- ✅ **No runtime required** - MSI files work on all Windows systems
- ✅ **Enterprise-ready** - IT departments can deploy via Group Policy
- ✅ **Silent install support** - `msiexec /i ArmoryX.msi /quiet`

## Quick Start

### 1. Install WiX (if not done already)
```powershell
# Option 1: Via winget
winget install --id=WiX.WiX -e

# Option 2: Direct download
# Visit: https://github.com/wixtoolset/wix3/releases
# Download: wix314.exe (or latest version)
```

### 2. Build the Installer
```powershell
cd installer-bootstrap
.\build-wix.ps1
```

### 3. Test the Installer
```powershell
# Install
msiexec /i ArmoryX-Setup.msi

# Install with logging (for debugging)
msiexec /i ArmoryX-Setup.msi /l*v install.log

# Uninstall
msiexec /x ArmoryX-Setup.msi
```

## File Structure
```
installer-bootstrap/
├── armory-x-installer.wxs    # Main WiX configuration
├── build-wix.ps1             # Build script
├── payload/                  # Application files
│   ├── ArmoryX.exe          # Main executable
│   ├── config.json          # Default configuration
│   ├── README.txt           # User documentation
│   └── LICENSE.txt          # License agreement
├── banner.bmp               # Top banner (493x58)
└── dialog.bmp               # Side image (493x312)
```

## Customization

### Change Install Location
The installer defaults to `%LOCALAPPDATA%\ArmoryX`. To change to Program Files:
```xml
<!-- Change this line in .wxs file -->
<Directory Id="LocalAppDataFolder">
<!-- To: -->
<Directory Id="ProgramFilesFolder">
```

### Add More Files
```xml
<Component Id="MyNewFile" Guid="NEW-GUID-HERE">
    <File Id="MyFile" Name="myfile.dll" Source="payload\myfile.dll" />
</Component>
```

### Custom Actions
Add custom actions for special installation steps:
```xml
<CustomAction Id="ConfigureFirewall" 
              Directory="INSTALLDIR"
              ExeCommand="netsh advfirewall firewall add rule name=&quot;Armory X&quot; dir=in action=allow program=&quot;[INSTALLDIR]ArmoryX.exe&quot;"
              Execute="deferred"
              Impersonate="no"
              Return="ignore" />
```

## Advanced Features

### 1. Per-User vs Per-Machine
Current: `InstallScope="perUser"` (no admin required)
Change to: `InstallScope="perMachine"` (requires admin, installs for all users)

### 2. Upgrade Handling
The installer automatically handles upgrades. Users can install new versions over old ones.

### 3. Feature Selection
Users can choose which features to install (desktop shortcut is optional).

### 4. Silent Installation
For enterprise deployment:
```powershell
# Silent install with all features
msiexec /i ArmoryX-Setup.msi /quiet

# Silent install without desktop shortcut
msiexec /i ArmoryX-Setup.msi /quiet ADDLOCAL=ProductFeature
```

## Signing the Installer

For production, you should sign the MSI:
```powershell
signtool sign /f "certificate.pfx" /p "password" /d "Armory X" ArmoryX-Setup.msi
```

## Troubleshooting

### "WiX not found"
- Make sure WiX is installed
- Try both Program Files locations
- Update the path in build-wix.ps1

### Build Errors
- Check that all source files exist
- Ensure GUIDs are unique
- Look at the detailed error messages

### Installation Fails
- Run with logging: `msiexec /i ArmoryX-Setup.msi /l*v install.log`
- Check Windows Event Viewer
- Ensure no files are in use

## Next Steps

1. **Replace placeholder files** - Put your actual ArmoryX.exe in the payload folder
2. **Create proper graphics** - Replace banner.bmp and dialog.bmp with branded images
3. **Test thoroughly** - Install, uninstall, upgrade scenarios
4. **Sign the MSI** - Get a code signing certificate for production

## Resources
- [WiX Documentation](https://wixtoolset.org/documentation/)
- [WiX Tutorial](https://www.firegiant.com/wix/tutorial/)
- [MSI Error Codes](https://docs.microsoft.com/en-us/windows/win32/msi/error-codes) 