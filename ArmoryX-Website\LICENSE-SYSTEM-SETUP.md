# ArmoryX License System Setup Guide

## 🚀 Quick Start Guide

Your license system has been successfully implemented! Follow these steps to set it up and test it.

## 1. Update Firebase Security Rules

**IMPORTANT**: You must update your Firebase security rules first!

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your ArmoryX project
3. Go to **Firestore Database** → **Rules**
4. Copy the rules from `firebase-security-rules.md` and paste them
5. Click **Publish**

## 2. Set Up Admin Account

To generate license keys, you need admin privileges:

### Method 1: Firebase Console (Recommended)
1. Go to Firebase Console → Firestore Database → Data
2. Find the `users` collection
3. Locate your user document (your user ID)
4. Edit the document and add:
   - **Field**: `role`
   - **Type**: `string` 
   - **Value**: `admin`
5. Save the document

### Method 2: Browser Console (Alternative)
1. Log in to your website account
2. Open browser Developer Tools (F12)
3. Go to Console tab
4. Run this command:
```javascript
// Set yourself as admin (run this once)
if (window.firebase && window.firebase.auth.currentUser) {
    const userId = window.firebase.auth.currentUser.uid;
    window.firebase.setDoc(window.firebase.doc(window.firebase.db, 'users', userId), {
        role: 'admin',
        email: window.firebase.auth.currentUser.email,
        displayName: window.firebase.auth.currentUser.displayName || 'Admin User',
        createdAt: window.firebase.serverTimestamp()
    }, { merge: true }).then(() => {
        console.log('✅ Admin role assigned successfully!');
        location.reload();
    }).catch(error => {
        console.error('❌ Error assigning admin role:', error);
    });
} else {
    console.log('Please log in first');
}
```

## 3. Test the License System

### Testing License Key Generation (Admin Only)
1. Log in to your account
2. Go to **Account** → **Premium** tab
3. You should see the **Admin Panel** tab (only visible to admins)
4. Click **Admin Panel**
5. In "Generate License Keys" section:
   - Select key type (Trial, Monthly, Yearly, Lifetime)
   - Set count (1-50)
   - Click **Generate Keys**
6. Copy one of the generated keys

### Testing License Key Activation
1. Go to **Activate License** tab
2. Paste the license key (format: ARMX-XXXX-XXXX-XXXX-XXXX)
3. Click **Activate**
4. The system should show success and update your account status

### Testing Trial License
1. Go to **Purchase License** tab
2. Click **Start Trial** on the Trial License card
3. If you're an admin, it should auto-generate and activate a 7-day trial

## 4. License System Features

### 🔑 **License Key Format**
- Format: `ARMX-XXXX-XXXX-XXXX-XXXX`
- Example: `ARMX-A1B2-C3D4-E5F6-G7H8`

### 📱 **Hardware Binding**
- Each license is bound to the user's browser fingerprint
- Prevents sharing keys between different machines
- Based on: User agent, screen resolution, timezone, hardware specs

### 📊 **License Types**
- **Trial**: 7 days, free
- **Monthly**: $9/month, recurring
- **Yearly**: 12 months, one-time
- **Lifetime**: Permanent, one-time

### 🛡️ **Security Features**
- Server-side validation
- Hardware ID verification
- Comprehensive logging
- Admin-only key generation
- Automatic expiration handling

## 5. Database Collections Created

The system creates these Firestore collections:

### License Management
- `license_keys` - All generated license keys
- `user_licenses` - User license information and hardware binding
- `key_generation_log` - Admin key generation history
- `key_validation_log` - Security monitoring logs

## 6. API Reference

### For Developers

The license system exposes these global functions:

```javascript
// Initialize license manager
const licenseManager = new window.LicenseKeyManager();

// Generate a license key (admin only)
const result = await licenseManager.createLicenseKey('lifetime');

// Activate a license key
const activation = await licenseManager.activateLicenseKey('ARMX-1234-5678-9012-3456');

// Check user's license status
const status = await licenseManager.getUserLicenseStatus(userId);

// Validate license (for premium feature checks)
const validation = await licenseManager.validateLicenseKey(userId, hwid);
```

## 7. Troubleshooting

### Common Issues

**❌ "License manager not initialized"**
- Wait for Firebase to load completely
- Check browser console for Firebase connection errors

**❌ "Insufficient permissions to generate keys"**
- Make sure your account has `role: 'admin'` in Firestore
- Refresh the page after setting admin role

**❌ "Invalid license key"**
- Check the key format: ARMX-XXXX-XXXX-XXXX-XXXX
- Make sure the key exists in the database
- Verify the key hasn't been revoked

**❌ "License key is already in use"**
- Each key can only be activated once
- Hardware binding prevents sharing between machines

### Debug Mode

Enable debug logging in browser console:
```javascript
// Enable debug mode
window.LicenseKeyManager.DEBUG = true;
```

## 8. Next Steps

### Integration with Desktop App
Now that the website license system is working, the next phase is:

1. **WPF App Integration**
   - Add login/authentication to Armory X desktop app
   - Implement license validation on app startup
   - Connect to Firebase from WPF application

2. **Feature Restrictions**
   - Block premium features for free users
   - Show upgrade prompts for locked features
   - Implement license level checking

3. **Payment Integration**
   - Add Stripe/PayPal for license purchases
   - Automated license delivery
   - Subscription management

## 🎉 Congratulations!

Your license system is now fully functional! Users can:
- ✅ Purchase and activate license keys
- ✅ Get hardware-bound premium access
- ✅ Admins can generate and manage keys
- ✅ Full security and validation system
- ✅ Trial system for testing

The system is production-ready and scalable for thousands of users!

---

## Support

If you encounter any issues:
1. Check the browser console for errors
2. Verify Firebase security rules are applied
3. Ensure admin role is properly set
4. Review the troubleshooting section above

Happy licensing! 🚀 