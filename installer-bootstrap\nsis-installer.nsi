; Armory X NSIS Installer
; Modern UI with custom styling

!include "MUI2.nsh"
!include "nsDialogs.nsh"

; --------------------------------
; General Configuration

Name "Armory X"
OutFile "ArmoryX-Setup.exe"
InstallDir "$LOCALAPPDATA\ArmoryX"
InstallDirRegKey HKCU "Software\ArmoryX" ""
RequestExecutionLevel user

; --------------------------------
; Interface Settings

!define MUI_ABORTWARNING
!define MUI_ICON "..\ArmoryX-Website\assets\Armory_X.ico"
!define MUI_HEADERIMAGE
!define MUI_WELCOMEFINISHPAGE_BITMAP "installer-sidebar.bmp"
!define MUI_BGCOLOR 1a1a2e
!define MUI_TEXTCOLOR edf2f4

; --------------------------------
; Pages

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; --------------------------------
; Languages

!insertmacro MUI_LANGUAGE "English"

; --------------------------------
; Installer Sections

Section "Armory X" SecMain
  SetOutPath "$INSTDIR"
  
  ; Extract files
  File /r "..\armory-x\target\release\*.*"
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\Armory X"
  CreateShortcut "$SMPROGRAMS\Armory X\Armory X.lnk" "$INSTDIR\armory-x.exe"
  CreateShortcut "$DESKTOP\Armory X.lnk" "$INSTDIR\armory-x.exe"
  
  ; Write uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Registry information
  WriteRegStr HKCU "Software\ArmoryX" "" $INSTDIR
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "DisplayName" "Armory X"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX" \
                   "UninstallString" "$INSTDIR\Uninstall.exe"
SectionEnd

; --------------------------------
; Uninstaller Section

Section "Uninstall"
  Delete "$INSTDIR\*.*"
  RMDir /r "$INSTDIR"
  Delete "$SMPROGRAMS\Armory X\*.*"
  RMDir "$SMPROGRAMS\Armory X"
  Delete "$DESKTOP\Armory X.lnk"
  
  DeleteRegKey HKCU "Software\ArmoryX"
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\ArmoryX"
SectionEnd 