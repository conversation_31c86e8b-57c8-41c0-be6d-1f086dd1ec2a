<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" 
           Name="Armory X" 
           Language="1033" 
           Version="1.0.0.0" 
           Manufacturer="Armory X"
           UpgradeCode="YOUR-GUID-HERE">
           
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" />

    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
    <MediaTemplate EmbedCab="yes" />

    <!-- Custom UI -->
    <UIRef Id="WixUI_Minimal" />
    <WixVariable Id="WixUILicenseRtf" Value="LICENSE.rtf" />
    <WixVariable Id="WixUIBannerBmp" Value="banner.bmp" />
    <WixVariable Id="WixUIDialogBmp" Value="dialog.bmp" />

    <!-- Installation Directory -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="Armory X">
          
          <!-- Main Application Files -->
          <Component Id="MainExecutable" Guid="YOUR-GUID-HERE">
            <File Id="ArmoryX.exe" 
                  Source="..\armory-x\target\release\armory-x.exe" 
                  KeyPath="yes">
              <Shortcut Id="StartMenuShortcut"
                        Directory="ProgramMenuFolder"
                        Name="Armory X"
                        WorkingDirectory="INSTALLFOLDER"
                        Icon="ArmoryX.ico"
                        Advertise="yes" />
            </File>
          </Component>
          
          <!-- Additional Files -->
          <Component Id="ConfigFiles" Guid="YOUR-GUID-HERE">
            <File Id="config.json" Source="config.json" />
          </Component>
          
        </Directory>
      </Directory>
      
      <!-- Start Menu -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Armory X"/>
      </Directory>
      
      <!-- Desktop -->
      <Directory Id="DesktopFolder" Name="Desktop">
        <Component Id="DesktopShortcut" Guid="YOUR-GUID-HERE">
          <Shortcut Id="DesktopShortcut"
                    Name="Armory X"
                    Target="[INSTALLFOLDER]armory-x.exe"
                    WorkingDirectory="INSTALLFOLDER"
                    Icon="ArmoryX.ico" />
          <RemoveFolder Id="DesktopFolder" On="uninstall"/>
          <RegistryValue Root="HKCU" 
                         Key="Software\ArmoryX" 
                         Name="installed" 
                         Type="integer" 
                         Value="1" 
                         KeyPath="yes"/>
        </Component>
      </Directory>
    </Directory>

    <!-- Features -->
    <Feature Id="ProductFeature" Title="Armory X" Level="1">
      <ComponentRef Id="MainExecutable" />
      <ComponentRef Id="ConfigFiles" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>

    <!-- Icons -->
    <Icon Id="ArmoryX.ico" SourceFile="..\ArmoryX-Website\assets\Armory_X.ico"/>
  </Product>
</Wix> 