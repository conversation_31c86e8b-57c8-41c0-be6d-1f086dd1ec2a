<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  
  <!-- Product Information -->
  <Package Name="Armory X" 
           Language="1033" 
           Version="1.0.0.0" 
           Manufacturer="Armory X"
           UpgradeCode="A7B8C9D0-1234-5678-90AB-CDEF12345678"
           InstallerVersion="500"
           Scope="perUser"
           Compressed="yes">
           
    <Property Id="ARPPRODUCTICON" Value="ArmoryX.ico" />
    <Property Id="ARPURLINFOABOUT" Value="https://armoryx.com" />
    <Property Id="ARPURLUPDATEINFO" Value="https://armoryx.com/updates" />
    
    <!-- Upgrade behavior -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
    
    <!-- Media -->
    <Media Id="1" Cabinet="product.cab" EmbedCab="yes" CompressionLevel="high" />

    <!-- Directory Structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <!-- LocalAppData -->
      <Directory Id="LocalAppDataFolder">
        <Directory Id="INSTALLFOLDER" Name="ArmoryX">
          
          <!-- Core Files -->
          <Component Id="MainExecutable" Guid="B1C2D3E4-2345-6789-0ABC-DEF123456789">
            <File Id="ArmoryXExe" 
                  Name="ArmoryX.exe" 
                  Source="payload\ArmoryX.exe" 
                  KeyPath="yes" />
          </Component>
          
          <!-- Config Files -->
          <Component Id="ConfigFiles" Guid="C2D3E4F5-3456-7890-1BCD-EF2345678901">
            <File Id="ConfigJson" Name="config.json" Source="payload\config.json" />
            <File Id="ReadmeTxt" Name="README.txt" Source="payload\README.txt" />
            <File Id="LicenseTxt" Name="LICENSE.txt" Source="payload\LICENSE.txt" />
          </Component>
          
          <!-- Create directories -->
          <Directory Id="ResourcesDir" Name="Resources">
            <Component Id="Resources" Guid="D3E4F5A6-4567-8901-2CDE-F34567890123">
              <CreateFolder />
            </Component>
          </Directory>
          
          <Directory Id="DataDir" Name="Data">
            <Component Id="DataFolder" Guid="E4F5A6B7-5678-9012-3DEF-************">
              <CreateFolder />
            </Component>
          </Directory>
          
          <Directory Id="LogsDir" Name="Logs">
            <Component Id="LogsFolder" Guid="F5A6B7C8-6789-0123-4EFA-************">
              <CreateFolder />
            </Component>
          </Directory>
        </Directory>
      </Directory>
      
      <!-- Desktop -->
      <Directory Id="DesktopFolder">
        <Component Id="DesktopShortcut" Guid="B7C8D9E0-8901-2345-6ABC-************">
          <Shortcut Id="DesktopShortcut"
                    Name="Armory X"
                    Description="Professional System Utility Suite"
                    Target="[INSTALLFOLDER]ArmoryX.exe"
                    WorkingDirectory="INSTALLFOLDER"
                    Icon="ArmoryX.ico" />
          <RegistryValue Root="HKCU" 
                         Key="Software\ArmoryX" 
                         Name="DesktopShortcut" 
                         Type="integer" 
                         Value="1" 
                         KeyPath="yes" />
        </Component>
      </Directory>
      
      <!-- Start Menu -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Armory X">
          <Component Id="ApplicationShortcuts" Guid="A6B7C8D9-7890-1234-5FAB-************">
            <Shortcut Id="StartMenuShortcut"
                      Name="Armory X"
                      Description="Professional System Utility Suite"
                      Target="[INSTALLFOLDER]ArmoryX.exe"
                      WorkingDirectory="INSTALLFOLDER"
                      Icon="ArmoryX.ico" />
            <Shortcut Id="UninstallProduct"
                      Name="Uninstall Armory X"
                      Description="Uninstalls Armory X"
                      Target="[SystemFolder]msiexec.exe"
                      Arguments="/x [ProductCode]" />
            <RemoveFolder Id="RemoveApplicationProgramsFolder" On="uninstall" />
            <RegistryValue Root="HKCU" 
                           Key="Software\ArmoryX" 
                           Name="installed" 
                           Type="integer" 
                           Value="1" 
                           KeyPath="yes" />
          </Component>
        </Directory>
      </Directory>
    </Directory>

    <!-- Icons -->
    <Icon Id="ArmoryX.ico" SourceFile="..\ArmoryX-Website\assets\Armory_X.ico" />

    <!-- Features -->
    <Feature Id="ProductFeature" Title="Armory X" Level="1">
      <ComponentRef Id="MainExecutable" />
      <ComponentRef Id="ConfigFiles" />
      <ComponentRef Id="Resources" />
      <ComponentRef Id="DataFolder" />
      <ComponentRef Id="LogsFolder" />
      <ComponentRef Id="ApplicationShortcuts" />
      <ComponentRef Id="DesktopShortcut" />
    </Feature>
  </Package>
</Wix> 