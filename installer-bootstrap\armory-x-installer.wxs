<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">
  
  <!-- Product Information -->
  <Product Id="*" 
           Name="Armory X" 
           Language="1033" 
           Version="*******" 
           Manufacturer="Armory X"
           UpgradeCode="A7B8C9D0-1234-5678-90AB-CDEF12345678">
           
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perUser"
             Description="Armory X - Professional System Utility Suite"
             Manufacturer="Armory X"
             Comments="Your all-in-one system optimization toolkit" />

    <!-- Upgrade behavior -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." 
                  AllowSameVersionUpgrades="yes" />
    
    <!-- Media -->
    <MediaTemplate EmbedCab="yes" CompressionLevel="high" />

    <!-- Properties -->
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    <Property Id="ARPPRODUCTICON" Value="ArmoryX.ico" />
    <Property Id="ARPURLINFOABOUT" Value="https://armoryx.com" />
    <Property Id="ARPURLUPDATEINFO" Value="https://armoryx.com/updates" />
    
    <!-- Launch condition -->
    <Condition Message="This application requires Windows 10 or higher.">
      <![CDATA[Installed OR (VersionNT >= 603)]]>
    </Condition>

    <!-- Custom UI -->
    <UI>
      <UIRef Id="WixUI_InstallDir" />
      
      <!-- Skip license dialog -->
      <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="InstallDirDlg" Order="2">1</Publish>
      <Publish Dialog="InstallDirDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="2">1</Publish>
    </UI>
    
    <!-- Custom Images -->
    <WixVariable Id="WixUIBannerBmp" Value="banner.bmp" />
    <WixVariable Id="WixUIDialogBmp" Value="dialog.bmp" />

    <!-- Directory Structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <!-- User's Local AppData -->
      <Directory Id="LocalAppDataFolder">
        <Directory Id="INSTALLFOLDER" Name="ArmoryX">
          
          <!-- Core Files -->
          <Directory Id="INSTALLDIR" Name="app">
            <Component Id="MainExecutable" Guid="B1C2D3E4-2345-6789-0ABC-DEF123456789">
              <File Id="ArmoryXExe" 
                    Name="ArmoryX.exe" 
                    Source="payload\ArmoryX.exe" 
                    KeyPath="yes">
                <Shortcut Id="StartMenuShortcut"
                          Directory="ProgramMenuFolder"
                          Name="Armory X"
                          Description="Professional System Utility Suite"
                          WorkingDirectory="INSTALLDIR"
                          Icon="ArmoryX.ico"
                          Advertise="yes">
                  <Icon Id="StartMenuIcon" SourceFile="..\ArmoryX-Website\assets\Armory_X.ico" />
                </Shortcut>
              </File>
              
              <!-- File association -->
              <ProgId Id="ArmoryX.Document" Description="Armory X Document">
                <Extension Id="armx" ContentType="application/armory-x">
                  <Verb Id="open" Command="Open" TargetFile="ArmoryXExe" Argument='"%1"' />
                </Extension>
              </ProgId>
            </Component>
            
            <!-- Config Files -->
            <Component Id="ConfigFiles" Guid="C2D3E4F5-3456-7890-1BCD-EF2345678901">
              <File Id="ConfigJson" Name="config.json" Source="payload\config.json" />
              <File Id="ReadmeTxt" Name="README.txt" Source="payload\README.txt" />
              <File Id="LicenseTxt" Name="LICENSE.txt" Source="payload\LICENSE.txt" />
            </Component>
          </Directory>
          
          <!-- Resources Directory -->
          <Directory Id="ResourcesDir" Name="Resources">
            <Component Id="Resources" Guid="D3E4F5A6-4567-8901-2CDE-F34567890123">
              <CreateFolder />
            </Component>
          </Directory>
          
          <!-- Data Directory -->
          <Directory Id="DataDir" Name="Data">
            <Component Id="DataFolder" Guid="E4F5A6B7-5678-9012-3DEF-************">
              <CreateFolder />
              <RemoveFolder Id="DataDir" On="uninstall" />
            </Component>
          </Directory>
          
          <!-- Logs Directory -->
          <Directory Id="LogsDir" Name="Logs">
            <Component Id="LogsFolder" Guid="F5A6B7C8-6789-0123-4EFA-************">
              <CreateFolder />
              <RemoveFolder Id="LogsDir" On="uninstall" />
            </Component>
          </Directory>
        </Directory>
      </Directory>
      
      <!-- Start Menu -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Armory X">
          <Component Id="ApplicationShortcuts" Guid="A6B7C8D9-7890-1234-5FAB-************">
            <Shortcut Id="UninstallProduct"
                      Name="Uninstall Armory X"
                      Description="Uninstalls Armory X"
                      Target="[SystemFolder]msiexec.exe"
                      Arguments="/x [ProductCode]" />
            <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
            <RegistryValue Root="HKCU" 
                           Key="Software\ArmoryX" 
                           Name="installed" 
                           Type="integer" 
                           Value="1" 
                           KeyPath="yes" />
          </Component>
        </Directory>
      </Directory>
      
      <!-- Desktop -->
      <Directory Id="DesktopFolder" Name="Desktop">
        <Component Id="DesktopShortcut" Guid="B7C8D9E0-8901-2345-6ABC-************">
          <Shortcut Id="DesktopShortcut"
                    Name="Armory X"
                    Description="Professional System Utility Suite"
                    Target="[INSTALLDIR]ArmoryX.exe"
                    WorkingDirectory="INSTALLDIR"
                    Icon="DesktopIcon.ico" />
          <RemoveFolder Id="RemoveDesktopFolder" Directory="DesktopFolder" On="uninstall" />
          <RegistryValue Root="HKCU" 
                         Key="Software\ArmoryX" 
                         Name="DesktopShortcut" 
                         Type="integer" 
                         Value="1" 
                         KeyPath="yes" />
          <Icon Id="DesktopIcon.ico" SourceFile="..\ArmoryX-Website\assets\Armory_X.ico" />
        </Component>
      </Directory>
    </Directory>

    <!-- Features -->
    <Feature Id="ProductFeature" 
             Title="Armory X Core" 
             Description="Core application files and features"
             Level="1"
             Display="expand"
             ConfigurableDirectory="INSTALLFOLDER"
             AllowAdvertise="no"
             Absent="disallow">
      <ComponentRef Id="MainExecutable" />
      <ComponentRef Id="ConfigFiles" />
      <ComponentRef Id="Resources" />
      <ComponentRef Id="DataFolder" />
      <ComponentRef Id="LogsFolder" />
      <ComponentRef Id="ApplicationShortcuts" />
      
      <Feature Id="DesktopShortcutFeature" 
               Title="Desktop Shortcut" 
               Description="Add shortcut to desktop"
               Level="1">
        <ComponentRef Id="DesktopShortcut" />
      </Feature>
    </Feature>

    <!-- Icons -->
    <Icon Id="ArmoryX.ico" SourceFile="..\ArmoryX-Website\assets\Armory_X.ico" />
    
    <!-- Custom Actions -->
    <CustomAction Id="LaunchApplication" 
                  FileKey="ArmoryXExe" 
                  ExeCommand="" 
                  Execute="immediate" 
                  Impersonate="yes" 
                  Return="asyncNoWait" />
    
    <!-- UI Customization -->
    <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="Launch Armory X" />
    <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1" />
    <Property Id="WixShellExecTarget" Value="[#ArmoryXExe]" />
    <CustomAction Id="LaunchApp" 
                  BinaryKey="WixCA" 
                  DllEntry="WixShellExec" 
                  Impersonate="yes" />
    
    <!-- Execute Sequence -->
    <InstallExecuteSequence>
      <Custom Action="LaunchApp" After="InstallFinalize">
        WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed
      </Custom>
    </InstallExecuteSequence>
  </Product>
</Wix> 